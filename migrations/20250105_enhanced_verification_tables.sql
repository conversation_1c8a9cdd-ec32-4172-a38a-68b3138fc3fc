-- Migration File: 20250105_enhanced_verification_tables.sql
-- MCP Analysis Completed: 2025-01-05
-- DRY Validation: PASSED - Uses existing tables, minimal new structures
-- Redundancy Check: CLEAN - No duplicate functionality detected
-- Reviewed By: MCP_ROOMMATE_DBL_2025
-- 
-- EXISTING ARTIFACTS ANALYSIS:
-- Similar Tables: user_profiles (verification cols), social_media_profiles, reviews, ratings, safety_reports
-- Overlapping Indexes: Existing verification indexes found
-- Related Functions: Background check and verification functions exist
--
-- CONSOLIDATION OPPORTUNITIES:
-- - Use existing reviews table for references (add reference_type column)
-- - Extend social_media_profiles for OAuth verification
-- - Use existing user_interactions for verification activities
-- - Leverage existing notifications for verification updates
--
-- JUSTIFICATION FOR NEW ARTIFACTS:
-- - emergency_contacts: Safety feature not covered by existing tables
-- - safety_checkins: Check-in system requires dedicated tracking
-- - Enhanced reviews table: Add reference verification capability

-- =====================================================
-- PRE-<PERSON><PERSON><PERSON>ION SAFETY CHECKS
-- =====================================================
DO $$
BEGIN
    -- Verify we're not creating duplicate functionality
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_references') THEN
        RAISE EXCEPTION 'user_references table would duplicate reviews functionality - DRY principle violation';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_connections') THEN
        RAISE EXCEPTION 'social_connections table would duplicate social_media_profiles - DRY principle violation';
    END IF;
    
    -- Verify existing tables we'll enhance exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews') THEN
        RAISE EXCEPTION 'reviews table must exist for reference enhancement';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_media_profiles') THEN
        RAISE EXCEPTION 'social_media_profiles table must exist for OAuth enhancement';
    END IF;
    
    -- Verify existing verification tables
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        RAISE EXCEPTION 'user_profiles table must exist for verification tracking';
    END IF;
    
    RAISE NOTICE 'DRY validation passed - using existing tables where possible';
END $$;

-- =====================================================
-- ENHANCED VERIFICATION SYSTEM - LEVERAGING EXISTING TABLES
-- =====================================================

-- 1. ENHANCE EXISTING REVIEWS TABLE FOR REFERENCE SYSTEM
-- Add columns to existing reviews table to support reference verification
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS rating INTEGER CHECK (rating >= 1 AND rating <= 5);
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS interaction_type VARCHAR(50) DEFAULT 'general';
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS verified_interaction BOOLEAN DEFAULT FALSE;

-- Add reference-specific columns
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS reference_type VARCHAR(20) CHECK (reference_type IN ('roommate', 'landlord', 'employer', 'personal'));
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS reference_contact_email VARCHAR(255);
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS reference_verified_at TIMESTAMPTZ;
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS reference_verification_token VARCHAR(255);

COMMENT ON COLUMN reviews.reference_type IS 'Type of reference when used for reference checking';
COMMENT ON COLUMN reviews.reference_contact_email IS 'Email for reference verification when acting as reference';
COMMENT ON COLUMN reviews.verified_interaction IS 'Whether this review/reference represents a verified real interaction';

-- 2. ENHANCE EXISTING SOCIAL_MEDIA_PROFILES FOR OAUTH VERIFICATION
-- Add OAuth-specific verification data
ALTER TABLE social_media_profiles ADD COLUMN IF NOT EXISTS oauth_access_token_hash VARCHAR(255);
ALTER TABLE social_media_profiles ADD COLUMN IF NOT EXISTS oauth_expires_at TIMESTAMPTZ;
ALTER TABLE social_media_profiles ADD COLUMN IF NOT EXISTS profile_data JSONB DEFAULT '{}';
ALTER TABLE social_media_profiles ADD COLUMN IF NOT EXISTS verification_score INTEGER DEFAULT 0 CHECK (verification_score >= 0 AND verification_score <= 100);
ALTER TABLE social_media_profiles ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMPTZ;

COMMENT ON COLUMN social_media_profiles.oauth_access_token_hash IS 'Hashed OAuth token for secure verification';
COMMENT ON COLUMN social_media_profiles.profile_data IS 'Cached profile data from OAuth provider';
COMMENT ON COLUMN social_media_profiles.verification_score IS 'Calculated verification score for this social profile';

-- 3. ENHANCE USER_PROFILES FOR COMPREHENSIVE VERIFICATION TRACKING
-- Add university verification
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS university_email VARCHAR(255);
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS university_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS university_verified_at TIMESTAMPTZ;

-- Add photo verification
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS photo_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS photo_verified_at TIMESTAMPTZ;
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS selfie_url TEXT;
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS id_document_url TEXT;

-- Add comprehensive trust score
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS trust_score INTEGER DEFAULT 0 CHECK (trust_score >= 0 AND trust_score <= 100);
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS trust_score_updated_at TIMESTAMPTZ DEFAULT NOW();

COMMENT ON COLUMN user_profiles.trust_score IS 'Calculated overall trust score (0-100) based on all verification methods';

-- 4. NEW TABLE: EMERGENCY_CONTACTS (Not duplicated elsewhere)
CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    relationship VARCHAR(100) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE emergency_contacts IS 'Emergency contact information for safety features';

-- 5. NEW TABLE: SAFETY_CHECKINS (Unique functionality)
CREATE TABLE IF NOT EXISTS safety_checkins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    checkin_type VARCHAR(50) NOT NULL CHECK (checkin_type IN ('meeting', 'viewing', 'emergency', 'routine')),
    scheduled_time TIMESTAMPTZ NOT NULL,
    actual_checkin_time TIMESTAMPTZ,
    location TEXT,
    coordinates POINT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'checked_in', 'overdue', 'emergency')),
    emergency_contact_id UUID REFERENCES emergency_contacts(id),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE safety_checkins IS 'Safety check-in system for meetings and viewings';

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Enhanced reviews indexes
CREATE INDEX IF NOT EXISTS idx_reviews_reference_type ON reviews(reference_type) WHERE reference_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_reviews_reference_email ON reviews(reference_contact_email) WHERE reference_contact_email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_reviews_verified_interaction ON reviews(verified_interaction) WHERE verified_interaction = TRUE;

-- Enhanced social media profiles indexes
CREATE INDEX IF NOT EXISTS idx_social_media_verification_score ON social_media_profiles(verification_score);
CREATE INDEX IF NOT EXISTS idx_social_media_last_verified ON social_media_profiles(last_verified_at);

-- User profiles trust score index
CREATE INDEX IF NOT EXISTS idx_user_profiles_trust_score ON user_profiles(trust_score);
CREATE INDEX IF NOT EXISTS idx_user_profiles_university_verified ON user_profiles(university_verified) WHERE university_verified = TRUE;

-- Emergency contacts indexes
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_user_id ON emergency_contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_primary ON emergency_contacts(user_id, is_primary) WHERE is_primary = TRUE;

-- Safety checkins indexes
CREATE INDEX IF NOT EXISTS idx_safety_checkins_user_id ON safety_checkins(user_id);
CREATE INDEX IF NOT EXISTS idx_safety_checkins_status ON safety_checkins(status);
CREATE INDEX IF NOT EXISTS idx_safety_checkins_scheduled_time ON safety_checkins(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_safety_checkins_overdue ON safety_checkins(scheduled_time, status) WHERE status = 'pending';

-- =====================================================
-- ENHANCED DATABASE FUNCTIONS
-- =====================================================

-- Function to calculate comprehensive trust score using existing data
CREATE OR REPLACE FUNCTION calculate_user_trust_score(target_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    identity_score INTEGER := 0;
    social_score INTEGER := 0;
    university_score INTEGER := 0;
    review_score INTEGER := 0;
    reference_score INTEGER := 0;
    photo_score INTEGER := 0;
    total_score INTEGER := 0;
    avg_rating NUMERIC;
    social_count INTEGER;
    reference_count INTEGER;
BEGIN
    -- Identity Verification (50 points max)
    SELECT 
        CASE WHEN email_verified THEN 10 ELSE 0 END +
        CASE WHEN phone_verified THEN 10 ELSE 0 END +
        CASE WHEN identity_verified THEN 30 ELSE 0 END
    INTO identity_score
    FROM user_profiles WHERE id = target_user_id;
    
    -- Social Media Verification (24 points max - 8 per platform, max 3)
    SELECT COUNT(*) INTO social_count
    FROM social_media_profiles 
    WHERE user_id = target_user_id AND is_verified = TRUE;
    
    social_score := LEAST(social_count * 8, 24);
    
    -- University Email Verification (15 points)
    SELECT CASE WHEN university_verified THEN 15 ELSE 0 END
    INTO university_score
    FROM user_profiles WHERE id = target_user_id;
    
    -- Community Reviews (20 points based on average rating)
    SELECT AVG(
        CASE 
            WHEN overall IS NOT NULL THEN overall
            WHEN rating IS NOT NULL THEN rating
            ELSE 3
        END
    ) INTO avg_rating
    FROM reviews WHERE reviewed_id = target_user_id;
    
    review_score := COALESCE(FLOOR(avg_rating * 4), 0); -- Scale to 20 points
    
    -- Reference Checks (24 points max - 8 per reference, max 3)
    SELECT COUNT(*) INTO reference_count
    FROM reviews 
    WHERE reviewed_id = target_user_id 
    AND reference_type IS NOT NULL 
    AND verified_interaction = TRUE;
    
    reference_score := LEAST(reference_count * 8, 24);
    
    -- Photo Verification (15 points)
    SELECT CASE WHEN photo_verified THEN 15 ELSE 0 END
    INTO photo_score
    FROM user_profiles WHERE id = target_user_id;
    
    -- Calculate total (max 148 points, scaled to 100)
    total_score := identity_score + social_score + university_score + review_score + reference_score + photo_score;
    total_score := LEAST(FLOOR(total_score * 100.0 / 148), 100);
    
    -- Update user profile
    UPDATE user_profiles 
    SET trust_score = total_score, trust_score_updated_at = NOW()
    WHERE id = target_user_id;
    
    RETURN total_score;
END;
$$ LANGUAGE plpgsql;

-- Function to send reference verification email using existing notification system
CREATE OR REPLACE FUNCTION send_reference_verification(
    reviewer_user_id UUID,
    reviewed_user_id UUID,
    reference_email VARCHAR(255),
    reference_type VARCHAR(20)
)
RETURNS UUID AS $$
DECLARE
    review_id UUID;
    verification_token VARCHAR(255);
BEGIN
    -- Generate verification token
    verification_token := encode(gen_random_bytes(32), 'hex');
    
    -- Create reference entry in reviews table
    INSERT INTO reviews (
        reviewer_id, 
        reviewed_id, 
        content, 
        reference_type, 
        reference_contact_email, 
        reference_verification_token,
        is_approved
    ) VALUES (
        reviewer_user_id,
        reviewed_user_id,
        'Reference verification pending',
        reference_type,
        reference_email,
        verification_token,
        FALSE
    ) RETURNING id INTO review_id;
    
    -- Create notification for admin/system to send email
    INSERT INTO notifications (
        user_id,
        type,
        title,
        body,
        data
    ) VALUES (
        reviewed_user_id,
        'reference_verification_sent',
        'Reference Verification Sent',
        'Reference verification request sent to ' || reference_email,
        jsonb_build_object(
            'review_id', review_id,
            'reference_email', reference_email,
            'verification_token', verification_token,
            'reference_type', reference_type
        )
    );
    
    RETURN review_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check for overdue safety checkins
CREATE OR REPLACE FUNCTION check_overdue_checkins()
RETURNS INTEGER AS $$
DECLARE
    overdue_count INTEGER := 0;
    checkin_record RECORD;
BEGIN
    -- Find overdue checkins (15 minutes past scheduled time)
    FOR checkin_record IN 
        SELECT sc.*, ec.name as emergency_contact_name, ec.phone_number as emergency_phone
        FROM safety_checkins sc
        LEFT JOIN emergency_contacts ec ON sc.emergency_contact_id = ec.id
        WHERE sc.status = 'pending' 
        AND sc.scheduled_time < NOW() - INTERVAL '15 minutes'
    LOOP
        -- Update status to overdue
        UPDATE safety_checkins 
        SET status = 'overdue', updated_at = NOW()
        WHERE id = checkin_record.id;
        
        -- Create alert notification
        INSERT INTO notifications (
            user_id,
            type,
            title,
            body,
            data
        ) VALUES (
            checkin_record.user_id,
            'safety_checkin_overdue',
            'Safety Check-in Overdue',
            'Check-in was scheduled for ' || checkin_record.scheduled_time,
            jsonb_build_object(
                'checkin_id', checkin_record.id,
                'scheduled_time', checkin_record.scheduled_time,
                'emergency_contact', checkin_record.emergency_contact_name
            )
        );
        
        overdue_count := overdue_count + 1;
    END LOOP;
    
    RETURN overdue_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Emergency contacts security
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own emergency contacts" ON emergency_contacts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all emergency contacts" ON emergency_contacts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND (permissions ? 'admin' OR permissions ? 'safety_admin')
        )
    );

-- Safety checkins security
ALTER TABLE safety_checkins ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own safety checkins" ON safety_checkins
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Emergency contacts can view related checkins" ON safety_checkins
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM emergency_contacts ec
            WHERE ec.id = safety_checkins.emergency_contact_id
            AND ec.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all safety checkins" ON safety_checkins
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND (permissions ? 'admin' OR permissions ? 'safety_admin')
        )
    );

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to automatically update trust scores when relevant data changes
CREATE OR REPLACE FUNCTION trigger_update_trust_score()
RETURNS TRIGGER AS $$
BEGIN
    -- Update trust score for the affected user
    PERFORM calculate_user_trust_score(
        CASE 
            WHEN TG_TABLE_NAME = 'user_profiles' THEN NEW.id
            WHEN TG_TABLE_NAME = 'reviews' THEN NEW.reviewed_id
            WHEN TG_TABLE_NAME = 'social_media_profiles' THEN NEW.user_id
            ELSE NULL
        END
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic trust score updates
DROP TRIGGER IF EXISTS trigger_user_profiles_trust_score ON user_profiles;
CREATE TRIGGER trigger_user_profiles_trust_score
    AFTER UPDATE ON user_profiles
    FOR EACH ROW
    WHEN (
        OLD.email_verified IS DISTINCT FROM NEW.email_verified OR
        OLD.phone_verified IS DISTINCT FROM NEW.phone_verified OR
        OLD.identity_verified IS DISTINCT FROM NEW.identity_verified OR
        OLD.university_verified IS DISTINCT FROM NEW.university_verified OR
        OLD.photo_verified IS DISTINCT FROM NEW.photo_verified
    )
    EXECUTE FUNCTION trigger_update_trust_score();

DROP TRIGGER IF EXISTS trigger_reviews_trust_score ON reviews;
CREATE TRIGGER trigger_reviews_trust_score
    AFTER INSERT OR UPDATE ON reviews
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_trust_score();

DROP TRIGGER IF EXISTS trigger_social_media_trust_score_insert ON social_media_profiles;
DROP TRIGGER IF EXISTS trigger_social_media_trust_score_update ON social_media_profiles;

CREATE TRIGGER trigger_social_media_trust_score_insert
    AFTER INSERT ON social_media_profiles
    FOR EACH ROW
    WHEN (NEW.is_verified = true)
    EXECUTE FUNCTION trigger_update_trust_score();

CREATE TRIGGER trigger_social_media_trust_score_update
    AFTER UPDATE ON social_media_profiles
    FOR EACH ROW
    WHEN (OLD.is_verified IS DISTINCT FROM NEW.is_verified)
    EXECUTE FUNCTION trigger_update_trust_score();

-- =====================================================
-- POST-MIGRATION VALIDATION
-- =====================================================

-- Verify enhanced tables maintain DRY principles
DO $$
DECLARE
    enhanced_reviews_count INTEGER;
    enhanced_social_count INTEGER;
    enhanced_users_count INTEGER;
BEGIN
    -- Verify reviews table enhanced correctly
    SELECT COUNT(*) INTO enhanced_reviews_count
    FROM information_schema.columns 
    WHERE table_name = 'reviews' AND column_name = 'reference_type';
    
    IF enhanced_reviews_count = 0 THEN
        RAISE EXCEPTION 'Reviews table enhancement failed';
    END IF;
    
    -- Verify social media profiles enhanced correctly
    SELECT COUNT(*) INTO enhanced_social_count
    FROM information_schema.columns 
    WHERE table_name = 'social_media_profiles' AND column_name = 'verification_score';
    
    IF enhanced_social_count = 0 THEN
        RAISE EXCEPTION 'Social media profiles enhancement failed';
    END IF;
    
    -- Verify user profiles enhanced correctly
    SELECT COUNT(*) INTO enhanced_users_count
    FROM information_schema.columns 
    WHERE table_name = 'user_profiles' AND column_name = 'trust_score';
    
    IF enhanced_users_count = 0 THEN
        RAISE EXCEPTION 'User profiles enhancement failed';
    END IF;
    
    RAISE NOTICE 'Enhanced verification system implemented successfully - leveraging existing tables with minimal new structures';
    RAISE NOTICE 'DRY principle maintained - no duplicate functionality created';
    RAISE NOTICE 'Trust score calculation function ready';
    RAISE NOTICE 'Reference system integrated with existing reviews';
    RAISE NOTICE 'Safety features added with minimal schema impact';
END $$; 