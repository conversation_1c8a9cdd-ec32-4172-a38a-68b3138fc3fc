/**
 * Unified Profile Service - Core Profile Operations
 *
 * This module handles basic profile operations like fetching, updating,
 * and managing core profile data.
 */

import { supabase } from '@utils/supabaseUtils';
import {
  ApiResponse,
  createNotFoundError,
  createSuccessResponse,
  handleServiceError,
} from '@utils/errorHandling';
import { Profile, ProfileWithRelations } from '@types/models';
import { logger } from '@utils/logger';
import { getProfileCoreRepository } from '@services/unified/repositories/UnifiedRepositoryFactory';
import { profileQueryOptimizer } from '@utils/performance/profileQueryOptimizer';

// Constants
const SERVICE_NAME = 'unifiedProfile.core';

// Add in-memory cache and request deduplication
const profileCache = new Map<string, { data: ProfileWithRelations; timestamp: number }>();
const pendingRequests = new Map<string, Promise<ApiResponse<ProfileWithRelations>>>();
const CACHE_TTL = 30000; // 30 seconds cache
const MAX_CACHE_SIZE = 100;

// Cache cleanup function
function cleanupCache() {
  const now = Date.now();
  const entries = Array.from(profileCache.entries());

  // Remove expired entries
  for (const [key, value] of entries) {
    if (now - value.timestamp > CACHE_TTL) {
      profileCache.delete(key);
    }
  }

  // If still too large, remove oldest entries
  if (profileCache.size > MAX_CACHE_SIZE) {
    const sortedEntries = entries
      .sort((a, b) => a[1].timestamp - b[1].timestamp)
      .slice(0, profileCache.size - MAX_CACHE_SIZE);

    for (const [key] of sortedEntries) {
      profileCache.delete(key);
    }
  }
}

/**
 * Create a new profile
 * @param profileData - Profile data to create
 * @returns ApiResponse with created profile
 */
export async function createProfile(profileData: Partial<Profile>): Promise<ApiResponse<Profile>> {
  try {
    logger.info('Creating profile', `${SERVICE_NAME}.createProfile`, { userId: profileData.id });

    if (!profileData.id) {
      return {
        data: null,
        error: 'Profile ID is required',
        status: 400,
      };
    }

    // Use atomic database function for profile creation with all data
    const { data: createdProfile, error: createError } = await supabase.rpc(
      'create_user_profile_v2',
      {
        user_id: profileData.id,
        profile_data: {
          email: profileData.email,
          username: profileData.username || profileData.display_name || 'User',
          display_name: profileData.display_name || profileData.username || 'User',
          role: profileData.role || 'roommate_seeker',
          ...profileData
        },
        created_at: new Date().toISOString()
      }
    );

    if (createError) {
      if (createError.message?.includes('duplicate') || createError.message?.includes('already exists')) {
        return {
          data: null,
          error: 'Profile already exists',
          status: 409,
        };
      }
      return {
        data: null,
        error: createError.message || 'Failed to create profile',
        status: 500,
      };
    }

    return createSuccessResponse(createdProfile);
  } catch (error) {
    return handleServiceError('createProfile', error, { profileId: profileData.id });
  }
}

/**
 * Get the current user's profile
 * @returns ApiResponse with the current user's profile
 */
export async function getCurrentProfile(): Promise<ApiResponse<Profile>> {
  try {
    logger.info('Getting current profile', `${SERVICE_NAME}.getCurrentProfile`);

    // Get current user from Supabase auth
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        data: null,
        error: 'User not authenticated',
        status: 401,
      };
    }

    // Get the profile using the user ID
    return await getProfileById(user.id);
  } catch (error) {
    return handleServiceError('getCurrentProfile', error);
  }
}

/**
 * Get a full profile by ID with all related data
 * @param profileId - User's profile ID
 * @returns ApiResponse with profile and related data
 */
export async function getCompleteProfile(
  profileId: string
): Promise<ApiResponse<ProfileWithRelations>> {
  try {
    logger.info('Getting complete profile', `${SERVICE_NAME}.getCompleteProfile`, { profileId });

    // Using the repository to get complete profile
    const profileRepository = getProfileCoreRepository();
    const profile = await profileRepository.getCompleteProfile(profileId);

    if (!profile) {
      return createNotFoundError('Profile not found');
    }

    return createSuccessResponse(profile);
  } catch (error) {
    return handleServiceError('getCompleteProfile', error, { profileId });
  }
}

/**
 * Update a user's profile
 * @param profileData - Profile data to update
 * @returns ApiResponse with updated profile
 */
export async function updateProfile(profileData: Partial<Profile>): Promise<ApiResponse<Profile>> {
  try {
    logger.info('Updating profile', `${SERVICE_NAME}.updateProfile`, { profileId: profileData.id });

    if (!profileData.id) {
      return {
        data: null,
        error: 'Profile ID is required',
        status: 400,
      };
    }

    // Use optimized database function for atomic updates with completion calculation
    const { data: updatedProfile, error: updateError } = await supabase.rpc(
      'update_profile_with_completion',
      {
        profile_id: profileData.id,
        profile_data: profileData,
      }
    );

    if (updateError) {
      if (updateError.message?.includes('not found')) {
        return createNotFoundError('Profile not found');
      }
      
      logger.error('Database error updating profile', `${SERVICE_NAME}.updateProfile`, { 
        error: updateError, 
        profileId: profileData.id 
      });
      
      return {
        data: null,
        error: updateError.message || 'Failed to update profile',
        status: 500,
      };
    }

    // Clear cache for this profile
    profileCache.delete(profileData.id);

    const profile = JSON.parse(updatedProfile);
    return createSuccessResponse(profile);
  } catch (error) {
    return handleServiceError('updateProfile', error, { profileId: profileData.id });
  }
}

/**
 * Get a profile by ID
 * @param profileId - User's profile ID
 * @returns ApiResponse with profile data
 */
export async function getProfileById(profileId: string): Promise<ApiResponse<Profile>> {
  try {
    logger.info('Getting profile by ID', `${SERVICE_NAME}.getProfileById`, { profileId });

    if (!profileId) {
      return {
        data: null,
        error: 'Profile ID is required',
        status: 400,
      };
    }

    // Use direct database query for optimal performance
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select(`
        id, email, username, display_name, first_name, last_name,
        avatar_url, bio, occupation, phone_number, is_verified,
        profile_completion, preferences, created_at, updated_at,
        email_verified, phone_verified, identity_verified, role,
        meta_data, video_intro_url, video_thumbnail_url, location
      `)
      .eq('id', profileId)
      .maybeSingle();

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          data: null,
          error: 'Profile not found',
          status: 404,
        };
      }
      
      logger.error('Database error getting profile', `${SERVICE_NAME}.getProfileById`, { 
        error, 
        profileId 
      });
      return {
        data: null,
        error: 'Database error occurred',
        status: 500,
      };
    }

    return createSuccessResponse(profile);
  } catch (error) {
    logger.error('Error in getProfileById', `${SERVICE_NAME}.getProfileById`, error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      status: 500,
    };
  }
}

/**
 * Get a profile by ID with direct database access (bypasses some caching)
 * This method is used by authentication systems and critical operations
 * @param profileId - User's profile ID
 * @param retryCount - Current retry attempt (for internal use)
 * @param bypassCache - Whether to bypass caching mechanisms
 * @param isBackgroundLoad - Whether this is a background operation
 * @returns ApiResponse with profile and related data
 */
export async function getProfileByIdDirect(
  profileId: string,
  retryCount: number = 0,
  bypassCache: boolean = false,
  isBackgroundLoad: boolean = false
): Promise<ApiResponse<ProfileWithRelations>> {
  try {
    logger.debug('Getting profile by ID (direct)', `${SERVICE_NAME}.getProfileByIdDirect`, {
      profileId,
      retryCount,
      bypassCache,
      isBackgroundLoad,
    });

    // Safety check for empty ID
    if (!profileId) {
      return {
        data: null,
        error: 'Profile ID is required',
        status: 400,
      };
    }

    // Check optimizer cache first
    if (!bypassCache) {
      const cachedProfile = profileQueryOptimizer.getCachedProfile(profileId);
      if (cachedProfile) {
        return createSuccessResponse(cachedProfile);
      }

      // Check if query should be allowed (prevents excessive queries)
      if (!profileQueryOptimizer.shouldAllowQuery(profileId)) {
        // Return a loading state or wait for existing query
        return {
          data: null,
          error: 'Profile query in progress',
          status: 202, // Accepted but processing
        };
      }
    }

    // Check cache first (unless bypassing)
    if (!bypassCache) {
      const cached = profileCache.get(profileId);
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        logger.debug('Profile cache hit', `${SERVICE_NAME}.getProfileByIdDirect`, { profileId });
        return createSuccessResponse(cached.data);
      }
    }

    // Check for pending request to avoid duplicate queries
    const pendingRequest = pendingRequests.get(profileId);
    if (pendingRequest && !bypassCache) {
      logger.debug('Deduplicating profile request', `${SERVICE_NAME}.getProfileByIdDirect`, {
        profileId,
      });
      return pendingRequest;
    }

    // Mark query as started in optimizer
    if (!bypassCache) {
      profileQueryOptimizer.markQueryStarted(profileId);
    }

    const startTime = Date.now();
    const MAX_RETRIES = 2;

    // Create the request promise
    const requestPromise = (async (): Promise<ApiResponse<ProfileWithRelations>> => {
      try {
        // Direct database query with simplified approach
        const { data, error } = await supabase
          .from('user_profiles')
          .select(
            `
            *,
            user_personality_profiles(*),
            social_media_profiles(*),
            profile_boosts(*)
          `
          )
          .eq('id', profileId)
          .maybeSingle();

        const duration = Date.now() - startTime;

        if (error) {
          logger.warn(
            `Direct profile query error (${duration}ms)`,
            `${SERVICE_NAME}.getProfileByIdDirect`,
            {
              profileId,
              error: error.message,
            }
          );

          // Mark query as completed with error
          if (!bypassCache) {
            profileQueryOptimizer.markQueryCompleted(profileId, null, error.message);
          }

          // Retry logic for certain errors
          if (
            retryCount < MAX_RETRIES &&
            (error.code === 'PGRST502' || // Bad gateway
              error.message?.includes('timeout') ||
              error.message?.includes('Too many pending'))
          ) {
            const backoffDelay = Math.min(100 * Math.pow(2, retryCount), 2000);
            logger.info(
              `Retrying profile query after ${backoffDelay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`,
              `${SERVICE_NAME}.getProfileByIdDirect`,
              { profileId }
            );

            await new Promise(resolve => setTimeout(resolve, backoffDelay));
            return getProfileByIdDirect(profileId, retryCount + 1, true, isBackgroundLoad);
          }

          // Handle specific error cases
          if (
            error.code === 'PGRST116' ||
            error.message?.includes('not found') ||
            error.message?.includes('does not exist')
          ) {
            return createNotFoundError(`Profile with ID ${profileId} not found`);
          }

          return {
            data: null,
            error: `Database error: ${error.message}`,
            status: 500,
          };
        }

        if (!data) {
          // Mark query as completed with no data
          if (!bypassCache) {
            profileQueryOptimizer.markQueryCompleted(profileId, null, 'Profile not found');
          }
          return createNotFoundError(`Profile with ID ${profileId} not found`);
        }

        logger.debug(
          `Direct profile query successful (${duration}ms)`,
          `${SERVICE_NAME}.getProfileByIdDirect`,
          {
            profileId,
          }
        );

        // Cache the result
        if (!bypassCache) {
          profileCache.set(profileId, {
            data: data as ProfileWithRelations,
            timestamp: Date.now(),
          });
          cleanupCache();

          // Mark query as completed successfully
          profileQueryOptimizer.markQueryCompleted(profileId, data as ProfileWithRelations);
        }

        return createSuccessResponse(data as ProfileWithRelations);
      } catch (queryError) {
        const duration = Date.now() - startTime;
        const errorMessage = queryError instanceof Error ? queryError.message : String(queryError);

        logger.error(
          `Exception in direct profile query (${duration}ms)`,
          `${SERVICE_NAME}.getProfileByIdDirect`,
          {
            profileId,
            error: errorMessage,
          }
        );

        // Mark query as completed with error
        if (!bypassCache) {
          profileQueryOptimizer.markQueryCompleted(profileId, null, errorMessage);
        }

        // Implement retry for unexpected errors
        if (retryCount < MAX_RETRIES) {
          const backoffDelay = Math.min(100 * Math.pow(2, retryCount), 2000);
          logger.info(
            `Retrying profile query after ${backoffDelay}ms due to exception (attempt ${retryCount + 1}/${MAX_RETRIES})`,
            `${SERVICE_NAME}.getProfileByIdDirect`,
            { profileId }
          );

          await new Promise(resolve => setTimeout(resolve, backoffDelay));
          return getProfileByIdDirect(profileId, retryCount + 1, true, isBackgroundLoad);
        }

        return {
          data: null,
          error: `Unexpected error: ${errorMessage}`,
          status: 500,
        };
      } finally {
        // Clean up pending request
        pendingRequests.delete(profileId);
      }
    })();

    // Store pending request for deduplication
    if (!bypassCache) {
      pendingRequests.set(profileId, requestPromise);
    }

    return requestPromise;
  } catch (error) {
    // Clean up pending request on outer error
    pendingRequests.delete(profileId);

    // Mark query as completed with error
    if (!bypassCache) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      profileQueryOptimizer.markQueryCompleted(profileId, null, errorMessage);
    }

    return handleServiceError('getProfileByIdDirect', error, { profileId });
  }
}
