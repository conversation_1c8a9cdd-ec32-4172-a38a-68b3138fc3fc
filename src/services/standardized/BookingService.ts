/**
 * BookingService - Manages service booking functionality
 * 
 * This service handles all booking operations including creating, retrieving,
 * updating, and managing service bookings. It integrates with Supabase for data storage
 * and provides a comprehensive API for booking operations.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from "@utils/supabaseUtils";
import { ApiService } from '@utils/api';
import type { ApiResponse } from '@utils/api';
import { logger } from '@services/loggerService';
import { handleError, tryCatchAsync } from '@utils/standardErrorHandler';
import { ErrorCode, AppError } from '@core/errors/types';
import { ValidationService } from '@services/validationService';
import { v4 as uuidv4 } from 'uuid';
import { rateLimitService } from '@services/rateLimitService';

// Define rate limit keys
const RATE_LIMIT_KEYS = {
  CREATE_BOOKING: 'booking_creation',
  UPDATE_BOOKING: 'booking_update',
  CANCEL_BOOKING: 'booking_cancellation',
  RESCHEDULE_BOOKING: 'booking_reschedule'
}

/**
 * Booking status enum
 */
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  RESCHEDULED = 'rescheduled'
}

/**
 * Payment status enum
 */
export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

/**
 * Booking interface
 */
export interface Booking {
  /** Unique booking ID */
  id?: string;
  /** ID of the service being booked */
  service_id: string;
  /** ID of the user making the booking */
  user_id: string;
  /** Start date and time of the booking */
  booking_date: string;
  /** End date and time of the booking */
  end_date: string;
  /** Current status of the booking */
  status: BookingStatus;
  /** Service location address */
  address: string;
  /** Any special instructions for the service provider */
  special_instructions?: string;
  /** Total price of the booking */
  price: number;
  /** Current payment status */
  payment_status: PaymentStatus;
  /** ID of the payment if processed */
  payment_id?: string;
  /** Whether the booking is shared with roommates */
  roommate_shared: boolean;
  /** IDs of roommates sharing the booking */
  shared_with?: string[];
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** Whether the booking has been reviewed */
  is_reviewed: boolean;
  /** Service details (populated when retrieving) */
  service?: {
    name: string;
    description: string;
    price: number;
    duration: number;
    provider?: {
      business_name: string;
      profile_image?: string;
      contact_phone?: string;
      contact_email?: string;
    }
  };
}

/**
 * Shared cost interface for split bookings
 */
export interface SharedCost {
  /** Unique shared cost ID */
  id?: string;
  /** ID of the booking being shared */
  booking_id: string;
  /** ID of the user sharing the cost */
  user_id: string;
  /** Amount this user is responsible for */
  amount: number;
  /** Payment status for this user's portion */
  status: PaymentStatus;
  /** ID of the payment if processed */
  payment_id?: string;
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
}

/**
 * Time slot interface
 */
export interface TimeSlot {
  /** Start time of the slot */
  start_time: string;
  /** End time of the slot */
  end_time: string;
}

/**
 * Service for managing booking functionality
 */
export class BookingService extends ApiService {
  private static instance: BookingService | null = null;
  private supabase: SupabaseClient;
  
  /**
   * Private constructor to enforce singleton pattern
   * @param supabaseClient Supabase client instance
   */
  private constructor(supabaseClient: SupabaseClient = supabase) {
    super();
    this.supabase = supabaseClient;
    logger.info('Booking Service initialized', 'BookingService');
  }
  
  /**
   * Get the singleton instance of the BookingService
   * @returns BookingService instance
   */
  public static getInstance(): BookingService {
    if (!BookingService.instance) {
      BookingService.instance = new BookingService();
    }
    return BookingService.instance;
  }
  
  /**
   * Generate a UUID that works in all environments
   * @returns A UUID string
   * @private
   */
  private generateUUID(): string {
    try {
      return uuidv4();
    } catch (error) {
      // Fallback for environments where crypto might not be available
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
  }
  
  /**
   * Log booking operations for tracking and debugging
   * @param operation The operation being performed
   * @param method The method being used
   * @param data Optional data to log
   * @protected
   */
  protected logOperation(operation: string, method: string, data?: Record<string, any>): void {
    logger.debug(`BookingService.${operation}`, method, data);
  }
  
  /**
   * Create a new service booking
   * @param booking The booking details
   * @param authenticatedUserId ID of the authenticated user creating the booking
   * @returns API response with the created booking or error
   */
  async createBooking(
    booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>,
    authenticatedUserId: string
  ): Promise<ApiResponse<Booking>> {
    try {
      // Validate authenticated user
      if (!authenticatedUserId) {
        return {
          data: null,
          error: 'User authentication required',
          status: 401
        };
      }

      // Validate booking data
      if (!booking.service_id) {
        return {
          data: null,
          error: 'Service ID is required',
          status: 400
        };
      }

      if (!booking.booking_date || !booking.end_date) {
        return {
          data: null,
          error: 'Booking date and end date are required',
          status: 400
        };
      }

      // Ensure user_id matches authenticated user
      if (booking.user_id !== authenticatedUserId) {
        return {
          data: null,
          error: 'Cannot create bookings for other users',
          status: 403
        };
      }

      // Validate required fields
      if (!booking.address?.trim()) {
        return {
          data: null,
          error: 'Service address is required',
          status: 400
        };
      }

      if (typeof booking.price !== 'number' || booking.price <= 0) {
        return {
          data: null,
          error: 'Valid service price is required',
          status: 400
        };
      }

      // Pre-validation: Check if service exists
      const { data: serviceExists, error: serviceError } = await this.supabase
        .from('services')
        .select('id, name, provider_id')
        .eq('id', booking.service_id)
        .single();

      if (serviceError || !serviceExists) {
        console.error('Service validation failed:', serviceError);
        return {
          data: null,
          error: `Service not found: ${booking.service_id}`,
          status: 400
        };
      }

      // Rate limiting check
      this.logOperation('rateLimit', 'BookingService.createBooking', {
        action: RATE_LIMIT_KEYS.CREATE_BOOKING,
        userId: authenticatedUserId
      });

      // Generate timestamps
      const now = new Date().toISOString();
      
      // Prepare booking data - match database schema exactly
      const bookingData = {
        service_id: booking.service_id,
        user_id: booking.user_id,
        booking_date: booking.booking_date,
        end_date: booking.end_date,
        status: booking.status,
        address: booking.address.trim(),
        special_instructions: booking.special_instructions?.trim() || null,
        price: booking.price,
        payment_status: booking.payment_status,
        payment_id: booking.payment_id || null,
        roommate_shared: booking.roommate_shared || false,
        shared_with: booking.shared_with || [],
        created_at: now,
        updated_at: now
      };

      console.log('Creating booking with data:', JSON.stringify(bookingData, null, 2));

      // Create booking in database
      const { data, error } = await this.supabase
        .from('service_bookings')
        .insert(bookingData)
        .select()
        .single();
      
      if (error) {
        // Log the actual database error for debugging
        console.error('Database error creating booking:', {
          error: error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint,
          bookingData: bookingData
        });

        // Return specific error messages based on error type
        let errorMessage = 'Failed to create booking';
        let statusCode = 500;

        if (error.code === '23503') { // Foreign key violation
          if (error.message.includes('service_id')) {
            errorMessage = `Service not available: ${booking.service_id}`;
            statusCode = 400;
          } else if (error.message.includes('user_id')) {
            errorMessage = `User account not found. Please log out and log back in.`;
            statusCode = 400;
          } else {
            errorMessage = `Invalid reference in booking data: ${error.message}`;
            statusCode = 400;
          }
        } else if (error.code === '23505') { // Unique constraint violation
          errorMessage = 'Booking already exists for this time slot';
          statusCode = 409;
        } else if (error.code === '23514') { // Check constraint violation
          errorMessage = `Invalid booking data: ${error.message}`;
          statusCode = 400;
        } else if (error.code === '42703') { // Undefined column
          errorMessage = `Database schema error: ${error.message}`;
          statusCode = 500;
        } else {
          errorMessage = `Database error: ${error.message}`;
        }

        // Still log to error handling system but with more context
        handleError(error, errorMessage, {
          source: 'BookingService.createBooking',
          context: { 
            serviceId: booking.service_id, 
            userId: authenticatedUserId,
            errorCode: error.code,
            errorDetails: error.details
          },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: null,
          error: errorMessage,
          status: statusCode
        };
      }
      
      // Log successful booking creation
      this.logOperation('createBooking', 'BookingService.createBooking', {
        bookingId: data.id,
        serviceId: booking.service_id,
        userId: authenticatedUserId,
        serviceName: serviceExists.name
      });

      console.log('Booking created successfully:', data.id);
      
      return {
        data,
        error: null,
        status: 201
      };
    } catch (error) {
      console.error('Unexpected error in createBooking:', error);
      
      handleError(error, 'Unexpected error in createBooking', {
        source: 'BookingService.createBooking',
        context: { serviceId: booking.service_id, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: null,
        error: `An unexpected error occurred while creating the booking: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 500
      };
    }
  }

  /**
   * Get a specific booking by ID
   * @param id The booking ID
   * @param authenticatedUserId ID of the authenticated user requesting the booking
   * @returns API response with the booking or error
   */
  async getBookingById(
    id: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<Booking>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: null,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: null,
          error: 'User authentication required',
          status: 401
        };
      }

      // Get booking with service and provider details
      const { data, error } = await this.supabase
        .from('service_bookings')
        .select(`
          *,
          service:service_id (
            name,
            description,
            price,
            duration,
            provider:provider_id (
              business_name,
              profile_image,
              contact_phone,
              contact_email
            )
          )
        `)
        .eq('id', id)
        .single();
      
      if (error) {
        handleError(error, 'Failed to get booking', {
          source: 'BookingService.getBookingById',
          context: { bookingId: id, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: null,
          error: 'Failed to retrieve booking',
          status: 500
        };
      }
      
      if (!data) {
        return {
          data: null,
          error: 'Booking not found',
          status: 404
        };
      }
      
      // Verify the user has permission to view this booking
      // Users can only view their own bookings or bookings shared with them
      if (data.user_id !== authenticatedUserId && 
          (!data.shared_with || !data.shared_with.includes(authenticatedUserId))) {
        return {
          data: null,
          error: 'You do not have permission to view this booking',
          status: 403
        };
      }
      
      // Check if booking has a review
      const hasReviewResult = await this.hasReview(id);
      const isReviewed = hasReviewResult.data || false;
      
      // Return the booking with review status
      return {
        data: { ...data, is_reviewed: isReviewed },
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in getBookingById', {
        source: 'BookingService.getBookingById',
        context: { bookingId: id, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: null,
        error: 'An unexpected error occurred while retrieving the booking',
        status: 500
      };
    }
  }

  /**
   * Get bookings for a specific user
   * @param userId The user ID
   * @param authenticatedUserId ID of the authenticated user requesting the bookings
   * @param options Optional parameters for filtering and pagination
   * @returns API response with bookings or error
   */
  async getUserBookings(
    userId: string,
    authenticatedUserId: string,
    options?: {
      status?: BookingStatus;
      limit?: number;
      offset?: number;
      sortBy?: 'booking_date' | 'created_at';
      sortOrder?: 'asc' | 'desc';
    }
  ): Promise<ApiResponse<Booking[]>> {
    try {
      // Validate parameters
      if (!userId) {
        return {
          data: null,
          error: 'User ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: null,
          error: 'User authentication required',
          status: 401
        };
      }

      // Users can only view their own bookings
      if (userId !== authenticatedUserId) {
        return {
          data: null,
          error: 'You can only view your own bookings',
          status: 403
        };
      }

      // Set up query with defaults
      const limit = options?.limit || 20;
      const offset = options?.offset || 0;
      const sortBy = options?.sortBy || 'booking_date';
      const sortOrder = options?.sortOrder || 'desc';

      // Start building the query
      let query = this.supabase
        .from('service_bookings')
        .select(`
          *,
          service:service_id (
            name,
            description,
            price,
            duration,
            provider:provider_id (
              business_name,
              profile_image
            )
          )
        `)
        .eq('user_id', userId)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);

      // Add status filter if provided
      if (options?.status) {
        query = query.eq('status', options.status);
      }

      // Execute query
      const { data, error } = await query;
      
      if (error) {
        handleError(error, 'Failed to get user bookings', {
          source: 'BookingService.getUserBookings',
          context: { userId, limit, offset },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: null,
          error: 'Failed to retrieve bookings',
          status: 500
        };
      }
      
      // Return bookings
      return {
        data: data || [],
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in getUserBookings', {
        source: 'BookingService.getUserBookings',
        context: { userId, authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: null,
        error: 'An unexpected error occurred while retrieving bookings',
        status: 500
      };
    }
  }

  /**
   * Check if a booking has a review
   * @param bookingId The booking ID
   * @returns API response with boolean result or error
   */
  async hasReview(bookingId: string): Promise<ApiResponse<boolean>> {
    try {
      if (!bookingId) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      const { count, error } = await this.supabase
        .from('service_reviews')
        .select('id', { count: 'exact', head: true })
        .eq('booking_id', bookingId);
      
      if (error) {
        handleError(error, 'Error checking for review', {
          source: 'BookingService.hasReview',
          context: { bookingId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: null, // Don't return error to caller as this is a non-critical operation
          status: 200
        };
      }
      
      return {
        data: (count || 0) > 0,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in hasReview', {
        source: 'BookingService.hasReview',
        context: { bookingId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: null, // Don't return error to caller as this is a non-critical operation
        status: 200
      };
    }
  }

  /**
   * Update a booking status
   * @param id The booking ID
   * @param status The new status
   * @param authenticatedUserId ID of the authenticated user updating the booking
   * @returns API response with success or error
   */
  async updateBookingStatus(
    id: string,
    status: BookingStatus,
    authenticatedUserId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!status) {
        return {
          data: false,
          error: 'Status is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: false,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: false,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      // Simple rate limiting log
      this.logOperation('rateLimit', 'BookingService.updateBookingStatus', {
        action: RATE_LIMIT_KEYS.UPDATE_BOOKING,
        userId: authenticatedUserId
      });

      // Update booking status
      const { error } = await this.supabase
        .from('service_bookings')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        handleError(error, 'Failed to update booking status', {
          source: 'BookingService.updateBookingStatus',
          context: { bookingId: id, status, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: 'Failed to update booking status',
          status: 500
        };
      }
      
      // Log successful status update
      this.logOperation('updateBookingStatus', 'BookingService.updateBookingStatus', {
        bookingId: id,
        oldStatus: bookingResult.data?.status,
        newStatus: status,
        userId: authenticatedUserId
      });
      
      return {
        data: true,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in updateBookingStatus', {
        source: 'BookingService.updateBookingStatus',
        context: { bookingId: id, status, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: 'An unexpected error occurred while updating the booking status',
        status: 500
      };
    }
  }

  /**
   * Reschedule a booking
   * @param id The booking ID
   * @param newBookingDate The new booking date
   * @param newEndDate The new end date
   * @param authenticatedUserId ID of the authenticated user rescheduling the booking
   * @returns API response with success or error
   */
  async rescheduleBooking(
    id: string,
    newBookingDate: string,
    newEndDate: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!newBookingDate || !newEndDate) {
        return {
          data: false,
          error: 'New booking date and end date are required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: false,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: false,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      // Simple rate limiting log
      this.logOperation('rateLimit', 'BookingService.rescheduleBooking', {
        action: RATE_LIMIT_KEYS.RESCHEDULE_BOOKING,
        userId: authenticatedUserId
      });

      // Update booking dates and set status to rescheduled
      const { error } = await this.supabase
        .from('service_bookings')
        .update({ 
          booking_date: newBookingDate,
          end_date: newEndDate,
          status: BookingStatus.RESCHEDULED,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        handleError(error, 'Failed to reschedule booking', {
          source: 'BookingService.rescheduleBooking',
          context: { bookingId: id, newBookingDate, newEndDate, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: 'Failed to reschedule booking',
          status: 500
        };
      }
      
      // Log successful reschedule
      this.logOperation('rescheduleBooking', 'BookingService.rescheduleBooking', {
        bookingId: id,
        oldDate: bookingResult.data?.booking_date,
        newDate: newBookingDate,
        userId: authenticatedUserId
      });
      
      return {
        data: true,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in rescheduleBooking', {
        source: 'BookingService.rescheduleBooking',
        context: { bookingId: id, newBookingDate, newEndDate, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: 'An unexpected error occurred while rescheduling the booking',
        status: 500
      };
    }
  }

  /**
   * Cancel a booking
   * @param id The booking ID
   * @param authenticatedUserId ID of the authenticated user cancelling the booking
   * @returns API response with success or error
   */
  async cancelBooking(
    id: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: false,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: false,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      // Check if booking is already cancelled
      if (bookingResult.data?.status === BookingStatus.CANCELLED) {
        return {
          data: true, // Return success but with a warning
          error: 'Booking is already cancelled',
          status: 200
        };
      }

      // Simple rate limiting log
      this.logOperation('rateLimit', 'BookingService.cancelBooking', {
        action: RATE_LIMIT_KEYS.CANCEL_BOOKING,
        userId: authenticatedUserId
      });

      // Update booking status to cancelled
      const { error } = await this.supabase
        .from('service_bookings')
        .update({ 
          status: BookingStatus.CANCELLED,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        handleError(error, 'Failed to cancel booking', {
          source: 'BookingService.cancelBooking',
          context: { bookingId: id, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: 'Failed to cancel booking',
          status: 500
        };
      }
      
      // Log successful cancellation
      this.logOperation('cancelBooking', 'BookingService.cancelBooking', {
        bookingId: id,
        previousStatus: bookingResult.data?.status,
        userId: authenticatedUserId
      });
      
      return {
        data: true,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in cancelBooking', {
        source: 'BookingService.cancelBooking',
        context: { bookingId: id, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: 'An unexpected error occurred while cancelling the booking',
        status: 500
      };
    }
  }

  /**
   * Mark a booking as completed
   * @param id The booking ID
   * @param authenticatedUserId ID of the authenticated user marking the booking as completed
   * @returns API response with success or error
   */
  async markBookingAsCompleted(
    id: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: false,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: false,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      // Check if booking is already completed
      if (bookingResult.data?.status === BookingStatus.COMPLETED) {
        return {
          data: true, // Return success but with a warning
          error: 'Booking is already marked as completed',
          status: 200
        };
      }

      // Update booking status to completed
      const { error } = await this.supabase
        .from('service_bookings')
        .update({ 
          status: BookingStatus.COMPLETED,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        handleError(error, 'Failed to mark booking as completed', {
          source: 'BookingService.markBookingAsCompleted',
          context: { bookingId: id, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: 'Failed to mark booking as completed',
          status: 500
        };
      }
      
      // Log successful completion
      this.logOperation('markBookingAsCompleted', 'BookingService.markBookingAsCompleted', {
        bookingId: id,
        previousStatus: bookingResult.data?.status,
        userId: authenticatedUserId
      });
      
      return {
        data: true,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in markBookingAsCompleted', {
        source: 'BookingService.markBookingAsCompleted',
        context: { bookingId: id, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: 'An unexpected error occurred while marking the booking as completed',
        status: 500
      };
    }
  }

  /**
   * Check available time slots for a service
   * @param serviceId The service ID
   * @param date The date to check availability for
   * @param authenticatedUserId ID of the authenticated user checking availability
   * @returns API response with available time slots or error
   */
  async checkAvailableTimeSlots(
    serviceId: string,
    date: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<{ startTime: string; endTime: string }[]>> {
    try {
      // Validate parameters
      if (!serviceId) {
        return {
          data: null,
          error: 'Service ID is required',
          status: 400
        };
      }

      if (!date) {
        return {
          data: null,
          error: 'Date is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: null,
          error: 'User authentication required',
          status: 401
        };
      }

      // Get service details to determine duration
      const { data: service, error: serviceError } = await this.supabase
        .from('services')
        .select('duration, business_hours')
        .eq('id', serviceId)
        .single();
      
      if (serviceError || !service) {
        handleError(serviceError, 'Failed to get service details', {
          source: 'BookingService.checkAvailableTimeSlots',
          context: { serviceId, date, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: null,
          error: 'Failed to get service details',
          status: 500
        };
      }

      // Get existing bookings for the service on the specified date
      const { data: existingBookings, error: bookingsError } = await this.supabase
        .from('service_bookings')
        .select('booking_date, end_date')
        .eq('service_id', serviceId)
        .gte('booking_date', `${date}T00:00:00`)
        .lt('booking_date', `${date}T23:59:59`)
        .not('status', 'eq', BookingStatus.CANCELLED);
      
      if (bookingsError) {
        handleError(bookingsError, 'Failed to get existing bookings', {
          source: 'BookingService.checkAvailableTimeSlots',
          context: { serviceId, date, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: null,
          error: 'Failed to get existing bookings',
          status: 500
        };
      }

      // Parse business hours for the day of week
      const dayOfWeek = new Date(date).getDay(); // 0 = Sunday, 1 = Monday, etc.
      const businessHours = service.business_hours?.[dayOfWeek] || { start: '09:00', end: '17:00' };
      
      // Generate time slots based on service duration and business hours
      const serviceDuration = service.duration || 60; // Default to 60 minutes if not specified
      const slots: { startTime: string; endTime: string }[] = [];
      
      // Parse business hours
      const [startHour, startMinute] = businessHours.start.split(':').map(Number);
      const [endHour, endMinute] = businessHours.end.split(':').map(Number);
      
      // Convert to minutes for easier calculation
      const startTimeInMinutes = startHour * 60 + startMinute;
      const endTimeInMinutes = endHour * 60 + endMinute;
      
      // Generate slots
      for (let time = startTimeInMinutes; time + serviceDuration <= endTimeInMinutes; time += serviceDuration) {
        const slotStartHour = Math.floor(time / 60);
        const slotStartMinute = time % 60;
        const slotEndHour = Math.floor((time + serviceDuration) / 60);
        const slotEndMinute = (time + serviceDuration) % 60;
        
        const startTimeStr = `${date}T${slotStartHour.toString().padStart(2, '0')}:${slotStartMinute.toString().padStart(2, '0')}:00`;
        const endTimeStr = `${date}T${slotEndHour.toString().padStart(2, '0')}:${slotEndMinute.toString().padStart(2, '0')}:00`;
        
        // Check if slot overlaps with any existing booking
        const isAvailable = !existingBookings?.some(booking => {
          const bookingStart = new Date(booking.booking_date).getTime();
          const bookingEnd = new Date(booking.end_date).getTime();
          const slotStart = new Date(startTimeStr).getTime();
          const slotEnd = new Date(endTimeStr).getTime();
          
          // Check for overlap
          return (slotStart < bookingEnd && slotEnd > bookingStart);
        });
        
        if (isAvailable) {
          slots.push({
            startTime: startTimeStr,
            endTime: endTimeStr
          });
        }
      }
      
      return {
        data: slots,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in checkAvailableTimeSlots', {
        source: 'BookingService.checkAvailableTimeSlots',
        context: { serviceId, date, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: null,
        error: 'An unexpected error occurred while checking available time slots',
        status: 500
      };
    }
  }

  /**
   * Share a booking with roommates
   * @param id The booking ID
   * @param roommateIds Array of roommate user IDs to share with
   * @param authenticatedUserId ID of the authenticated user sharing the booking
   * @returns API response with success or error
   */
  async shareBookingWithRoommates(
    id: string,
    roommateIds: string[],
    authenticatedUserId: string
  ): Promise<ApiResponse<boolean>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: false,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!roommateIds || !roommateIds.length) {
        return {
          data: false,
          error: 'At least one roommate ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: false,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: false,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      // Ensure the user is the owner of the booking
      if (bookingResult.data?.user_id !== authenticatedUserId) {
        return {
          data: false,
          error: 'Only the booking owner can share it with roommates',
          status: 403
        };
      }

      // Get current shared_with array or initialize empty array
      const currentSharedWith = bookingResult.data?.shared_with || [];
      
      // Add new roommates to shared_with array (avoiding duplicates)
      const updatedSharedWith = [...new Set([...currentSharedWith, ...roommateIds])];
      
      // Update booking with new shared_with array
      const { error } = await this.supabase
        .from('service_bookings')
        .update({ 
          shared_with: updatedSharedWith,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        handleError(error, 'Failed to share booking with roommates', {
          source: 'BookingService.shareBookingWithRoommates',
          context: { bookingId: id, roommateIds, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
        
        return {
          data: false,
          error: 'Failed to share booking with roommates',
          status: 500
        };
      }
      
      // Log successful sharing
      this.logOperation('shareBookingWithRoommates', 'BookingService.shareBookingWithRoommates', {
        bookingId: id,
        roommateIds,
        userId: authenticatedUserId
      });
      
      return {
        data: true,
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in shareBookingWithRoommates', {
        source: 'BookingService.shareBookingWithRoommates',
        context: { bookingId: id, roommateIds, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: false,
        error: 'An unexpected error occurred while sharing the booking with roommates',
        status: 500
      };
    }
  }

  /**
   * Calculate the total price for a booking including any shared costs
   * @param id The booking ID
   * @param authenticatedUserId ID of the authenticated user requesting the calculation
   * @returns API response with price details or error
   */
  async calculateBookingPrice(
    id: string,
    authenticatedUserId: string
  ): Promise<ApiResponse<{ totalPrice: number; userShare: number; sharedWith: { userId: string; share: number }[] }>> {
    try {
      // Validate parameters
      if (!id) {
        return {
          data: null,
          error: 'Booking ID is required',
          status: 400
        };
      }

      if (!authenticatedUserId) {
        return {
          data: null,
          error: 'User authentication required',
          status: 401
        };
      }

      // Check if booking exists and user has permission
      const bookingResult = await this.getBookingById(id, authenticatedUserId);
      if (bookingResult.error) {
        return {
          data: null,
          error: bookingResult.error,
          status: bookingResult.status
        };
      }

      const booking = bookingResult.data;
      if (!booking) {
        return {
          data: null,
          error: 'Booking not found',
          status: 404
        };
      }

      // Get service price
      const servicePrice = booking.service?.price || 0;
      
      // Get shared costs if any
      const { data: sharedCosts, error: sharedCostsError } = await this.supabase
        .from('booking_shared_costs')
        .select('*')
        .eq('booking_id', id);
      
      if (sharedCostsError) {
        handleError(sharedCostsError, 'Failed to get shared costs', {
          source: 'BookingService.calculateBookingPrice',
          context: { bookingId: id, userId: authenticatedUserId },
          defaultErrorCode: ErrorCode.DATABASE_ERROR,
          throw: false
        });
      }
      
      // Calculate total price
      const totalPrice = servicePrice;
      
      // Calculate user shares
      const sharedWith = booking.shared_with || [];
      const totalParticipants = sharedWith.length + 1; // +1 for the booking owner
      
      // Equal split by default
      const equalShare = totalPrice / totalParticipants;
      
      // Create share details
      const shareDetails = sharedWith.map(userId => ({
        userId,
        share: equalShare
      }));
      
      // Owner's share
      const userShare = equalShare;
      
      // If there are custom shared costs, use those instead
      if (sharedCosts && sharedCosts.length > 0) {
        // TODO: Implement custom cost sharing logic if needed
      }
      
      return {
        data: {
          totalPrice,
          userShare,
          sharedWith: shareDetails
        },
        error: null,
        status: 200
      };
    } catch (error) {
      handleError(error, 'Unexpected error in calculateBookingPrice', {
        source: 'BookingService.calculateBookingPrice',
        context: { bookingId: id, userId: authenticatedUserId },
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        throw: false
      });
      
      return {
        data: null,
        error: 'An unexpected error occurred while calculating the booking price',
        status: 500
      };
    }
  }
}

// Export singleton instance
export const bookingService = BookingService.getInstance();
