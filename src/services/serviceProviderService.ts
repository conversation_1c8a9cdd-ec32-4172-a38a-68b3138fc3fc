import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import { logger } from '@utils/logger';
import { ApiService } from '@utils/api';
import type { ApiResponse } from '@utils/api';
import { adminService } from '@services/adminService';
import { getServiceRepository } from '@core/repositories/RepositoryFactory';
import type { ServiceWithProvider } from '../types/models';

/**
 * Service Provider Service - FIXED VERSION
 * Handles operations related to service providers and their services
 * Fixed: Category UUID/name mismatches, duplicate categories, data flow issues
 */

export interface ServiceProvider {
  id: string;
  user_id: string;
  business_name: string;
  description: string;
  contact_email: string;
  contact_phone: string;
  business_address: string;
  website: string | null;
  social_media: Record<string, string> | null;
  service_categories: string[];
  resolved_category_names?: string[];
  is_verified: boolean;
  verification_date: string | null;
  rating_average: number | null;
  review_count: number;
  availability: Record<string, any> | null;
  profile_image: string | null;
  gallery_images: string[] | null;
  created_at: string;
  updated_at: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  services_count: number;
}

export interface ServiceSearch {
  category?: string;
  location?: string;
  rating?: number;
  price_min?: number;
  price_max?: number;
  availability_date?: string;
  keyword?: string;
  verified_only?: boolean;
}

// FIXED: Updated Service interface to match database schema exactly
export interface Service {
  id: string;
  name: string;
  description: string;
  provider_id: string;
  category: string;
  price: number | null;
  duration?: number | null;
  is_available: boolean;
  booking_lead_time?: number | null;
  cancellation_policy?: string | null;
  images?: string[] | null;
  created_at: string;
  updated_at: string | null;
}

// Local interface for internal service methods that need full ServiceProvider data
export interface ServiceWithServiceProvider extends Service {
  provider?: ServiceProvider;
}

export class ServiceProviderService extends ApiService {
  /**
   * Check if the current user is an admin
   * Delegates to adminService to maintain DRY principles
   */
  async isUserAdmin(): Promise<boolean> {
    return await adminService.isUserAdmin();
  }

  private serviceRepository = getServiceRepository();

  /**
   * OPTIMIZED: Resolve service category names from UUIDs with batch query
   * This replaces the N+1 query problem with a single batch query
   */
  private async resolveCategoryNames(categories: string[]): Promise<string[]> {
    if (!categories || categories.length === 0) return [];
    
    // Separate UUIDs from names
    const uuids: string[] = [];
    const names: string[] = [];
    
    categories.forEach(category => {
      if (this.isUUID(category)) {
        uuids.push(category);
      } else {
        names.push(category);
      }
    });
    
    // If we have UUIDs, resolve them with a single batch query
    if (uuids.length > 0) {
      try {
        const { data, error } = await supabase
          .from('service_categories')
          .select('id, name')
          .in('id', uuids);
        
        if (error) {
          logger.warn('Error resolving category UUIDs', 'ServiceProviderService', { uuids, error });
        } else if (data) {
          // Create mapping for resolved names
          const uuidToNameMap = new Map(data.map(cat => [cat.id, cat.name]));
          
          // Replace UUIDs with resolved names, keep unresolved UUIDs as fallback
          uuids.forEach(uuid => {
            const resolvedName = uuidToNameMap.get(uuid);
            names.push(resolvedName || uuid);
          });
        }
      } catch (error) {
        logger.warn('Failed to resolve category names', 'ServiceProviderService', { uuids, error });
        // Fallback: add UUIDs as-is
        names.push(...uuids);
      }
    }
    
    return names;
  }

  /**
   * FIXED: Check if string is a UUID
   */
  private isUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  /**
   * FIXED: Get deduplicated service categories
   */
  async getServiceCategories(): Promise<ApiResponse<ServiceCategory[]>> {
    this.logOperation('GET', 'service_categories');

    try {
      // Get all categories and deduplicate by name, keeping the most recent one
      const { data, error } = await supabase
        .from('service_categories')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Deduplicate by name, keeping the first (most recent) occurrence
      const uniqueCategories = new Map<string, ServiceCategory>();
      data?.forEach(category => {
        if (!uniqueCategories.has(category.name)) {
          uniqueCategories.set(category.name, category);
        }
      });

      const deduplicatedCategories = Array.from(uniqueCategories.values());

      return { data: deduplicatedCategories, error: null, status: 200 };
    } catch (error) {
      logger.error('Error fetching service categories', 'ServiceProviderService', { error: (error as Error).message });
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * OPTIMIZED: Get service providers with categories resolved efficiently
   * This method eliminates the N+1 query problem by batching category resolution
   */
  async getServiceProvidersOptimized(
    search?: ServiceSearch,
    pagination?: { limit: number; offset: number }
  ): Promise<ApiResponse<{ providers: ServiceProvider[]; total: number }>> {
    this.logOperation('GET', 'service_providers_optimized', { search, pagination });

    try {
      let query = supabase.from('service_providers').select('*', { count: 'exact' });

      // Apply search filters
      if (search) {
        if (search.category) {
          // Handle both UUID and name searches
          if (this.isUUID(search.category)) {
            query = query.contains('service_categories', [search.category]);
          } else {
            // Search by category name - need to resolve to UUID first
            const { data: categoryData } = await supabase
              .from('service_categories')
              .select('id')
              .eq('name', search.category)
              .limit(1);
            
            if (categoryData && categoryData.length > 0) {
              query = query.contains('service_categories', [categoryData[0].id]);
            }
          }
        }

        if (search.location) {
          query = query.ilike('business_address', `%${search.location}%`);
        }

        if (search.rating) {
          query = query.gte('rating_average', search.rating);
        }

        if (search.verified_only) {
          query = query.eq('is_verified', true);
        }

        if (search.keyword) {
          query = query.or(
            `business_name.ilike.%${search.keyword}%,description.ilike.%${search.keyword}%`
          );
        }
      }

      // Apply pagination and ordering
      if (pagination) {
        query = query
          .range(pagination.offset, pagination.offset + pagination.limit - 1)
          .order('rating_average', { ascending: false })
          .order('review_count', { ascending: false })
          .order('created_at', { ascending: false });
      } else {
        // Default ordering for non-paginated requests
        query = query
          .order('rating_average', { ascending: false })
          .order('review_count', { ascending: false })
          .limit(50); // Reasonable default limit
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      // OPTIMIZATION: Batch resolve all unique category UUIDs in one query
      const allCategoryIds = new Set<string>();
      data?.forEach(provider => {
        provider.service_categories?.forEach((cat: string) => {
          if (this.isUUID(cat)) {
            allCategoryIds.add(cat);
          }
        });
      });

      // Batch fetch all category details
      let categoryMap = new Map<string, ServiceCategory>();
      if (allCategoryIds.size > 0) {
        const { data: categoryData, error: categoryError } = await supabase
          .from('service_categories')
          .select('id, name, icon, color, description, services_count')
          .in('id', Array.from(allCategoryIds));

        if (!categoryError && categoryData) {
          categoryData.forEach(cat => {
            categoryMap.set(cat.id, cat);
          });
        }
      }

      // Process providers with resolved categories
      const providers: ServiceProvider[] = (data || []).map(provider => {
        const resolvedCategoryNames: string[] = [];
        const resolvedCategories: ServiceCategory[] = [];

        provider.service_categories?.forEach((cat: string) => {
          if (this.isUUID(cat)) {
            const categoryDetails = categoryMap.get(cat);
            if (categoryDetails) {
              resolvedCategoryNames.push(categoryDetails.name);
              resolvedCategories.push(categoryDetails);
            } else {
              // Fallback for unresolved UUIDs
              resolvedCategoryNames.push(cat);
            }
          } else {
            // It's already a name
            resolvedCategoryNames.push(cat);
          }
        });

        return {
          ...provider,
          resolved_category_names: resolvedCategoryNames,
          resolved_categories: resolvedCategories
        };
      });

      return {
        data: { providers, total: count || 0 },
        error: null,
        status: 200
      };
    } catch (error) {
      logger.error(
        'Error fetching optimized service providers',
        'ServiceProviderService',
        { search, pagination, error }
      );
      return {
        data: { providers: [], total: 0 },
        error: (error as Error).message,
        status: 500
      };
    }
  }

  /**
   * LEGACY: Fetch service providers with resolved category names
   * @deprecated Use getServiceProvidersOptimized instead for better performance
   */
  async getServiceProviders(search?: ServiceSearch): Promise<ApiResponse<ServiceProvider[]>> {
    this.logOperation('GET', 'service_providers', search);

    try {
      let query = supabase.from('service_providers').select('*');

      // Apply filters if provided
      if (search) {
        if (search.category) {
          // FIXED: Handle both UUID and name searches
          if (this.isUUID(search.category)) {
            query = query.contains('service_categories', [search.category]);
          } else {
            // Search by category name - need to resolve to UUID first
            const { data: categoryData } = await supabase
              .from('service_categories')
              .select('id')
              .eq('name', search.category)
              .limit(1);
            
            if (categoryData && categoryData.length > 0) {
              query = query.contains('service_categories', [categoryData[0].id]);
            }
          }
        }

        if (search.location) {
          query = query.ilike('business_address', `%${search.location}%`);
        }

        if (search.rating) {
          query = query.gte('rating_average', search.rating);
        }

        if (search.verified_only) {
          query = query.eq('is_verified', true);
        }

        if (search.keyword) {
          query = query.or(
            `business_name.ilike.%${search.keyword}%,description.ilike.%${search.keyword}%`
          );
        }
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // FIXED: Resolve category names for all providers
      const providersWithResolvedCategories = await Promise.all(
        (data || []).map(async (provider) => {
          const resolvedNames = await this.resolveCategoryNames(provider.service_categories);
          return {
            ...provider,
            resolved_category_names: resolvedNames
          };
        })
      );

      return { data: providersWithResolvedCategories, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error fetching service providers',
        'ServiceProviderService',
        { search, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * FIXED: Get a specific service provider by ID with resolved categories
   */
  async getServiceProviderById(id: string): Promise<ApiResponse<ServiceProvider | null>> {
    this.logOperation('GET', `service_provider/${id}`);

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        // FIXED: Resolve category names
        const resolvedNames = await this.resolveCategoryNames(data.service_categories);
        data.resolved_category_names = resolvedNames;
      }

      return { data, error: null, status: data ? 200 : 404 };
    } catch (error) {
      logger.error(
        'Error fetching service provider',
        'ServiceProviderService',
        { id, error }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * FIXED: Get services offered by a specific service provider using direct database query
   */
  async getServicesByProviderId(providerId: string): Promise<ApiResponse<Service[]>> {
    this.logOperation('GET', `service_provider/${providerId}/services`);

    try {
      // FIXED: Use direct database query instead of repository to avoid dependency issues
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('provider_id', providerId)
        .eq('is_available', true)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return { data: data || [], error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error fetching provider services',
        'ServiceProviderService',
        { providerId, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Search for services with various filters
   */
  async searchServices(search: ServiceSearch): Promise<ApiResponse<ServiceWithProvider[]>> {
    this.logOperation('SEARCH', 'services', search);

    try {
      // Convert search params to filter options for the repository
      const filters = {
        category: search.category,
        minPrice: search.price_min,
        maxPrice: search.price_max,
        location: search.location,
      };

      // Use repository to search services with filters
      // Note: Using a fixed page/limit for compatibility, could be parameterized in the future
      const services = await this.serviceRepository.findServicesWithFilters(
        filters,
        1, // page
        50 // limit
      );

      return { data: services as ServiceWithProvider[], error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error searching services',
        'ServiceProviderService',
        { search, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Create a new service provider profile
   */
  async createServiceProvider(
    serviceProvider: Omit<
      ServiceProvider,
      'id' | 'created_at' | 'updated_at' | 'rating_average' | 'review_count'
    >
  ): Promise<ApiResponse<ServiceProvider>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('CREATE', 'service_provider', serviceProvider);

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .insert({
          ...serviceProvider,
          rating_average: 0,
          review_count: 0,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null, status: 201 };
    } catch (error) {
      logger.error('Error creating service provider', 'ServiceProviderService', { error: (error as Error).message });
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Update a service provider profile
   */
  async updateServiceProvider(
    id: string,
    updates: Partial<Omit<ServiceProvider, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<ApiResponse<ServiceProvider>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('UPDATE', `service_provider/${id}`, updates);

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error updating service provider',
        'ServiceProviderService',
        { id, error }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Create a new service
   */
  async createService(
    service: Omit<Service, 'id' | 'created_at' | 'updated_at'>
  ): Promise<ApiResponse<Service>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('CREATE', 'service', service);

    try {
      // Use repository to create service
      const newService = await this.serviceRepository.create({
        ...service,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      } as any);

      return { data: newService, error: null, status: 201 };
    } catch (error) {
      logger.error('Error creating service', 'ServiceProviderService', { error: (error as Error).message });
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Update a service
   */
  async updateService(
    id: string,
    updates: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<ApiResponse<Service>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('UPDATE', `service/${id}`, updates);

    try {
      // Use repository to update service
      const updatedService = await this.serviceRepository.update(id, {
        ...updates,
        updated_at: new Date().toISOString(),
      });

      return { data: updatedService, error: null, status: 200 };
    } catch (error) {
      logger.error('Error updating service', 'ServiceProviderService', { id, error });
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Delete a service
   */
  async deleteService(id: string): Promise<ApiResponse<null>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('DELETE', `service/${id}`);

    try {
      // Use repository to delete service
      const success = await this.serviceRepository.delete(id);

      return { data: null, error: null, status: success ? 200 : 404 };
    } catch (error) {
      logger.error('Error deleting service', 'ServiceProviderService', { id, error });
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Verify a service provider
   */
  async verifyServiceProvider(id: string): Promise<ApiResponse<ServiceProvider>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    // Check if user is an admin
    const isAdmin = await this.isUserAdmin();
    if (!isAdmin) {
      return { data: null, error: 'Only admins can verify service providers', status: 403 };
    }

    this.logOperation('VERIFY', `service_provider/${id}`);

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .update({
          is_verified: true,
          verification_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error verifying service provider',
        'ServiceProviderService',
        { id, error }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Get featured service providers
   */
  async getFeaturedServiceProviders(limit = 5): Promise<ApiResponse<ServiceProvider[]>> {
    this.logOperation('GET', 'service_providers/featured', { limit });

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .eq('is_verified', true)
        .order('rating_average', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return { data: data || [], error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error fetching featured providers',
        'ServiceProviderService',
        { limit, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Get provider availability
   */
  async getProviderAvailability(
    providerId: string
  ): Promise<ApiResponse<{ day_of_week: number; start_time: string; end_time: string }[]>> {
    this.logOperation('GET', `service_provider/${providerId}/availability`);

    try {
      const { data, error } = await supabase
        .from('service_availability')
        .select('*')
        .eq('provider_id', providerId);

      if (error) {
        throw error;
      }

      return { data: data || [], error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error fetching provider availability',
        'ServiceProviderService',
        { providerId, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Get service provider by user ID
   */
  async getServiceProviderByUserId(userId: string): Promise<ApiResponse<ServiceProvider | null>> {
    this.logOperation('GET', `user/${userId}/service_provider`);

    try {
      // ✅ FIXED: Remove .single() to be more robust with timing issues
      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false }) // Get most recent first
        .limit(1);

      // ✅ ENHANCED: Better error handling for timing issues
      if (error) {
        console.error('❌ [ServiceProviderService] Database error in getServiceProviderByUserId:', {
          userId,
          error: error.message,
          code: error.code,
          details: error.details
        });
        throw error;
      }

      // ✅ FIXED: Handle array response instead of single object
      const provider = data && data.length > 0 ? data[0] : null;

      console.log('🔍 [ServiceProviderService] Provider lookup result:', {
        userId,
        found: !!provider,
        providerId: provider?.id,
        businessName: provider?.business_name,
        createdAt: provider?.created_at
      });

      return { data: provider, error: null, status: provider ? 200 : 404 };
    } catch (error) {
      console.error('❌ [ServiceProviderService] Error fetching provider by user ID:', {
        userId,
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      
      logger.error(
        'Error fetching provider by user ID',
        'ServiceProviderService',
        { userId, error }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Check if a user is a service provider
   */
  async isUserServiceProvider(userId: string): Promise<ApiResponse<boolean>> {
    this.logOperation('CHECK', `user/${userId}/is_provider`);

    try {
      const { data } = await this.getServiceProviderByUserId(userId);
      return { data: !!data, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error checking if user is provider',
        'ServiceProviderService',
        { userId, error }
      );
      return { data: false, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Get all service providers
   */
  async getAllServiceProviders(): Promise<ApiResponse<ServiceProvider[]>> {
    this.logOperation('GET', 'service_providers/all');

    try {
      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return { data: data || [], error: null, status: 200 };
    } catch (error) {
      logger.error('Error fetching all providers', 'ServiceProviderService', { error: (error as Error).message });
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * Search service providers
   */
  async searchServiceProviders(
    query: string,
    categories?: string[],
    location?: string
  ): Promise<ApiResponse<ServiceProvider[]>> {
    this.logOperation('SEARCH', 'service_providers', { query, categories, location });

    try {
      let dbQuery = supabase.from('service_providers').select('*');

      // Text search across multiple fields
      if (query) {
        dbQuery = dbQuery.or(`business_name.ilike.%${query}%,description.ilike.%${query}%`);
      }

      // Filter by categories if provided
      if (categories && categories.length > 0) {
        // Need to check for overlap between service_categories array and the provided categories
        // This requires more advanced query capabilities or post-processing
        // For now, we'll implement a simple contains check
        categories.forEach(category => {
          dbQuery = dbQuery.contains('service_categories', [category]);
        });
      }

      // Filter by location if provided
      if (location) {
        dbQuery = dbQuery.ilike('business_address', `%${location}%`);
      }

      const { data, error } = await dbQuery;

      if (error) {
        throw error;
      }

      return { data: data || [], error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error searching providers',
        'ServiceProviderService',
        { query, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * FIXED: Get provider reviews using correct database structure
   * Uses service_reviews table linked through services to providers
   */
  async getProviderReviews(providerId: string): Promise<ApiResponse<any[]>> {
    this.logOperation('GET', `service_provider/${providerId}/reviews`);

    try {
      // OPTIMIZED: Single query with JOINs instead of separate queries
      const { data, error } = await supabase
        .from('service_reviews')
        .select(`
          *,
          services!inner(
            id,
            name,
            provider_id
          ),
          user_profiles!inner(
            id, 
            first_name, 
            last_name, 
            avatar_url
          )
        `)
        .eq('services.provider_id', providerId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }
      
      // Transform the data to include user and service information
      const transformedData = (data || []).map(review => {
        // Construct the display name from first_name and last_name
        const firstName = review.user_profiles?.first_name || '';
        const lastName = review.user_profiles?.last_name || '';
        const displayName = `${firstName} ${lastName}`.trim() || 'Anonymous User';
        
        return {
          ...review,
          service_name: review.services?.name || 'Unknown Service',
          user: {
            id: review.user_profiles?.id,
            display_name: displayName,
            avatar_url: review.user_profiles?.avatar_url
          },
          // Clean up the joined data
          services: undefined,
          user_profiles: undefined
        };
      });

      return { data: transformedData, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error fetching provider reviews',
        'ServiceProviderService',
        { providerId, error }
      );
      return { data: [], error: (error as Error).message, status: 500 };
    }
  }

  /**
   * FIXED: Add a review for a provider through service booking
   * Uses service_reviews table with proper relationships
   */
  async addProviderReview(
    serviceId: string,
    bookingId: string,
    userId: string,
    rating: number,
    reviewText: string
  ): Promise<ApiResponse<any>> {
    const authError = await this.ensureAuthenticated();
    if (authError) {
      return { data: null, error: authError, status: 401 };
    }

    this.logOperation('CREATE', `service/${serviceId}/review`, { rating, reviewText });

    try {
      // Validate input parameters
      if (!serviceId || !bookingId || !userId) {
        return { data: null, error: 'Missing required parameters', status: 400 };
      }

      if (rating < 1 || rating > 5) {
        return { data: null, error: 'Rating must be between 1 and 5', status: 400 };
      }

      if (!reviewText || reviewText.trim().length < 10) {
        return { data: null, error: 'Review text must be at least 10 characters', status: 400 };
      }

      // Check if user has already reviewed this service/booking
      const { data: existingReview, error: checkError } = await supabase
        .from('service_reviews')
        .select('id')
        .eq('service_id', serviceId)
        .eq('booking_id', bookingId)
        .eq('user_id', userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // Not found is expected
        throw checkError;
      }

      if (existingReview) {
        return {
          data: null,
          error: 'You have already reviewed this service booking',
          status: 400,
        };
      }

      // Get the provider ID for this service
      const { data: service, error: serviceError } = await supabase
        .from('services')
        .select('provider_id')
        .eq('id', serviceId)
        .single();

      if (serviceError || !service) {
        return { data: null, error: 'Service not found', status: 404 };
      }

      // Add the review
      const { data, error } = await supabase
        .from('service_reviews')
        .insert({
          service_id: serviceId,
          booking_id: bookingId,
          user_id: userId,
          rating,
          review_text: reviewText.trim(),
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update provider's average rating atomically
      const ratingUpdateResult = await this.updateProviderRatingAtomic(service.provider_id);
      
      if (ratingUpdateResult.error) {
        logger.warn(
          'Failed to update provider rating after review',
          'ServiceProviderService',
          { providerId: service.provider_id, serviceId, userId, error: ratingUpdateResult.error }
        );
      }

      return { data, error: null, status: 201 };
    } catch (error) {
      logger.error(
        'Error adding service review',
        'ServiceProviderService',
        { serviceId, bookingId, userId, error: (error as Error).message }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * ATOMIC: Update a provider's average rating using database function
   * This prevents race conditions in concurrent rating updates
   */
  async updateProviderRatingAtomic(providerId: string): Promise<ApiResponse<ServiceProvider | null>> {
    try {
      // Use database function for atomic rating calculation and update
      const { data, error } = await supabase.rpc('update_provider_rating_atomic', {
        p_provider_id: providerId
      });

      if (error) {
        throw error;
      }

      return { data, error: null, status: 200 };
    } catch (error) {
      logger.error(
        'Error updating provider rating atomically',
        'ServiceProviderService',
        { providerId, error: (error as Error).message }
      );
      return { data: null, error: (error as Error).message, status: 500 };
    }
  }

  /**
   * LEGACY: Update a provider's average rating (non-atomic)
   * @private
   * @deprecated Use updateProviderRatingAtomic instead to prevent race conditions
   */
  private async updateProviderRating(providerId: string): Promise<void> {
    try {
      // Calculate new average rating
      const { data: reviews, error: countError } = await supabase
        .from('provider_reviews')
        .select('rating')
        .eq('provider_id', providerId);

      if (countError) {
        throw countError;
      }

      if (!reviews || reviews.length === 0) {
        return;
      }

      const total = reviews.reduce((sum, review) => sum + review.rating, 0);
      const average = total / reviews.length;

      // Update provider record - WARNING: NOT ATOMIC, can cause race conditions
      const { error: updateError } = await supabase
        .from('service_providers')
        .update({
          rating_average: average,
          review_count: reviews.length,
          updated_at: new Date().toISOString(),
        })
        .eq('id', providerId);

      if (updateError) {
        throw updateError;
      }
    } catch (error) {
      logger.error(
        'Error updating provider rating (legacy)',
        'ServiceProviderService',
        { providerId, error: (error as Error).message }
      );
    }
  }
}

// Export a singleton instance
export const serviceProviderService = new ServiceProviderService();

/**
 * BACKWARD COMPATIBILITY EXPORTS
 * These function exports maintain compatibility with existing code
 * that imports the functions directly rather than using the service instance.
 */

// OPTIMIZED: New optimized getServiceProviders for better performance
export async function getServiceProvidersOptimized(
  search?: ServiceSearch,
  pagination?: { limit: number; offset: number }
): Promise<{ providers: ServiceProvider[]; total: number }> {
  try {
    const response = await serviceProviderService.getServiceProvidersOptimized(search, pagination);
    return response.data || { providers: [], total: 0 };
  } catch (error) {
    logger.error(
      'Error in optimized getServiceProviders',
      'ServiceProviderService',
      { search, pagination, error: (error as Error).message }
    );
    return { providers: [], total: 0 };
  }
}

// LEGACY: Backward compatibility for getServiceProviders
export async function getServiceProviders(search?: ServiceSearch): Promise<ServiceProvider[]> {
  try {
    const response = await serviceProviderService.getServiceProviders(search);
    return response.data || [];
  } catch (error) {
    logger.error(
      'Error in legacy getServiceProviders',
      'ServiceProviderService',
      { search, error: (error as Error).message }
    );
    return [];
  }
}

// Backward compatibility for getServiceProviderById
export async function getServiceProviderById(id: string): Promise<ServiceProvider | null> {
  try {
    const response = await serviceProviderService.getServiceProviderById(id);
    return response.data;
  } catch (error) {
    logger.error('Error in legacy getServiceProviderById', 'ServiceProviderService', { id, error: (error as Error).message });
    return null;
  }
}

// Backward compatibility for getServicesByProviderId
export async function getServicesByProviderId(providerId: string): Promise<Service[]> {
  try {
    const response = await serviceProviderService.getServicesByProviderId(providerId);
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy getServicesByProviderId', 'ServiceProviderService', { providerId, error: (error as Error).message });
    return [];
  }
}

// Backward compatibility for getServiceCategories
export async function getServiceCategories(): Promise<ServiceCategory[]> {
  try {
    const response = await serviceProviderService.getServiceCategories();
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy getServiceCategories', 'ServiceProviderService', { error: (error as Error).message });
    return [];
  }
}

// Backward compatibility for searchServices
export async function searchServices(search: ServiceSearch): Promise<ServiceWithProvider[]> {
  try {
    const response = await serviceProviderService.searchServices(search);
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy searchServices', 'ServiceProviderService', { search, error: (error as Error).message });
    return [];
  }
}

// Backward compatibility for createServiceProvider
export async function createServiceProvider(
  serviceProvider: Omit<
    ServiceProvider,
    'id' | 'created_at' | 'updated_at' | 'rating_average' | 'review_count'
  >
): Promise<ServiceProvider> {
  try {
    const response = await serviceProviderService.createServiceProvider(serviceProvider);
    if (!response.data) throw new Error('Failed to create service provider');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy createServiceProvider', 'ServiceProviderService', { error: (error as Error).message });
    throw error;
  }
}

// Backward compatibility for updateServiceProvider
export async function updateServiceProvider(
  id: string,
  updates: Partial<Omit<ServiceProvider, 'id' | 'created_at' | 'updated_at'>>
): Promise<ServiceProvider> {
  try {
    const response = await serviceProviderService.updateServiceProvider(id, updates);
    if (!response.data) throw new Error('Failed to update service provider');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy updateServiceProvider', 'ServiceProviderService', { id, error: (error as Error).message });
    throw error;
  }
}

// Backward compatibility for createService
export async function createService(
  service: Omit<Service, 'id' | 'created_at' | 'updated_at'>
): Promise<Service> {
  try {
    const response = await serviceProviderService.createService(service);
    if (!response.data) throw new Error('Failed to create service');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy createService', 'ServiceProviderService', { error: (error as Error).message });
    throw error;
  }
}

// Backward compatibility for updateService
export async function updateService(
  id: string,
  updates: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at'>>
): Promise<Service> {
  try {
    const response = await serviceProviderService.updateService(id, updates);
    if (!response.data) throw new Error('Failed to update service');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy updateService', 'ServiceProviderService', { id, error });
    throw error;
  }
}

// Backward compatibility for deleteService
export async function deleteService(id: string): Promise<void> {
  try {
    await serviceProviderService.deleteService(id);
  } catch (error) {
    logger.error('Error in legacy deleteService', 'ServiceProviderService', { id, error });
    throw error;
  }
}

// Backward compatibility for verifyServiceProvider
export async function verifyServiceProvider(id: string): Promise<ServiceProvider> {
  try {
    const response = await serviceProviderService.verifyServiceProvider(id);
    if (!response.data) throw new Error('Failed to verify service provider');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy verifyServiceProvider', 'ServiceProviderService', { id, error: (error as Error).message });
    throw error;
  }
}

// Backward compatibility for getFeaturedServiceProviders
export async function getFeaturedServiceProviders(limit = 5): Promise<ServiceProvider[]> {
  try {
    const response = await serviceProviderService.getFeaturedServiceProviders(limit);
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy getFeaturedServiceProviders', 'ServiceProviderService', { limit, error: (error as Error).message });
    return [];
  }
}

// Backward compatibility for getProviderAvailability
export async function getProviderAvailability(
  providerId: string
): Promise<{ day_of_week: number; start_time: string; end_time: string }[]> {
  try {
    const response = await serviceProviderService.getProviderAvailability(providerId);
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy getProviderAvailability', 'ServiceProviderService', { providerId, error: (error as Error).message });
    return [];
  }
}

// Add other backward compatibility exports as needed
export async function getServiceProviderByUserId(userId: string): Promise<ServiceProvider | null> {
  try {
    const response = await serviceProviderService.getServiceProviderByUserId(userId);
    return response.data;
  } catch (error) {
    logger.error('Error in legacy getServiceProviderByUserId', 'ServiceProviderService', { userId, error: (error as Error).message });
    return null;
  }
}

export const isUserServiceProvider = async (userId: string): Promise<boolean> => {
  try {
    const response = await serviceProviderService.isUserServiceProvider(userId);
    return response.data || false;
  } catch (error) {
    logger.error('Error in legacy isUserServiceProvider', 'ServiceProviderService', { userId, error: (error as Error).message });
    return false;
  }
};

export async function getAllServiceProviders(): Promise<ServiceProvider[]> {
  try {
    const response = await serviceProviderService.getAllServiceProviders();
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy getAllServiceProviders', 'ServiceProviderService', { error: (error as Error).message });
    return [];
  }
}

export async function searchServiceProviders(
  query: string,
  categories?: string[],
  location?: string
): Promise<ServiceProvider[]> {
  try {
    const response = await serviceProviderService.searchServiceProviders(
      query,
      categories,
      location
    );
    return response.data || [];
  } catch (error) {
    logger.error('Error in legacy searchServiceProviders', 'ServiceProviderService', { query, error: (error as Error).message });
    return [];
  }
}

/**
 * Get reviews for a service provider - direct implementation to avoid circular reference
 */
export async function getProviderReviews(providerId: string): Promise<any[]> {
  try {
    // Query service_reviews by joining with services to filter by provider_id
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('id')
      .eq('provider_id', providerId);
      
    if (servicesError) {
      throw servicesError;
    }
    
    // If no services found for this provider, return empty array
    if (!services || services.length === 0) {
      return [];
    }
    
    // Get the service IDs for this provider
    const serviceIds = services.map(service => service.id);
    
    // Query reviews for these services
    const { data, error } = await supabase
      .from('service_reviews')
      .select(`
        *,
        user_profiles(id, first_name, last_name, avatar_url)
      `)
      .in('service_id', serviceIds)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }
    
    // Transform the data to include user profile information
    const transformedData = (data || []).map(review => {
      // Construct the display name from first_name and last_name
      const firstName = review.user_profiles?.first_name || '';
      const lastName = review.user_profiles?.last_name || '';
      const displayName = `${firstName} ${lastName}`.trim() || 'Anonymous User';
      
      return {
        ...review,
        user: {
          id: review.user_profiles?.id,
          username: review.user_profiles?.username || displayName,
          display_name: displayName,
          avatar_url: review.user_profiles?.avatar_url
        },
        user_profiles: undefined // Remove the joined data to keep the structure clean
      };
    });

    return transformedData || [];
  } catch (error) {
    logger.error('Error in getProviderReviews', 'ServiceProviderService', { providerId, error: (error as Error).message });
    return [];
  }
}

export async function addProviderReview(
  serviceId: string,
  bookingId: string,
  userId: string,
  rating: number,
  comment: string
): Promise<any> {
  try {
    const response = await serviceProviderService.addProviderReview(
      serviceId,
      bookingId,
      userId,
      rating,
      comment
    );
    if (!response.data) throw new Error('Failed to add provider review');
    return response.data;
  } catch (error) {
    logger.error('Error in legacy addProviderReview', 'ServiceProviderService', { serviceId, bookingId, userId, error: (error as Error).message });
    throw error;
  }
}
