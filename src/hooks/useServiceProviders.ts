import { useState, useEffect, useCallback } from 'react';
import {
  ServiceProvider,
  ServiceCategory,
  Service,
  getServiceProviders,
  getServiceCategories,
  getServiceProviderById,
  getServicesByProviderId,
  ServiceSearch,
} from '@services/index';
import Toast from 'react-native-toast-message';

interface UseServiceProvidersReturn {
  providers: ServiceProvider[];
  categories: ServiceCategory[];
  isLoading: boolean;
  error: string | null;
  selectedProvider: ServiceProvider | null;
  selectedProviderServices: Service[];
  fetchProviders: (search?: ServiceSearch) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchProviderDetails: (id: string) => Promise<void>;
  isProviderLoading: boolean;
}

export function useServiceProviders(): UseServiceProvidersReturn {
  const [providers, setProviders] = useState<ServiceProvider[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProviderLoading, setIsProviderLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(null);
  const [selectedProviderServices, setSelectedProviderServices] = useState<Service[]>([]);

  const sanitizeCategory = (category: ServiceCategory): ServiceCategory => {
    return {
      ...category,
      // Ensure color is a valid string
      color: typeof category.color === 'string' ? category.color : '#6366F1',
    };
  };

  const fetchProviders = useCallback(async (search?: ServiceSearch) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await getServiceProviders(search);
      setProviders(data);
    } catch (err) {
      console.error('Error fetching providers:', err);
      // Don't show error toast for database table not found (common during initial setup)
      if ((err as any)?.code === '42P01') {
        setError('Service provider tables not set up yet.');
      } else {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load service providers';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }
      // Set empty array on error to avoid undefined issues
      setProviders([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await getServiceCategories();
      // Sanitize the categories to ensure valid color values
      setCategories(data.map(sanitizeCategory));
    } catch (err) {
      console.error('Error fetching categories:', err);
      // Don't show error toast for database table not found (common during initial setup)
      if ((err as any)?.code === '42P01') {
        // Just fail silently for categories, as we'll use defaults
        setCategories([]);
      } else {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load service categories';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchProviderDetails = useCallback(async (id: string) => {
    try {
      setIsProviderLoading(true);
      setError(null);

      // Get provider details
      const provider = await getServiceProviderById(id);
      if (!provider) {
        throw new Error('Service provider not found');
      }
      setSelectedProvider(provider);

      // Get provider services
      const services = await getServicesByProviderId(id);
      setSelectedProviderServices(services);
    } catch (err) {
      console.error('Error fetching provider details:', err);
      if ((err as any)?.code === '42P01') {
        setError('Service provider tables not set up yet.');
      } else {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load provider details';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }

      // Set empty values on error
      setSelectedProvider(null);
      setSelectedProviderServices([]);
    } finally {
      setIsProviderLoading(false);
    }
  }, []);

  // Load providers and categories on mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([fetchProviders(), fetchCategories()]);
    };

    loadInitialData();
  }, [fetchProviders, fetchCategories]);

  return {
    providers,
    categories,
    isLoading,
    error,
    selectedProvider,
    selectedProviderServices,
    fetchProviders,
    fetchCategories,
    fetchProviderDetails,
    isProviderLoading,
  };
}
