import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import {
  ServiceProvider,
  ServiceCategory,
  getServiceProvidersOptimized,
  getServiceCategories,
  getServiceProviderById,
  getServicesByProviderId,
  ServiceSearch,
} from '@services/index';
import { Service } from '@types/models';
import Toast from 'react-native-toast-message';

interface UseServiceProvidersWithCacheReturn {
  providers: ServiceProvider[];
  categories: ServiceCategory[];
  isLoading: boolean;
  error: string | null;
  selectedProvider: ServiceProvider | null;
  selectedProviderServices: Service[];
  totalProviders: number;
  hasMore: boolean;
  fetchMoreProviders: () => void;
  refetchProviders: () => Promise<void>;
  refetchCategories: () => Promise<void>;
  fetchProviderDetails: (id: string) => Promise<void>;
  isProviderLoading: boolean;
  clearCache: () => void;
  getCacheStatus: () => CacheStatus;
}

interface CacheStatus {
  providersLastFetched: number | null;
  categoriesLastFetched: number | null;
  providersCacheHit: boolean;
  categoriesCacheHit: boolean;
  totalCacheSize: number;
}

interface PaginationState {
  offset: number;
  limit: number;
  hasMore: boolean;
}

// Cache configuration for different data types
const CACHE_CONFIG = {
  providers: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    retry: 3,
  },
  categories: {
    staleTime: 60 * 60 * 1000, // 1 hour
    cacheTime: 2 * 60 * 60 * 1000, // 2 hours  
    refetchOnWindowFocus: false,
    retry: 2,
  },
  providerDetails: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 20 * 60 * 1000, // 20 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  }
};

export function useServiceProvidersWithCache(
  initialSearch?: ServiceSearch,
  initialLimit: number = 20
): UseServiceProvidersWithCacheReturn {
  const queryClient = useQueryClient();
  const [currentSearch, setCurrentSearch] = useState<ServiceSearch | undefined>(initialSearch);
  const [pagination, setPagination] = useState<PaginationState>({
    offset: 0,
    limit: initialLimit,
    hasMore: true
  });
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(null);

  // ==================== PROVIDERS QUERY ====================
  const {
    data: providersData,
    isLoading: isProvidersLoading,
    error: providersError,
    refetch: refetchProviders,
    dataUpdatedAt: providersLastFetched
  } = useQuery({
    queryKey: ['serviceProviders', currentSearch, pagination.offset, pagination.limit],
    queryFn: async () => {
      const startTime = performance.now();
      
      const result = await getServiceProvidersOptimized(currentSearch, {
        limit: pagination.limit,
        offset: pagination.offset
      });
      
      const endTime = performance.now();
      console.log(`🚀 CACHED: Provider query took ${endTime - startTime}ms`);
      
      return result;
    },
    ...CACHE_CONFIG.providers,
    onSuccess: (data) => {
      // Update pagination state based on results
      setPagination(prev => ({
        ...prev,
        hasMore: data.providers.length === prev.limit
      }));
    },
    onError: (error) => {
      console.error('Failed to fetch providers:', error);
      Toast.show({
        type: 'error',
        text1: 'Loading Error',
        text2: 'Failed to load service providers. Using cached data.',
      });
    }
  });

  // ==================== CATEGORIES QUERY ====================
  const {
    data: categories = [],
    isLoading: isCategoriesLoading,
    error: categoriesError,
    refetch: refetchCategories,
    dataUpdatedAt: categoriesLastFetched
  } = useQuery({
    queryKey: ['serviceCategories'],
    queryFn: async () => {
      const data = await getServiceCategories();
      // Sanitize categories for consistent color values
      return data.map(category => ({
        ...category,
        color: typeof category.color === 'string' ? category.color : '#6366F1',
      }));
    },
    ...CACHE_CONFIG.categories,
    onError: (error) => {
      console.error('Failed to fetch categories:', error);
      // Fail silently for categories and use defaults
    }
  });

  // ==================== PROVIDER DETAILS QUERY ====================
  const {
    data: selectedProvider = null,
    isLoading: isProviderLoading,
    error: providerError
  } = useQuery({
    queryKey: ['serviceProvider', selectedProviderId],
    queryFn: () => selectedProviderId ? getServiceProviderById(selectedProviderId) : null,
    enabled: !!selectedProviderId,
    ...CACHE_CONFIG.providerDetails,
    onError: (error) => {
      console.error('Failed to fetch provider details:', error);
      Toast.show({
        type: 'error',
        text1: 'Provider Error',
        text2: 'Failed to load provider details',
      });
    }
  });

  // ==================== PROVIDER SERVICES QUERY ====================
  const {
    data: selectedProviderServices = []
  } = useQuery({
    queryKey: ['providerServices', selectedProviderId],
    queryFn: () => selectedProviderId ? getServicesByProviderId(selectedProviderId) : [],
    enabled: !!selectedProviderId,
    ...CACHE_CONFIG.providerDetails
  });

  // ==================== INFINITE SCROLL MUTATION ====================
  const fetchMoreMutation = useMutation({
    mutationFn: async () => {
      const nextOffset = pagination.offset + pagination.limit;
      
      const result = await getServiceProvidersOptimized(currentSearch, {
        limit: pagination.limit,
        offset: nextOffset
      });
      
      return { ...result, offset: nextOffset };
    },
    onSuccess: (newData) => {
      // Update cache with new data (append to existing)
      const currentData = providersData;
      if (currentData) {
        const updatedData = {
          providers: [...currentData.providers, ...newData.providers],
          total: newData.total
        };
        
        // Update cache
        queryClient.setQueryData(
          ['serviceProviders', currentSearch, pagination.offset, pagination.limit],
          updatedData
        );
        
        // Update pagination
        setPagination(prev => ({
          ...prev,
          offset: newData.offset,
          hasMore: newData.providers.length === prev.limit
        }));
      }
    },
    onError: (error) => {
      console.error('Failed to load more providers:', error);
      Toast.show({
        type: 'error',
        text1: 'Loading Error',
        text2: 'Failed to load more providers',
      });
    }
  });

  // ==================== CALLBACKS ====================
  const fetchMoreProviders = useCallback(() => {
    if (pagination.hasMore && !fetchMoreMutation.isLoading) {
      fetchMoreMutation.mutate();
    }
  }, [pagination.hasMore, fetchMoreMutation]);

  const fetchProviderDetails = useCallback(async (id: string) => {
    setSelectedProviderId(id);
  }, []);

  const clearCache = useCallback(() => {
    // Clear all service provider related cache
    queryClient.removeQueries({ queryKey: ['serviceProviders'] });
    queryClient.removeQueries({ queryKey: ['serviceCategories'] });
    queryClient.removeQueries({ queryKey: ['serviceProvider'] });
    queryClient.removeQueries({ queryKey: ['providerServices'] });
    
    Toast.show({
      type: 'success',
      text1: 'Cache Cleared',
      text2: 'All cached data has been refreshed',
    });
  }, [queryClient]);

  const getCacheStatus = useCallback((): CacheStatus => {
    const providersCache = queryClient.getQueryState(['serviceProviders', currentSearch, pagination.offset, pagination.limit]);
    const categoriesCache = queryClient.getQueryState(['serviceCategories']);
    
    return {
      providersLastFetched: providersLastFetched || null,
      categoriesLastFetched: categoriesLastFetched || null,
      providersCacheHit: !!providersCache?.data,
      categoriesCacheHit: !!categoriesCache?.data,
      totalCacheSize: queryClient.getQueryCache().getAll().length
    };
  }, [queryClient, currentSearch, pagination, providersLastFetched, categoriesLastFetched]);

  // ==================== SEARCH FUNCTIONALITY ====================
  const handleSearchChange = useCallback((search?: ServiceSearch) => {
    setCurrentSearch(search);
    setPagination(prev => ({ ...prev, offset: 0, hasMore: true }));
  }, []);

  // Extract data with fallbacks
  const providers = providersData?.providers || [];
  const totalProviders = providersData?.total || 0;
  const isLoading = isProvidersLoading || isCategoriesLoading;
  const error = providersError?.message || categoriesError?.message || providerError?.message || null;

  return {
    providers,
    categories,
    isLoading,
    error,
    selectedProvider,
    selectedProviderServices,
    totalProviders,
    hasMore: pagination.hasMore,
    fetchMoreProviders,
    refetchProviders: async () => {
      await refetchProviders();
    },
    refetchCategories: async () => {
      await refetchCategories();
    },
    fetchProviderDetails,
    isProviderLoading,
    clearCache,
    getCacheStatus,
    // Hidden search function for internal use
    _handleSearchChange: handleSearchChange,
  };
} 