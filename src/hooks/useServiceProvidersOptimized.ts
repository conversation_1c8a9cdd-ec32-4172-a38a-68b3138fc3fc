import { useState, useEffect, useCallback } from 'react';
import {
  ServiceProvider,
  ServiceCategory,
  getServiceProvidersOptimized,
  getServiceCategories,
  getServiceProviderById,
  getServicesByProviderId,
  ServiceSearch,
} from '@services/index';
import { Service } from '@types/models';
import Toast from 'react-native-toast-message';

interface UseServiceProvidersOptimizedReturn {
  providers: ServiceProvider[];
  categories: ServiceCategory[];
  isLoading: boolean;
  error: string | null;
  selectedProvider: ServiceProvider | null;
  selectedProviderServices: Service[];
  totalProviders: number;
  hasMore: boolean;
  fetchProviders: (search?: ServiceSearch, loadMore?: boolean) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchProviderDetails: (id: string) => Promise<void>;
  isProviderLoading: boolean;
  refreshProviders: () => Promise<void>;
}

interface PaginationState {
  offset: number;
  limit: number;
  hasMore: boolean;
}

export function useServiceProvidersOptimized(): UseServiceProvidersOptimizedReturn {
  const [providers, setProviders] = useState<ServiceProvider[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProviderLoading, setIsProviderLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(null);
  const [selectedProviderServices, setSelectedProviderServices] = useState<Service[]>([]);
  const [totalProviders, setTotalProviders] = useState(0);
  const [pagination, setPagination] = useState<PaginationState>({
    offset: 0,
    limit: 20,
    hasMore: true
  });
  const [currentSearch, setCurrentSearch] = useState<ServiceSearch | undefined>();

  const sanitizeCategory = (category: ServiceCategory): ServiceCategory => {
    return {
      ...category,
      // Ensure color is a valid string
      color: typeof category.color === 'string' ? category.color : '#6366F1',
    };
  };

  const fetchProviders = useCallback(async (search?: ServiceSearch, loadMore = false) => {
    try {
      if (!loadMore) {
        setIsLoading(true);
        setPagination(prev => ({ ...prev, offset: 0 }));
      }

      setError(null);
      
      const paginationParams = {
        limit: pagination.limit,
        offset: loadMore ? pagination.offset : 0
      };

      // Track performance
      const startTime = performance.now();
      
      const { providers: newProviders, total } = await getServiceProvidersOptimized(
        search, 
        paginationParams
      );
      
      const endTime = performance.now();
      console.log(`✅ OPTIMIZED: Category resolution took ${endTime - startTime}ms for ${newProviders.length} providers`);

      if (loadMore) {
        // Append to existing providers for infinite scroll
        setProviders(prev => [...prev, ...newProviders]);
        setPagination(prev => ({
          ...prev,
          offset: prev.offset + prev.limit,
          hasMore: newProviders.length === prev.limit
        }));
      } else {
        // Replace providers for new search
        setProviders(newProviders);
        setPagination(prev => ({
          ...prev,
          offset: paginationParams.limit,
          hasMore: newProviders.length === prev.limit
        }));
      }

      setTotalProviders(total);
      setCurrentSearch(search);
      
    } catch (err) {
      console.error('Error fetching optimized providers:', err);
      
      // Handle specific error types
      if (err && typeof err === 'object' && 'code' in err && err.code === '42P01') {
        setError('Service provider tables not set up yet.');
      } else {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load service providers';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Performance Error',
          text2: 'Using optimized loading. Please try again.',
        });
      }
      
      // Set empty array on error to avoid undefined issues
      if (!loadMore) {
        setProviders([]);
        setTotalProviders(0);
      }
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit, pagination.offset]);

  const refreshProviders = useCallback(async () => {
    setPagination(prev => ({ ...prev, offset: 0, hasMore: true }));
    await fetchProviders(currentSearch, false);
  }, [currentSearch, fetchProviders]);

  const fetchCategories = useCallback(async () => {
    try {
      setError(null);
      const data = await getServiceCategories();
      // Sanitize the categories to ensure valid color values
      setCategories(data.map(sanitizeCategory));
    } catch (err) {
      console.error('Error fetching categories:', err);
      // Don't show error toast for database table not found (common during initial setup)
      if (err && typeof err === 'object' && 'code' in err && err.code === '42P01') {
        // Just fail silently for categories, as we'll use defaults
        setCategories([]);
      } else {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load service categories';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }
    }
  }, []);

  const fetchProviderDetails = useCallback(async (id: string) => {
    try {
      setIsProviderLoading(true);
      setError(null);

      // Get provider details
      const provider = await getServiceProviderById(id);
      if (!provider) {
        throw new Error('Service provider not found');
      }
      setSelectedProvider(provider);

      // Get provider services
      const services = await getServicesByProviderId(id);
      setSelectedProviderServices(services);
    } catch (err) {
      console.error('Error fetching provider details:', err);
      if (err && typeof err === 'object' && 'code' in err && err.code === '42P01') {
        setError('Service provider tables not set up yet.');
      } else {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load provider details';
        setError(errorMessage);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }

      // Set empty values on error
      setSelectedProvider(null);
      setSelectedProviderServices([]);
    } finally {
      setIsProviderLoading(false);
    }
  }, []);

  // Load providers and categories on mount
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([fetchProviders(), fetchCategories()]);
    };

    loadInitialData();
  }, []);

  return {
    providers,
    categories,
    isLoading,
    error,
    selectedProvider,
    selectedProviderServices,
    totalProviders,
    hasMore: pagination.hasMore,
    fetchProviders,
    fetchCategories,
    fetchProviderDetails,
    isProviderLoading,
    refreshProviders,
  };
} 