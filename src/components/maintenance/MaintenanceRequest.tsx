import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { TextInput, Select } from '@components/ui';
import { Button } from '@design-system';
import { MaintenanceService } from '@services/MaintenanceService';
import { useAuth } from '@hooks/useAuth';
import { ImageUpload } from '@components/common/ImageUpload';
import { ServiceBooking } from '@types/services';

interface MaintenanceRequestProps {
  agreementId: string;
  onRequestCreated?: (request: ServiceBooking) => void;
}

export const MaintenanceRequest: React.FC<MaintenanceRequestProps> = ({
  agreementId,
  onRequestCreated,
}) => {
  const { authState } = useAuth();
  const user = authState?.user;
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'normal' | 'high' | 'emergency'>('normal');
  const [category, setCategory] = useState('');
  const [location, setLocation] = useState('');
  const [photos, setPhotos] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const maintenanceService = new MaintenanceService();

  const handleSubmit = async () => {
    if (!title || !description || !category || !location) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      const request = await maintenanceService.createMaintenanceRequest({
        agreement_id: agreementId,
        title,
        description,
        priority,
        category,
        location,
        photos,
      });

      Alert.alert('Success', 'Maintenance request created successfully', [
        {
          text: 'OK',
          onPress: () => {
            // Reset form
            setTitle('');
            setDescription('');
            setPriority('normal');
            setCategory('');
            setLocation('');
            setPhotos([]);
            onRequestCreated?.(request);
          },
        },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create maintenance request');
      console.error('Error creating maintenance request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>New Maintenance Request</Text>

      <TextInput
        label="Title"
        value={title}
        onChangeText={setTitle}
        placeholder="Brief description of the issue"
        required
      />

      <TextInput
        label="Description"
        value={description}
        onChangeText={setDescription}
        placeholder="Detailed description of the maintenance issue"
        multiline
        numberOfLines={4}
        required
      />

      <Select
        label="Priority"
        value={priority}
        onValueChange={value => setPriority(value as typeof priority)}
        items={[
          { label: 'Low', value: 'low' },
          { label: 'Normal', value: 'normal' },
          { label: 'High', value: 'high' },
          { label: 'Emergency', value: 'emergency' },
        ]}
      />

      <Select
        label="Category"
        value={category}
        onValueChange={setCategory}
        items={[
          { label: 'General Maintenance', value: 'Maintenance' },
          { label: 'Plumbing', value: 'Plumbing' },
          { label: 'Electrical', value: 'Electrical' },
        ]}
        required
      />

      <TextInput
        label="Location"
        value={location}
        onChangeText={setLocation}
        placeholder="Where is the issue located?"
        required
      />

      <ImageUpload
        images={photos}
        onImagesChanged={setPhotos}
        maxImages={4}
        label="Add Photos (Optional)"
      />

      <Button
        title="Submit Request"
        onPress={handleSubmit}
        loading={isSubmitting}
        disabled={isSubmitting}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
});

export default MaintenanceRequest;
