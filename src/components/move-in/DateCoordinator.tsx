import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  MoveInScheduleService,
  MoveInSchedule,
  TimeSlot,
  DateRange,
  TimePreference,
} from '@services/MoveInScheduleService';
import { useAuth } from '@hooks/useAuth';
import { Card } from '@components/common/Card';
import { Button } from '@components/common/Button';
import { TextInput } from '@components/common/TextInput';
import { Avatar } from '@components/common/Avatar';
import { Calendar } from '@components/common/Calendar';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface DateCoordinatorProps {
  agreementId: string;
  onDateConfirmed?: (date: Date) => void;
}

export const DateCoordinator: React.FC<DateCoordinatorProps> = ({
  agreementId,
  onDateConfirmed,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [schedule, setSchedule] = useState<MoveInSchedule | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [availabilityData, setAvailabilityData] = useState<any[]>([]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const scheduleService = new MoveInScheduleService();
  const { authState } = useAuth();
  const user = authState?.user;

  useEffect(() => {
    loadScheduleData();
  }, [agreementId]);

  const loadScheduleData = async () => {
    try {
      setLoading(true);
      const availability = await scheduleService.getAvailabilityForAgreement(
        agreementId
      );
      setAvailabilityData(availability);

      // If there's an existing schedule, load it
      const { data: existingSchedule } = await supabase
        .from('move_in_schedules')
        .select('*')
        .eq('agreement_id', agreementId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (existingSchedule) {
        const fullSchedule = await scheduleService.getSchedule(existingSchedule.id);
        setSchedule(fullSchedule);
        setTimeSlots(fullSchedule.move_in_time_slots);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load schedule data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (schedule) {
      const subscription = scheduleService.subscribeToScheduleUpdates(
        schedule.id,
        (payload) => {
          if (payload.new) {
            setSchedule(payload.new);
          }
        }
      );

      return () => {
        subscription.then((sub) => sub.unsubscribe());
      };
    }
  }, [schedule]);

  const handleSetAvailability = async () => {
    try {
      const timePreference: TimePreference = {
        preferredTimeRanges: [{ start: '09:00', end: '17:00' }],
        preferredDays: ['weekday'],
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };

      await scheduleService.setAvailability(
        user?.id!,
        agreementId,
        [], // preferred dates
        [], // blocked dates
        timePreference
      );

      Alert.alert('Success', 'Availability preferences saved');
      loadScheduleData();
    } catch (err) {
      Alert.alert('Error', 'Failed to save availability preferences');
    }
  };

  const handleProposeDate = async () => {
    if (!selectedDate) return;

    try {
      const newSchedule = await scheduleService.proposeDate(
        agreementId,
        selectedDate,
        notes
      );
      setSchedule(newSchedule);
      setSelectedDate(null);
      setNotes('');
      Alert.alert('Success', 'Move-in date proposed successfully');
    } catch (err) {
      Alert.alert('Error', 'Failed to propose date');
    }
  };

  const handleRespond = async (response: 'confirmed' | 'rejected') => {
    if (!schedule) return;

    try {
      await scheduleService.respondToProposal(schedule.id, response, notes);
      if (response === 'confirmed' && onDateConfirmed) {
        onDateConfirmed(new Date(schedule.proposedDate));
      }
      loadScheduleData();
    } catch (err) {
      Alert.alert('Error', `Failed to ${response} date`);
    }
  };

  const handleBookSlot = async (slotId: string) => {
    try {
      await scheduleService.bookTimeSlot(slotId, user?.id!);
      loadScheduleData();
    } catch (err) {
      Alert.alert('Error', 'Failed to book time slot');
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text>Loading schedule data...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button onPress={loadScheduleData} title="Retry" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Move-in Date Coordination</Text>
        {schedule && (
          <Text style={styles.status}>
            Status: {schedule.status.toUpperCase()}
          </Text>
        )}
      </View>

      <ScrollView style={styles.content}>
        {/* Availability Section */}
        <PropertyCard style={styles.section}>
          <Text style={styles.sectionTitle}>Availability Preferences</Text>
          <Button
            title="Set My Availability"
            onPress={handleSetAvailability}
          />
          {availabilityData.map((availability) => (
            <View key={availability.id} style={styles.availabilityItem}>
              <Avatar
                size="small"
                url={availability.user.avatar_url}
                name={availability.user.full_name}
              />
              <Text style={styles.userName}>{availability.user.full_name}</Text>
            </View>
          ))}
        </PropertyCard>

        {/* Date Proposal Section */}
        {!schedule?.confirmedDate && (
          <PropertyCard style={styles.section}>
            <Text style={styles.sectionTitle}>Propose Move-in Date</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => setShowDatePicker(true)}
            >
              <Text>
                {selectedDate
                  ? selectedDate.toLocaleDateString()
                  : 'Select a date'}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={selectedDate || new Date()}
                mode="date"
                display="default"
                onChange={(event, date) => {
                  setShowDatePicker(false);
                  if (date) setSelectedDate(date);
                }}
                minimumDate={new Date()}
              />
            )}
            <TextInput
              value={notes}
              onChangeText={setNotes}
              placeholder="Add notes (optional)"
              multiline
            />
            <Button
              title="Propose Date"
              onPress={handleProposeDate}
              disabled={!selectedDate}
            />
          </PropertyCard>
        )}

        {/* Current Schedule Section */}
        {schedule && (
          <PropertyCard style={styles.section}>
            <Text style={styles.sectionTitle}>Current Schedule</Text>
            <Text style={styles.date}>
              {new Date(schedule.proposedDate).toLocaleDateString()}
            </Text>
            {schedule.notes && (
              <Text style={styles.notes}>{schedule.notes}</Text>
            )}
            {schedule.status === 'proposed' && (
              <View style={styles.actions}>
                <Button
                  title="Confirm"
                  onPress={() => handleRespond('confirmed')}
                />
                <Button
                  title="Reject"
                  onPress={() => handleRespond('rejected')}
                  type="secondary"
                />
              </View>
            )}
          </PropertyCard>
        )}

        {/* Time Slots Section */}
        {schedule?.status === 'confirmed' && (
          <PropertyCard style={styles.section}>
            <Text style={styles.sectionTitle}>Time Slots</Text>
            {timeSlots.map((slot) => (
              <View key={slot.id} style={styles.timeSlot}>
                <Text>
                  {new Date(slot.startTime).toLocaleTimeString()} -{' '}
                  {new Date(slot.endTime).toLocaleTimeString()}
                </Text>
                {slot.status === 'available' ? (
                  <Button
                    title="Book Slot"
                    onPress={() => handleBookSlot(slot.id)}
                  />
                ) : (
                  <Text style={styles.bookedText}>
                    {slot.status.toUpperCase()}
                  </Text>
                )}
              </View>
            ))}
          </PropertyCard>
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  status: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  availabilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  userName: {
    marginLeft: 8,
    fontSize: 14,
  },
  dateSelector: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    marginBottom: 12,
  },
  date: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  notes: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
  },
  timeSlot: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  bookedText: {
    color: theme.colors.error,
    fontWeight: '500',
  },
  errorText: {
    color: theme.colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
});
export default DateCoordinator;
