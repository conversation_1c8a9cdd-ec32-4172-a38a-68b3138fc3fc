import React, { useEffect, useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import {
  InventoryService,
  InventoryItem,
  ItemShare,
  MaintenanceRecord,
} from '@services/InventoryService';
import { useAuth } from '@hooks/useAuth';
import { Card } from '@components/common/Card';
import { Button } from '@components/common/Button';
import { TextInput } from '@components/common/TextInput';
import { Avatar } from '@components/common/Avatar';
import { Modal } from '@components/common/Modal';
import { ImagePicker } from '@components/common/ImagePicker';
import { CurrencyInput } from '@components/common/CurrencyInput';
import { DatePicker } from '@components/common/DatePicker';
import { useColorFix } from '@hooks/useColorFix';
;

interface InventoryManagerProps {
  agreementId: string;
  onItemAdded?: () => void;
}

export const InventoryManager: React.FC<InventoryManagerProps> = ({ agreementId, onItemAdded }) => {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [itemName, setItemName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [condition, setCondition] = useState('');
  const [purchaseDate, setPurchaseDate] = useState<Date | null>(null);
  const [purchasePrice, setPurchasePrice] = useState('');
  const [currentValue, setCurrentValue] = useState('');
  const [ownershipType, setOwnershipType] = useState<'shared' | 'individual'>('individual');
  const [location, setLocation] = useState('');
  const [notes, setNotes] = useState('');
  const [photos, setPhotos] = useState<string[]>([]);

  // Maintenance form state
  const [maintenanceType, setMaintenanceType] = useState('');
  const [maintenanceDescription, setMaintenanceDescription] = useState('');
  const [maintenanceCost, setMaintenanceCost] = useState('');
  const [maintenanceDate, setMaintenanceDate] = useState<Date>(new Date());
  const [nextMaintenanceDate, setNextMaintenanceDate] = useState<Date | null>(null);

  const inventoryService = new InventoryService();
  const { authState } = useAuth();
  const user = authState?.user;

  useEffect(() => {
    loadInventory();
  }, [agreementId]);

  useEffect(() => {
    if (agreementId) {
      const subscription = inventoryService.subscribeToInventoryUpdates(agreementId, payload => {
        if (payload.new) {
          setItems(current =>
            current.map(item => (item.id === payload.new.id ? payload.new : item))
          );
        }
      });

      return () => {
        subscription.then(sub => sub.unsubscribe());
      };
    }
  }, [agreementId]);

  const loadInventory = async () => {
    try {
      setLoading(true);
      const inventoryItems = await inventoryService.getInventoryItems(agreementId);
      setItems(inventoryItems);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load inventory');
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = async () => {
    if (!itemName || !category || !condition) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const newItem = await inventoryService.addItem({
        agreement_id: agreementId,
        name: itemName,
        description,
        category,
        condition_status: condition,
        purchase_date: purchaseDate,
        purchase_price: purchasePrice ? parseFloat(purchasePrice) : undefined,
        current_value: currentValue ? parseFloat(currentValue) : undefined,
        ownership_type: ownershipType,
        owner_id: ownershipType === 'individual' ? user?.id : undefined,
        location,
        notes,
        photos,
      });

      setItems(current => [newItem, ...current]);
      setShowAddModal(false);
      resetForm();
      if (onItemAdded) onItemAdded();
    } catch (err) {
      Alert.alert('Error', 'Failed to add item');
    }
  };

  const handleAddMaintenance = async () => {
    if (!selectedItem || !maintenanceType || !maintenanceDescription) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      await inventoryService.addMaintenanceRecord({
        item_id: selectedItem,
        maintenance_type: maintenanceType,
        description: maintenanceDescription,
        cost: parseFloat(maintenanceCost || '0'),
        performed_by: user?.id!,
        performed_date: maintenanceDate,
        next_maintenance_date: nextMaintenanceDate,
      });

      setShowMaintenanceModal(false);
      resetMaintenanceForm();
      loadInventory();
    } catch (err) {
      Alert.alert('Error', 'Failed to add maintenance record');
    }
  };

  const handlePhotoUpload = async (file: File) => {
    try {
      const { path, error } = await inventoryService.uploadItemPhoto(file, selectedItem || 'new');
      if (error) throw error;
      setPhotos(current => [...current, path]);
    } catch (err) {
      Alert.alert('Error', 'Failed to upload photo');
    }
  };

  const resetForm = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    setItemName('');
    setDescription('');
    setCategory('');
    setCondition('');
    setPurchaseDate(null);
    setPurchasePrice('');
    setCurrentValue('');
    setOwnershipType('individual');
    setLocation('');
    setNotes('');
    setPhotos([]);
  };

  const resetMaintenanceForm = () => {
    setMaintenanceType('');
    setMaintenanceDescription('');
    setMaintenanceCost('');
    setMaintenanceDate(new Date());
    setNextMaintenanceDate(null);
  };

  const categories = [
    'Furniture',
    'Electronics',
    'Appliances',
    'Kitchen',
    'Decor',
    'Tools',
    'Other',
  ];

  const conditions = ['New', 'Like New', 'Good', 'Fair', 'Poor', 'Needs Repair'];

  if (loading) {
    return (
      <View style={styles.container}>
        <Text>Loading inventory...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button onPress={loadInventory} title="Retry" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Inventory Management</Text>
        <Button title="Add Item" onPress={() => setShowAddModal(true)} />
      </View>

      <ScrollView style={styles.itemList}>
        {items.map(item => (
          <PropertyCard key={item.id} style={styles.itemCard}>
            <TouchableOpacity style={styles.itemHeader} onPress={() => setSelectedItem(item.id)}>
              <View style={styles.itemInfo}>
                <MaterialIcons
                  name={item.category === 'Electronics' ? 'devices' : 'inventory'}
                  size={24}
                  color="#4B5563"
                />
                <View style={styles.itemContent}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={styles.itemCategory}>{item.category}</Text>
                </View>
              </View>
              <Text style={styles.itemCondition}>{item.condition_status}</Text>
            </TouchableOpacity>

            {selectedItem === item.id && (
              <View style={styles.itemDetails}>
                {item.description && <Text style={styles.description}>{item.description}</Text>}

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Owner:</Text>
                  {item.ownership_type === 'shared' ? (
                    <Text style={styles.detailValue}>Shared</Text>
                  ) : (
                    <View style={styles.ownerInfo}>
                      <Avatar
                        size="small"
                        url={item.owner?.avatar_url}
                        name={item.owner?.full_name}
                      />
                      <Text style={styles.userName}>{item.owner?.full_name}</Text>
                    </View>
                  )}
                </View>

                {item.purchase_date && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Purchased:</Text>
                    <Text style={styles.detailValue}>
                      {new Date(item.purchase_date).toLocaleDateString()}
                    </Text>
                  </View>
                )}

                {item.current_value && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Current Value:</Text>
                    <Text style={styles.detailValue}>${item.current_value.toFixed(2)}</Text>
                  </View>
                )}

                {item.location && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Location:</Text>
                    <Text style={styles.detailValue}>{item.location}</Text>
                  </View>
                )}

                <View style={styles.actionButtons}>
                  <Button title="Add Maintenance" onPress={() => setShowMaintenanceModal(true)} />
                  <Button
                    title="Edit"
                    type="secondary"
                    onPress={() => {
                      // Handle edit
                    }}
                  />
                </View>
              </View>
            )}
          </PropertyCard>
        ))}
      </ScrollView>

      <Modal
        visible={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          resetForm();
        }}
        title="Add New Item"
      >
        <View style={styles.form}>
          <TextInput
            label="Item Name"
            value={itemName}
            onChangeText={setItemName}
            placeholder="Enter item name"
          />
          <TextInput
            label="Description"
            value={description}
            onChangeText={setDescription}
            placeholder="Enter description (optional)"
            multiline
          />

          <View style={styles.categorySelect}>
            <Text style={styles.label}>Category</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {categories.map(cat => (
                <TouchableOpacity
                  key={cat}
                  style={[styles.categoryButton, category === cat && styles.categoryButtonSelected]}
                  onPress={() => setCategory(cat)}
                >
                  <Text
                    style={[styles.categoryText, category === cat && styles.categoryTextSelected]}
                  >
                    {cat}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.conditionSelect}>
            <Text style={styles.label}>Condition</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {conditions.map(cond => (
                <TouchableOpacity
                  key={cond}
                  style={[
                    styles.conditionButton,
                    condition === cond && styles.conditionButtonSelected,
                  ]}
                  onPress={() => setCondition(cond)}
                >
                  <Text
                    style={[
                      styles.conditionText,
                      condition === cond && styles.conditionTextSelected,
                    ]}
                  >
                    {cond}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <DatePicker label="Purchase Date" value={purchaseDate} onChange={setPurchaseDate} />

          <CurrencyInput
            label="Purchase Price"
            value={purchasePrice}
            onChangeText={setPurchasePrice}
            placeholder="0.00"
          />

          <CurrencyInput
            label="Current Value"
            value={currentValue}
            onChangeText={setCurrentValue}
            placeholder="0.00"
          />

          <View style={styles.ownershipSelect}>
            <Text style={styles.label}>Ownership</Text>
            <View style={styles.ownershipButtons}>
              <TouchableOpacity
                style={[
                  styles.ownershipButton,
                  ownershipType === 'individual' && styles.ownershipButtonSelected,
                ]}
                onPress={() => setOwnershipType('individual')}
              >
                <Text
                  style={[
                    styles.ownershipText,
                    ownershipType === 'individual' && styles.ownershipTextSelected,
                  ]}
                >
                  Individual
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.ownershipButton,
                  ownershipType === 'shared' && styles.ownershipButtonSelected,
                ]}
                onPress={() => setOwnershipType('shared')}
              >
                <Text
                  style={[
                    styles.ownershipText,
                    ownershipType === 'shared' && styles.ownershipTextSelected,
                  ]}
                >
                  Shared
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <TextInput
            label="Location"
            value={location}
            onChangeText={setLocation}
            placeholder="Where is this item located?"
          />

          <TextInput
            label="Notes"
            value={notes}
            onChangeText={setNotes}
            placeholder="Additional notes (optional)"
            multiline
          />

          <ImagePicker label="Photos" onImageSelected={handlePhotoUpload} multiple />

          <Button title="Add Item" onPress={handleAddItem} />
        </View>
      </Modal>

      <Modal
        visible={showMaintenanceModal}
        onClose={() => {
          setShowMaintenanceModal(false);
          resetMaintenanceForm();
        }}
        title="Add Maintenance Record"
      >
        <View style={styles.form}>
          <TextInput
            label="Maintenance Type"
            value={maintenanceType}
            onChangeText={setMaintenanceType}
            placeholder="e.g., Repair, Cleaning, Inspection"
          />
          <TextInput
            label="Description"
            value={maintenanceDescription}
            onChangeText={setMaintenanceDescription}
            placeholder="Describe the maintenance performed"
            multiline
          />
          <CurrencyInput
            label="Cost"
            value={maintenanceCost}
            onChangeText={setMaintenanceCost}
            placeholder="0.00"
          />
          <DatePicker
            label="Maintenance Date"
            value={maintenanceDate}
            onChange={setMaintenanceDate}
          />
          <DatePicker
            label="Next Maintenance Due"
            value={nextMaintenanceDate}
            onChange={setNextMaintenanceDate}
          />
          <Button title="Add Record" onPress={handleAddMaintenance} />
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  itemList: {
    flex: 1,
  },
  itemCard: {
    margin: 16,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContent: {
    marginLeft: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemCategory: {
    fontSize: 14,
    color: '#6B7280',
  },
  itemCondition: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4B5563',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 12,
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 100,
  },
  detailValue: {
    fontSize: 14,
    color: '#4B5563',
  },
  ownerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    marginLeft: 8,
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  form: {
    gap: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  categorySelect: {
    marginBottom: 16,
  },
  categoryButton: {
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  categoryButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  categoryText: {
    color: '#4B5563',
  },
  categoryTextSelected: {
    color: theme.colors.background,
  },
  conditionSelect: {
    marginBottom: 16,
  },
  conditionButton: {
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  conditionButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  conditionText: {
    color: '#4B5563',
  },
  conditionTextSelected: {
    color: theme.colors.background,
  },
  ownershipSelect: {
    marginBottom: 16,
  },
  ownershipButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  ownershipButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
  },
  ownershipButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  ownershipText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  ownershipTextSelected: {
    color: theme.colors.background,
  },
  errorText: {
    color: theme.colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
});

export default InventoryManager;
