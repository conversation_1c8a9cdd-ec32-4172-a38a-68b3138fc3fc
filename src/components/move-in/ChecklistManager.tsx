import React, { useEffect, useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import {
  MoveInChecklistService,
  ChecklistItem,
  MoveInChecklist,
} from '@services/MoveInChecklistService';
import { useAuth } from '@hooks/useAuth';
import { Avatar } from '@components/common/Avatar';
import { Card } from '@components/common/Card';
import { Button } from '@components/common/Button';
import { TextInput } from '@components/common/TextInput';
;

interface ChecklistManagerProps {
  agreementId: string;
  onComplete?: () => void;
}

export const ChecklistManager: React.FC<ChecklistManagerProps> = ({
}) => {
  const theme = useTheme();
  const [checklist, setChecklist] = useState<MoveInChecklist | null>(null);
  const [items, setItems] = useState<ChecklistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [comment, setComment] = useState('');

  const checklistService = new MoveInChecklistService();
  const { authState } = useAuth();
  const user = authState?.user;

  useEffect(() => {
    loadChecklist();
  }, [agreementId]);

  const loadChecklist = async () => {
    try {
      setLoading(true);
      const existingChecklist = await checklistService.getChecklist(agreementId);

      if (existingChecklist) {
        setChecklist(existingChecklist);
        setItems(existingChecklist.checklist_items);
      } else {
        // Create new checklist from default template
        const { data: defaultTemplate } = await supabase
          .from('checklist_templates')
          .select()
          .eq('is_default', true)
          .single();

        if (defaultTemplate) {
          const newChecklist = await checklistService.createMoveInChecklist(
            agreementId,
            defaultTemplate.id
          );
          setChecklist(newChecklist);
          setItems(newChecklist.checklist_items);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load checklist');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (checklist) {
      const subscription = checklistService.subscribeToChecklist(checklist.id, payload => {
        if (payload.new) {
          setItems(current =>
            current.map(item => (item.id === payload.new.id ? payload.new : item))
          );
        }
      });

      return () => {
        subscription.then(sub => sub.unsubscribe());
      };
    }
  }, [checklist]);

  const handleStatusChange = async (itemId: string, newStatus: ChecklistItem['status']) => {
    try {
      await checklistService.updateItem(itemId, {
        status: newStatus,
        completedBy: newStatus === 'completed' ? user?.id : undefined,
        completedAt: newStatus === 'completed' ? new Date() : undefined,
      });
    } catch (err) {
      Alert.alert('Error', 'Failed to update item status');
    }
  };

  const handleAssign = async (itemId: string, userId: string) => {
    try {
      await checklistService.updateItem(itemId, { assignedTo: userId });
    } catch (err) {
      Alert.alert('Error', 'Failed to assign item');
    }
  };

  const handleAddComment = async () => {
    if (!selectedItem || !comment.trim()) return;

    try {
      await checklistService.addComment(selectedItem, user?.id!, comment.trim());
      setComment('');
      loadChecklist(); // Reload to get new comments
    } catch (err) {
      Alert.alert('Error', 'Failed to add comment');
    }
  };

  const getStatusColor = (status: ChecklistItem['status']) => {
    const colors = {
      pending: theme.colors.warning,
      in_progress: theme.colors.primary,
      completed: theme.colors.success,
      blocked: theme.colors.error,
    };
    return colors[status];
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text>Loading checklist...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button onPress={loadChecklist} title="Retry" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Move-in Checklist</Text>
        <Text style={styles.status}>Status: {checklist?.status.toUpperCase()}</Text>
      </View>

      <ScrollView style={styles.itemList}>
        {items.map(item => (
          <PropertyCard key={item.id} style={styles.itemCard}>
            <TouchableOpacity style={styles.itemHeader} onPress={() => setSelectedItem(item.id)}>
              <MaterialIcons
                name={item.status === 'completed' ? 'check-circle' : 'radio-button-unchecked'}
                size={24}
                color={getStatusColor(item.status)}
                onPress={() =>
                  handleStatusChange(item.id, item.status === 'completed' ? 'pending' : 'completed')
                }
              />
              <View style={styles.itemContent}>
                <Text style={styles.itemTitle}>{item.title}</Text>
                {item.description && <Text style={styles.itemDescription}>{item.description}</Text>}
                {item.assignedTo && (
                  <View style={styles.assignee}>
                    <Avatar
                      size="small"
                      url={item.assigned_to?.avatar_url}
                      name={item.assigned_to?.full_name}
                    />
                    <Text style={styles.assigneeName}>{item.assigned_to?.full_name}</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>

            {selectedItem === item.id && (
              <View style={styles.itemDetails}>
                <View style={styles.commentSection}>
                  {item.checklist_comments?.map(comment => (
                    <View key={comment.id} style={styles.comment}>
                      <Avatar
                        size="small"
                        url={comment.user?.avatar_url}
                        name={comment.user?.full_name}
                      />
                      <View style={styles.commentContent}>
                        <Text style={styles.commentAuthor}>{comment.user?.full_name}</Text>
                        <Text style={styles.commentText}>{comment.content}</Text>
                      </View>
                    </View>
                  ))}
                </View>

                <View style={styles.addComment}>
                  <TextInput
                    value={comment}
                    onChangeText={setComment}
                    placeholder="Add a comment..."
                    multiline
                  />
                  <Button title="Send" onPress={handleAddComment} disabled={!comment.trim()} />
                </View>
              </View>
            )}
          </PropertyCard>
        ))}
      </ScrollView>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  status: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  itemList: {
    flex: 1,
  },
  itemCard: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
  },
  itemContent: {
    flex: 1,
    marginLeft: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  assignee: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  assigneeName: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4B5563',
  },
  itemDetails: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 12,
  },
  commentSection: {
    marginBottom: 12,
  },
  comment: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  commentContent: {
    flex: 1,
    marginLeft: 8,
    backgroundColor: '#f3f4f6',
    padding: 8,
    borderRadius: 8,
  },
  commentAuthor: {
    fontWeight: '500',
    fontSize: 14,
  },
  commentText: {
    fontSize: 14,
    marginTop: 4,
  },
  addComment: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  errorText: {
    color: theme.colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
});

export default ChecklistManager;
