import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import {
  SharedExpenseService,
  SharedExpense,
  ExpenseCategory,
  ExpenseShare,
} from '@services/SharedExpenseService';
import { useAuth } from '@hooks/useAuth';
import { Card } from '@components/common/Card';
import { Button } from '@components/common/Button';
import { TextInput } from '@components/common/TextInput';
import { Avatar } from '@components/common/Avatar';
import { Modal } from '@components/common/Modal';
import { CurrencyInput } from '@components/common/CurrencyInput';
import { ImagePicker } from '@components/common/ImagePicker';
import { useColorFix } from '@hooks/useColorFix';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ExpenseTrackerProps {
  agreementId: string;
  onExpenseCreated?: () => void;
}

export const ExpenseTracker: React.FC<ExpenseTrackerProps> = ({
  agreementId,
  onExpenseCreated,
}) => {
  const [expenses, setExpenses] = useState<SharedExpense[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [selectedExpense, setSelectedExpense] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [categoryId, setCategoryId] = useState<string | null>(null);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);
  const [shares, setShares] = useState<{ userId: string; amount: number }[]>([]);

  const expenseService = new SharedExpenseService();
  const { authState } = useAuth();
  const user = authState?.user;

  useEffect(() => {
    loadData();
  }, [agreementId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [expensesData, categoriesData] = await Promise.all([
        expenseService.getExpensesForAgreement(agreementId),
        expenseService.getCategories(),
      ]);
      setExpenses(expensesData);
      setCategories(categoriesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load expenses');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedExpense) {
      const subscription = expenseService.subscribeToExpenseUpdates(
        selectedExpense,
        (payload) => {
          if (payload.new) {
            setExpenses((current) =>
              current.map((exp) =>
                exp.id === payload.new.id ? payload.new : exp
              )
            );
          }
        }
      );

      return () => {
        subscription.then((sub) => sub.unsubscribe());
      };
    }
  }, [selectedExpense]);

  const handleAddExpense = async () => {
    if (!title || !amount || !categoryId) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const totalAmount = parseFloat(amount);
      const shareAmount = totalAmount / (shares.length + 1);

      const newExpense = await expenseService.createExpense(
        {
          agreementId,
          categoryId,
          title,
          description,
          totalAmount,
          currency: 'USD',
          paidBy: user?.id!,
          receiptUrl,
        },
        shares.map((share) => ({
          userId: share.userId,
          amount: shareAmount,
        }))
      );

      setExpenses((current) => [newExpense, ...current]);
      setShowAddModal(false);
      resetForm();
      if (onExpenseCreated) onExpenseCreated();
    } catch (err) {
      Alert.alert('Error', 'Failed to create expense');
    }
  };

  const handlePayShare = async (shareId: string) => {
    try {
      // In a real app, integrate with payment provider here
      const paymentId = 'dummy-payment-id';
      await expenseService.markShareAsPaid(shareId, paymentId);
      loadData();
    } catch (err) {
      Alert.alert('Error', 'Failed to process payment');
    }
  };

  const handleDispute = async (shareId: string) => {
    Alert.prompt(
      'Dispute Expense',
      'Please provide a reason for the dispute:',
      async (reason) => {
        if (reason) {
          try {
            await expenseService.disputeShare(shareId, reason);
            loadData();
          } catch (err) {
            Alert.alert('Error', 'Failed to dispute expense');
          }
        }
      }
    );
  };

  const resetForm = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    setTitle('');
    setDescription('');
    setAmount('');
    setCategoryId(null);
    setReceiptUrl(null);
    setShares([]);
  };

  const getStatusColor = (status: SharedExpense['status']) => {
    const colors = {
      pending: theme.colors.warning,
      paid: theme.colors.success,
      partially_paid: theme.colors.primary,
      disputed: theme.colors.error,
    };
    return colors[status];
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text>Loading expenses...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button onPress={loadData} title="Retry" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Shared Expenses</Text>
        <Button
          title="Add Expense"
          onPress={() => setShowAddModal(true)}
        />
      </View>

      <ScrollView style={styles.expenseList}>
        {expenses.map((expense) => (
          <PropertyCard key={expense.id} style={styles.expenseCard}>
            <TouchableOpacity
              style={styles.expenseHeader}
              onPress={() => setSelectedExpense(expense.id)}
            >
              <View style={styles.expenseInfo}>
                <MaterialIcons
                  name={expense.category?.icon || 'attach-money'}
                  size={24}
                  color={getStatusColor(expense.status)}
                />
                <View style={styles.expenseContent}>
                  <Text style={styles.expenseTitle}>{expense.title}</Text>
                  <Text style={styles.expenseAmount}>
                    {expense.currency} {expense.totalAmount.toFixed(2)}
                  </Text>
                </View>
              </View>
              <Text
                style={[
                  styles.expenseStatus,
                  { color: getStatusColor(expense.status) },
                ]}
              >
                {expense.status.toUpperCase()}
              </Text>
            </TouchableOpacity>

            {selectedExpense === expense.id && (
              <View style={styles.expenseDetails}>
                {expense.description && (
                  <Text style={styles.description}>
                    {expense.description}
                  </Text>
                )}
                <View style={styles.paidBy}>
                  <Text>Paid by: </Text>
                  <Avatar
                    size="small"
                    url={expense.paid_by_user?.avatar_url}
                    name={expense.paid_by_user?.full_name}
                  />
                  <Text style={styles.userName}>
                    {expense.paid_by_user?.full_name}
                  </Text>
                </View>

                <View style={styles.shares}>
                  <Text style={styles.sharesTitle}>Shares</Text>
                  {expense.expense_shares?.map((share: ExpenseShare) => (
                    <View key={share.id} style={styles.share}>
                      <View style={styles.shareUser}>
                        <Avatar
                          size="small"
                          url={share.user?.avatar_url}
                          name={share.user?.full_name}
                        />
                        <Text style={styles.userName}>
                          {share.user?.full_name}
                        </Text>
                      </View>
                      <Text style={styles.shareAmount}>
                        {expense.currency} {share.amount.toFixed(2)}
                      </Text>
                      {share.status === 'pending' && (
                        <View style={styles.shareActions}>
                          <Button
                            title="Pay"
                            onPress={() => handlePayShare(share.id)}
                          />
                          <Button
                            title="Dispute"
                            type="secondary"
                            onPress={() => handleDispute(share.id)}
                          />
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </View>
            )}
          </PropertyCard>
        ))}
      </ScrollView>

      <Modal
        visible={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          resetForm();
        }}
        title="Add New Expense"
      >
        <View style={styles.form}>
          <TextInput
            label="Title"
            value={title}
            onChangeText={setTitle}
            placeholder="Enter expense title"
          />
          <TextInput
            label="Description"
            value={description}
            onChangeText={setDescription}
            placeholder="Enter description (optional)"
            multiline
          />
          <CurrencyInput
            label="Amount"
            value={amount}
            onChangeText={setAmount}
            currency="USD"
          />
          <View style={styles.categorySelect}>
            <Text style={styles.label}>Category</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    categoryId === category.id && styles.categoryButtonSelected,
                  ]}
                  onPress={() => setCategoryId(category.id)}
                >
                  <MaterialIcons
                    name={category.icon}
                    size={24}
                    color={
                      categoryId === category.id ? theme.colors.background : '#4B5563'
                    }
                  />
                  <Text
                    style={[
                      styles.categoryText,
                      categoryId === category.id && styles.categoryTextSelected,
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
          <ImagePicker
            label="Receipt"
            onImageSelected={(url) => setReceiptUrl(url)}
          />
          <Button title="Add Expense" onPress={handleAddExpense} />
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  expenseList: {
    flex: 1,
  },
  expenseCard: {
    margin: 16,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  expenseInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expenseContent: {
    marginLeft: 12,
  },
  expenseTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  expenseAmount: {
    fontSize: 14,
    color: '#6B7280',
  },
  expenseStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  expenseDetails: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    padding: 12,
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  paidBy: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  shares: {
    marginTop: 12,
  },
  sharesTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  share: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  shareUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    marginLeft: 8,
    fontSize: 14,
  },
  shareAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  shareActions: {
    flexDirection: 'row',
    gap: 8,
  },
  form: {
    gap: 16,
  },
  categorySelect: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  categoryButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  categoryText: {
    marginLeft: 4,
    color: '#4B5563',
  },
  categoryTextSelected: {
    color: theme.colors.background,
  },
  errorText: {
    color: theme.colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
});
export default ExpenseTracker;
