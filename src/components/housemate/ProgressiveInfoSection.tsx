import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ProgressiveInfoSectionProps {
  title: string;
  initiallyExpanded?: boolean;
  importance?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

/**
 * ProgressiveInfoSection component
 * Implements progressive disclosure pattern for housemate profile sections
 * Allows users to expand/collapse sections based on their interest
 */
const ProgressiveInfoSection: React.FC<ProgressiveInfoSectionProps> = ({
  title,
  initiallyExpanded = false,
  importance = 'medium',
  children,
  accessibilityLabel,
  accessibilityHint
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const [contentHeight, setContentHeight] = useState(0);
  const animatedHeight = React.useRef(new Animated.Value(initiallyExpanded ? 1 : 0)).current;

  const toggleExpanded = () => {
    const newExpandedState = !expanded;
    setExpanded(newExpandedState);
    
    Animated.timing(animatedHeight, {
      toValue: newExpandedState ? 1 : 0,
      duration: 300,
      useNativeDriver: false
    }).start();
  };

  // Determine styling based on importance using theme colors
  const getImportanceStyles = () => {
    switch (importance) {
      case 'high':
        return {
          headerBackground: colorWithOpacity(theme.colors.primary, 0.08),
          titleColor: theme.colors.primary,
          borderColor: colorWithOpacity(theme.colors.primary, 0.2)
        };
      case 'medium':
        return {
          headerBackground: theme.colors.surface,
          titleColor: theme.colors.text,
          borderColor: theme.colors.border
        };
      case 'low':
        return {
          headerBackground: theme.colors.background,
          titleColor: theme.colors.textSecondary,
          borderColor: colorWithOpacity(theme.colors.border, 0.5)
        };
      default:
        return {
          headerBackground: theme.colors.surface,
          titleColor: theme.colors.text,
          borderColor: theme.colors.border
        };
    }
  };

  const importanceStyles = getImportanceStyles();
  
  // Calculate the animated height for smooth expansion/collapse
  const height = animatedHeight.interpolate({
    inputRange: [0, 1],
    outputRange: [0, contentHeight]
  });

  return (
    <View style={[
      styles.container, 
      { borderColor: importanceStyles.borderColor }
    ]}>
      <TouchableOpacity 
        style={[
          styles.header, 
          { backgroundColor: importanceStyles.headerBackground }
        ]}
        onPress={toggleExpanded}
        accessible={true}
        accessibilityRole="button"
        accessibilityState={{ expanded }}
        accessibilityLabel={accessibilityLabel || `${title}. ${expanded ? 'Collapse' : 'Expand'} section.`}
        accessibilityHint={accessibilityHint || `Double tap to ${expanded ? 'collapse' : 'expand'} this information section`}
      >
        <Text style={[styles.title, { color: importanceStyles.titleColor }]}>
          {title}
        </Text>
        {expanded ? (
          <ChevronUp size={20} color={importanceStyles.titleColor} />
        ) : (
          <ChevronDown size={20} color={importanceStyles.titleColor} />
        )}
      </TouchableOpacity>
      
      <Animated.View 
        style={[
          styles.content,
          { height: expanded ? 'auto' : height, overflow: expanded ? 'visible' : 'hidden' }
        ]}
        onLayout={(event) => {
          if (!initiallyExpanded && contentHeight === 0) {
            setContentHeight(event.nativeEvent.layout.height);
          }
        }}
      >
        {children}
      </Animated.View>
    </View>
  );
};

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    padding: 16,
    paddingTop: 0,
    backgroundColor: theme.colors.background,
  }
});

export default ProgressiveInfoSection;
