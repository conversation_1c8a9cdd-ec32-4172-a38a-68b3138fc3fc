import React, { useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { Zap } from 'lucide-react-native';
;
import { profileBoostService } from '@services/profileBoostService';
import { unifiedPaymentService } from '@services';

interface BoostButtonProps {
  userId: string;
  hasActiveBoost: boolean;
  onBoostCreated?: () => void;
}

export default function BoostButton({
 userId, hasActiveBoost, onBoostCreated }: BoostButtonProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(false);
  const [hasPremium, setHasPremium] = useState(false);
  
  // Check premium status when component mounts
  React.useEffect(() => {
    checkPremiumStatus();
  }, []);
  
  const checkPremiumStatus = async () => {
    try {
      const isPremium = await unifiedPaymentService.hasActivePremium(userId);
      setHasPremium(isPremium);
    } catch (error) {
      console.error('Error checking premium status:', error);
    }
  };
  
  const handleBoostPress = async () => {
    if (!userId) return;
    
    // If not premium, show upgrade prompt
    if (!hasPremium) {
      Alert.alert(
        'Premium Feature',
        'Boosting your profile requires a premium subscription. Upgrade now?',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'View Plans', onPress: () => {/* Navigate to subscription plans */} }
        ]
      );
      return;
    }
    
    // If already boosted, show info
    if (hasActiveBoost) {
      Alert.alert(
        'Profile Already Boosted',
        'Your profile is already boosted. You will appear higher in potential matches for others.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }
    
    // Confirm boost
    Alert.alert(
      'Boost Your Profile',
      'Boosting your profile will make you appear higher in potential matches for 24 hours. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Boost Now', onPress: createBoost }
      ]
    );
  };
  
  const createBoost = async () => {
    try {
      setLoading(true);
      const boost = await profileBoostService.createBoost(userId);
      
      if (boost) {
        Alert.alert(
          'Profile Boosted!',
          'Your profile has been boosted for 24 hours. You will appear higher in potential matches for others.',
          [{ text: 'Great!', style: 'default' }]
        );
        
        // Call the callback if provided
        if (onBoostCreated) {
          onBoostCreated();
        }
      } else {
        Alert.alert(
          'Boost Failed',
          'Could not boost your profile. You may have reached the maximum number of active boosts.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    } catch (error) {
      console.error('Error boosting profile:', error);
      Alert.alert(
        'Error',
        'An error occurred while boosting your profile. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.boostButton,
        hasActiveBoost && styles.activatedBoost,
        !hasPremium && styles.disabledBoost
      ]}
      onPress={handleBoostPress}
      disabled={loading}
    >
      {loading ? (
        <ActivityIndicator size="small" color={theme.colors.white} />
      ) : (
        <>
          <Zap 
            size={20} 
            color={hasActiveBoost || hasPremium ? theme.colors.white : theme.colors.gray} 
            fill={hasActiveBoost ? theme.colors.white : 'transparent'}
          />
          <Text style={[
            styles.boostText,
            (!hasPremium && !hasActiveBoost) && styles.disabledText
          ]}>
            {hasActiveBoost ? 'Boosted' : 'Boost Profile'}
          </Text>
          
          {!hasPremium && !hasActiveBoost && (
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumText}>PREMIUM</Text>
            </View>
          )}
        </>
      )}
    </TouchableOpacity>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  boostButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.blue,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 30,
    shadowColor: theme.colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activatedBoost: {
    backgroundColor: theme.colors.amber,
  },
  disabledBoost: {
    backgroundColor: theme.colors.gray,
  },
  boostText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.white,
  },
  disabledText: {
    color: theme.colors.gray,
  },
  premiumBadge: {
    backgroundColor: theme.colors.amber,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  premiumText: {
    color: theme.colors.gray,
    fontSize: 10,
    fontWeight: '700',
  },
});