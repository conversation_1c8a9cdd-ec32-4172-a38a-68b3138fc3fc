/**
 * Enhanced Match Celebration Modal
 * 
 * An improved version of the match celebration modal that integrates with the
 * MatchNotificationService to provide a seamless transition from matching to messaging.
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  ActivityIndicator,
  Dimensions,
  Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  Heart,
  MessageSquare,
  User,
  ChevronRight,
  Send,
  ArrowLeft,
  X,
  Sparkles,
} from 'lucide-react-native';
import { Button } from '@design-system';

import { conversationStarterService } from '@services/conversationStarterService';
import { ConversationStarter } from '@services/conversationStarterService';
import CompatibilityScore from '@components/matching/CompatibilityScore';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { matchNotificationService, MatchData } from '@services/MatchNotificationService';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { logger } from '@services/loggerService';
import { hapticFeedback } from '@utils/hapticFeedback';

interface EnhancedMatchCelebrationModalProps {
  visible: boolean;
  onClose: () => void;
  matchData?: MatchData;
  onStartMessaging?: (matchId: string, initialMessage?: string) => void;
  onViewProfile?: (userId: string) => void;
}

enum ModalState {
  CELEBRATION = 'celebration',
  MESSAGE_STARTERS = 'message_starters',
  SENDING = 'sending',
  SUCCESS = 'success',
}

export function EnhancedMatchCelebrationModal({
  visible,
  onClose,
  matchData,
  onStartMessaging,
  onViewProfile,
}: EnhancedMatchCelebrationModalProps) {
  const router = useRouter();
  const { user } = useSupabaseUser();
  const theme = useTheme();
  const styles = createStyles(theme);
  const [modalState, setModalState] = useState<ModalState>(ModalState.CELEBRATION);
  const [conversationStarters, setConversationStarters] = useState<Array<ConversationStarter>>([]);
  const [selectedStarterId, setSelectedStarterId] = useState<string | null>(null);
  const [customMessage, setCustomMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);

  // Animation values
  const scale = useRef(new Animated.Value(0.8)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const confettiOpacity = useRef(new Animated.Value(0)).current;
  const sparkleRotation = useRef(new Animated.Value(0)).current;

  // Reset state when modal becomes visible
  useEffect(() => {
    if (visible) {
      setModalState(ModalState.CELEBRATION);
      setCustomMessage('');
      setSelectedStarterId(null);
      setSendingMessage(false);

      // Start animations
      scale.setValue(0.8);
      opacity.setValue(0);
      confettiOpacity.setValue(0);
      sparkleRotation.setValue(0);

      Animated.parallel([
        Animated.timing(scale, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(confettiOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.loop(
          Animated.timing(sparkleRotation, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          })
        ),
      ]).start();

      // Trigger haptic feedback for celebration
      hapticFeedback.success();

      // Mark match as viewed if we have match data
      if (matchData) {
        matchNotificationService.markMatchViewed(matchData.matchId);
      }
    }
  }, [visible, matchData]);

  // Load conversation starters when entering message starters state
  useEffect(() => {
    if (modalState === ModalState.MESSAGE_STARTERS && matchData) {
      loadConversationStarters();
    }
  }, [modalState, matchData]);

  const loadConversationStarters = useCallback(async () => {
    if (!user?.id || !matchData) return;

    try {
      setLoading(true);
      const starters = await conversationStarterService.getStartersForMatch(
        user.id,
        matchData.matchedUserId
      );

      setConversationStarters(starters);
    } catch (err) {
      logger.warn('Failed to load conversation starters', 'EnhancedMatchCelebrationModal', {
        error: err instanceof Error ? err.message : String(err),
      });

      // Fallback starters
      setConversationStarters([
        { id: 'starter-1', text: `Hi ${matchData.matchedUserName}! We matched! 👋`, category: 'general' },
        { id: 'starter-2', text: 'What are you looking for in a roommate?', category: 'general' },
        { id: 'starter-3', text: 'I like your profile! When are you looking to move?', category: 'general' },
        { id: 'starter-4', text: 'What part of town are you interested in?', category: 'location' },
        { id: 'starter-5', text: "What's your budget range for rent?", category: 'general' },
      ]);
    } finally {
      setLoading(false);
    }
  }, [user?.id, matchData]);

  const handleStartMessaging = useCallback(() => {
    setModalState(ModalState.MESSAGE_STARTERS);
  }, []);

  const handleViewProfile = useCallback(() => {
    if (matchData && onViewProfile) {
      onViewProfile(matchData.matchedUserId);
      onClose();
    } else if (matchData) {
      // Navigate to profile page
      router.push('/(tabs)/profile');
      onClose();
    }
  }, [matchData, onViewProfile, router, onClose]);

  const handleSelectStarter = useCallback((starterId: string) => {
    setSelectedStarterId(starterId === selectedStarterId ? null : starterId);
  }, [selectedStarterId]);

  const getSelectedStarterText = useCallback(() => {
    if (!selectedStarterId) return '';
    const starter = conversationStarters.find(s => s.id === selectedStarterId);
    return starter ? starter.text : '';
  }, [selectedStarterId, conversationStarters]);

  const handleSendMessage = useCallback(async () => {
    if (!matchData || (!customMessage.trim() && !selectedStarterId)) return;

    try {
      setSendingMessage(true);
      setModalState(ModalState.SENDING);

      const messageToSend = selectedStarterId ? getSelectedStarterText() : customMessage.trim();

      if (onStartMessaging) {
        onStartMessaging(matchData.matchId, messageToSend);
      } else {
        // Use the match notification service to initiate chat
        const success = await matchNotificationService.initiateMatchChat(
          matchData.matchId,
          messageToSend
        );

        if (success) {
          // Show success state briefly before navigating
          setModalState(ModalState.SUCCESS);
          
          // Trigger haptic feedback for success
          hapticFeedback.success();
          
          // Navigate after a short delay
          setTimeout(() => {
            matchNotificationService.navigateToMatchChat(matchData.matchId, messageToSend);
            onClose();
          }, 1500);
          return;
        } else {
          throw new Error('Failed to initiate chat');
        }
      }

      // Close modal after sending
      onClose();
    } catch (error) {
      logger.error('Error sending initial message', 'EnhancedMatchCelebrationModal', {
        error: error instanceof Error ? error.message : String(error),
      });
      // Return to message starters state on error
      setModalState(ModalState.MESSAGE_STARTERS);
    } finally {
      setSendingMessage(false);
    }
  }, [matchData, customMessage, selectedStarterId, getSelectedStarterText, onStartMessaging, onClose]);

  // Animation styles
  const animatedCardStyle = {
    transform: [{ scale }],
    opacity,
  };

  const sparkleAnimatedStyle = {
    transform: [
      {
        rotate: sparkleRotation.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
  };

  // Render content based on current modal state
  const renderContent = () => {
    if (!matchData) return null;

    switch (modalState) {
      case ModalState.CELEBRATION:
        return (
          <View style={styles.celebrationContainer}>
            <Animated.View style={[styles.confettiContainer, { opacity: confettiOpacity }]}>
              <Animated.View style={[styles.sparkleIcon, sparkleAnimatedStyle]}>
                <Sparkles size={32} color={theme.colors.primary} />
              </Animated.View>
            </Animated.View>

            <View style={styles.avatarsContainer}>
              {user?.id && (
                <View style={[styles.avatarContainer, { zIndex: 1 }]}>
                  {user.user_metadata?.avatar_url ? (
                    <Image
                      source={{ uri: user.user_metadata.avatar_url }}
                      style={styles.avatar}
                    />
                  ) : (
                    <View style={[styles.avatar, styles.avatarPlaceholder]}>
                      <Text style={styles.avatarText}>
                        {user.user_metadata?.full_name?.[0] || user.email?.[0] || '?'}
                      </Text>
                    </View>
                  )}
                </View>
              )}

              <View style={[styles.heartContainer]}>
                <Heart size={40} color={theme.colors.primary} fill={theme.colors.primary} />
              </View>

              <View style={[styles.avatarContainer, { zIndex: 1 }]}>
                {matchData.matchedUserAvatar ? (
                  <Image
                    source={{ uri: matchData.matchedUserAvatar }}
                    style={styles.avatar}
                  />
                ) : (
                  <View style={[styles.avatar, styles.avatarPlaceholder]}>
                    <Text style={styles.avatarText}>
                      {matchData.matchedUserName[0] || '?'}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            <Text style={styles.matchText}>It's a Match!</Text>
            
            <Text style={styles.matchSubtext}>
              You and {matchData.matchedUserName} have liked each other.
              {matchData.compatibility ? (
                <Text style={styles.scoreText}>
                  {' '}
                  You have a{' '}
                  <CompatibilityScore score={matchData.compatibility} />{' '}
                  compatibility score!
                </Text>
              ) : null}
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleStartMessaging}
              >
                <MessageSquare
                  size={20}
                  color={theme.colors.surface}
                />
                <Text style={styles.primaryButtonText}>Send Message</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={handleViewProfile}
              >
                <User
                  size={20}
                  color={theme.colors.primary}
                  style={styles.buttonIcon}
                />
                <Text style={styles.secondaryButtonText}>View Profile</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.secondaryButton} onPress={onClose}>
                <Text style={styles.secondaryButtonText}>Keep Browsing</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case ModalState.MESSAGE_STARTERS:
        return (
          <View style={styles.messageStartersContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setModalState(ModalState.CELEBRATION)}
            >
              <ArrowLeft size={20} color={theme.colors.primary} />
            </TouchableOpacity>

            <Text style={styles.messageStartersTitle}>
              Start a conversation with {matchData.matchedUserName}
            </Text>

            {loading ? (
              <ActivityIndicator
                size="large"
                color={theme.colors.primary}
                style={styles.loader}
              />
            ) : (
              <>
                <FlatList
                  data={conversationStarters}
                  keyExtractor={(item) => item.id}
                  style={styles.startersList}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[
                        styles.starterItem,
                        selectedStarterId === item.id && styles.selectedStarter,
                      ]}
                      onPress={() => handleSelectStarter(item.id)}
                    >
                      <Text
                        style={[
                          styles.starterText,
                          selectedStarterId === item.id && styles.selectedStarterText,
                        ]}
                      >
                        {item.text}
                      </Text>
                    </TouchableOpacity>
                  )}
                />

                <View style={styles.customMessageContainer}>
                  <TextInput
                    style={styles.customMessageInput}
                    placeholder={
                      selectedStarterId
                        ? 'Using selected starter message'
                        : 'Or write your own message...'
                    }
                    placeholderTextColor={theme.colors.textMuted}
                    multiline
                    value={customMessage}
                    onChangeText={setCustomMessage}
                    editable={!selectedStarterId}
                  />

                  <TouchableOpacity
                    style={[
                      styles.sendButton,
                      (!customMessage.trim() && !selectedStarterId) && styles.sendButtonDisabled,
                    ]}
                    onPress={handleSendMessage}
                    disabled={!customMessage.trim() && !selectedStarterId}
                  >
                    <Send size={20} color={theme.colors.surface} />
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        );

      case ModalState.SENDING:
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Sending message...</Text>
          </View>
        );

      case ModalState.SUCCESS:
        return (
          <View style={styles.successContainer}>
            <View style={styles.successIconContainer}>
              <MessageSquare size={40} color={theme.colors.primary} />
            </View>
            <Text style={styles.successTitle}>Message Sent!</Text>
            <Text style={styles.successText}>
              Your message has been sent to {matchData.matchedUserName}.
              Opening your conversation...
            </Text>
          </View>
        );

      default:
        return null;
    }
  };

  if (!visible || !matchData) return null;

  return (
    <Modal transparent visible={visible} animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <Animated.View style={[styles.modalContent, animatedCardStyle]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            disabled={modalState === ModalState.SENDING}
          >
            <X size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          {renderContent()}
        </Animated.View>
      </View>
    </Modal>
  );
}

const { width } = Dimensions.get('window');

const createStyles = (theme: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.overlay,
  },
  modalContent: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: theme.colors.background,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 28,
    alignItems: 'center',
    position: 'relative',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
    padding: 4,
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
  },
  confettiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sparkleIcon: {
    position: 'absolute',
    top: 20,
    right: 30,
  },
  celebrationContainer: {
    alignItems: 'center',
    width: '100%',
  },
  avatarsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    borderWidth: 3,
    borderColor: theme.colors.background,
    borderRadius: 50,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 5,
  },
  heartContainer: {
    marginHorizontal: -10,
    zIndex: 2,
    backgroundColor: theme.colors.background,
    borderRadius: 50,
    padding: 8,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    backgroundColor: theme.colors.primarySurface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primaryVariant,
  },
  matchText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  matchSubtext: {
    fontSize: 16,
    textAlign: 'center',
    color: theme.colors.primary,
    marginBottom: 24,
  },
  scoreText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    width: '100%',
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: theme.colors.surface,
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: theme.colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  messageStartersContainer: {
    width: '100%',
    flex: 1,
  },
  messageStartersTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  startersList: {
    width: '100%',
    maxHeight: 220,
  },
  starterItem: {
    backgroundColor: theme.colors.primarySurface,
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedStarter: {
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.primary,
  },
  starterText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  selectedStarterText: {
    color: theme.colors.text,
    fontWeight: '500',
  },
  customMessageContainer: {
    flexDirection: 'row',
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 12,
    alignItems: 'flex-end',
  },
  customMessageInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  loader: {
    marginVertical: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  successIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.primarySurface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  successText: {
    fontSize: 16,
    textAlign: 'center',
    color: theme.colors.textSecondary,
    lineHeight: 22,
  },
});

export default EnhancedMatchCelebrationModal;
