import React, { useState, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowRight, RefreshCw } from 'lucide-react-native';

;
import { matchingService } from '@services/matchingService';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import EnhancedMatchCard from '@components/matching/EnhancedMatchCard';
import { Button } from '@design-system';

interface EnhancedMatchRecommendationsProps {
  onViewAllMatches?: () => void;
  limit?: number;
  showViewAll?: boolean;
}

const EnhancedMatchRecommendations: React.FC<EnhancedMatchRecommendationsProps> = ({
  onViewAllMatches,
  limit = 3,
  showViewAll = true,
}) => {
  const { user } = useSupabaseUser();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [overallRecommendation, setOverallRecommendation] = useState('');
  const [error, setError] = useState<string | null>(null);

  const fetchRecommendations = async (isRefreshing = false) => {
  const theme = useTheme();
  const styles = createStyles(theme);
    if (!user?.id) return;

    try {
      if (!isRefreshing) setLoading(true);
      setError(null);

      const result = await matchingService.getEnhancedRoommateRecommendations(user.id, limit);
      
      setRecommendations(result.recommendations);
      setOverallRecommendation(result.overallRecommendation);
    } catch (err) {
      console.error('Error fetching enhanced recommendations:', err);
      setError('Unable to load recommendations. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchRecommendations(true);
  };

  useEffect(() => {
    fetchRecommendations();
  }, [user?.id]);

  const handleViewProfile = (userId: string) => {
    router.push(`/profile/view?id=${userId}`);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Finding your best matches...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button variant="outlined" onPress={handleRefresh} style={styles.retryButton}>
          Try Again
        </Button>
      </View>
    );
  }

  if (recommendations.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No recommendations found</Text>
        <Text style={styles.emptySubtext}>
          We couldn't find any matches based on your preferences. Try adjusting your preferences or
          check back later.
        </Text>
        <Button variant="outlined" onPress={handleRefresh} style={styles.retryButton}>
          Refresh
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
      >
        {overallRecommendation ? (
          <View style={styles.recommendationCard}>
            <Text style={styles.recommendationTitle}>AI Recommendation</Text>
            <Text style={styles.recommendationText}>{overallRecommendation}</Text>
          </View>
        ) : null}

        <View style={styles.matchesContainer}>
          {recommendations.map((recommendation, index) => (
            <EnhancedMatchCard
              key={`enhanced-match-${recommendation.profile.id}-${index}`}
              profile={recommendation.profile}
              compatibilityScore={recommendation.compatibilityScore}
              compatibilityInsights={recommendation.compatibilityInsights}
              boosted={recommendation.boosted}
              onViewProfile={() => handleViewProfile(recommendation.profile.id)}
            />
          ))}
        </View>

        {showViewAll && recommendations.length > 0 && (
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={onViewAllMatches}
            activeOpacity={0.7}
          >
            <Text style={styles.viewAllText}>View all matches</Text>
            <ArrowRight size={16} color={theme.colors.primary} />
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.gray,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.gray,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.gray,
    textAlign: 'center',
    marginBottom: 16,
  },
  recommendationCard: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
    color: theme.colors.gray,
  },
  matchesContainer: {
    padding: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
    marginRight: 4,
  },
});

export default EnhancedMatchRecommendations;
