/**
 * EnhancedMatchingFlow - Complete Matching to Agreement UI Component
 * 
 * Provides a comprehensive interface for the entire matching flow:
 * - Match discovery and swiping
 * - Progress tracking
 * - Agreement proposal integration
 * - Flow state management
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system/ThemeProvider';
import { useAuth } from '@context/AuthContext';
import { useToast } from '@hooks/useToast';
import { matchingToAgreementFlow, type MatchCandidate, type FlowProgress } from '@services/flows/MatchingToAgreementFlowService';
import { EnhancedMatchCard } from './EnhancedMatchCard';
import { FlowProgressIndicator } from './FlowProgressIndicator';
import { AgreementProposalModal } from './AgreementProposalModal';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedMatchingFlowProps {
  initialPreferences?: any;
  onFlowComplete?: (agreementId: string) => void;
  onFlowCancel?: () => void;
}

export const EnhancedMatchingFlow: React.FC<EnhancedMatchingFlowProps> = ({
  initialPreferences,
  onFlowComplete,
  onFlowCancel
}) => {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const { showToast } = useToast();
  const styles = createStyles(theme);

  // State management
  const [candidates, setCandidates] = useState<MatchCandidate[]>([]);
  const [currentCandidateIndex, setCurrentCandidateIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [flowProgress, setFlowProgress] = useState<FlowProgress | null>(null);
  const [showAgreementModal, setShowAgreementModal] = useState(false);
  const [mutualMatchData, setMutualMatchData] = useState<{
    userId: string;
    chatRoomId: string;
  } | null>(null);

  // Initialize matching flow
  useEffect(() => {
    if (user?.id) {
      initializeFlow();
    }
  }, [user?.id]);

  const initializeFlow = async () => {
    try {
      setIsLoading(true);
      
      const result = await matchingToAgreementFlow.startMatchingFlow(
        user!.id,
        initialPreferences
      );

      if (result.success) {
        setCandidates(result.candidates);
        showToast(`Found ${result.candidates.length} potential matches!`, 'success');
      } else {
        showToast(result.error || 'Failed to find matches', 'error');
      }
    } catch (error) {
      console.error('Failed to initialize matching flow:', error);
      showToast('Failed to load matches', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle match actions (like/pass)
  const handleMatchAction = async (action: 'like' | 'pass') => {
    if (!user?.id || currentCandidateIndex >= candidates.length) return;

    const currentCandidate = candidates[currentCandidateIndex];
    
    try {
      const result = await matchingToAgreementFlow.handleMatchAction(
        user.id,
        currentCandidate.user_id,
        action
      );

      if (result.success) {
        if (result.is_mutual_match && result.chat_room_id) {
          // Handle mutual match
          setMutualMatchData({
            userId: currentCandidate.user_id,
            chatRoomId: result.chat_room_id
          });
          
          showToast('🎉 It\'s a match! You can now start chatting.', 'success');
          
          // Show options to chat or propose agreement
          Alert.alert(
            'It\'s a Match!',
            `You and ${currentCandidate.profile.first_name} liked each other!`,
            [
              {
                text: 'Start Chatting',
                onPress: () => navigateToChat(result.chat_room_id!)
              },
              {
                text: 'Propose Agreement',
                onPress: () => setShowAgreementModal(true)
              }
            ]
          );
        } else if (action === 'like') {
          showToast('Like sent! Waiting for them to like you back.', 'info');
        }

        // Move to next candidate
        setCurrentCandidateIndex(prev => prev + 1);
      } else {
        showToast(result.error || 'Failed to process match action', 'error');
      }
    } catch (error) {
      console.error('Failed to handle match action:', error);
      showToast('Failed to process match action', 'error');
    }
  };

  // Navigate to chat
  const navigateToChat = (chatRoomId: string) => {
    router.push({
      pathname: '/chat',
      params: {
        roomId: chatRoomId,
        context: 'match'
      }
    });
  };

  // Handle agreement proposal
  const handleAgreementProposal = async (proposalData: {
    template_id: string;
    title: string;
    initial_terms?: any;
  }) => {
    if (!mutualMatchData) return;

    try {
      const result = await matchingToAgreementFlow.proposeAgreement(
        user!.id,
        mutualMatchData.chatRoomId,
        proposalData
      );

      if (result.success) {
        showToast('Agreement proposed successfully!', 'success');
        setShowAgreementModal(false);
        
        // Navigate to agreement details
        router.push({
          pathname: `/agreement/details/${result.agreement_id}`,
          params: {
            source: 'match_proposal'
          }
        });
      } else {
        showToast(result.error || 'Failed to propose agreement', 'error');
      }
    } catch (error) {
      console.error('Failed to propose agreement:', error);
      showToast('Failed to propose agreement', 'error');
    }
  };

  // Get current candidate
  const currentCandidate = candidates[currentCandidateIndex];
  const hasMoreCandidates = currentCandidateIndex < candidates.length;

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Finding your perfect matches...</Text>
      </View>
    );
  }

  if (!hasMoreCandidates) {
    return (
      <View style={styles.noMoreContainer}>
        <Text style={styles.noMoreTitle}>No More Matches</Text>
        <Text style={styles.noMoreText}>
          You've seen all available matches. Check back later for new profiles!
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={initializeFlow}
        >
          <Text style={styles.refreshButtonText}>Refresh Matches</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Flow Progress Indicator */}
      {flowProgress && (
        <FlowProgressIndicator
          progress={flowProgress}
          style={styles.progressIndicator}
        />
      )}

      {/* Match Counter */}
      <View style={styles.counterContainer}>
        <Text style={styles.counterText}>
          {currentCandidateIndex + 1} of {candidates.length}
        </Text>
      </View>

      {/* Current Match Card */}
      <View style={styles.cardContainer}>
        <EnhancedMatchCard
          candidate={currentCandidate}
          onLike={() => handleMatchAction('like')}
          onPass={() => handleMatchAction('pass')}
          onViewProfile={() => {
            // Navigate to profile view
            router.push({
              pathname: `/profile/${currentCandidate.user_id}`,
              params: { source: 'matching' }
            });
          }}
        />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.passButton]}
          onPress={() => handleMatchAction('pass')}
        >
          <Text style={styles.passButtonText}>Pass</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={() => handleMatchAction('like')}
        >
          <Text style={styles.likeButtonText}>Like</Text>
        </TouchableOpacity>
      </View>

      {/* Agreement Proposal Modal */}
      <AgreementProposalModal
        visible={showAgreementModal}
        onClose={() => setShowAgreementModal(false)}
        onPropose={handleAgreementProposal}
        recipientName={mutualMatchData ? 
          candidates.find(c => c.user_id === mutualMatchData.userId)?.profile.first_name || 'User'
          : ''
        }
      />
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  noMoreContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: theme.spacing.xl,
  },
  noMoreTitle: {
    fontSize: theme.typography.h2.fontSize,
    fontWeight: theme.typography.h2.fontWeight,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  noMoreText: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  refreshButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  refreshButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.button.fontSize,
    fontWeight: theme.typography.button.fontWeight,
  },
  progressIndicator: {
    marginBottom: theme.spacing.md,
  },
  counterContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  counterText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: theme.spacing.lg,
  },
  actionButton: {
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    minWidth: 120,
    alignItems: 'center',
  },
  passButton: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  passButtonText: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography.button.fontSize,
    fontWeight: theme.typography.button.fontWeight,
  },
  likeButton: {
    backgroundColor: theme.colors.primary,
  },
  likeButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.button.fontSize,
    fontWeight: theme.typography.button.fontWeight,
  },
});

export default EnhancedMatchingFlow; 