import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Text } from '@components/ui';
import { router, useLocalSearchParams } from 'expo-router';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { createChatForMatch } from '@utils/chatUtils';
import { matchingService } from '@services/matchingService';
import { unifiedProfileService } from '@services/unified-profile';
import Toast from 'react-native-toast-message';
import { logger } from '@utils/logger';

/**
 * MatchToMessageConnector
 * 
 * A component that handles the seamless transition from a match to a conversation.
 * This component should be mounted in match-related screens (like match-success.tsx)
 * to provide automated chat room creation and direct messaging flow.
 */
export const MatchToMessageConnector: React.FC<{
  matchId: string;
  autoStartChat?: boolean;
  onChatRoomCreated?: (roomId: string) => void;
}> = ({ matchId, autoStartChat = false, onChatRoomCreated }) => {
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatRoomId, setChatRoomId] = useState<string | null>(null);
  
  // Establish a direct chat connection
  const initiateChat = async () => {
    if (!user?.id || !matchId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      logger.info('Initiating chat connection', 'MatchToMessageConnector.initiateChat', {
        matchId,
        autoStartChat
      });
      
      // First, get the match user's profile for personalization
      const { data: matchProfile } = await unifiedProfileService.getUserProfile(matchId);
      const matchName = matchProfile?.first_name || 'your match';
      
      // Create or retrieve a chat room between users
      const roomId = await createChatForMatch(user.id, matchId);
      
      if (!roomId) {
        throw new Error('Failed to create chat room');
      }
      
      setChatRoomId(roomId);
      
      // Call the callback if provided
      if (onChatRoomCreated) {
        onChatRoomCreated(roomId);
      }
      
      // Mark the match as having initiated a chat
      await matchingService.markChatInitiated(user.id, matchId, roomId);
      
      // If autoStartChat is true, redirect to chat room immediately
      if (autoStartChat) {
        // Short delay for UX purposes
        setTimeout(() => {
          // Use query parameters instead of params object
          const queryParams = new URLSearchParams({
            roomId: String(roomId),
            recipientId: String(matchId),
            recipientName: String(matchName),
            fromMatch: 'true'
          });
          
          router.push(`/chat?${queryParams.toString()}`);
        }, 500);
      }
      
      logger.info('Chat connection successfully established', 'MatchToMessageConnector.initiateChat', {
        matchId,
        roomId,
        autoNavigated: autoStartChat
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect to chat';
      setError(errorMessage);
      logger.error('Error initiating chat', 'MatchToMessageConnector.initiateChat', {
        error: errorMessage,
        matchId
      });
      
      // Show error toast
      Toast.show({
        type: 'error',
        text1: 'Connection Error',
        text2: 'Unable to start conversation. Please try again.',
        position: 'bottom'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // If autoStartChat is true, initiate the chat automatically on mount
  useEffect(() => {
    if (autoStartChat && matchId && user?.id) {
      initiateChat();
    }
  }, [autoStartChat, matchId, user?.id]);
  
  // The component doesn't render anything visible by default
  // It's primarily functional, not visual
  return null;
};

export default MatchToMessageConnector;
