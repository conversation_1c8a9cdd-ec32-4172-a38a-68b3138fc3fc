import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Image,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Heart, X, Star, Bookmark, Info } from 'lucide-react-native';
// Define MatchResult interface based on the actual data structure
interface MatchResult {
  id: string;
  profile: {
    id: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
    location?: string;
  };
  compatibility: {
    score: number;
    factors: string[];
  };
}
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { MatchCelebrationModal } from '@components/matching/MatchCelebrationModal';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { startChatWithMatch } from '@utils/chatUtils';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = width * 0.9;
const CARD_HEIGHT = height * 0.6;
const SWIPE_THRESHOLD = width * 0.25;

interface SwipeDeckProps {
  data: MatchResult[];
  onSwipeRight: (item: MatchResult) => void;
  onSwipeLeft: (item: MatchResult) => void;
  onSuperLike?: (item: MatchResult) => void;
  onSave?: (item: MatchResult) => void;
  onViewProfile: (userId: string) => void;
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
  // Add new prop for detecting mutual matches
  isMutualMatch?: (item: MatchResult) => Promise<boolean>;
}

const SwipeDeck: React.FC<SwipeDeckProps> = ({
  data,
  onSwipeRight,
  onSwipeLeft,
  onSuperLike,
  onSave,
  onViewProfile,
  currentIndex,
  setCurrentIndex,
  isMutualMatch,
}) => {
  const [loading, setLoading] = useState(false);
  const { user } = useSupabaseUser();
  const theme = useTheme();
  const styles = createStyles(theme);
  const position = useRef(new Animated.ValueXY()).current;
  const swipeAnimation = useRef(new Animated.Value(0)).current;
  
  // State for match celebration
  const [showMatchModal, setShowMatchModal] = useState(false);
  const [matchedProfile, setMatchedProfile] = useState<MatchResult | null>(null);
  const rotationValue = position.x.interpolate({
    inputRange: [-width / 2, 0, width / 2],
    outputRange: ['-10deg', '0deg', '10deg'],
    extrapolate: 'clamp',
  });

  const likeOpacity = position.x.interpolate({
    inputRange: [0, width / 4],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const dislikeOpacity = position.x.interpolate({
    inputRange: [-width / 4, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  // Reset position when data or currentIndex changes
  useEffect(() => {
    position.setValue({ x: 0, y: 0 });
    swipeAnimation.setValue(0);
  }, [data, currentIndex]);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (event, gesture) => {
        position.setValue({ x: gesture.dx, y: gesture.dy });
        swipeAnimation.setValue(Math.abs(gesture.dx) / SWIPE_THRESHOLD);
      },
      onPanResponderRelease: (event, gesture) => {
        if (gesture.dx > SWIPE_THRESHOLD) {
          forceSwipeRight();
        } else if (gesture.dx < -SWIPE_THRESHOLD) {
          forceSwipeLeft();
        } else {
          resetPosition();
        }
      },
    })
  ).current;

  const forceSwipeRight = async () => {
    if (currentIndex >= data.length) return;
    setLoading(true);
    
    const currentItem = data[currentIndex];
    
    // Trigger the swipe right animation
    Animated.timing(position, {
      toValue: { x: width + 100, y: 0 },
      duration: 300,
      useNativeDriver: false,
    }).start(async () => {
      // Call the onSwipeRight handler
      onSwipeRight(currentItem);
      
      // Check if this is a mutual match
      let isMatch = false;
      if (isMutualMatch) {
        try {
          isMatch = await isMutualMatch(currentItem);
        } catch (error) {
          console.error('Error checking for mutual match:', error);
        }
      }
      
      if (isMatch) {
        // If it's a match, show the celebration modal
        setMatchedProfile(currentItem);
        setShowMatchModal(true);
      } else {
        // If not a match, just advance to the next card
        setCurrentIndex(currentIndex + 1);
      }
      
      setLoading(false);
    });
  };

  const forceSwipeLeft = () => {
    setLoading(true);
    Animated.timing(position, {
      toValue: { x: -width - 100, y: 0 },
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      if (currentIndex < data.length) {
        onSwipeLeft(data[currentIndex]);
        setCurrentIndex(currentIndex + 1);
      }
      setLoading(false);
    });
  };

  const resetPosition = () => {
    Animated.spring(position, {
      toValue: { x: 0, y: 0 },
      friction: 4,
      useNativeDriver: false,
    }).start();
  };

  const handleSuperLike = async () => {
    if (loading || currentIndex >= data.length) return;
    
    const currentItem = data[currentIndex];

    if (onSuperLike) {
      onSuperLike(currentItem);
    }
    
    // Check if this is a mutual match
    let isMatch = false;
    if (isMutualMatch) {
      try {
        isMatch = await isMutualMatch(currentItem);
      } catch (error) {
        console.error('Error checking for mutual match:', error);
      }
    }
    
    Animated.timing(position, {
      toValue: { x: 0, y: -height },
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      if (isMatch) {
        // If it's a match, show the celebration modal
        setMatchedProfile(currentItem);
        setShowMatchModal(true);
      } else {
        // If not a match, just advance to the next card
        setCurrentIndex(currentIndex + 1);
      }
    });
  };

  const handleSave = () => {
    if (loading || currentIndex >= data.length) return;

    if (onSave) {
      onSave(data[currentIndex]);
    }
  };
  
  // Handle starting a chat with a matched user
  const handleStartMessaging = async (userId: string, name: string) => {
    if (!user?.id) return;
    
    try {
      await startChatWithMatch(user.id, userId, name);
      // Close the match modal after navigation
      setShowMatchModal(false);
    } catch (error) {
      console.error('Error starting chat:', error);
    }
  };
  
  // Close the match modal and proceed to next card
  const handleCloseMatchModal = () => {
    setShowMatchModal(false);
    setCurrentIndex(currentIndex + 1);
  };

  const renderCards = () => {
    if (currentIndex >= data.length) {
      return (
        <View style={styles.noMoreCards}>
          <Text style={styles.noMoreCardsText}>No more matches</Text>
          <Text style={styles.noMoreCardsSubText}>
            Check back later for new potential roommates
          </Text>
        </View>
      );
    }

    return data
      .map((item, index) => {
        if (index < currentIndex) return null;

        // Only show top 3 cards for performance
        if (index > currentIndex + 2) return null;

        // Get the profile from the match result
        const profile = item.profile;
        const avatarUrl = profile.avatar_url || 'https://via.placeholder.com/400';
        const name = profile.first_name || 'Unknown';
        const compatibility = Math.round(item.compatibility.score * 100);

        // Only apply animation to the top card
        if (index === currentIndex) {
          return (
            <Animated.View
              key={profile.id}
              style={[
                styles.card,
                {
                  transform: [
                    { translateX: position.x },
                    { translateY: position.y },
                    { rotate: rotationValue },
                  ],
                  zIndex: data.length - index,
                },
              ]}
              {...panResponder.panHandlers}
            >
              <View style={styles.cardInner}>
                <Image source={{ uri: avatarUrl }} style={styles.cardImage} />
                
                <LinearGradient
                  colors={['transparent', theme.colors.surfaceGradient]}
                  style={styles.cardGradient}
                />
                
                {/* Like and Dislike Indicators */}
                <Animated.View
                  style={[
                    styles.likeContainer,
                    { opacity: likeOpacity, transform: [{ rotate: '-30deg' }] },
                  ]}
                >
                  <Text style={styles.likeText}>LIKE</Text>
                </Animated.View>
                
                <Animated.View
                  style={[
                    styles.dislikeContainer,
                    { opacity: dislikeOpacity, transform: [{ rotate: '30deg' }] },
                  ]}
                >
                  <Text style={styles.dislikeText}>NOPE</Text>
                </Animated.View>
                
                <View style={styles.cardContent}>
                  <View style={styles.cardHeader}>
                    <View>
                      <Text style={styles.nameText}>{name}, {(profile as any).age || 25}</Text>
                      <Text style={styles.locationText}>
                        {(profile as any).city || (profile as any).location || 'San Francisco, CA'}
                      </Text>
                    </View>
                    <View style={styles.compatibilityContainer}>
                      <Text style={styles.compatibilityText}>{compatibility}%</Text>
                      <Text style={styles.compatibilityLabel}>Match</Text>
                    </View>
                  </View>
                  
                  <TouchableOpacity
                    style={styles.infoButton}
                    onPress={() => onViewProfile(profile.id)}
                  >
                    <Info size={20} color={theme.colors.surface} />
                    <Text style={styles.infoText}>View Profile</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          );
        }

        // Background cards (don't animate)
        return (
          <Animated.View
            key={profile.id}
            style={[
              styles.card,
              {
                top: 10 * (index - currentIndex),
                zIndex: data.length - index,
                opacity: index === currentIndex + 1 ? 0.9 : 0.7,
                transform: [
                  { scale: index === currentIndex + 1 ? 0.95 : 0.9 },
                ],
              },
            ]}
          >
            <View style={styles.cardInner}>
              <Image source={{ uri: avatarUrl }} style={styles.cardImage} />
              <LinearGradient
                colors={['transparent', theme.colors.surfaceGradient]}
                style={styles.cardGradient}
              />
              <View style={styles.cardContent}>
                <View style={styles.cardHeader}>
                  <View>
                    <Text style={styles.nameText}>{name}, {(profile as any).age || 25}</Text>
                    <Text style={styles.locationText}>
                      {(profile as any).city || (profile as any).location || 'San Francisco, CA'}
                    </Text>
                  </View>
                  <View style={styles.compatibilityContainer}>
                    <Text style={styles.compatibilityText}>{compatibility}%</Text>
                    <Text style={styles.compatibilityLabel}>Match</Text>
                  </View>
                </View>
              </View>
            </View>
          </Animated.View>
        );
      })
      .reverse();
  };

  return (
    <View style={styles.container}>
      {/* Match Celebration Modal */}
      {matchedProfile && (
        <MatchCelebrationModal
          visible={showMatchModal}
          onClose={handleCloseMatchModal}
          matchedUser={{
            id: matchedProfile.profile.id,
            name: `${matchedProfile.profile.first_name} ${matchedProfile.profile.last_name}`,
            display_name: matchedProfile.profile.first_name || matchedProfile.profile.display_name,
            avatar: matchedProfile.profile.avatar_url,
            compatibility: matchedProfile.compatibility.score
          }}
          currentUser={{
            id: user?.id || '',
            avatar: user?.user_metadata?.avatar_url
          }}
          onStartMessaging={handleStartMessaging}
          onViewProfile={onViewProfile}
        />
      )}
      <View style={styles.cardsContainer}>{renderCards()}</View>

      {currentIndex < data.length && (
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.button, styles.dislikeButton]}
            onPress={forceSwipeLeft}
            disabled={loading}
          >
            <X size={30} color={theme.colors.surface} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={handleSave}
            disabled={loading}
          >
            <Bookmark size={24} color={theme.colors.surface} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.superLikeButton]}
            onPress={handleSuperLike}
            disabled={loading}
          >
            <Star size={26} color={theme.colors.surface} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.likeButton]}
            onPress={forceSwipeRight}
            disabled={loading}
          >
            <Heart size={30} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  cardsContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.lg,
  },
  card: {
    position: 'absolute',
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
    ...theme.shadows.lg,
  },
  cardInner: {
    flex: 1,
  },
  cardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  cardGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 180,
  },
  cardContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: theme.spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginBottom: theme.spacing.md,
  },
  nameText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  locationText: {
    fontSize: 16,
    color: theme.colors.surfaceSecondary,
    marginTop: 5,
  },
  compatibilityContainer: {
    backgroundColor: theme.colors.surfaceOverlay,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.xs,
    alignItems: 'center',
  },
  compatibilityText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  compatibilityLabel: {
    fontSize: 12,
    color: theme.colors.surfaceSecondary,
    marginTop: 2,
  },
  infoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.darkOverlay,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    alignSelf: 'flex-start',
  },
  infoText: {
    color: theme.colors.surface,
    marginLeft: theme.spacing.xs,
    fontWeight: '500',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.lg,
  },
  button: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.md,
  },
  dislikeButton: {
    backgroundColor: theme.colors.error,
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
  },
  superLikeButton: {
    backgroundColor: theme.colors.info,
  },
  likeButton: {
    backgroundColor: theme.colors.success,
  },
  likeContainer: {
    position: 'absolute',
    top: 50,
    left: 40,
    zIndex: 1000,
    transform: [{ rotate: '-30deg' }],
  },
  likeText: {
    borderWidth: 5,
    borderColor: theme.colors.success,
    color: theme.colors.success,
    fontSize: 32,
    fontWeight: 'bold',
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  },
  dislikeContainer: {
    position: 'absolute',
    top: 50,
    right: 40,
    zIndex: 1000,
    transform: [{ rotate: '30deg' }],
  },
  dislikeText: {
    borderWidth: 5,
    borderColor: theme.colors.error,
    color: theme.colors.error,
    fontSize: 32,
    fontWeight: 'bold',
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  },
  noMoreCards: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
  },
  noMoreCardsText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  noMoreCardsSubText: {
    fontSize: 16,
    color: theme.colors.textMuted,
    textAlign: 'center',
  },
});

export default SwipeDeck;
