import React, { useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Send } from 'lucide-react-native';
;
import { matchService } from '@services/MatchService';
import { hapticFeedback } from '@utils/hapticFeedback';

interface QuickMessageSelectorProps {
  userId: string;
  matchedUserId: string;
  matchId: string;
  onMessageSent: (roomId: string, message: string) => void;
  onClose: () => void;
}

export default function QuickMessageSelector({
  userId,
  matchedUserId,
  matchId,
  onMessageSent,
  onClose
}: QuickMessageSelectorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);
  const templates = matchService.getQuickMessageTemplates();

  const handleSelectTemplate = (templateId: string) => {
    // Provide haptic feedback
    hapticFeedback.selection();
    setSelectedTemplate(templateId);
  };

  const handleSendMessage = async () => {
    if (!selectedTemplate || isSending) return;
    
    try {
      setIsSending(true);
      
      // Provide haptic feedback
      hapticFeedback.medium();
      
      // Start conversation with the selected template
      const result = await matchService.startConversationWithTemplate(
        userId,
        matchedUserId,
        selectedTemplate
      );
      
      if (result.success && result.roomId) {
        // Call the onMessageSent callback with the room ID and message
        onMessageSent(result.roomId, result.message || '');
      } else {
        console.error('Failed to send message:', result.message);
      }
    } catch (error) {
      console.error('Error sending quick message:', error);
    } finally {
      setIsSending(false);
      onClose();
    }
  };

  const renderTemplate = ({ item }: { item: { id: string, text: string, emoji: string } }) => (
    <TouchableOpacity
      style={[
        styles.templateItem,
        selectedTemplate === item.id && styles.selectedTemplate
      ]}
      onPress={() => handleSelectTemplate(item.id)}
      disabled={isSending}
    >
      <Text style={styles.templateEmoji}>{item.emoji}</Text>
      <Text 
        style={[
          styles.templateText,
          selectedTemplate === item.id && styles.selectedTemplateText
        ]}
        numberOfLines={2}
      >
        {item.text}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Start the conversation</Text>
        <Text style={styles.subtitle}>Select a message to send</Text>
      </View>
      
      <FlatList
        data={templates}
        renderItem={renderTemplate}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!selectedTemplate || isSending) && styles.disabledButton
          ]}
          onPress={handleSendMessage}
          disabled={!selectedTemplate || isSending}
        >
          {isSending ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <>
              <Send size={18} color="#FFFFFF" />
              <Text style={styles.sendButtonText}>Send Message</Text>
            </>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.skipButton}
          onPress={onClose}
          disabled={isSending}
        >
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    padding: 16,
    maxHeight: 400,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.gray,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.gray,
  },
  listContent: {
    paddingVertical: 8,
  },
  templateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: theme.colors.gray,
  },
  selectedTemplate: {
    backgroundColor: theme.colors.primary,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  templateEmoji: {
    fontSize: 20,
    marginRight: 12,
  },
  templateText: {
    fontSize: 14,
    color: theme.colors.gray,
    flex: 1,
  },
  selectedTemplateText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  footer: {
    marginTop: 16,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  disabledButton: {
    backgroundColor: theme.colors.gray,
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  skipButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  skipButtonText: {
    color: theme.colors.gray,
    fontWeight: '500',
  },
});
