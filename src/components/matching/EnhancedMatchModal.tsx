/**
 * EnhancedMatchModal
 * 
 * A simplified version of the match celebration modal for direct integration
 * with the enhanced browse screen.
 */

import React from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Heart, MessageSquare, User, X } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withDelay,
} from 'react-native-reanimated';
import { hapticFeedback } from '@utils/hapticFeedback';

interface EnhancedMatchModalProps {
  visible: boolean;
  onClose: () => void;
  onMessage: (matchId: string) => void;
  matchId?: string;
  matchName?: string;
  matchAvatar?: string;
  compatibility?: number;
}

export const EnhancedMatchModal: React.FC<EnhancedMatchModalProps> = ({
  visible,
  onClose,
  onMessage,
  matchId = '',
  matchName = 'Your match',
  matchAvatar,
  compatibility = 75,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  // Animation values
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);
  
  // Animation effects
  React.useEffect(() => {
    if (visible) {
      scale.value = 0.8;
      opacity.value = 0;
      
      scale.value = withSpring(1);
      opacity.value = withTiming(1, { duration: 300 });
    }
  }, [visible]);
  
  const animatedCardStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });
  
  const handleStartMessaging = () => {
    hapticFeedback.success();
    if (matchId) {
      onMessage(matchId);
    }
    onClose();
  };
  
  if (!visible) return null;
  
  return (
    <Modal transparent visible={visible} animationType="fade" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <Animated.View style={[styles.modalContent, animatedCardStyle]}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          <View style={styles.celebrationContainer}>
            {/* Header */}
            <View style={styles.header}>
              <Heart size={40} color={theme.colors.primary} fill={theme.colors.primary} />
              <Text style={styles.matchText}>It's a match!</Text>
              <Text style={styles.matchSubtext}>
                You and {matchName} have liked each other.
              </Text>
            </View>
            
            {/* Avatar */}
            <View style={styles.avatarContainer}>
              {matchAvatar ? (
                <Image source={{ uri: matchAvatar }} style={styles.avatar} />
              ) : (
                <View style={[styles.avatar, styles.avatarPlaceholder]}>
                  <Text style={styles.avatarText}>
                    {matchName.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
            </View>
            
            {/* Compatibility Score */}
            <View style={styles.compatibilityBadge}>
              <Text style={styles.compatibilityText}>
                {compatibility}% compatibility match
              </Text>
            </View>
            
            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleStartMessaging}
              >
                <MessageSquare size={20} color={theme.colors.background} style={styles.buttonIcon} />
                <Text style={styles.primaryButtonText}>Send a Message</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const createStyles = (theme: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.overlay,
  },
  modalContent: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: theme.colors.background,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 28,
    alignItems: 'center',
    position: 'relative',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
  },
  celebrationContainer: {
    alignItems: 'center',
    width: '100%',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    marginBottom: 16,
    borderWidth: 3,
    borderColor: theme.colors.primarySurface,
    borderRadius: 75,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 5,
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  avatarPlaceholder: {
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 64,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
  },
  matchText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  matchSubtext: {
    fontSize: 16,
    textAlign: 'center',
    color: theme.colors.text,
    marginBottom: 8,
  },
  compatibilityBadge: {
    backgroundColor: theme.colors.primarySurface,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  compatibilityText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    width: '100%',
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EnhancedMatchModal;
