import React, { useState, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Zap, Clock, X } from 'lucide-react-native';
;
import { profileBoostService, ProfileBoost } from '@services/profileBoostService';

interface BoostStatusProps {
  userId: string;
  boost: ProfileBoost;
  onBoostCancelled?: () => void;
}

export default function BoostStatus({
 userId, boost, onBoostCancelled }: BoostStatusProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [timeRemaining, setTimeRemaining] = useState<{ hours: number; minutes: number }>({ hours: 0, minutes: 0 });
  const [cancelling, setCancelling] = useState(false);
  
  // Update time remaining every minute
  useEffect(() => {
    // Initial calculation
    updateTimeRemaining();
    
    // Set up interval to update every minute
    const interval = setInterval(updateTimeRemaining, 60000);
    
    // Clean up interval
    return () => clearInterval(interval);
  }, [boost]);
  
  const updateTimeRemaining = () => {
    const remaining = profileBoostService.getRemainingBoostTime(boost);
    setTimeRemaining(remaining);
  };
  
  const handleCancelPress = () => {
    Alert.alert(
      'Cancel Boost',
      'Are you sure you want to cancel your profile boost? This cannot be undone.',
      [
        { text: 'Keep Boost', style: 'cancel' },
        { text: 'Cancel Boost', style: 'destructive', onPress: cancelBoost }
      ]
    );
  };
  
  const cancelBoost = async () => {
    try {
      setCancelling(true);
      const success = await profileBoostService.cancelBoost(boost.id, userId);
      
      if (success) {
        if (onBoostCancelled) {
          onBoostCancelled();
        }
      } else {
        Alert.alert(
          'Error',
          'Could not cancel the boost. Please try again.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    } catch (error) {
      console.error('Error cancelling boost:', error);
      Alert.alert(
        'Error',
        'An error occurred while cancelling the boost. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setCancelling(false);
    }
  };
  
  // If boost has expired, don't show
  if (timeRemaining.hours === 0 && timeRemaining.minutes === 0) {
    return null;
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.boostBadge}>
        <Zap size={16} color={theme.colors.white} fill={theme.colors.white} style={styles.boostIcon} />
        <Text style={styles.boostText}>Boosted</Text>
      </View>
      
      <View style={styles.timeContainer}>
        <Clock size={14} color={theme.colors.gray} style={styles.clockIcon} />
        <Text style={styles.timeText}>
          {timeRemaining.hours}h {timeRemaining.minutes}m remaining
        </Text>
      </View>
      
      <TouchableOpacity 
        style={styles.cancelButton}
        onPress={handleCancelPress}
        disabled={cancelling}
      >
        <X size={14} color={theme.colors.gray} />
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.amber,
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
  },
  boostBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.amber,
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  boostIcon: {
    marginRight: 4,
  },
  boostText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.white,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    flex: 1,
  },
  clockIcon: {
    marginRight: 4,
  },
  timeText: {
    fontSize: 12,
    color: theme.colors.gray,
  },
  cancelButton: {
    padding: 4,
  },
});