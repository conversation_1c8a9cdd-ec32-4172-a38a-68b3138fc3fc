/**
 * EnhancedMatchToMessageTransition Component
 *
 * An improved transition component that guides users from matching to messaging
 * with enhanced UI/UX and integration with the useMatchChat hook.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Image,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  MessageSquare,
  ArrowRight,
  Send,
  X,
  Heart,
  CheckCircle,
  BarChart2,
} from 'lucide-react-native';
;
import { Button } from '@design-system';
import { hapticFeedback } from '@utils/hapticFeedback';
import { logger } from '@services/loggerService';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { useMatchChat } from '@features/home/<USER>/useMatchChat';
import { matchService } from '@services/MatchService';
import { HousemateListing } from '@features/home/<USER>';
import { createChatWithMatchAndNavigate } from '@utils/navigationUtils';

const { width, height } = Dimensions.get('window');

// Default placeholder avatar
const DEFAULT_AVATAR = 'https://via.placeholder.com/150?text=No+Photo';

interface EnhancedMatchToMessageTransitionProps {
  visible: boolean;
  onClose: () => void;
  matchedUser: HousemateListing;
  onTransitionComplete: () => void;
}

/**
 * An enhanced component that provides a smooth transition from matching to messaging
 * It guides users through the process of starting a conversation with a new match
 * and integrates with the useMatchChat hook for consistent messaging functionality
 */
const EnhancedMatchToMessageTransition: React.FC<EnhancedMatchToMessageTransitionProps> = ({
  visible,
  onClose,
  matchedUser,
  onTransitionComplete,
}) => {
  const router = useRouter();
  const { user } = useSupabaseUser();
  const { handleQuickChat } = useMatchChat();

  const [step, setStep] = useState(1); // 1: Intro, 2: Message Starters, 3: Success
  const [customMessage, setCustomMessage] = useState('');
  const [starters, setStarters] = useState<Array<{ id: string; text: string; emoji: string }>>([]);
  const [selectedStarterId, setSelectedStarterId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingStarters, setLoadingStarters] = useState(true);

  // Animation values
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  // Load conversation starters when component mounts
  useEffect(() => {
    if (visible) {
      loadConversationStarters();
    }
  }, [visible]);

  // Animate in when visible changes
  useEffect(() => {
    if (visible) {
      // Reset state
      setStep(1);
      setCustomMessage('');
      setSelectedStarterId(null);

      // Start animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.9);
      slideAnim.setValue(0);
    }
  }, [visible]);

  // Load conversation starters
  const loadConversationStarters = async () => {
    try {
      setLoadingStarters(true);
      // Get quick message templates from the hook
      const templates = matchService.getQuickMessageTemplates();
      setStarters(templates);

      // Pre-select the first template
      if (templates.length > 0) {
        setSelectedStarterId(templates[0].id);
      }
    } catch (error) {
      logger.error('Failed to load conversation starters', 'EnhancedMatchToMessageTransition', {
        error: error instanceof Error ? error.message : String(error),
      });

      // Set default starters if loading fails
      setStarters([
        {
          id: '1',
          text: `Hi ${matchedUser.first_name}! We matched! 👋`,
          emoji: '👋',
        },
        {
          id: '2',
          text: 'What are you looking for in a roommate?',
          emoji: '🏠',
        },
        {
          id: '3',
          text: 'When are you planning to move?',
          emoji: '📅',
        },
      ]);

      // Pre-select the first template
      setSelectedStarterId('1');
    } finally {
      setLoadingStarters(false);
    }
  };

  // Handle next step
  const handleNextStep = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    try {
      hapticFeedback.selection();
    } catch (err) {
      console.log('Haptic feedback not available');
    }

    // Animate out current step
    Animated.timing(slideAnim, {
      toValue: 2,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // Reset animation and move to next step
      slideAnim.setValue(0);
      setStep(prevStep => prevStep + 1);

      // Animate in next step
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    });
  };

  // Handle sending a message using standardized navigation
  const handleSendMessage = async () => {
    if (loading) return;

    try {
      // Try to use haptic feedback if available
      try {
        hapticFeedback.selection();
      } catch (err) {
        console.log('Haptic feedback not available');
      }

      if (!user?.id) {
        logger.error('No user found', 'EnhancedMatchToMessageTransition');
        return;
      }

      setLoading(true);

      let messageToSend = '';

      if (selectedStarterId) {
        // Get the selected starter message
        const selectedStarter = starters.find(s => s.id === selectedStarterId);
        if (selectedStarter) {
          messageToSend = selectedStarter.text;
        }
      } else if (customMessage.trim()) {
        // Use custom message
        messageToSend = customMessage.trim();
      } else {
        // No message selected
        logger.error('No message selected', 'EnhancedMatchToMessageTransition');
        setLoading(false);
        return;
      }

      // Use the standardized navigation utility for consistent chat creation
      const success = await createChatWithMatchAndNavigate(
        user.id,
        matchedUser.id,
        matchedUser.first_name || 'User',
        messageToSend
      );

      if (!success) {
        throw new Error('Failed to create chat room');
      }

      logger.info('Message sent successfully', 'EnhancedMatchToMessageTransition', {
        recipientId: matchedUser.id,
      });

      // Move to success step
      setStep(3);

      // Wait a bit before closing and navigating
      setTimeout(() => {
        // Call onTransitionComplete
        onTransitionComplete();
      }, 1500);
    } catch (error) {
      logger.error('Failed to send message', 'EnhancedMatchToMessageTransition', {
        error: error instanceof Error ? error.message : String(error),
      });

      Alert.alert(
        'Error',
        'Something went wrong while trying to start the conversation. Please try again.',
        [{ text: 'OK', onPress: () => setLoading(false) }]
      );
    }
  };

  // Handle selecting a starter message
  const handleSelectStarter = (id: string) => {
    try {
      hapticFeedback.selection();
    } catch (err) {
      console.log('Haptic feedback not available');
    }

    setSelectedStarterId(id);
  };

  // Render content based on current step
  const renderContent = () => {
    switch (step) {
      case 1: // Intro step
        return (
          <View style={styles.stepContainer}>
            <View style={styles.iconContainer}>
              <MessageSquare size={32} color={theme.colors.primary} />
            </View>

            <Text style={styles.title}>Start a Conversation</Text>
            <Text style={styles.description}>
              You've matched with {matchedUser.first_name}! Start a conversation to get to know each
              other better.
            </Text>

            <View style={styles.matchInfoContainer}>
              <View style={styles.avatarContainer}>
                {matchedUser.avatar_url ? (
                  <Image
                    source={{ uri: matchedUser.avatar_url }}
                    style={styles.avatar}
                    defaultSource={{ uri: DEFAULT_AVATAR }}
                  />
                ) : (
                  <View style={[styles.avatar, styles.avatarPlaceholder]}>
                    <Text style={styles.avatarText}>{matchedUser.first_name?.[0]}</Text>
                  </View>
                )}
              </View>
              <View style={styles.matchTextContainer}>
                <Text style={styles.matchName}>
                  {matchedUser.first_name} {matchedUser.last_name}
                </Text>
                <Text style={styles.compatibilityText}>
                  {matchedUser.compatibility_score
                    ? `${Math.round(matchedUser.compatibility_score * 100)}% Compatible`
                    : 'New Match!'}
                </Text>
              </View>
            </View>

            {/* Tips */}
            <View style={styles.tipContainer}>
              <Text style={styles.tipTitle}>Conversation Tips</Text>
              <Text style={styles.tipText}>• Ask about their housing preferences</Text>
              <Text style={styles.tipText}>• Discuss potential move-in dates</Text>
              <Text style={styles.tipText}>• Share a bit about your daily routine</Text>
            </View>

            <TouchableOpacity
              style={[styles.button, { backgroundColor: theme.colors.primary }]}
              onPress={handleNextStep}
              disabled={loading}
            >
              <Text style={{ color: theme.colors.white, fontWeight: '600', marginRight: 8 }}>
                Continue
              </Text>
              <ArrowRight size={18} color={theme.colors.background} />
            </TouchableOpacity>
          </View>
        );

      case 2: // Message starters step
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.title}>Send a Message</Text>
            <Text style={styles.description}>
              Choose a conversation starter or write your own message to {matchedUser.first_name}.
            </Text>

            {loadingStarters ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Loading conversation starters...</Text>
              </View>
            ) : (
              <>
                {/* Conversation starters */}
                <View style={styles.startersContainer}>
                  <FlatList
                    data={starters}
                    keyExtractor={item => item.id}
                    style={styles.startersList}
                    renderItem={({ item }) => (
                      <TouchableOpacity
                        style={[
                          styles.starterItem,
                          selectedStarterId === item.id && styles.selectedStarter,
                        ]}
                        onPress={() => handleSelectStarter(item.id)}
                      >
                        <Text
                          style={[
                            styles.starterText,
                            selectedStarterId === item.id && styles.selectedStarterText,
                          ]}
                        >
                          {item.emoji} {item.text}
                        </Text>
                      </TouchableOpacity>
                    )}
                  />
                </View>

                {/* Custom message input */}
                <View style={styles.customMessageContainer}>
                  <TextInput
                    style={styles.customMessageInput}
                    placeholder={`Write your own message to ${matchedUser.first_name}...`}
                    placeholderTextColor={theme.colors.gray}
                    multiline
                    value={customMessage}
                    onChangeText={setCustomMessage}
                  />
                </View>

                {/* Send button */}
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.sendButton, { backgroundColor: theme.colors.primary }]}
                    onPress={handleSendMessage}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator size="small" color={theme.colors.background} />
                    ) : (
                      <>
                        <Send size={18} color={theme.colors.background} />
                        <Text style={{ color: theme.colors.background, marginLeft: 8, fontWeight: '600' }}>
                          Send Message
                        </Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        );

      case 3: // Success step
        return (
          <View style={[styles.stepContainer, styles.successStep]}>
            <View style={styles.successIconContainer}>
              <CheckCircle size={40} color={theme.colors.primary} />
            </View>

            <Text style={styles.successTitle}>Message Sent!</Text>
            <Text style={styles.successDescription}>
              Your message has been sent to {matchedUser.first_name}. You'll be redirected to the
              chat shortly.
            </Text>

            {/* Static match insights instead of MatchStatistics component */}
            <View style={styles.statsSection}>
              <View style={styles.statsSectionHeader}>
                <BarChart2 size={18} color={theme.colors.primary} />
                <Text style={styles.statsSectionTitle}>Match Insights</Text>
              </View>

              <Text style={styles.successDescription}>
                You now have an active conversation with {matchedUser.first_name}. Respond quickly
                to increase your chances of finding the perfect roommate!
              </Text>

              <TouchableOpacity
                style={styles.viewStatsButton}
                onPress={() => router.push({ pathname: '/profile/statistics' } as any)}
              >
                <Text style={styles.viewStatsButtonText}>View Full Statistics</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      default:
        return null;
    }
  };
  // Don't render if not visible
  if (!visible) return null;

  // Calculate transform based on animation value
  const translateX = slideAnim.interpolate({
    inputRange: [0, 1, 2],
    outputRange: [width, 0, -width],
  });

  return (
    <Modal animationType="fade" transparent={true} visible={visible} onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={theme.colors.gray} />
          </TouchableOpacity>

          {renderContent()}
        </Animated.View>
      </View>
    </Modal>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: theme.colors.white,
    borderRadius: 16,
    padding: 24,
    maxHeight: height * 0.8,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
  },
  stepContainer: {
    width: '100%',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: theme.colors.gray,
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: theme.colors.gray,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  matchInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    backgroundColor: theme.colors.gray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.gray,
  },
  matchTextContainer: {
    flex: 1,
  },
  matchName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.gray,
    marginBottom: 4,
  },
  compatibilityText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  tipContainer: {
    backgroundColor: theme.colors.gray,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: theme.colors.gray,
    marginBottom: 6,
    lineHeight: 20,
  },
  button: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  startersContainer: {
    marginBottom: 16,
  },
  startersList: {
    maxHeight: 200,
  },
  starterItem: {
    backgroundColor: theme.colors.gray,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.gray,
  },
  selectedStarter: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  starterText: {
    fontSize: 14,
    color: theme.colors.gray,
  },
  selectedStarterText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  customMessageContainer: {
    marginBottom: 16,
  },
  customMessageInput: {
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.gray,
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    color: theme.colors.gray,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    width: '100%',
  },
  sendButton: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.gray,
    marginTop: 12,
  },
  successStep: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  successIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.gray,
    marginBottom: 16,
  },
  successDescription: {
    fontSize: 16,
    color: theme.colors.gray,
    textAlign: 'center',
    lineHeight: 24,
  },
  // Match Statistics Section Styles
  statsSection: {
    marginTop: 16,
    backgroundColor: theme.colors.gray,
    borderRadius: 12,
    padding: 16,
    width: '100%',
    borderWidth: 1,
    borderColor: theme.colors.gray,
  },
  statsSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray,
    marginLeft: 8,
  },
  viewStatsButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  viewStatsButtonText: {
    color: theme.colors.primary,
    fontWeight: '600',
    fontSize: 14,
  },
});

export default EnhancedMatchToMessageTransition;
