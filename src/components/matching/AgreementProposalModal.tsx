/**
 * AgreementProposalModal - Agreement Proposal Interface
 * 
 * Modal component for proposing roommate agreements during the matching flow.
 * Includes template selection, title customization, and initial terms setup.
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '@design-system/ThemeProvider';
import { useToast } from '@hooks/useToast';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';

interface AgreementTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  sections: any;
}

interface AgreementProposalModalProps {
  visible: boolean;
  onClose: () => void;
  onPropose: (proposalData: {
    template_id: string;
    title: string;
    initial_terms?: any;
  }) => void;
  recipientName: string;
}

export const AgreementProposalModal: React.FC<AgreementProposalModalProps> = ({
  visible,
  onClose,
  onPropose,
  recipientName
}) => {
  const theme = useTheme();
  const { showToast } = useToast();
  const styles = createStyles(theme);

  // State management
  const [templates, setTemplates] = useState<AgreementTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [agreementTitle, setAgreementTitle] = useState('');
  const [initialTerms, setInitialTerms] = useState({
    rent_amount: '',
    security_deposit: '',
    lease_duration: '',
    move_in_date: '',
    special_terms: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  // Load templates when modal opens
  useEffect(() => {
    if (visible) {
      loadTemplates();
      // Set default title
      setAgreementTitle(`Roommate Agreement with ${recipientName}`);
    }
  }, [visible, recipientName]);

  const loadTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      
      const { data, error } = await supabase
        .from('agreement_templates')
        .select('id, name, description, category, sections')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      setTemplates(data || []);
      
      // Auto-select first template if available
      if (data && data.length > 0) {
        setSelectedTemplate(data[0].id);
      }
    } catch (error) {
      logger.error('Failed to load agreement templates', 'AgreementProposalModal', {}, error as Error);
      showToast('Failed to load agreement templates', 'error');
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const handlePropose = async () => {
    // Validation
    if (!selectedTemplate) {
      showToast('Please select an agreement template', 'error');
      return;
    }

    if (!agreementTitle.trim()) {
      showToast('Please enter an agreement title', 'error');
      return;
    }

    try {
      setIsLoading(true);

      // Prepare proposal data
      const proposalData = {
        template_id: selectedTemplate,
        title: agreementTitle.trim(),
        initial_terms: {
          ...initialTerms,
          proposed_at: new Date().toISOString(),
          recipient_name: recipientName
        }
      };

      // Call the proposal handler
      await onPropose(proposalData);

      // Reset form
      resetForm();
    } catch (error) {
      logger.error('Failed to propose agreement', 'AgreementProposalModal', {}, error as Error);
      showToast('Failed to propose agreement', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedTemplate(null);
    setAgreementTitle('');
    setInitialTerms({
      rent_amount: '',
      security_deposit: '',
      lease_duration: '',
      move_in_date: '',
      special_terms: ''
    });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Propose Agreement</Text>
          <TouchableOpacity 
            onPress={handlePropose} 
            style={[styles.proposeButton, isLoading && styles.proposeButtonDisabled]}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={theme.colors?.white || '#FFFFFF'} />
            ) : (
              <Text style={styles.proposeButtonText}>Propose</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Introduction */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Propose Agreement to {recipientName}</Text>
            <Text style={styles.sectionDescription}>
              Create a roommate agreement proposal that {recipientName} can review and customize.
            </Text>
          </View>

          {/* Agreement Title */}
          <View style={styles.section}>
            <Text style={styles.fieldLabel}>Agreement Title</Text>
            <TextInput
              style={styles.textInput}
              value={agreementTitle}
              onChangeText={setAgreementTitle}
              placeholder="Enter agreement title"
              placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
            />
          </View>

          {/* Template Selection */}
          <View style={styles.section}>
            <Text style={styles.fieldLabel}>Agreement Template</Text>
            <Text style={styles.fieldDescription}>
              Choose a template that best fits your living arrangement
            </Text>
            
            {isLoadingTemplates ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.colors?.primary || '#3B82F6'} />
                <Text style={styles.loadingText}>Loading templates...</Text>
              </View>
            ) : (
              <View style={styles.templatesContainer}>
                {templates.map((template) => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateCard,
                      selectedTemplate === template.id && styles.templateCardSelected
                    ]}
                    onPress={() => setSelectedTemplate(template.id)}
                  >
                    <View style={styles.templateHeader}>
                      <Text style={[
                        styles.templateName,
                        selectedTemplate === template.id && styles.templateNameSelected
                      ]}>
                        {template.name}
                      </Text>
                      <Text style={styles.templateCategory}>{template.category}</Text>
                    </View>
                    <Text style={[
                      styles.templateDescription,
                      selectedTemplate === template.id && styles.templateDescriptionSelected
                    ]}>
                      {template.description}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* Initial Terms (Optional) */}
          <View style={styles.section}>
            <Text style={styles.fieldLabel}>Initial Terms (Optional)</Text>
            <Text style={styles.fieldDescription}>
              Provide initial terms to help start the discussion
            </Text>

            <View style={styles.termsGrid}>
              <View style={styles.termField}>
                <Text style={styles.termLabel}>Monthly Rent</Text>
                <TextInput
                  style={styles.termInput}
                  value={initialTerms.rent_amount}
                  onChangeText={(value) => setInitialTerms(prev => ({ ...prev, rent_amount: value }))}
                  placeholder="$0"
                  placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.termField}>
                <Text style={styles.termLabel}>Security Deposit</Text>
                <TextInput
                  style={styles.termInput}
                  value={initialTerms.security_deposit}
                  onChangeText={(value) => setInitialTerms(prev => ({ ...prev, security_deposit: value }))}
                  placeholder="$0"
                  placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.termField}>
                <Text style={styles.termLabel}>Lease Duration</Text>
                <TextInput
                  style={styles.termInput}
                  value={initialTerms.lease_duration}
                  onChangeText={(value) => setInitialTerms(prev => ({ ...prev, lease_duration: value }))}
                  placeholder="12 months"
                  placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
                />
              </View>

              <View style={styles.termField}>
                <Text style={styles.termLabel}>Move-in Date</Text>
                <TextInput
                  style={styles.termInput}
                  value={initialTerms.move_in_date}
                  onChangeText={(value) => setInitialTerms(prev => ({ ...prev, move_in_date: value }))}
                  placeholder="MM/DD/YYYY"
                  placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
                />
              </View>
            </View>

            <View style={styles.termField}>
              <Text style={styles.termLabel}>Special Terms</Text>
              <TextInput
                style={[styles.termInput, styles.termInputMultiline]}
                value={initialTerms.special_terms}
                onChangeText={(value) => setInitialTerms(prev => ({ ...prev, special_terms: value }))}
                placeholder="Any special arrangements or terms..."
                placeholderTextColor={theme.colors?.textSecondary || '#64748B'}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>

          {/* Template Preview */}
          {selectedTemplateData && (
            <View style={styles.section}>
              <Text style={styles.fieldLabel}>Template Preview</Text>
              <View style={styles.previewContainer}>
                <Text style={styles.previewTitle}>{selectedTemplateData.name}</Text>
                <Text style={styles.previewDescription}>{selectedTemplateData.description}</Text>
                
                {selectedTemplateData.sections?.sections && (
                  <View style={styles.previewSections}>
                    <Text style={styles.previewSectionsTitle}>Included Sections:</Text>
                    {selectedTemplateData.sections.sections.map((section: any, index: number) => (
                      <Text key={index} style={styles.previewSectionItem}>
                        • {section.title}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors?.background || '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing?.md || 16,
    paddingVertical: theme.spacing?.md || 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors?.border || '#E2E8F0',
    backgroundColor: theme.colors?.surface || '#F8F9FA',
  },
  closeButton: {
    padding: theme.spacing?.sm || 8,
  },
  closeButtonText: {
    fontSize: theme.typography?.button?.fontSize || 16,
    color: theme.colors?.textSecondary || '#64748B',
  },
  headerTitle: {
    fontSize: theme.typography?.h3?.fontSize || 18,
    fontWeight: theme.typography?.h3?.fontWeight || '600',
    color: theme.colors?.text || '#1F2937',
  },
  proposeButton: {
    backgroundColor: theme.colors?.primary || '#3B82F6',
    paddingHorizontal: theme.spacing?.md || 16,
    paddingVertical: theme.spacing?.sm || 8,
    borderRadius: theme.borderRadius?.sm || 6,
    minWidth: 80,
    alignItems: 'center',
  },
  proposeButtonDisabled: {
    opacity: 0.6,
  },
  proposeButtonText: {
    fontSize: theme.typography?.button?.fontSize || 16,
    fontWeight: theme.typography?.button?.fontWeight || '600',
    color: theme.colors?.white || '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: theme.spacing?.md || 16,
  },
  section: {
    marginBottom: theme.spacing?.xl || 32,
  },
  sectionTitle: {
    fontSize: theme.typography?.h2?.fontSize || 20,
    fontWeight: theme.typography?.h2?.fontWeight || '700',
    color: theme.colors?.text || '#1F2937',
    marginBottom: theme.spacing?.sm || 8,
  },
  sectionDescription: {
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.textSecondary || '#64748B',
    lineHeight: 22,
  },
  fieldLabel: {
    fontSize: theme.typography?.subtitle?.fontSize || 16,
    fontWeight: theme.typography?.subtitle?.fontWeight || '600',
    color: theme.colors?.text || '#1F2937',
    marginBottom: theme.spacing?.xs || 4,
  },
  fieldDescription: {
    fontSize: theme.typography?.caption?.fontSize || 14,
    color: theme.colors?.textSecondary || '#64748B',
    marginBottom: theme.spacing?.md || 16,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E2E8F0',
    borderRadius: theme.borderRadius?.sm || 6,
    padding: theme.spacing?.md || 16,
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.text || '#1F2937',
    backgroundColor: theme.colors?.surface || '#F8F9FA',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing?.lg || 24,
  },
  loadingText: {
    marginLeft: theme.spacing?.sm || 8,
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.textSecondary || '#64748B',
  },
  templatesContainer: {
    gap: theme.spacing?.sm || 8,
  },
  templateCard: {
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E2E8F0',
    borderRadius: theme.borderRadius?.md || 8,
    padding: theme.spacing?.md || 16,
    backgroundColor: theme.colors?.surface || '#F8F9FA',
  },
  templateCardSelected: {
    borderColor: theme.colors?.primary || '#3B82F6',
    backgroundColor: (theme.colors?.primary || '#3B82F6') + '10',
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing?.xs || 4,
  },
  templateName: {
    fontSize: theme.typography?.subtitle?.fontSize || 16,
    fontWeight: theme.typography?.subtitle?.fontWeight || '600',
    color: theme.colors?.text || '#1F2937',
    flex: 1,
  },
  templateNameSelected: {
    color: theme.colors?.primary || '#3B82F6',
  },
  templateCategory: {
    fontSize: theme.typography?.caption?.fontSize || 14,
    color: theme.colors?.textSecondary || '#64748B',
    backgroundColor: theme.colors?.border || '#E2E8F0',
    paddingHorizontal: theme.spacing?.sm || 8,
    paddingVertical: theme.spacing?.xs || 4,
    borderRadius: theme.borderRadius?.sm || 6,
  },
  templateDescription: {
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.textSecondary || '#64748B',
    lineHeight: 20,
  },
  templateDescriptionSelected: {
    color: theme.colors?.text || '#1F2937',
  },
  termsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing?.md || 16,
    marginBottom: theme.spacing?.md || 16,
  },
  termField: {
    flex: 1,
    minWidth: '45%',
  },
  termLabel: {
    fontSize: theme.typography?.caption?.fontSize || 14,
    fontWeight: '500',
    color: theme.colors?.text || '#1F2937',
    marginBottom: theme.spacing?.xs || 4,
  },
  termInput: {
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E2E8F0',
    borderRadius: theme.borderRadius?.sm || 6,
    padding: theme.spacing?.sm || 8,
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.text || '#1F2937',
    backgroundColor: theme.colors?.surface || '#F8F9FA',
  },
  termInputMultiline: {
    height: 80,
    textAlignVertical: 'top',
  },
  previewContainer: {
    backgroundColor: theme.colors?.surface || '#F8F9FA',
    borderRadius: theme.borderRadius?.md || 8,
    padding: theme.spacing?.md || 16,
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E2E8F0',
  },
  previewTitle: {
    fontSize: theme.typography?.subtitle?.fontSize || 16,
    fontWeight: theme.typography?.subtitle?.fontWeight || '600',
    color: theme.colors?.text || '#1F2937',
    marginBottom: theme.spacing?.xs || 4,
  },
  previewDescription: {
    fontSize: theme.typography?.body?.fontSize || 16,
    color: theme.colors?.textSecondary || '#64748B',
    marginBottom: theme.spacing?.md || 16,
    lineHeight: 20,
  },
  previewSections: {
    marginTop: theme.spacing?.sm || 8,
  },
  previewSectionsTitle: {
    fontSize: theme.typography?.caption?.fontSize || 14,
    fontWeight: '600',
    color: theme.colors?.text || '#1F2937',
    marginBottom: theme.spacing?.xs || 4,
  },
  previewSectionItem: {
    fontSize: theme.typography?.caption?.fontSize || 14,
    color: theme.colors?.textSecondary || '#64748B',
    marginLeft: theme.spacing?.sm || 8,
    marginBottom: theme.spacing?.xs || 4,
  },
});

export default AgreementProposalModal; 