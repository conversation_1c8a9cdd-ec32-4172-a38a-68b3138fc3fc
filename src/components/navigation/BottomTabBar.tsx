import React, { useEffect, useMemo } from 'react';

import { useRouter, usePathname } from 'expo-router';
import {
  Home,
  Search,
  Plus,
  MessageSquare,
  Heart,
  Settings,
  Compass,
  Wrench,
  User,

} from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface CustomTabBarProps extends Partial<BottomTabBarProps> {
  filterInfo?: {
    priceRange?: [number, number];
    neighborhood?: string;
  };
}

export default function BottomTabBar(props: CustomTabBarProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const insets = useSafeAreaInsets();
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useAuthCompat();
  const user = authState.user; // Access user through authState

  // Memoize user existence check to prevent unnecessary re-renders
  const isAuthenticated = useMemo(() => !!user, [user]);

  // Extract the active route name
  const getActiveRouteName = (pathname: string) => {
    if (pathname === '/' || pathname.includes('/index')) return 'index';
    if (pathname.includes('/saved')) return 'saved';
    if (pathname.includes('/create')) return 'create';
    if (pathname.includes('/services')) return 'services';
    if (pathname.includes('/messages')) return 'messages';
    if (pathname.includes('/profile')) return 'profile';

    return 'index';
  };

  const activeRoute = getActiveRouteName(pathname);

  const onTabPress = (route: string) => {
    // Special handling for create tab when in different contexts
    if (route === 'create') {
      // If we're in the messages section, create a new chat instead
      if (pathname.includes('/messages')) {
        router.push('/chat/new' as any);
        return;
      }
      
      // Always allow navigation to create screen - let the screen handle auth
      router.push('/(tabs)/create');
      return;
    }

    // For all other tabs
    router.push(`/${route === 'index' ? '' : route}` as any);
  };

  // These are our tab definitions
  const tabs = [
    {
      name: 'index',
      label: 'Home',
      icon: (active: boolean) => <Home size={24} color={active ? theme.colors.primary : theme.colors.textSecondary} />,
    },
    {
      name: 'services',
      label: 'Services',
      icon: (active: boolean) => <Wrench size={24} color={active ? theme.colors.primary : theme.colors.textSecondary} />,
    },
    {
      name: 'create',
      label: 'Post',
      icon: () => (
        <View style={styles.createButtonContainer}>
          <Plus size={24} color={theme.colors.background} />
        </View>
      ),
    },
    {
      name: 'saved',
      label: 'Saved',
      icon: (active: boolean) => <Heart size={24} color={active ? theme.colors.primary : theme.colors.textSecondary} />,
    },
    {
      name: 'messages',
      label: 'Messages',
      icon: (active: boolean) => <MessageSquare size={24} color={active ? theme.colors.primary : theme.colors.textSecondary} />,
    },
    {
      name: 'profile',
      label: 'Profile',
      icon: (active: boolean) => <User size={24} color={active ? theme.colors.primary : theme.colors.textSecondary} />,
    },

  ];

  return (
    <View
      style={[
        styles.container,
        {
          height: insets.bottom > 0 ? 80 : 60,
          paddingBottom: insets.bottom,
        },
      ]}
    >
      {tabs.map(tab => {
        const isActive = activeRoute === tab.name;

        return (
          <TouchableOpacity
            key={tab.name}
            style={styles.tab}
            onPress={() => onTabPress(tab.name)}
            accessibilityRole="button"
            accessibilityLabel={tab.label}
            accessibilityState={{ selected: isActive }}
          >
            {tab.icon(isActive)}
            <Text
              style={[
                styles.tabLabel,
                isActive ? styles.tabLabelActive : null,
                tab.name === 'create' ? styles.createLabel : null,
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 8,
    width: '100%',
    justifyContent: 'space-between',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  tabLabel: {
    fontSize: 9,
    marginTop: 2,
    color: theme.colors.textSecondary,
  },
  tabLabelActive: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  createButtonContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Platform.OS === 'ios' ? -15 : -20,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  createLabel: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
});
