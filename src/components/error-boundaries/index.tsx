/**
 * Error Boundaries Index
 *
 * Centralized exports for all error boundary components and utilities.
 * Provides convenient access to error handling infrastructure.
 */

import React, { ReactNode } from 'react';

// Core error boundary components
export { GlobalErrorBoundary } from './GlobalErrorBoundary';
export { FeatureErrorBoundary, withFeatureErrorBoundary } from './FeatureErrorBoundary';
export { AsyncErrorBoundary, withAsyncErrorBoundary } from './AsyncErrorBoundary';

// Error boundary manager and services
export { errorBoundaryManager, ErrorBoundaryManager } from '@core/services/ErrorBoundaryManager';

// Error boundary interfaces and types
export type {
  ErrorBoundaryState,
  ErrorInfo,
  EnhancedErrorDetails,
  ErrorBoundaryProps,
  ErrorBoundaryConfig,
  ErrorMetrics,
  ErrorClassifier,
  ErrorRecoveryStrategy,
  ErrorReporter,
  ErrorBoundaryAnalytics,
  IErrorBoundaryManager,
} from '@core/interfaces/IErrorBoundary';

// Import components for JSX usage
import { GlobalErrorBoundary } from './GlobalErrorBoundary';
import { FeatureErrorBoundary } from './FeatureErrorBoundary';
import { AsyncErrorBoundary } from './AsyncErrorBoundary';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

/**
 * Wrap app with comprehensive error boundaries
 */
export function withErrorBoundaries(
  children: ReactNode,
  options?: {
    enableGlobal?: boolean;
    enableAsync?: boolean;
    enableDeveloperMode?: boolean;
    enableErrorReporting?: boolean;
    enableAutoRecovery?: boolean;
    maxRetries?: number;
  }
): ReactNode {
  const {
    enableGlobal = true,
    enableAsync = true,
    enableDeveloperMode = process.env.NODE_ENV === 'development',
    enableErrorReporting = true,
    enableAutoRecovery = true,
    maxRetries = 3,
  } = options || {};

  let wrappedChildren = children;

  // Wrap with async error boundary first (innermost)
  if (enableAsync) {
    wrappedChildren = (
      <AsyncErrorBoundary
        enableRetry={true}
        enableReporting={enableErrorReporting}
        maxRetries={maxRetries}
      >
        {wrappedChildren}
      </AsyncErrorBoundary>
    );
  }

  // Wrap with global error boundary (outermost)
  if (enableGlobal) {
    wrappedChildren = (
      <GlobalErrorBoundary
        enableDeveloperMode={enableDeveloperMode}
        enableErrorReporting={enableErrorReporting}
        enableAutoRecovery={enableAutoRecovery}
        maxRetries={maxRetries}
      >
        {wrappedChildren}
      </GlobalErrorBoundary>
    );
  }

  return wrappedChildren;
}

/**
 * Create a feature-specific error boundary wrapper
 */
export function createFeatureErrorBoundary(
  featureName: string,
  options?: {
    fallbackComponent?: ReactNode;
    enableRetry?: boolean;
    enableReporting?: boolean;
    maxRetries?: number;
    onError?: (error: Error, errorInfo: any) => void;
    onRecover?: () => void;
  }
) {
  return function FeatureWrapper({ children }: { children: ReactNode }) {
    return (
      <FeatureErrorBoundary
        featureName={featureName}
        fallbackComponent={options?.fallbackComponent}
        enableRetry={options?.enableRetry}
        enableReporting={options?.enableReporting}
        maxRetries={options?.maxRetries}
        onError={options?.onError}
        onRecover={options?.onRecover}
      >
        {children}
      </FeatureErrorBoundary>
    );
  };
}

/**
 * Error boundary configuration presets
 */
export const ErrorBoundaryPresets = {
  /**
   * Development preset - verbose error reporting and debugging
   */
  development: {
    enableGlobal: true,
    enableAsync: true,
    enableDeveloperMode: true,
    enableErrorReporting: true,
    enableAutoRecovery: false, // Disable auto-recovery in dev for debugging
    maxRetries: 5,
  },

  /**
   * Production preset - user-friendly error handling
   */
  production: {
    enableGlobal: true,
    enableAsync: true,
    enableDeveloperMode: false,
    enableErrorReporting: true,
    enableAutoRecovery: true,
    maxRetries: 3,
  },

  /**
   * Testing preset - minimal error boundaries for testing
   */
  testing: {
    enableGlobal: false,
    enableAsync: false,
    enableDeveloperMode: false,
    enableErrorReporting: false,
    enableAutoRecovery: false,
    maxRetries: 1,
  },

  /**
   * Critical features preset - aggressive error handling
   */
  critical: {
    enableGlobal: true,
    enableAsync: true,
    enableDeveloperMode: false,
    enableErrorReporting: true,
    enableAutoRecovery: true,
    maxRetries: 5,
  },
};

/**
 * Get error boundary preset based on environment
 */
export function getErrorBoundaryPreset() {
  if (process.env.NODE_ENV === 'development') {
    return ErrorBoundaryPresets.development;
  }

  if (process.env.NODE_ENV === 'test') {
    return ErrorBoundaryPresets.testing;
  }

  return ErrorBoundaryPresets.production;
}

/**
 * Initialize error boundaries with preset configuration
 */
export function initializeErrorBoundaries(
  children: ReactNode,
  preset?: keyof typeof ErrorBoundaryPresets
): ReactNode {
  const config = preset ? ErrorBoundaryPresets[preset] : getErrorBoundaryPreset();
  return withErrorBoundaries(children, config);
}

/**
 * Error boundary utilities
 */
export const ErrorBoundaryUtils = {
  /**
   * Check if error boundaries are properly configured
   */
  validateConfiguration(): boolean {
    try {
      // Check if error boundary manager is available
      if (!errorBoundaryManager) {
        console.warn('Error boundary manager not available');
        return false;
      }

      // Check if global error handlers are set up
      const hasUnhandledRejectionHandler =
        typeof window !== 'undefined' && window.onunhandledrejection !== null;
      const hasErrorHandler = typeof window !== 'undefined' && window.onerror !== null;

      if (!hasUnhandledRejectionHandler || !hasErrorHandler) {
        console.warn('Global error handlers not properly configured');
      }

      return true;
    } catch (error) {
      console.error('Error boundary configuration validation failed:', error);
      return false;
    }
  },

  /**
   * Get error boundary statistics
   */
  async getStatistics() {
    try {
      return await errorBoundaryManager.getErrorStats();
    } catch (error) {
      console.error('Failed to get error boundary statistics:', error);
      return null;
    }
  },

  /**
   * Clear error history
   */
  clearErrorHistory() {
    try {
      errorBoundaryManager.clearErrorHistory();
    } catch (error) {
      console.error('Failed to clear error history:', error);
    }
  },

  /**
   * Test error boundary functionality
   */
  testErrorBoundary(boundaryType: 'global' | 'feature' | 'async' = 'global') {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Error boundary testing is only available in development mode');
      return;
    }

    try {
      switch (boundaryType) {
        case 'global':
          throw new Error('Test error for global error boundary');
        case 'feature':
          throw new Error('Test error for feature error boundary');
        case 'async':
          Promise.reject(new Error('Test async error for async error boundary'));
          break;
        default:
          throw new Error('Test error for default error boundary');
      }
    } catch (error) {
      console.log('Error boundary test triggered:', error);
    }
  },

  /**
   * Create error boundary HOC for components
   */
  withErrorBoundary: <P extends object>(
    Component: React.ComponentType<P>,
    errorBoundaryProps?: {
      fallbackComponent?: ReactNode;
      enableRetry?: boolean;
      enableReporting?: boolean;
      maxRetries?: number;
      onError?: (error: Error, errorInfo: any) => void;
      onRecover?: () => void;
    }
  ) => {
    return React.forwardRef<any, P>((props, ref) => (
      <FeatureErrorBoundary
        featureName={Component.displayName || Component.name || 'Component'}
        {...errorBoundaryProps}
      >
        <Component {...props} ref={ref} />
      </FeatureErrorBoundary>
    ));
  },

  /**
   * Create async error boundary HOC for async components
   */
  withAsyncErrorBoundary: <P extends object>(
    Component: React.ComponentType<P>,
    errorBoundaryProps?: {
      enableRetry?: boolean;
      enableReporting?: boolean;
      maxRetries?: number;
      onError?: (error: Error, errorInfo: any) => void;
      onRecover?: () => void;
    }
  ) => {
    return React.forwardRef<any, P>((props, ref) => (
      <AsyncErrorBoundary {...errorBoundaryProps}>
        <Component {...props} ref={ref} />
      </AsyncErrorBoundary>
    ));
  },
};

/**
 * Error boundary hooks for functional components
 */
export const useErrorBoundary = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};

/**
 * Hook for error reporting
 */
export const useErrorReporting = () => {
  const reportError = React.useCallback((error: Error, context?: any) => {
    try {
      errorBoundaryManager.reportError(error, context);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }, []);

  return { reportError };
};

/**
 * Default export for convenience
 */
export default {
  withErrorBoundaries,
  createFeatureErrorBoundary,
  initializeErrorBoundaries,
  ErrorBoundaryPresets,
  ErrorBoundaryUtils,
  useErrorBoundary,
  useErrorReporting,
};
