import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { AlertTriangle, RefreshCw, ChevronDown, ChevronUp, Bug } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { ErrorFallbackProps } from './types';

export const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  resetError,
  retryCount,
  maxRetries,
  errorId,
  showDetails = false,
  boundaryName,
}) => {
  const { colors, isDark } = useTheme();
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);

    // Add a small delay to show the loading state
    setTimeout(() => {
      resetError();
      setIsRetrying(false);
    }, 1000);
  };

  const canRetry = retryCount < maxRetries;

  const renderErrorDetails = () => {
    if (!showErrorDetails || !error) return null;

    return (
      <View style={[styles.detailsContainer, { backgroundColor: colors.surface }]}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>Error Details</Text>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Error ID:</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>{errorId}</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Boundary:</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>{boundaryName}</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Retry Count:</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {retryCount}/{maxRetries}
          </Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Error Message:</Text>
          <ScrollView style={styles.errorMessageContainer} nestedScrollEnabled>
            <Text style={[styles.errorMessage, { color: colors.error }]}>{error.message}</Text>
          </ScrollView>
        </View>

        {error.stack && (
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Stack Trace:</Text>
            <ScrollView style={styles.stackTraceContainer} nestedScrollEnabled>
              <Text style={[styles.stackTrace, { color: colors.textTertiary }]}>{error.stack}</Text>
            </ScrollView>
          </View>
        )}

        {errorInfo?.componentStack && (
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Component Stack:
            </Text>
            <ScrollView style={styles.stackTraceContainer} nestedScrollEnabled>
              <Text style={[styles.stackTrace, { color: colors.textTertiary }]}>
                {errorInfo.componentStack}
              </Text>
            </ScrollView>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.content, { backgroundColor: colors.surface }]}>
        {/* Error Icon */}
        <View style={[styles.iconContainer, { backgroundColor: colors.errorLight }]}>
          <AlertTriangle size={32} color={colors.error} />
        </View>

        {/* Error Message */}
        <Text style={[styles.title, { color: colors.text }]}>Something went wrong</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          We encountered an unexpected error. Don't worry, this has been reported and we're working
          on a fix.
        </Text>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {canRetry && (
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.primary }]}
              onPress={handleRetry}
              disabled={isRetrying}
            >
              <RefreshCw
                size={20}
                color={colors.textInverse}
                style={isRetrying ? { transform: [{ rotate: '180deg' }] } : {}}
              />
              <Text style={[styles.retryButtonText, { color: colors.textInverse }]}>
                {isRetrying ? 'Retrying...' : `Try Again (${maxRetries - retryCount} left)`}
              </Text>
            </TouchableOpacity>
          )}

          {!canRetry && (
            <View style={[styles.maxRetriesContainer, { backgroundColor: colors.errorLight }]}>
              <Text style={[styles.maxRetriesText, { color: colors.error }]}>
                Maximum retry attempts reached. Please refresh the app.
              </Text>
            </View>
          )}
        </View>

        {/* Error Details Toggle */}
        {(showDetails || process.env.NODE_ENV === 'development') && (
          <TouchableOpacity
            style={[styles.detailsToggle, { borderColor: colors.border }]}
            onPress={() => setShowErrorDetails(!showErrorDetails)}
          >
            <Bug size={16} color={colors.textSecondary} />
            <Text style={[styles.detailsToggleText, { color: colors.textSecondary }]}>
              {showErrorDetails ? 'Hide' : 'Show'} Error Details
            </Text>
            {showErrorDetails ? (
              <ChevronUp size={16} color={colors.textSecondary} />
            ) : (
              <ChevronDown size={16} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
        )}

        {/* Error Details */}
        {renderErrorDetails()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  maxRetriesContainer: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  maxRetriesText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  detailsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderRadius: 6,
    gap: 6,
    marginBottom: 16,
  },
  detailsToggleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  detailsContainer: {
    width: '100%',
    padding: 16,
    borderRadius: 8,
    maxHeight: 300,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailItem: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  errorMessageContainer: {
    maxHeight: 60,
    borderRadius: 4,
    padding: 8,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  errorMessage: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  stackTraceContainer: {
    maxHeight: 100,
    borderRadius: 4,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  stackTrace: {
    fontSize: 10,
    fontFamily: 'monospace',
    lineHeight: 14,
  },
});
