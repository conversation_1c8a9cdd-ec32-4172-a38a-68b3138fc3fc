import React from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Text } from '@components/ui';
import { AlertCircle, Clock, MessageCircle } from 'lucide-react-native';
import { Dispute } from '@utils/agreement';
import { useNavigation } from '@react-navigation/native';
import { useColorFix } from '@hooks/useColorFix';
;

interface DisputeListProps {
  disputes: Dispute[];
  isLoading: boolean;
  error: string | null;
  onSelectDispute: (disputeId: string) => void;
}

export default function DisputeList({
  disputes,
  isLoading,
  error,
  onSelectDispute,
}: DisputeListProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusInfo = (status: string): { color: string; label: string } => {
    switch (status) {
      case 'open':
        return { color: theme.colors.warning, label: 'Open' };
      case 'in_progress':
        return { color: theme.colors.primary, label: 'In Progress' };
      case 'resolved':
        return { color: theme.colors.success, label: 'Resolved' };
      case 'closed':
        return { color: '#6B7280', label: 'Closed' };
      case 'escalated':
        return { color: theme.colors.error, label: 'Escalated' };
      default:
        return { color: '#6B7280', label: 'Unknown' };
    }
  };

  if (isLoading) {
    return (
      <View style={styles.centeredContainer}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading disputes...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centeredContainer}>
        <AlertCircle size={40} color={fix(theme.colors.error, theme.colors.error)} />
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  }

  if (disputes.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No disputes have been raised for this agreement.</Text>
      </View>
    );
  }

  const renderDisputeItem = ({ item }: { item: Dispute }) => {
    const statusInfo = getStatusInfo(item.status);

    return (
      <TouchableOpacity style={styles.disputeCard} onPress={() => onSelectDispute(item.id)}>
        <View style={styles.disputeHeader}>
          <Text style={styles.disputeTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: `${statusInfo.color}20` }]}>
            <Text style={[styles.statusText, { color: statusInfo.color }]}>{statusInfo.label}</Text>
          </View>
        </View>

        <Text style={styles.disputeDescription} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.disputeFooter}>
          <View style={styles.metadataContainer}>
            <Clock size={14} color="#6B7280" />
            <Text style={styles.metadataText}>{formatDate(item.created_at)}</Text>
          </View>

          <View style={styles.metadataContainer}>
            <MessageCircle size={14} color="#6B7280" />
            <Text style={styles.metadataText}>
              {item.message_count || 0} message{item.message_count !== 1 ? 's' : ''}
            </Text>
          </View>

          <Text style={styles.raisedByText}>by {item.raised_by_name || 'Unknown'}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      data={disputes}
      renderItem={renderDisputeItem}
      keyExtractor={item => item.id}
      contentContainerStyle={styles.list}
      showsVerticalScrollIndicator={false}
    />
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  list: {
    padding: 16,
  },
  disputeCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  disputeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  disputeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  disputeDescription: {
    fontSize: 14,
    color: '#475569',
    marginBottom: 12,
    lineHeight: 20,
  },
  disputeFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  metadataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metadataText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  raisedByText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    padding: 20,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  errorText: {
    marginTop: 12,
    fontSize: 14,
    color: theme.colors.error,
    textAlign: 'center',
  },
});
