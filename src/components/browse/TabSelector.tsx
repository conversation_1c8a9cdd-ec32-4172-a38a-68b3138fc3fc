import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { BrowseTabType } from '@hooks/useBrowseData';
import { ListingType } from '@hooks/useListingsController';

export interface TabItem {
  id: string;
  label: string;
}

interface TabSelectorProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  tabs?: TabItem[];
}

const TabSelector: React.FC<TabSelectorProps> = ({ 
  activeTab, 
  onTabChange,
  tabs = [
    { id: 'room', label: 'Rooms' },
    { id: 'housemate', label: 'Housemates' }
  ]
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.tabs}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={[styles.tab, activeTab === tab.id && styles.activeTab]}
          onPress={() => onTabChange(tab.id)}
        >
          <Text style={[styles.tabText, activeTab === tab.id && styles.activeTabText]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  tabs: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center'
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary
  },
  tabText: {
    fontSize: 16,
    color: theme.colors.textMuted
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: '500'
  }
});

// Use React.memo to prevent unnecessary re-renders
export default memo(TabSelector);
