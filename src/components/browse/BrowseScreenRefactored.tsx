import React, { useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { useSafeAreaInsets, SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { BrowseTabType } from '@hooks/useBrowseData';
import Pagination from '@components/common/Pagination';
import { useBrowseScreenController } from '@/controllers/BrowseScreenController';
import { createLogger } from '@utils/loggerUtils';
// Debug utilities removed for production
import { ErrorBoundary, withErrorBoundary } from '@components/common/ErrorBoundary';
import DataErrorFallback from '@components/common/DataErrorFallback';
import NetworkErrorFallback from '@components/common/NetworkErrorFallback';
import useErrorHandler from '@hooks/useErrorHandler';
import { ErrorCategory } from '@utils/errorDetectionUtils';

// Import optimized components
import RoomListItem from '@components/browse/RoomListItem';
import HousemateListItem from '@components/browse/HousemateListItem';
import TabSelector from '@components/browse/TabSelector';
import { UnifiedSearchInterface } from '@components/search/UnifiedSearchInterface';
import ProfileCompletionBanner from '@components/browse/ProfileCompletionBanner';
import WelcomeHeader from '@components/browse/WelcomeHeader';

/**
 * BrowseScreen Component
 * Main entry point for the app, shows the Browse screen with toggle navigation
 * between room and housemate listings
 */
export const BrowseScreenRefactoredComponent: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const logger = createLogger('BrowseScreenRefactored');
  const perfMonitor = createPerformanceMonitor('BrowseScreen');
  const router = useRouter();
  
  // Error handling
  const { 
    error: errorState, 
    errorCategory, 
    hasError, 
    handleError, 
    resetError, 
    tryCatch 
  } = useErrorHandler({ componentName: 'BrowseScreenRefactored' });
  const insets = useSafeAreaInsets();
  const { tabParam } = useLocalSearchParams<{ tabParam: BrowseTabType }>();

  // Use the controller to manage state and business logic
  const {
    activeTab,
    isLoading,
    refreshing,
    error: controllerError,
    roomListings,
    housemateListings,
    currentPage,
    totalPages,
    hasMorePages,
    userName,
    completionPercentage,
    showCompletionCard,
    handleTabChange,
    handleRefresh,
    handlePageChange,
    handleMessageRoom,
    handleMessageHousemate,
    handleLikeHousemate,
    handleCompleteProfile,
    handleSearchPress: handleSearch,
    handleFilterPress: handleFilter,
    handleLoadMore
  } = useBrowseScreenController();

  // Log component render for debugging
  useEffect(() => {
    debug.renderLog('BrowseScreenRefactored', { activeTab, isLoading, itemCount: activeTab === 'room' ? roomListings.length : housemateListings.length });
  }, [activeTab, isLoading, roomListings.length, housemateListings.length]);
  
  // Handle controller errors
  useEffect(() => {
    if (controllerError) {
      handleError(new Error(controllerError));
    }
  }, [controllerError, handleError]);

  // Memoized callbacks for optimized rendering
  const onTabChange = useCallback((tab: string) => {
    // Convert the string tab to BrowseTabType for internal use
    const browseTab = tab as BrowseTabType;
    const endTimer = perfMonitor.start(`Tab change to ${tab}`);
    tryCatch(() => handleTabChange(browseTab), `Failed to change tab to ${tab}`);
    endTimer();
  }, [handleTabChange, perfMonitor, tryCatch]);

  const memoizedHandleRefresh = useCallback(() => {
    resetError(); // Clear any existing errors
    tryCatch(() => handleRefresh(), 'Failed to refresh data');
  }, [handleRefresh, resetError, tryCatch]);

  const memoizedHandleMessageRoom = useCallback((roomId: string, ownerId: string) => {
    return tryCatch(() => handleMessageRoom(roomId, ownerId), 'Failed to start conversation with room owner');
  }, [handleMessageRoom, tryCatch]);

  const memoizedHandleMessageHousemate = useCallback((housemateId: string, name: string) => {
    return tryCatch(() => handleMessageHousemate(housemateId, name), 'Failed to start conversation with housemate');
  }, [handleMessageHousemate, tryCatch]);

  const memoizedHandleSearch = useCallback(() => {
    tryCatch(() => handleSearch(), 'Failed to search');
  }, [handleSearch, tryCatch]);

  const memoizedHandleLikeHousemate = useCallback((housemateId: string) => {
    return tryCatch(() => handleLikeHousemate(housemateId), 'Failed to like housemate');
  }, [handleLikeHousemate, tryCatch]);

  // Initialize the controller with tab parameter if provided
  useEffect(() => {
    if (tabParam && tabParam !== activeTab) {
      handleTabChange(tabParam as BrowseTabType);
    }
  }, [tabParam, handleTabChange, activeTab]);

  // Memoized empty state components to prevent re-renders
  const EmptyRoomState = useMemo(() => (
    <View style={{alignItems: 'center', justifyContent: 'center', padding: 32}}>
      <Text style={styles.emptyText}>No room listings found</Text>
      <Text style={styles.emptyText}>Try adjusting your filters or check back later</Text>
    </View>
  ), []);

  const EmptyHousemateState = useMemo(() => (
    <View style={{alignItems: 'center', justifyContent: 'center', padding: 32}}>
      <Text style={styles.emptyText}>No housemates found</Text>
      <Text style={styles.emptyText}>Try adjusting your filters or check back later</Text>
    </View>
  ), []);

  // Memoized loading footer component
  const LoadingFooter = useMemo(() => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="small" color={theme.colors.primary} />
      <Text style={styles.loadingText}>Loading more...</Text>
    </View>
  ), [theme.colors.primary]);

  // Render appropriate error UI based on error category
  if (hasError) {
    switch (errorCategory) {
      case ErrorCategory.NETWORK:
        return (
          <NetworkErrorFallback
            resetError={resetError}
            retry={memoizedHandleRefresh}
            isFullScreen={true}
          />
        );
      default:
        return (
          <DataErrorFallback
            error={errorState}
            resetError={resetError}
            retry={memoizedHandleRefresh}
            isFullScreen={true}
          />
        );
    }
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top + 16 }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={memoizedHandleRefresh} />
        }
      >
        {/* Welcome Header */}
        <WelcomeHeader userName={userName} variant="orange" />

        {/* Profile Completion Card */}
        <ProfileCompletionBanner
          userName={userName}
          completionPercentage={completionPercentage}
          onPress={handleCompleteProfile}
          showCompletionCard={showCompletionCard}
        />

        {/* Unified Search Interface */}
        <View style={styles.searchContainer}>
          <UnifiedSearchInterface
            initialSearchType={activeTab === 'room' ? 'rooms' : 'housemates'}
            embedded={true}
            showFilters={true}
            onResultSelect={(result, type) => {
              if (type === 'room') {
                router.push(`/rooms/${result.id}`);
              } else {
                router.push(`/profile/${result.id}`);
              }
            }}
          />
        </View>

        {/* Tab Selector */}
        <TabSelector
          activeTab={activeTab}
          onTabChange={onTabChange}
        />

        {/* Recommended Section */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            {activeTab === 'room' ? 'Featured Places' : 'Featured Roommates'}
          </Text>
        </View>

        {/* Loading Indicator */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        )}

        {/* Error State */}
        {controllerError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{controllerError}</Text>
          </View>
        )}

        {/* Room Listings */}
        {!isLoading && activeTab === 'room' ? (
          <View style={styles.listingsContainer}>
            {roomListings.length === 0 ? (
              <Text style={styles.emptyText}>No rooms available at the moment.</Text>
            ) : (
              <FlatList
                data={roomListings}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <RoomListItem 
                    room={item} 
                    onMessagePress={memoizedHandleMessageRoom} 
                  />
                )}
                refreshControl={
                  <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
                }
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                ListEmptyComponent={!isLoading ? EmptyRoomState : null}
                ListFooterComponent={hasMorePages && !isLoading ? LoadingFooter : null}
              />
            )}
          </View>
        ) : (
          <View style={styles.listingsContainer}>
            {housemateListings.length === 0 ? (
              <Text style={styles.emptyText}>No potential roommates found.</Text>
            ) : (
              <FlatList
                data={housemateListings}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <HousemateListItem 
                    housemate={item} 
                    onMessagePress={memoizedHandleMessageHousemate} 
                    onLikePress={memoizedHandleLikeHousemate} 
                  />
                )}
                refreshControl={
                  <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
                }
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                ListEmptyComponent={!isLoading ? EmptyHousemateState : null}
                ListFooterComponent={hasMorePages && !isLoading ? LoadingFooter : null}
              />
            )}
          </View>
        )}

        {/* Pagination Controls */}
        {!isLoading && (activeTab === 'room' ? roomListings.length > 0 : housemateListings.length > 0) && (
          <View style={{marginTop: 16, marginBottom: 24}}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background
  },
  searchContainer: {
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs,
  },
  welcomeHeader: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textMuted
  },
  completionCard: {
          backgroundColor: theme.colors.primarySurface,
    borderRadius: theme.borderRadius.lg,
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md
  },
  completionContent: {
    flex: 1
  },
  completionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    marginBottom: 4
  },
  completionSubtitle: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginBottom: theme.spacing.xs
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden'
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 3
  },
  searchFilterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md
  },
  searchInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 10,
    flex: 1,
    marginRight: theme.spacing.sm
  },
  searchPlaceholder: {
    marginLeft: theme.spacing.xs,
    color: theme.colors.textMuted,
    fontSize: 14
  },
  filterButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    padding: 10,
  },
  optionsContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.md,
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    padding: 4,
  },
  optionButton: {
    flex: 1,
    paddingVertical: theme.spacing.xs,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeOption: {
    backgroundColor: theme.colors.surface,
    ...theme.shadows.sm,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textMuted,
  },
  activeOptionText: {
    color: theme.colors.primary,
  },
  sectionHeader: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.xs,
    fontSize: 14,
    color: theme.colors.textMuted,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  errorText: {
    fontSize: 14,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: 6,
  },
  retryText: {
    color: theme.colors.surface,
    fontWeight: '500',
  },
  listingsContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.lg,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 14,
    color: theme.colors.textMuted,
    paddingVertical: theme.spacing.xl,
  },
});

// Use React.memo to prevent unnecessary re-renders
// Wrap component with error boundary for component-level errors
const BrowseScreenRefactored = withErrorBoundary(BrowseScreenRefactoredComponent, {
  onError: (error, errorInfo) => {
    const logger = createLogger('BrowseScreenErrorBoundary');
    logger.error('Unhandled error in BrowseScreen', error, { errorInfo });
  },
});

export default React.memo(BrowseScreenRefactored);
