import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, AccessibilityRole } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ProfileCompletionBannerProps {
  userName?: string;
  completionPercentage: number;
  onPress: () => void;
  showCompletionCard?: boolean;
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
}

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({
  userName = 'there',
  completionPercentage,
  onPress,
  showCompletionCard = true,
  accessible = true,
  accessibilityLabel,
  accessibilityHint = 'Opens the profile completion screen',
  accessibilityRole = 'button' as AccessibilityRole
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  if (!showCompletionCard) return null;

  return (
    <TouchableOpacity 
      style={styles.completionCard} 
      onPress={onPress}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel || `Profile ${completionPercentage}% complete. Tap to complete your profile.`}
      accessibilityHint={accessibilityHint}
      accessibilityRole={accessibilityRole}
    >
      <View style={styles.completionContent}>
        <Text style={styles.completionTitle}>Complete your profile, {userName}!</Text>
        <Text style={styles.completionSubtitle}>
          {completionPercentage}% complete - Finish to get more matches
        </Text>
        <View style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressBar, 
              { width: `${completionPercentage}%` }
            ]} 
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  completionCard: {
    backgroundColor: theme.colors.primarySurface,
    borderRadius: theme.borderRadius.md,
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
    // ACCESSIBILITY: Ensure minimum touch target height (44px for WCAG 2.1 AA compliance)
    minHeight: 44,
    padding: theme.spacing.md
  },
  completionContent: {
    flex: 1
  },
  completionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    marginBottom: 4
  },
  completionSubtitle: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginBottom: theme.spacing.xs
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    overflow: 'hidden'
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 3
  }
});

// Use React.memo to prevent unnecessary re-renders
export default memo(ProfileCompletionBanner);
