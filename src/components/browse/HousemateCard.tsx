import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { MapPin, MessageSquare, Users } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { HousemateListing } from '@hooks/useBrowseData';

type HousemateCardProps = {
  housemate: HousemateListing;
  onLike?: (id: string) => void;
  onMessage?: (id: string, name: string) => void;
  isFeatured?: boolean;
};

const HousemateCard: React.FC<HousemateCardProps> = ({ 
  housemate, 
  onLike,
  onMessage,
  isFeatured = false 
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const handlePress = () => {
    // Navigate to the profile screen using string-based navigation
    const queryParams = new URLSearchParams({ id: String(housemate.id) });
    router.push(`/(tabs)/profile?${queryParams.toString()}`);
  };

  const handleLike = () => {
    if (onLike) {
      onLike(housemate.id);
    }
  };

  const handleMessage = () => {
    // If onMessage handler is provided, use it
    if (onMessage) {
      const userName = `${housemate.first_name || ''} ${housemate.last_name || ''}`.trim() || 'User';
      onMessage(housemate.id, userName);
    } else {
      // Fallback to direct navigation if no handler
      const queryParams = new URLSearchParams();
      queryParams.set('recipientId', housemate.id);
      router.push(`/chat/new?${queryParams.toString()}`);
    }
  };

  // Default avatar if none is provided
  const avatarUrl = housemate.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1287&q=80';

  // Only show first 3 interests
  const displayInterests = housemate.interests?.slice(0, 3) || [];

  // Calculate a background color based on compatibility score
  const getCompatibilityColor = (score?: number) => {
    if (!score) return theme.colors.disabled;
    if (score >= 90) return theme.colors.scales.success[600];
    if (score >= 75) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  };

  const compatibilityColor = getCompatibilityColor(housemate.compatibility_score);

  return (
    <TouchableOpacity 
      style={[styles.container, isFeatured && styles.featuredContainer]} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: avatarUrl }} style={styles.avatar} />
          {isFeatured && (
            <View style={styles.featuredBadge}>
              <Text style={styles.featuredText}>Featured</Text>
            </View>
          )}
        </View>

        {housemate.compatibility_score && (
          <View style={[styles.compatibilityBadge, { backgroundColor: compatibilityColor }]}>
            <Text style={styles.compatibilityText}>{housemate.compatibility_score}% Match</Text>
          </View>
        )}
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.name}>
          {housemate.first_name} {housemate.last_name?.[0] || ''}
        </Text>

        {housemate.occupation && (
          <Text style={styles.occupation}>{housemate.occupation}</Text>
        )}
        
        {housemate.location && (
          <View style={styles.locationContainer}>
            <MapPin size={14} color={theme.colors.textMuted} />
            <Text style={styles.locationText}>{housemate.location}</Text>
          </View>
        )}

        {housemate.bio && (
          <Text style={styles.bio} numberOfLines={2}>
            {housemate.bio}
          </Text>
        )}

        {displayInterests.length > 0 && (
          <View style={styles.interestsContainer}>
            {displayInterests.map((interest, index) => (
              <View key={index} style={styles.interestChip}>
                <Text style={styles.interestText}>{interest}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.actionContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, styles.messageButton]} 
            onPress={handleMessage}
          >
            <MessageSquare size={16} color={theme.colors.surface} />
            <Text style={styles.actionButtonText}>Message</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.connectButton]} 
            onPress={handleLike}
          >
            <Users size={16} color={theme.colors.surface} />
            <Text style={styles.actionButtonText}>Connect</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    width: Dimensions.get('window').width * 0.9,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
    overflow: 'hidden',
  },
  featuredContainer: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  header: {
    position: 'relative',
  },
  avatarContainer: {
    height: 200,
    width: '100%',
  },
  avatar: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
  },
  featuredBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.pill,
  },
  featuredText: {
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 12,
  },
  compatibilityBadge: {
    position: 'absolute',
    bottom: -15,
    right: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.pill,
  },
  compatibilityText: {
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 14,
  },
  contentContainer: {
    padding: theme.spacing.md,
    paddingTop: 20,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  occupation: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  locationText: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginLeft: 4,
  },
  bio: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginBottom: theme.spacing.sm,
    lineHeight: 20,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.md,
  },
  interestChip: {
    backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.pill,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  interestText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    flex: 1,
    marginHorizontal: 4,
  },
  messageButton: {
    backgroundColor: theme.colors.primary,
  },
  connectButton: {
    backgroundColor: theme.colors.success,
  },
  actionButtonText: {
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: theme.spacing.sm,
  },
});

export default HousemateCard;
