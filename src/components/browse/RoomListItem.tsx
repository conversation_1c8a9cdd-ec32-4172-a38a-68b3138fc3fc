import React, { memo } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { RoomListing } from '@hooks/useBrowseData';

interface RoomListItemProps {
  room: RoomListing;
  onMessagePress: (roomId: string, ownerId: string) => Promise<void>;
}

const RoomListItem: React.FC<RoomListItemProps> = ({ room, onMessagePress }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => {/* Navigate to room detail */}}>
        <View style={styles.card}>
          <Image 
            source={{uri: 'https://via.placeholder.com/300x200'}} 
            style={styles.image}
            resizeMode="cover"
          />
          <View style={styles.content}>
            <Text style={styles.title}>{room.title}</Text>
            <Text style={styles.price}>${room.price}/month</Text>
            <Text style={styles.location}>{room.location}</Text>
            <TouchableOpacity 
              style={styles.messageButton}
              onPress={() => onMessagePress(room.id, room.owner_id)}
            >
              <Text style={styles.messageButtonText}>Message</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  image: {
    width: '100%',
    height: 180
  },
  content: {
    padding: theme.spacing.md
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs
  },
  location: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginBottom: theme.spacing.sm
  },
  messageButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
    alignSelf: 'flex-start'
  },
  messageButtonText: {
    color: theme.colors.surface,
    fontWeight: '500'
  }
});

// Use React.memo to prevent unnecessary re-renders
export default memo(RoomListItem);
