import React, { useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, ScrollView, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets, SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { withErrorBoundary } from '@components/common/ErrorBoundary';
import { createLogger } from '@utils/loggerUtils';
// Debug utilities removed for production
import useErrorHandler from '@hooks/useErrorHandler';
// Search and Sliders icons removed - now using UnifiedSearchInterface

// Import shared components
import ListingCard from '@components/common/ListingCard';
import TabSelector from '@components/browse/TabSelector';
import AdvancedSearchFilters from '@components/search/AdvancedSearchFilters';
import { UnifiedSearchInterface } from '@components/search/UnifiedSearchInterface';
import { useRealTimeSearch } from '@hooks/useRealTimeSearch';
import ProfileCompletionBanner from '@components/browse/ProfileCompletionBanner';
import WelcomeHeader from '@components/browse/WelcomeHeader';
import Pagination from '@components/common/Pagination';
import { ErrorState, EmptyState, LoadingFooter } from '@components/common/ListingStateHandlers';
import { ListingCardSkeleton, HousemateCardSkeleton, ProfileCompletionSkeleton } from '@components/common/SkeletonLoaders';

// Import controller
import { useListingsController, ListingType } from '@hooks/useListingsController';

// SearchFilterBar component removed - now using UnifiedSearchInterface

/**
 * UnifiedListingScreen Component
 * A flexible component that can be used for both Browse and Search screens
 * with configurable behavior and appearance
 */
export interface UnifiedListingScreenProps {
  screenType?: 'browse' | 'search';
  showWelcomeHeader?: boolean;
  showCompletionBanner?: boolean;
  showTabSelector?: boolean;
  showSearchFilter?: boolean;
  initialListingType?: ListingType;
  title?: string;
}

export const UnifiedListingScreenComponent: React.FC<UnifiedListingScreenProps> = ({
  screenType = 'browse',
  showWelcomeHeader = true,
  showCompletionBanner = true,
  showTabSelector = true,
  showSearchFilter = true,
  initialListingType = 'room',
  title = 'Browse'
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const logger = createLogger(`UnifiedListingScreen:${screenType}`);
  const perfMonitor = createPerformanceMonitor(`UnifiedListingScreen:${screenType}`);
  const router = useRouter();
  
  // Error handling
  const { 
    error: errorState, 
    errorCategory, 
    hasError, 
    handleError, 
    resetError, 
    tryCatch 
  } = useErrorHandler({ componentName: `UnifiedListingScreen:${screenType}` });
  
  const insets = useSafeAreaInsets();
  const { tabParam } = useLocalSearchParams<{ tabParam: ListingType }>();

  // Use the controller to manage state and business logic
  const {
    activeType,
    isLoading,
    refreshing,
    error: controllerError,
    listings,
    userName,
    completionPercentage,
    showCompletionCard,
    currentPage,
    totalPages,
    hasMorePages,
    handleTypeChange,
    handleRefresh,
    handlePageChange,
    handleMessageRoom,
    handleMessageHousemate,
    handleLikeHousemate,
    handleCompleteProfile,
    handleSearchPress,
    handleFilterPress,
    handleLoadMore,
    initialize
  } = useListingsController(initialListingType);

  // Initialize with tab parameter if provided
  useEffect(() => {
    initialize(tabParam as ListingType);
  }, [tabParam, initialize]);

  // Log component render for debugging
  useEffect(() => {
    debug.renderLog(`UnifiedListingScreen:${screenType}`, { 
      activeType, 
      isLoading, 
      itemCount: listings.length 
    });
  }, [activeType, isLoading, listings.length, screenType]);
  
  // Handle controller errors
  useEffect(() => {
    if (controllerError) {
      handleError(new Error(controllerError));
    }
  }, [controllerError, handleError]);

  // Memoized callbacks for optimized rendering
  const onTypeChange = useCallback((tab: string) => {
    const endTimer = perfMonitor.start(`Tab change to ${tab}`);
    tryCatch(() => handleTypeChange(tab as ListingType), `Failed to change tab to ${tab}`);
    endTimer();
  }, [handleTypeChange, perfMonitor, tryCatch]);

  const memoizedHandleRefresh = useCallback(() => {
    resetError(); // Clear any existing errors
    tryCatch(() => handleRefresh(), 'Failed to refresh data');
  }, [handleRefresh, resetError, tryCatch]);

  const memoizedHandleMessageRoom = useCallback((roomId: string, ownerId: string, roomTitle: string) => {
    return tryCatch(() => handleMessageRoom(roomId, ownerId, roomTitle), 'Failed to start conversation with room owner');
  }, [handleMessageRoom, tryCatch]);

  const memoizedHandleMessageHousemate = useCallback((housemateId: string, ownerId: string, name: string) => {
    return tryCatch(() => handleMessageHousemate(housemateId, name), 'Failed to start conversation with housemate');
  }, [handleMessageHousemate, tryCatch]);

  const memoizedHandleLikeHousemate = useCallback((housemateId: string) => {
    return tryCatch(() => handleLikeHousemate(housemateId), 'Failed to like housemate');
  }, [handleLikeHousemate, tryCatch]);

  const memoizedHandleCompleteProfile = useCallback(() => {
    tryCatch(() => handleCompleteProfile(), 'Failed to navigate to profile');
  }, [handleCompleteProfile, tryCatch]);

  // Render list items based on active type
  const renderListItem = useCallback(({ item }: { item: any }) => {
    if (activeType === 'room') {
      return (
        <ListingCard
          id={item.id}
          title={item.title || 'Unnamed Room'}
          subtitle={item.room_type}
          description={item.description}
          imageUrl={item.images && item.images.length > 0 ? item.images[0] : undefined}
          price={item.price}
          location={item.location}
          rating={item.rating || 4.8}
          badges={[{ text: item.room_type || 'Room', color: theme.colors.surfaceVariant }]}
          isFeatured={item.is_featured}
          type="room"
          ownerId={item.owner_id}
          onMessagePress={memoizedHandleMessageRoom}
        />
      );
    } else {
      return (
        <ListingCard
          id={item.id}
          title={item.first_name || 'Unnamed User'}
          subtitle={item.occupation || 'Not specified'}
          description={item.bio || 'No bio provided'}
          imageUrl={item.avatar_url}
          location={item.location}
          badges={[
            { text: `${item.age || '?'} years`, color: theme.colors.surfaceVariant },
            ...(item.interests || []).slice(0, 2).map((interest: string) => ({ 
              text: interest, 
              color: theme.colors.primaryVariant 
            }))
          ]}
          type="housemate"
          ownerId={item.id}
          onMessagePress={memoizedHandleMessageHousemate}
          onLikePress={memoizedHandleLikeHousemate}
        />
      );
    }
  }, [activeType, memoizedHandleMessageRoom, memoizedHandleMessageHousemate, memoizedHandleLikeHousemate]);

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={memoizedHandleRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Welcome Header */}
        {showWelcomeHeader && (
          <WelcomeHeader 
            userName={userName}
            subtitle={`Find your ideal ${activeType === 'room' ? 'room' : 'roommate'}`}
          />
        )}

        {/* Profile Completion Banner */}
        {showCompletionBanner && (
          isLoading ? (
            <ProfileCompletionSkeleton />
          ) : showCompletionCard && (
            <ProfileCompletionBanner
              completionPercentage={completionPercentage}
              onPress={memoizedHandleCompleteProfile}
              accessible={true}
              accessibilityLabel={`Profile ${completionPercentage}% complete. Tap to complete your profile.`}
              accessibilityHint="Opens the profile completion screen"
            />
          )
        )}

        {/* Unified Search Interface */}
        {showSearchFilter && (
          <View style={styles.searchContainer}>
            <UnifiedSearchInterface
              initialSearchType={activeType === 'room' ? 'rooms' : 'housemates'}
              embedded={true}
              showFilters={true}
              onResultSelect={(result, type) => {
                if (type === 'room') {
                  router.push(`/rooms/${result.id}`);
                } else {
                  router.push(`/profile/${result.id}`);
                }
              }}
            />
          </View>
        )}

        {/* Tab Selector */}
        {showTabSelector && (
          <TabSelector
            activeTab={activeType}
            onTabChange={onTypeChange}
            tabs={[
              { id: 'room', label: 'Rooms' },
              { id: 'housemate', label: 'Housemates' }
            ]}
          />
        )}

        {/* Section Title */}
        {!showTabSelector && title && (
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
        )}

        {/* Content Area */}
        {hasError ? (
          <ErrorState error={errorState || 'An error occurred'} onRetry={memoizedHandleRefresh} />
        ) : isLoading && listings.length === 0 ? (
          // Use skeleton loaders instead of simple loading spinner
          <View style={styles.skeletonContainer}>
            {Array(3).fill(0).map((_, index) => (
              <React.Fragment key={`skeleton-${index}`}>
                {activeType === 'room' ? (
                  <ListingCardSkeleton />
                ) : (
                  <HousemateCardSkeleton />
                )}
              </React.Fragment>
            ))}
          </View>
        ) : listings.length === 0 ? (
          <EmptyState 
            title={`No ${activeType}s found`} 
            message={`We couldn't find any ${activeType}s matching your criteria. Try adjusting your filters or check back later.`}
            onRefresh={memoizedHandleRefresh}
            accessible={true}
            accessibilityLabel={`No ${activeType}s found. Try adjusting your filters.`}
          />
        ) : (
          <View style={styles.listingsContainer}>
            <FlatList
              data={listings}
              renderItem={renderListItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              ListFooterComponent={hasMorePages && !isLoading ? LoadingFooter : null}
            />
          </View>
        )}

        {/* Pagination Controls */}
        {!isLoading && listings.length > 0 && (
          <View style={{marginTop: 16, marginBottom: 24}}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background
  },
  searchContainer: {
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs,
  },
  sectionHeader: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  listingsContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.lg,
  },
  skeletonContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.lg,
  }
});

// Use withErrorBoundary for component-level errors
const UnifiedListingScreen = withErrorBoundary(UnifiedListingScreenComponent, {
  onError: (error, errorInfo) => {
    const logger = createLogger('UnifiedListingScreenErrorBoundary');
    logger.error('Unhandled error in UnifiedListingScreen', error, { errorInfo });
  },
});

export default React.memo(UnifiedListingScreen);
