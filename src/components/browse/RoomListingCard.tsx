import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { MapPin, Star, DollarSign, MessageCircle } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { RoomListing } from '@hooks/useBrowseData';
import RoomChatService, { RoomChatContext } from '@services/rooms/RoomChatService';
import { Alert } from 'react-native';

type RoomListingCardProps = {
  listing: RoomListing;
  isFeatured?: boolean;
  onMessagePress?: (roomId: string, ownerId: string) => void;
};

const RoomListingCard: React.FC<RoomListingCardProps> = ({ listing, isFeatured = false, onMessagePress }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { user } = useSupabaseUser();

  const handlePress = () => {
    // Use a simpler path format compatible with Expo Router
    router.push(`/(tabs)/rooms/${listing.id}`);
  };
  
  const handleMessagePress = async () => {
    if (!user) {
      // Redirect to login if not logged in
      router.push('/login?redirectTo=%2Fbrowse');
      return;
    }

    if (onMessagePress) {
      onMessagePress(listing.id, listing.owner_id);
      return;
    }

    try {
      // Create room chat context
      const roomContext: RoomChatContext = {
        roomId: listing.id,
        roomTitle: listing.title,
        roomPrice: listing.price,
        roomLocation: listing.location,
        ownerId: listing.owner_id,
        ownerName: listing.owner_name || 'Room Owner',
        roomImages: listing.images,
        roomType: listing.room_type
      };

      // Create room chat with enhanced context
      const result = await RoomChatService.createRoomChat(
        user.id,
        roomContext,
        {
          includeRoomDetails: true,
          autoNavigateToChat: true,
          initialMessage: `Hi! I'm interested in your room listing "${listing.title}". Is it still available?`
        }
      );

      if (!result.success) {
        Alert.alert(
          'Chat Error',
          result.error || 'Failed to start conversation. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error starting room chat:', error);
      Alert.alert(
        'Chat Error',
        'Failed to start conversation. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Default image if none is provided
  const imageUrl = listing.images && listing.images.length > 0 
    ? listing.images[0]
    : 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80';

  return (
    <TouchableOpacity 
      style={[styles.container, isFeatured && styles.featuredContainer]} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUrl }} style={styles.image} />
        {isFeatured && (
          <View style={styles.featuredBadge}>
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}
        <View style={styles.priceBadge}>
          <DollarSign size={14} color={theme.colors.surface} />
          <Text style={styles.priceText}>{listing.price}/month</Text>
        </View>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={1}>
          {listing.title}
        </Text>
        
        <View style={styles.locationContainer}>
          <MapPin size={14} color={theme.colors.textMuted} />
          <Text style={styles.locationText} numberOfLines={1}>
            {listing.location}
          </Text>
        </View>

        <View style={styles.detailsContainer}>
          <Text style={styles.propertyType}>{listing.room_type || 'Room'}</Text>
          
          <View style={styles.ratingContainer}>
            <Star size={14} fill={theme.colors.warning} color={theme.colors.warning} />
            <Text style={styles.ratingText}>4.8</Text>
          </View>
        </View>

        {/* Enhanced Message button with room context */}
        <TouchableOpacity
          style={styles.messageButton}
          onPress={handleMessagePress}
          activeOpacity={0.7}
        >
          <MessageCircle size={16} color={theme.colors.surface} />
          <Text style={styles.messageButtonText}>Message Owner</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    width: Dimensions.get('window').width * 0.9,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
    overflow: 'hidden',
  },
  // Message button styles
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    marginTop: theme.spacing.sm,
  },
  messageButtonText: {
    color: theme.colors.surface,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  featuredContainer: {
    borderWidth: 1,
    borderColor: theme.colors.warning,
  },
  imageContainer: {
    position: 'relative',
    height: 180,
    width: '100%',
  },
  image: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
  },
  featuredBadge: {
    position: 'absolute',
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    backgroundColor: theme.colors.warning,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 6,
    borderRadius: 20,
  },
  featuredText: {
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 12,
  },
  priceBadge: {
    position: 'absolute',
    bottom: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: theme.colors.success,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  priceText: {
    color: theme.colors.surface,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  contentContainer: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  locationText: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginLeft: 4,
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  propertyType: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 4,
    borderRadius: theme.borderRadius.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default RoomListingCard;
