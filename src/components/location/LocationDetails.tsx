import React from 'react';
import * as Linking from 'expo-linking';
import { MapPin, Navigation } from 'lucide-react-native';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import type { LocationData } from '@services/LocationService';

interface LocationDetailsProps {
  location: LocationData;
  distance?: number;
  style?: object;
}

export default function LocationDetails({
  location, 
  distance, 
  style 
}: LocationDetailsProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const handleOpenMaps = () => {
    if (location.latitude && location.longitude) {
      const url = `https://maps.google.com/maps?q=${location.latitude},${location.longitude}`;
      Linking.openURL(url);
    } else {
      // If no coordinates, search by address
      const query = encodeURIComponent(
        `${location.name}, ${location.neighborhood || ''} ${location.city}, ${location.state || ''} ${location.postal_code || ''} ${location.country}`
      );
      const url = `https://maps.google.com/maps?q=${query}`;
      Linking.openURL(url);
    }
  };

  const formatAddress = () => {
    const parts = [];
    if (location.name) {
      parts.push(location.name);
    }
    if (location.neighborhood) {
      parts.push(location.neighborhood);
    }
    if (location.city) {
      parts.push(location.city);
    }
    if (location.state) {
      parts.push(location.state);
    }
    if (location.postal_code) {
      parts.push(location.postal_code);
    }

    return parts.join(', ');
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.headerContainer}>
        <View style={styles.iconTextContainer}>
          <MapPin size={18} color={theme.colors.primary} />
          <Text style={[styles.title, { color: theme.colors.text }]}>Location</Text>
        </View>
        {distance !== undefined && (
          <View style={[styles.distanceBadge, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.distanceText, { color: theme.colors.primary }]}>{distance.toFixed(1)} miles away</Text>
          </View>
        )}
      </View>

      <Text style={[styles.address, { color: theme.colors.textSecondary }]}>{formatAddress()}</Text>

      {location.description && <Text style={[styles.description, { color: theme.colors.textSecondary }]}>{location.description}</Text>}

      <TouchableOpacity style={[styles.directionsButton, { backgroundColor: theme.colors.primary }]} onPress={handleOpenMaps}>
        <Navigation size={18} color="#FFFFFF" />
        <Text style={styles.directionsText}>Get Directions</Text>
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 6,
  },
  address: {
    fontSize: 15,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  directionsButton: {
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginTop: 8,
  },
  directionsText: {
    color: theme.colors.background,
    fontWeight: '500',
    marginLeft: 8,
    fontSize: 14,
  },
  distanceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
  },
  distanceText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
