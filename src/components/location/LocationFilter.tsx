import React, { useState, useEffect } from 'react';

import { X, MapPin, Navigation } from 'lucide-react-native';
import { StyleSheet, View, Text, TouchableOpacity, Modal, FlatList } from 'react-native';
// Define Region type for expo-maps
type Region = {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
};

import Slider from '@components/common/Slider';
import type { LocationData } from '@services/LocationService';
import { locationService } from '@services/LocationService';

import LocationSearch from '@components/location/LocationSearch';
import MapViewComponent from '@components/location/MapView';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface LocationFilterProps {
  visible: boolean;
  onClose: () => void;
  onApply: (params: { location?: LocationData; radius?: number }) => void;
  initialValues?: {
    location?: LocationData;
    radius?: number;
  };
}

export default function LocationFilter({
  visible,
  onClose,
  onApply,
  initialValues = {},
}: LocationFilterProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | undefined>(
    initialValues.location
  );
  const [radius, setRadius] = useState(initialValues.radius || 5);
  const [popularLocations, setPopularLocations] = useState<LocationData[]>([]);
  const [mapRegion, setMapRegion] = useState<Region | undefined>(undefined);

  useEffect(() => {
    loadPopularLocations();
  }, []);

  useEffect(() => {
    // Update map region when location changes
    if (selectedLocation?.latitude && selectedLocation?.longitude) {
      setMapRegion({
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
    }
  }, [selectedLocation]);

  const loadPopularLocations = async () => {
    try {
      const locations = await locationService.getAllLocations();
      setPopularLocations(locations.slice(0, 5));
    } catch (error) {
      console.error('Error loading popular locations:', error);
    }
  };

  const handleApply = () => {
    onApply({
      location: selectedLocation,
      radius: radius,
    });
    onClose();
  };

  const renderPopularLocationItem = ({ item }: { item: LocationData }) => {
    return (
      <TouchableOpacity
        style={[styles.popularItem, selectedLocation?.id === item.id && styles.selectedPopularItem]}
        onPress={() => setSelectedLocation(item)}
      >
        <MapPin size={16} color={selectedLocation?.id === item.id ? theme.colors.background : '#6366F1'} />
        <Text
          style={[
            styles.popularItemText,
            selectedLocation?.id === item.id && styles.selectedPopularItemText,
          ]}
        >
          {item.neighborhood || item.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={false} onRequestClose={onClose}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Location Filter</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color="#1F2937" />
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={styles.sectionTitle}>Search Location</Text>
          <LocationSearch
            onSelectLocation={setSelectedLocation}
            placeholder="Search neighborhoods, cities..."
            style={styles.searchInput}
          />

          {popularLocations.length > 0 && (
            <View style={styles.popularSection}>
              <Text style={styles.sectionTitle}>Popular Locations</Text>
              <FlatList
                data={popularLocations}
                renderItem={renderPopularLocationItem}
                keyExtractor={item => item.id}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.popularList}
              />
            </View>
          )}

          <View style={styles.radiusSection}>
            <Text style={styles.sectionTitle}>Distance Radius</Text>
            <Slider
              value={radius}
              onValueChange={setRadius}
              minimumValue={1}
              maximumValue={50}
              step={1}
              minimumTrackTintColor="#6366F1"
              maximumTrackTintColor="#E5E7EB"
              thumbTintColor="#6366F1"
            />
            <Text style={styles.radiusValue}>{radius} miles</Text>
          </View>

          {selectedLocation?.latitude && selectedLocation?.longitude && (
            <View style={styles.mapSection}>
              <Text style={styles.sectionTitle}>Map Preview</Text>
              <MapViewComponent
                initialRegion={mapRegion}
                markers={[
                  {
                    id: selectedLocation.id,
                    coordinate: {
                      latitude: selectedLocation.latitude,
                      longitude: selectedLocation.longitude,
                    },
                    title: selectedLocation.name,
                    description: [selectedLocation.neighborhood, selectedLocation.city]
                      .filter(Boolean)
                      .join(', '),
                  },
                ]}
                showUserLocation={true}
                style={styles.map}
              />
            </View>
          )}
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.resetButton}
            onPress={() => {
              setSelectedLocation(undefined);
              setRadius(5);
            }}
          >
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
            <Text style={styles.applyText}>Apply Filter</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  searchInput: {
    marginBottom: 24,
  },
  popularSection: {
    marginBottom: 24,
  },
  popularList: {
    paddingVertical: 8,
    paddingRight: 20,
  },
  popularItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  selectedPopularItem: {
    backgroundColor: '#6366F1',
  },
  popularItemText: {
    fontSize: 14,
    color: '#4F46E5',
    fontWeight: '500',
    marginLeft: 6,
  },
  selectedPopularItemText: {
    color: theme.colors.background,
  },
  radiusSection: {
    marginBottom: 24,
  },
  radiusValue: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
  },
  mapSection: {
    marginBottom: 24,
  },
  map: {
    height: 200,
    borderRadius: 10,
    overflow: 'hidden',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  resetButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginRight: 8,
  },
  resetText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4B5563',
  },
  applyButton: {
    flex: 2,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366F1',
    borderRadius: 8,
    marginLeft: 8,
  },
  applyText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.background,
  },
});
