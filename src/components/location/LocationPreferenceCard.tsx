import React from 'react';

import { MapPin, X, Home, Briefcase, BookOpen, HeartHandshake } from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import type { UserLocationPreference } from '@services/LocationService';

// Preference type icons and labels

const PREFERENCE_TYPES = [
  { id: 'home', label: 'Home', icon: Home },
  { id: 'work', label: 'Work', icon: Briefcase },
  { id: 'study', label: 'Study', icon: BookOpen },
  { id: 'interest', label: 'Interest', icon: HeartHandshake },
];

interface LocationPreferenceCardProps {
  preference: UserLocationPreference;
  onDelete: (id: string) => void;
}

export default function LocationPreferenceCard({
  preference,
  onDelete,
}: LocationPreferenceCardProps) {
  const { colors } = useTheme();

  // Get the appropriate icon for the preference type
  const getPreferenceTypeIcon = (type: string) => {
    const preference = PREFERENCE_TYPES.find(p => p.id === type);
    if (!preference) {
      return MapPin;
    }
    return preference.icon;
  };

  // Get the preference type label
  const getPreferenceTypeLabel = (type: string) => {
    const preference = PREFERENCE_TYPES.find(p => p.id === type);
    return preference?.label || type;
  };

  const IconComponent = getPreferenceTypeIcon(preference.preference_type);

  return (
    <View style={[styles.preferenceItem, { borderColor: colors.gray[200] }]}>
      <View style={styles.preferenceHeader}>
        <View style={[styles.preferenceTypeTag, { backgroundColor: colors.primary[100] }]}>
          <IconComponent size={14} color={colors.primary[700]} />
          <Text style={[styles.preferenceTypeText, { color: colors.primary[700] }]}>
            {getPreferenceTypeLabel(preference.preference_type)}
          </Text>
        </View>

        {preference.is_primary && (
          <View style={[styles.primaryTag, { backgroundColor: colors.success[100] }]}>
            <Text style={[styles.primaryTagText, { color: colors.success[700] }]}>Primary</Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => preference.id && onDelete(preference.id)}
        >
          <X size={16} color={colors.error[500]} />
        </TouchableOpacity>
      </View>

      <View style={styles.locationContainer}>
        <MapPin size={16} color={colors.gray[500]} style={styles.locationIcon} />
        <View style={styles.locationDetails}>
          <Text style={[styles.locationName, { color: colors.gray[900] }]}>
            {preference.location?.name}
          </Text>
          <Text style={[styles.locationAddress, { color: colors.gray[600] }]}>
            {preference.location?.neighborhood && `${preference.location.neighborhood}, `}
            {preference.location?.city}
            {preference.location?.state ? `, ${preference.location.state}` : ''}
          </Text>
        </View>
      </View>

      {preference.commute_distance_miles && (
        <View style={styles.commuteContainer}>
          <Text style={[styles.commuteText, { color: colors.gray[700] }]}>
            Max commute: {preference.commute_distance_miles} miles
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  preferenceItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  preferenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  preferenceTypeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  preferenceTypeText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  primaryTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  primaryTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  locationIcon: {
    marginTop: 2,
    marginRight: 8,
  },
  locationDetails: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 14,
  },
  commuteContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
  },
  commuteText: {
    fontSize: 14,
  },
});
