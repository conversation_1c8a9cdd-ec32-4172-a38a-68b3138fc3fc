import React from 'react';

import { MapPin, Navigation } from 'lucide-react-native';
import { View, Text, StyleSheet } from 'react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import type { LocationCompatibility } from '@services/LocationService';

interface LocationCompatibilityCardProps {
  compatibility: LocationCompatibility;
}

export function LocationCompatibilityCard({ compatibility }: LocationCompatibilityCardProps) {
  const { colors } = useTheme();

  // If no matching locations, show minimal card
  if (!compatibility.matchingLocations.length) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: colors.gray[50], borderColor: colors.gray[100] },
        ]}
      >
        <View style={styles.header}>
          <MapPin size={20} color={colors.gray[500]} />
          <Text style={[styles.title, { color: colors.gray[500] }]}>Location Compatibility</Text>
        </View>

        <Text style={[styles.emptyText, { color: colors.gray[500] }]}>
          No shared location preferences found.
        </Text>
      </View>
    );
  }

  // Determine color based on score
  const getScoreColor = () => {
    if (compatibility.score >= 80) {
      return colors.success;
    }
    if (compatibility.score >= 60) {
      return colors.primary;
    }
    if (compatibility.score >= 40) {
      return colors.warning;
    }
    return colors.error;
  };

  const scoreColor = getScoreColor();

  return (
    <View
      style={[styles.container, { backgroundColor: scoreColor[50], borderColor: scoreColor[100] }]}
    >
      <View style={styles.header}>
        <MapPin size={20} color={scoreColor[600]} />
        <Text style={[styles.title, { color: scoreColor[700] }]}>Location Compatibility</Text>

        <View style={[styles.scoreContainer, { backgroundColor: scoreColor[100] }]}>
          <Text style={[styles.score, { color: scoreColor[800] }]}>{compatibility.score}%</Text>
        </View>
      </View>

      {compatibility.matchingLocations.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: scoreColor[700] }]}>Shared Locations</Text>

          {compatibility.matchingLocations.map((location, index) => (
            <View key={`loc-${index}`} style={styles.locationItem}>
              <Navigation size={16} color={scoreColor[500]} style={styles.locationIcon} />
              <Text style={[styles.locationText, { color: colors.gray[800] }]}>{location}</Text>
            </View>
          ))}
        </View>
      )}

      {compatibility.matchReasons.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: scoreColor[700] }]}>Match Details</Text>

          {compatibility.matchReasons.map((reason, index) => (
            <Text key={`reason-${index}`} style={[styles.reasonText, { color: colors.gray[700] }]}>
              • {reason}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  scoreContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  score: {
    fontSize: 14,
    fontWeight: '700',
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  section: {
    marginTop: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationIcon: {
    marginRight: 8,
  },
  locationText: {
    fontSize: 14,
  },
  reasonText: {
    fontSize: 14,
    marginBottom: 6,
    lineHeight: 20,
  },
});

export default LocationCompatibilityCard;
