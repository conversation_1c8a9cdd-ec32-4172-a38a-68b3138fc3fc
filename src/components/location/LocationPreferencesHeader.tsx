import React from 'react';

import { ChevronLeft, Plus } from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface LocationPreferencesHeaderProps {
  onBack: () => void;
  onAdd: () => void;
}

export default function LocationPreferencesHeader({
  onBack,
  onAdd,
}: LocationPreferencesHeaderProps) {
  const { colors } = useTheme();

  return (
    <View style={[styles.header, { backgroundColor: colors.white }]}>
      <TouchableOpacity onPress={onBack} style={styles.backButton}>
        <ChevronLeft size={24} color={colors.gray[800]} />
      </TouchableOpacity>

      <Text style={[styles.title, { color: colors.gray[900] }]}>Location Preferences</Text>

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary[100] }]}
        onPress={onAdd}
      >
        <Plus size={20} color={colors.primary[600]} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  addButton: {
    padding: 8,
    borderRadius: 8,
  },
});
