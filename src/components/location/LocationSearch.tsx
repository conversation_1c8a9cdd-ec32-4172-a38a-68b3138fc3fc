import React, { useState, useEffect } from 'react';
import { MapPin, Search } from 'lucide-react-native';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import type { LocationData } from '@services/LocationService';
import { locationService } from '@services/LocationService';

interface LocationSearchProps {
  onSelectLocation: (location: LocationData) => void;
  placeholder?: string;
  style?: object;
}

export default function LocationSearch({
  onSelectLocation,
  placeholder = 'Search neighborhoods, cities...',
  style,
}: LocationSearchProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<LocationData[]>([]);

  useEffect(() => {
    // Load recent searches on mount
    loadRecentSearches();
  }, []);

  useEffect(() => {
    if (query.length > 2) {
      searchLocations();
    } else {
      setResults([]);
    }
  }, [query]);

  const loadRecentSearches = async () => {
    // In a real app, you might want to store recent searches in AsyncStorage
    try {
      const locations = await locationService.getAllLocations();
      setRecentSearches(locations.slice(0, 5));
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  const searchLocations = async () => {
    setLoading(true);
    try {
      const locations = await locationService.searchLocations(query);
      setResults(locations);
    } catch (error) {
      console.error('Error searching locations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectLocation = (location: LocationData) => {
    onSelectLocation(location);

    // Add to recent searches (avoiding duplicates)
    setRecentSearches(prev => {
      const exists = prev.some(item => item.id === location.id);
      if (exists) {
        return prev;
      }
      return [location, ...prev].slice(0, 5);
    });
  };

  const renderLocationItem = ({ item }: { item: LocationData }) => {
    const formattedAddress = [item.neighborhood, item.city, item.state].filter(Boolean).join(', ');

    return (
      <TouchableOpacity style={[styles.resultItem, { borderBottomColor: theme.colors.border }]} onPress={() => handleSelectLocation(item)}>
        <MapPin size={18} color={theme.colors.primary} />
        <View style={styles.locationInfo}>
          <Text style={[styles.locationName, { color: theme.colors.text }]}>{item.name}</Text>
          <Text style={[styles.locationAddress, { color: theme.colors.textSecondary }]}>{formattedAddress}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
        <Search size={20} color={theme.colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          style={[styles.input, { color: theme.colors.text }]}
          placeholder={placeholder}
          value={query}
          onChangeText={setQuery}
          placeholderTextColor={theme.colors.textSecondary}
        />
        {loading && <ActivityIndicator size="small" color={theme.colors.primary} />}
      </View>

      {query.length > 0 ? (
        <ScrollView 
          style={styles.resultsList}
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {results.length > 0 ? (
            results.map((item) => (
              <View key={item.id}>
                {renderLocationItem({ item })}
              </View>
            ))
          ) : (
            !loading && (
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>No locations found. Try a different search term.</Text>
            )
          )}
        </ScrollView>
      ) : recentSearches.length > 0 ? (
        <View style={styles.recentContainer}>
          <Text style={[styles.recentTitle, { color: theme.colors.textSecondary }]}>Recent Searches</Text>
          <ScrollView 
            style={styles.resultsList}
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {recentSearches.map((item) => (
              <View key={item.id}>
                {renderLocationItem({ item })}
              </View>
            ))}
          </ScrollView>
        </View>
      ) : null}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    width: '100%',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
  },
  resultsList: {
    paddingVertical: 8,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  locationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  locationName: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 13,
  },
  recentContainer: {
    marginTop: 16,
  },
  recentTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 16,
    marginBottom: 8,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 14,
    marginTop: 16,
  },
});
