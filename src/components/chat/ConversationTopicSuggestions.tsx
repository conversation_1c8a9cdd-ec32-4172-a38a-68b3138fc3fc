import React from 'react';

import { Lightbulb, X, ArrowUpRight } from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList } from 'react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ConversationTopicSuggestionsProps {
  suggestions: string[];
  onSelect: (suggestion: string) => void;
  onClose: () => void;
  title?: string;
}

export default function ConversationTopicSuggestions({
  suggestions,
  onSelect,
  onClose,
  title = 'Conversation Suggestions',
}: ConversationTopicSuggestionsProps) {
  const { colors } = useTheme();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Lightbulb size={20} color={colors.primary[500]} />
          <Text style={styles.title}>{title}</Text>
        </View>

        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <X size={20} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      {/* Suggestions */}
      {suggestions.length === 0 ? (
        <Text style={styles.emptyText}>No suggestions available at the moment.</Text>
      ) : (
        <FlatList
          data={suggestions}
          keyExtractor={(item, index) => `suggestion-${index}`}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.suggestionItem} onPress={() => onSelect(item)}>
              <Text style={styles.suggestionText}>{item}</Text>
              <View style={styles.useContainer}>
                <ArrowUpRight size={16} color={colors.primary[500]} />
                <Text style={styles.useText}>Use</Text>
              </View>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingBottom: 24,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginLeft: 8,
  },
  closeButton: {
    padding: 4,
  },
  listContent: {
    paddingTop: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  suggestionText: {
    flex: 1,
    fontSize: 16,
    color: '#334155',
    marginRight: 8,
  },
  useContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  useText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: '#6366F1',
  },
  emptyText: {
    fontSize: 16,
    color: '#94A3B8',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
});
