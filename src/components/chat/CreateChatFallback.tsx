import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';
import { ChatService } from '@/services/standardized/ChatService';
import {
  createMockChatRoom,
  shouldUseMockFallback,
  trackFailedOperation,
} from '@/utils/mockChatRooms';
import { logger } from '@/services/loggerService';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface CreateChatRoomProps {
  recipientId: string;
  onSuccess?: (roomId: string, isMock: boolean) => void;
  onError?: (error: Error) => void;
  context?: 'agreement' | 'match' | 'general';
  contextId?: string;
  navigate?: boolean;
  children: (props: {
    createChat: () => Promise<string | null>;
    isLoading: boolean;
    preferMockRooms: boolean;
  }) => React.ReactNode;
}

/**
 * A render prop component that handles chat room creation with automatic fallback
 * to mock chat rooms when the database operation fails.
 */
export default function CreateChatFallback({
  recipientId,
  onSuccess,
  onError,
  context = 'general',
  contextId,
  navigate = true,
  children,
}: CreateChatRoomProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [isLoading, setIsLoading] = useState(false);
  const [preferMockRooms, setPreferMockRooms] = useState(false);
  const { authState } = useAuth();
  const user = authState?.user;
  const router = useRouter();

  // Check if this user has previously had failures with chat rooms
  useEffect(() => {
    async function checkPreviousFailures() {
      if (user?.id) {
        const shouldUseMock = await shouldUseMockFallback(user.id, 'create_chat_room');
        setPreferMockRooms(shouldUseMock);

        if (shouldUseMock) {
          logger.info('Using mock rooms for this user based on previous failures', {
            component: 'CreateChatFallback',
            userId: user.id,
          });
        }
      }
    }

    checkPreviousFailures();
  }, [user?.id]);

  // Log context for consistent tracking
  const logContext = {
    component: 'CreateChatFallback',
    context,
    contextId,
    recipientId,
    preferMockRooms,
  };

  const createChat = async (): Promise<string | null> => {
    if (!user?.id) {
      const error = new Error('Missing user information');
      logger.error('Missing user information', { ...logContext, error });
      if (onError) onError(error);
      Alert.alert('Error', 'You must be logged in to create a chat room');
      return null;
    }

    if (!recipientId) {
      const error = new Error('Missing recipient ID');
      logger.error('Missing recipient ID', { ...logContext, error });
      if (onError) onError(error);
      Alert.alert('Error', 'Missing recipient information');
      return null;
    }

    setIsLoading(true);

    // If we already know this user has chat room issues, go straight to mock rooms
    if (preferMockRooms) {
      try {
        logger.info('Using mock room directly due to known database issues', logContext);

        const mockRoomId = await createMockChatRoom(user.id, recipientId);

        if (mockRoomId) {
          logger.info('Created mock room for user with known issues', {
            ...logContext,
            roomId: mockRoomId,
          });

          // Only show the developer mode warning the first time
          if (!(await shouldUseMockFallback(user.id, 'mock_warning_shown'))) {
            Alert.alert(
              'Developer Mode Chat',
              'A temporary chat has been created for development purposes. Messages may not be saved permanently.',
              [{ text: 'Continue', style: 'default' }]
            );
            await trackFailedOperation(user.id, 'mock_warning_shown');
          }

          // Handle navigation if requested
          if (navigate) {
            // Use query parameters instead of params object
            const queryParams = new URLSearchParams({
              roomId: String(mockRoomId),
              context: String(context),
              isMock: 'true',
            });
            
            if (contextId) {
              queryParams.append('contextId', String(contextId));
            }
            
            router.push(`/chat?${queryParams.toString()}`);
          }

          // Notify of success with mock flag
          if (onSuccess) {
            onSuccess(mockRoomId, true);
          }

          setIsLoading(false);
          return mockRoomId;
        }
      } catch (error) {
        logger.error('Error creating mock room for user with known issues', {
          ...logContext,
          error: error instanceof Error ? error.message : String(error),
        });
        // Continue to normal flow if mock creation fails
      }
    }

    try {
      logger.info('Attempting to create chat room', logContext);

      // Try to create a chat room using the ChatService
      const chatService = ChatService.getInstance();
      const chatRoom = await chatService.createChatRoom(user.id, [recipientId]);

      if (!chatRoom || !chatRoom.id) {
        throw new Error('Failed to create chat room');
      }

      logger.info('Successfully created chat room', { ...logContext, roomId: chatRoom.id });

      const isMockRoom = !!chatRoom.is_mock;

      if (isMockRoom) {
        logger.warn('Created mock room via ChatService', { ...logContext, roomId: chatRoom.id });
      }

      // Handle navigation if requested
      if (navigate) {
        // Use query parameters instead of params object
        const queryParams = new URLSearchParams({
          roomId: String(chatRoom.id),
          context: String(context),
          isMock: isMockRoom ? 'true' : 'false',
        });
        
        if (contextId) {
          queryParams.append('contextId', String(contextId));
        }
        
        router.push(`/chat?${queryParams.toString()}`);
      }

      // Notify of success
      if (onSuccess) {
        onSuccess(chatRoom.id, isMockRoom);
      }

      setIsLoading(false);
      return chatRoom.id;
    } catch (error) {
      logger.error('Error creating chat room', {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
      });

      // Track this failure for future reference
      await trackFailedOperation(user.id, 'create_chat_room');

      // Try to create a mock room as fallback
      try {
        logger.info('Attempting to create mock chat room as fallback', logContext);

        // Use our utility for mock chat rooms
        const mockRoomId = await createMockChatRoom(user.id, recipientId);

        if (mockRoomId) {
          logger.info('Successfully created mock room as fallback', {
            ...logContext,
            roomId: mockRoomId,
          });

          // Show a warning to the user about the fallback
          Alert.alert(
            'Developer Mode Chat',
            'A temporary chat has been created for development purposes. Messages may not be saved permanently.',
            [{ text: 'Continue', style: 'default' }]
          );

          // Handle navigation if requested
          if (navigate) {
            // Use query parameters instead of params object
            const queryParams = new URLSearchParams({
              roomId: String(mockRoomId),
              context: String(context),
              isMock: 'true',
            });
            
            if (contextId) {
              queryParams.append('contextId', String(contextId));
            }
            
            router.push(`/chat?${queryParams.toString()}`);
          }

          // Notify of success with mock flag
          if (onSuccess) {
            onSuccess(mockRoomId, true);
          }

          setIsLoading(false);
          return mockRoomId;
        }
      } catch (fallbackError) {
        logger.error('Fallback creation also failed', {
          ...logContext,
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError),
        });
      }

      // All attempts failed
      const finalError = error instanceof Error ? error : new Error(String(error));
      if (onError) onError(finalError);

      Alert.alert(
        'Chat Error',
        'Unable to create a chat room. Please try again later or contact support if this issue persists.',
        [{ text: 'OK' }]
      );

      setIsLoading(false);
      return null;
    }
  };

  return children({
    createChat,
    isLoading,
    preferMockRooms,
  });
}
