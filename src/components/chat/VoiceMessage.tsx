import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Audio, AVPlaybackStatus } from 'expo-av';
import { useTheme } from '@design-system/ThemeProvider';
import type { Theme } from '@design-system/ThemeProvider';

// Safe color utility to prevent [object Object] errors
const safeColor = (color: any): string => {
  if (typeof color === 'string') return color;
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected, converting to fallback:', color);
    return '#6366F1'; // Default primary color
  }
  return String(color);
};

// Safe color with opacity utility
const safeColorWithOpacity = (color: any, opacity: string): string => {
  const baseColor = safeColor(color);
  if (baseColor.startsWith('#')) {
    return `${baseColor}${opacity}`;
  }
  return baseColor;
};

interface VoiceMessageProps {
  messageId: string;
  audioUrl: string;
  duration: number; // in milliseconds
  isOwnMessage?: boolean;
  transcription?: string;
  waveform?: number[];
  onPlaybackStart?: () => void;
  onPlaybackEnd?: () => void;
  showTranscription?: boolean;
}

interface VoiceMessageState {
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  position: number;
  playbackRate: number;
}

export function VoiceMessage({
  messageId,
  audioUrl,
  duration,
  isOwnMessage = false,
  transcription,
  waveform,
  onPlaybackStart,
  onPlaybackEnd,
  showTranscription = false,
}: VoiceMessageProps): JSX.Element {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  // State management
  const [voiceState, setVoiceState] = useState<VoiceMessageState>({
    isPlaying: false,
    isPaused: false,
    isLoading: false,
    position: 0,
    playbackRate: 1.0,
  });
  
  const [showFullTranscription, setShowFullTranscription] = useState(false);
  
  // Animation values
  const playButtonScale = useRef(new Animated.Value(1)).current;
  const waveformProgress = useRef(new Animated.Value(0)).current;
  const transcriptionHeight = useRef(new Animated.Value(0)).current;
  
  // Audio management
  const soundRef = useRef<Audio.Sound | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (soundRef.current) {
        soundRef.current.unloadAsync();
      }
    };
  }, []);

  // Animation for transcription
  useEffect(() => {
    Animated.timing(transcriptionHeight, {
      toValue: showFullTranscription ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showFullTranscription]);

  const handlePlayPause = async (): Promise<void> => {
    try {
      animateButtonPress();
      
      if (!soundRef.current) {
        setVoiceState(prev => ({ ...prev, isLoading: true }));
        
        const { sound } = await Audio.Sound.createAsync(
          { uri: audioUrl },
          { shouldPlay: true, rate: voiceState.playbackRate },
          handlePlaybackStatusUpdate
        );
        
        soundRef.current = sound;
        onPlaybackStart?.();
        setVoiceState(prev => ({ ...prev, isPlaying: true, isLoading: false }));
      } else {
        if (voiceState.isPlaying) {
          await soundRef.current.pauseAsync();
          setVoiceState(prev => ({ ...prev, isPlaying: false, isPaused: true }));
        } else {
          await soundRef.current.playAsync();
          setVoiceState(prev => ({ ...prev, isPlaying: true, isPaused: false }));
        }
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      setVoiceState(prev => ({ ...prev, isLoading: false, isPlaying: false }));
    }
  };

  const handleStop = async (): Promise<void> => {
    try {
      if (soundRef.current) {
        await soundRef.current.stopAsync();
        await soundRef.current.setPositionAsync(0);
        setVoiceState(prev => ({ ...prev, isPlaying: false, isPaused: false, position: 0 }));
        onPlaybackEnd?.();
      }
    } catch (error) {
      console.error('Error stopping audio:', error);
    }
  };

  const handleSeek = async (percentage: number): Promise<void> => {
    try {
      if (soundRef.current) {
        const position = (percentage / 100) * duration;
        await soundRef.current.setPositionAsync(position);
        setVoiceState(prev => ({ ...prev, position }));
      }
    } catch (error) {
      console.error('Error seeking audio:', error);
    }
  };

  const handlePlaybackRateChange = async (): Promise<void> => {
    const rates = [1.0, 1.25, 1.5, 2.0];
    const currentIndex = rates.indexOf(voiceState.playbackRate);
    const nextRate = rates[(currentIndex + 1) % rates.length];
    
    try {
      if (soundRef.current) {
        await soundRef.current.setRateAsync(nextRate, true);
        setVoiceState(prev => ({ ...prev, playbackRate: nextRate }));
      }
    } catch (error) {
      console.error('Error changing playback rate:', error);
    }
  };

  const handlePlaybackStatusUpdate = (status: AVPlaybackStatus): void => {
    if (status.isLoaded) {
      setVoiceState(prev => ({
        ...prev,
        position: status.positionMillis || 0,
        isPlaying: status.isPlaying,
      }));
      
      // Update waveform progress
      const progress = duration > 0 ? (status.positionMillis || 0) / duration : 0;
      Animated.timing(waveformProgress, {
        toValue: progress,
        duration: 100,
        useNativeDriver: false,
      }).start();
      
      // Check if playback finished
      if (status.didJustFinish) {
        setVoiceState(prev => ({ ...prev, isPlaying: false, position: 0 }));
        onPlaybackEnd?.();
      }
    }
  };

  const toggleTranscription = (): void => {
    setShowFullTranscription(!showFullTranscription);
  };

  const animateButtonPress = (): void => {
    Animated.sequence([
      Animated.timing(playButtonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(playButtonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const formatTime = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = (): number => {
    return duration > 0 ? (voiceState.position / duration) * 100 : 0;
  };

  const renderWaveform = (): JSX.Element => {
    const { width } = Dimensions.get('window');
    const waveformWidth = width * 0.5;
    
    return (
      <View style={styles.waveformContainer}>
        <TouchableOpacity
          style={[styles.waveform, { width: waveformWidth }]}
          onPress={(event) => {
            const percentage = (event.nativeEvent.locationX / waveformWidth) * 100;
            handleSeek(percentage);
          }}
          accessibilityLabel="Seek audio"
        >
          <View style={styles.waveformBackground} />
          <Animated.View
            style={[
              styles.waveformProgress,
              {
                width: waveformProgress.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const renderPlayButton = (): JSX.Element => {
    const iconName = voiceState.isLoading
      ? 'ellipsis-horizontal'
      : voiceState.isPlaying
        ? 'pause'
        : 'play';

    return (
      <Animated.View style={{ transform: [{ scale: playButtonScale }] }}>
        <TouchableOpacity
          style={[
            styles.playButton,
            isOwnMessage ? styles.playButtonOwn : styles.playButtonOther,
          ]}
          onPress={handlePlayPause}
          disabled={voiceState.isLoading}
          accessibilityLabel={voiceState.isPlaying ? 'Pause' : 'Play'}
        >
          <Ionicons
            name={iconName}
            size={20}
            color={safeColor(isOwnMessage ? theme.colors.primary : theme.colors.surface)}
          />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderControls = (): JSX.Element => {
    return (
      <View style={styles.controls}>
        {/* Stop button */}
        {(voiceState.isPlaying || voiceState.isPaused) && (
          <TouchableOpacity
            style={styles.stopButton}
            onPress={handleStop}
            accessibilityLabel="Stop playback"
          >
            <Ionicons name="stop" size={16} color={safeColor(theme.colors.surface)} />
          </TouchableOpacity>
        )}

        {/* Playback rate button */}
        {voiceState.isPlaying && (
          <TouchableOpacity
            style={styles.rateButton}
            onPress={handlePlaybackRateChange}
            accessibilityLabel={`Playback speed ${voiceState.playbackRate}x`}
          >
            <Text style={styles.rateText}>
              {voiceState.playbackRate}x
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderTranscription = (): JSX.Element | null => {
    if (!transcription) return null;

    const animatedHeight = transcriptionHeight.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 60],
    });

    return (
      <View style={styles.transcriptionContainer}>
        <TouchableOpacity
          style={styles.transcriptionToggle}
          onPress={toggleTranscription}
          accessibilityLabel="Toggle transcription"
        >
          <Ionicons name="text" size={16} color={safeColor(theme.colors.textSecondary)} />
          <Text style={styles.transcriptionLabel}>
            {showFullTranscription ? 'Transcription' : 'Show transcription'}
          </Text>
          <Ionicons
            name={showFullTranscription ? 'chevron-up' : 'chevron-down'}
            size={16}
            color={safeColor(theme.colors.textSecondary)}
          />
        </TouchableOpacity>

        {showFullTranscription && (
          <Animated.View
            style={[
              styles.transcriptionText,
              {
                height: animatedHeight,
              },
            ]}
          >
            <Text
              style={styles.transcriptionContent}
              numberOfLines={showFullTranscription ? undefined : 2}
            >
              {transcription}
            </Text>
          </Animated.View>
        )}
      </View>
    );
  };

  // Main Render
  return (
    <View style={[styles.container, isOwnMessage ? styles.containerOwn : styles.containerOther]}>
      <LinearGradient
        colors={isOwnMessage ? 
          [safeColor(theme.colors.primary), safeColorWithOpacity(safeColor(theme.colors.primary), 'E6')] :
          [safeColor(theme.colors.surface), safeColorWithOpacity(safeColor(theme.colors.surface), 'F2')]
        }
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Play button and waveform */}
          <View style={styles.playerRow}>
            {renderPlayButton()}
            {renderWaveform()}
          </View>

          {/* Time info and controls */}
          <View style={styles.infoRow}>
            <View style={styles.timeInfo}>
              <Text style={[styles.timeText, isOwnMessage ? styles.timeTextOwn : styles.timeTextOther]}>
                {formatTime(voiceState.position)} / {formatTime(duration)}
              </Text>

              {voiceState.isPlaying && (
                <View style={styles.playingIndicator}>
                  <View style={[styles.playingDot, isOwnMessage ? styles.playingDotOwn : styles.playingDotOther]} />
                  <Text style={[styles.playingText, isOwnMessage ? styles.playingTextOwn : styles.playingTextOther]}>
                    Playing
                  </Text>
                </View>
              )}
            </View>

            {renderControls()}
          </View>

          {/* Progress bar */}
          <View style={styles.progressContainer}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${getProgressPercentage()}%`,
                },
                isOwnMessage ? styles.progressBarOwn : styles.progressBarOther,
              ]}
            />
          </View>

          {/* Transcription */}
          {renderTranscription()}
        </View>
      </LinearGradient>
    </View>
  );
}

const createStyles = (theme: Theme) => {
  const primaryColor = safeColor(theme.colors.primary);
  const surfaceColor = safeColor(theme.colors.surface);
  const textColor = safeColor(theme.colors.text);
  const textSecondaryColor = safeColor(theme.colors.textSecondary);
  const borderColor = safeColor(theme.colors.border);
  const errorColor = safeColor(theme.colors.error);
  const secondaryColor = safeColor(theme.colors.secondary);
  
  return StyleSheet.create({
    container: {
      borderRadius: theme.borderRadius?.md || 8,
      overflow: 'hidden',
      marginVertical: theme.spacing?.xs || 4,
    },
    containerOwn: {
      backgroundColor: primaryColor,
    },
    containerOther: {
      backgroundColor: surfaceColor,
    },
    gradient: {
      flex: 1,
    },
    // Gradient colors are now inline in the component
    // gradientOwn and gradientOther removed to fix TypeScript style errors
    content: {
      padding: theme.spacing?.md || 16,
    },
    playerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing?.sm || 8,
    },
    playButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing?.sm || 8,
    },
    playButtonOwn: {
      backgroundColor: surfaceColor,
    },
    playButtonOther: {
      backgroundColor: primaryColor,
    },
    waveformContainer: {
      flex: 1,
    },
    waveform: {
      height: 30,
      backgroundColor: borderColor,
      borderRadius: theme.borderRadius?.sm || 4,
      overflow: 'hidden',
      position: 'relative',
    },
    waveformBackground: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: borderColor,
    },
    waveformProgress: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: safeColorWithOpacity(primaryColor, '30'),
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing?.sm || 8,
    },
    timeInfo: {
      flex: 1,
    },
    timeText: {
      fontSize: theme.typography?.fontSize?.sm || 14,
      fontWeight: '500' as any,
    },
    timeTextOwn: {
      color: surfaceColor,
    },
    timeTextOther: {
      color: textColor,
    },
    playingIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: theme.spacing?.xs || 4,
    },
    playingDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      marginRight: theme.spacing?.xs || 4,
    },
    playingDotOwn: {
      backgroundColor: surfaceColor,
    },
    playingDotOther: {
      backgroundColor: primaryColor,
    },
    playingText: {
      fontSize: theme.typography?.fontSize?.xs || 12,
      fontWeight: '500' as any,
    },
    playingTextOwn: {
      color: surfaceColor,
    },
    playingTextOther: {
      color: primaryColor,
    },
    controls: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    stopButton: {
      backgroundColor: errorColor,
      paddingHorizontal: theme.spacing?.sm || 8,
      paddingVertical: theme.spacing?.xs || 4,
      borderRadius: theme.borderRadius?.sm || 4,
      marginRight: theme.spacing?.xs || 4,
    },
    rateButton: {
      backgroundColor: secondaryColor,
      paddingHorizontal: theme.spacing?.sm || 8,
      paddingVertical: theme.spacing?.xs || 4,
      borderRadius: theme.borderRadius?.sm || 4,
    },
    rateText: {
      color: surfaceColor,
      fontSize: theme.typography?.fontSize?.xs || 12,
      fontWeight: '500' as any,
    },
    progressContainer: {
      height: 4,
      backgroundColor: borderColor,
      borderRadius: 2,
      overflow: 'hidden',
      marginBottom: theme.spacing?.sm || 8,
    },
    progressBar: {
      height: '100%',
      borderRadius: 2,
    },
    progressBarOwn: {
      backgroundColor: surfaceColor,
    },
    progressBarOther: {
      backgroundColor: primaryColor,
    },
    transcriptionContainer: {
      marginTop: theme.spacing?.sm || 8,
    },
    transcriptionToggle: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing?.xs || 4,
    },
    transcriptionLabel: {
      flex: 1,
      marginLeft: theme.spacing?.xs || 4,
      fontSize: theme.typography?.fontSize?.sm || 14,
      color: textSecondaryColor,
    },
    transcriptionText: {
      borderTopWidth: 1,
      borderTopColor: borderColor,
      paddingTop: theme.spacing?.xs || 4,
      overflow: 'hidden',
    },
    transcriptionContent: {
      fontSize: theme.typography?.fontSize?.sm || 14,
      color: textSecondaryColor,
      lineHeight: theme.typography?.lineHeight?.normal || 20,
    },
  });
};

export default VoiceMessage;
