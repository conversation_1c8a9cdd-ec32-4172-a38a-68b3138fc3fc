import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import { ArrowLeft, MoreVertical } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

// Import the ChatMessage type directly from a local interface or model file
import { ChatMessage } from '@types/chat';
import ChatInput from '@components/chat/ChatInput';
import MessageBubble from '@components/chat/MessageBubble';
import ChatToAgreementConnector from '@components/agreement/ChatToAgreementConnector';

interface ChatViewProps {
  roomName: string;
  roomId?: string; // Added roomId for the connector
  messages: ChatMessage[];
  isLoading: boolean;
  isSending: boolean;
  messageText: string;
  setMessageText: (text: string) => void;
  handleSendMessage: () => void;
  handleGoBack: () => void;
  handleOpenReportModal: (message: ChatMessage) => void;
  loadMoreMessages: () => void;
  isLoadingMore: boolean;
  colors: any;
  userId: string;
  fromMatch?: boolean; // Flag to track if chat was started from a match
}

/**
 * Pure presentation component for rendering the chat screen
 */
const ChatView = ({
  roomName,
  roomId,
  messages,
  isLoading,
  isSending,
  messageText,
  setMessageText,
  handleSendMessage,
  handleGoBack,
  handleOpenReportModal,
  loadMoreMessages,
  isLoadingMore,
  colors,
  userId,
  fromMatch = false
}: ChatViewProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const flatListRef = useRef<FlatList<ChatMessage>>(null);

  // Helper function to render a message
  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <MessageBubble
      message={item}
      isOwnMessage={item.sender_id === userId}
      onLongPress={() => handleOpenReportModal(item)}
      // Only pass the props that MessageBubble expects
      showAvatar={true}
      recipientName={roomName}
    />
  );

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading messages...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton} testID="back-button">
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.colors.text }]} numberOfLines={1}>
          {roomName}
        </Text>
        
        <TouchableOpacity style={styles.menuButton} testID="menu-button">
          <MoreVertical size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.messageList}
        inverted={true}
        onEndReached={loadMoreMessages}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          isLoadingMore ? (
            <ActivityIndicator size="small" color={theme.colors.primary} style={styles.loadingMore} />
          ) : null
        }
        testID="message-list"
      />

      {/* Agreement Connector */}
      {roomId && messages.length > 10 && (
        <ChatToAgreementConnector
          roomId={roomId}
          messageCount={messages.length}
          conversationStartTime={messages.length > 0 ? new Date(messages[messages.length - 1].created_at) : undefined}
          lastActiveTime={messages.length > 0 ? new Date(messages[0].created_at) : undefined}
          recentMessages={messages.slice(0, 20).map(msg => msg.content)}
          onAgreementCreationStarted={() => console.log('Agreement creation started from chat')}
        />
      )}
      
      {/* Input area */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ChatInput
          value={messageText}
          onChangeText={setMessageText}
          onSend={handleSendMessage}
          isSending={isSending}
          colors={colors}
          testID="chat-input"
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    textAlign: 'center',
  },
  menuButton: {
    padding: 8,
  },
  messageList: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  loadingMore: {
    marginVertical: 16,
  },
});

export default ChatView;
