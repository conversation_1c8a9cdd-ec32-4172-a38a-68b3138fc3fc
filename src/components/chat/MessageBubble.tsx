import React, { useState, useEffect } from 'react';
import { Linking, Dimensions } from 'react-native';
import { format } from 'date-fns';

import {
  MapPin,
  Paperclip,
  AlertTriangle,
  FileText,
  Flag,
  Heart,
  Check,
  File,
} from 'lucide-react-native';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import ContentWarning from '@components/moderation/ContentWarning';
import ReportContentModal from '@components/moderation/ReportContentModal';
import { useContentModeration } from '@hooks/useContentModeration';
import type { ModerationResult } from '../../services/moderationService';
import type { ChatMessage, SentimentAnalysis } from '../../types/chat';
import type { Theme } from '../../types/theme';

import SentimentIndicator from '@components/chat/SentimentIndicator';
import { useColorFix } from '@hooks/useColorFix';

interface MessageBubbleProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  showAvatar?: boolean;
  sentiment?: SentimentAnalysis;
  showSentiment?: boolean;
  isFlagged?: boolean;
  onLocationPress?: (location: { latitude: number; longitude: number }) => void;
  onImagePress?: (url: string) => void;
  onDocumentPress?: (url: string, name: string) => void;
  onSentimentPress?: () => void;
  onModerationPress?: () => void;
  recipientName?: string;
  onLongPress?: () => void;
}

export default function MessageBubble({
  message,
  isOwnMessage,
  showAvatar = true,
  sentiment,
  showSentiment = false,
  isFlagged = false,
  onLocationPress,
  onImagePress,
  onDocumentPress,
  onSentimentPress,
  onModerationPress,
  recipientName,
  onLongPress,
}: MessageBubbleProps) {
  const { fix } = useColorFix();
  const theme = useTheme();
  const { colors } = theme;
  const [moderationResult, setModerationResult] = useState<ModerationResult | null>(null);
  const [showReportModal, setShowReportModal] = useState(false);
  const { getModerationResult } = useContentModeration();
  const screenWidth = Dimensions.get('window').width;
  const maxBubbleWidth = screenWidth * 0.75;
  const router = useRouter();

  // Fetch moderation status for this message
  useEffect(() => {
    const fetchModeration = async () => {
      const result = await getModerationResult(message.id);
      if (result) {
        setModerationResult(result);
      }
    };

    fetchModeration();
  }, [message.id, getModerationResult]);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getSentimentIcon = () => {
    if (!sentiment) return null;

    switch (sentiment.sentiment) {
      case 'positive':
        return <Heart size={14} color={fix("#10B981", "#10B981")} />;
      case 'negative':
        return <AlertTriangle size={14} color={fix("#EF4444", "#EF4444")} />;
      case 'neutral':
      default:
        return <Check size={14} color="#64748B" />;
    }
  };

  const handleLocationPress = () => {
    if (message.type === 'location' && message.metadata) {
      const { latitude, longitude } = message.metadata;
      const url = `https://maps.google.com/?q=${latitude},${longitude}`;
      Linking.openURL(url);
    }
  };

  const handleDocumentPress = () => {
    if (message.type === 'document' && message.metadata?.url) {
      Linking.openURL(message.metadata.url);
    }
  };

  const renderContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <TouchableOpacity
            onPress={() => onImagePress?.(message.metadata.url)}
            activeOpacity={0.9}
          >
            <Image
              source={{ uri: message.metadata.url }}
              style={styles.messageImage}
              resizeMode="cover"
            />
          </TouchableOpacity>
        );

      case 'location':
        return (
          <TouchableOpacity style={styles.locationContainer} onPress={handleLocationPress}>
            <MapPin size={24} color={colors.primary[500]} />
            <Text style={styles.locationText}>View Location</Text>
          </TouchableOpacity>
        );

      case 'document':
        return (
          <TouchableOpacity style={styles.documentContainer} onPress={handleDocumentPress}>
            <File size={24} color={colors.primary[500]} />
            <Text style={styles.documentName}>{message.metadata?.name || 'Document'}</Text>
          </TouchableOpacity>
        );

      case 'agreement':
        return (
          <View style={styles.agreementContent}>
            <View style={styles.agreementHeader}>
              <FileText size={20} color="#6366F1" />
              <Text style={styles.agreementTitle}>Roommate Agreement</Text>
            </View>
            <Text style={styles.agreementText}>{message.content}</Text>
            <TouchableOpacity
              style={styles.agreementButton}
              onPress={() => {
                if (message.metadata?.agreement_id) {
                  // Use string-based navigation to prevent [object Object] issues
                  const queryParams = new URLSearchParams({ templateId: '1' });
                  router.push(`/agreement/customize?${queryParams.toString()}`);
                }
              }}
            >
              <Text style={styles.agreementButtonText}>
                {message.metadata?.action === 'created' ? 'View Agreement' : 'Continue Setup'}
              </Text>
            </TouchableOpacity>
          </View>
        );

      default:
        return (
          <ContentWarning
            status={moderationResult?.status || 'approved'}
            onReport={() => setShowReportModal(true)}
          >
            <Text
              style={[
                styles.messageText,
                isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
                isFlagged && styles.flaggedText,
              ]}
            >
              {message.content}
            </Text>
          </ContentWarning>
        );
    }
  };

  return (
    <>
      <View style={[styles.container, isOwnMessage ? styles.ownMessage : styles.otherMessage]}>
        {showAvatar && !isOwnMessage && message.sender?.avatar_url && (
          <Image source={{ uri: message.sender.avatar_url }} style={styles.avatar} />
        )}

        <View style={styles.messageWrapper}>
          <View
            style={[
              styles.bubble,
              isOwnMessage ? styles.ownBubble : styles.otherBubble,
              message.type !== 'text' && styles.mediaBubble,
              isFlagged && styles.flaggedBubble,
              { maxWidth: maxBubbleWidth },
            ]}
          >
            {renderContent()}

            {isFlagged && (
              <TouchableOpacity
                style={styles.flagIndicator}
                onPress={onModerationPress}
                disabled={!onModerationPress}
              >
                <AlertTriangle size={14} color={fix("#EF4444", "#EF4444")} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.metadataContainer}>
            <Text style={[styles.timestamp, isOwnMessage ? styles.ownTimestamp : styles.otherTimestamp]}>
              {formatTime(message.created_at)}
            </Text>

            {showSentiment && message.type === 'text' && sentiment && (
              <TouchableOpacity
                style={styles.sentimentIndicator}
                onPress={onSentimentPress}
                disabled={!onSentimentPress}
              >
                {getSentimentIcon()}
              </TouchableOpacity>
            )}

            {!isOwnMessage && message.type === 'text' && !showSentiment && (
              <TouchableOpacity
                onPress={() => setShowReportModal(true)}
                style={styles.reportButton}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Flag size={12} color={colors.text.secondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      <ReportContentModal
        visible={showReportModal}
        onClose={() => setShowReportModal(false)}
        contentId={message.id}
        contentType="message"
        reportedUserId={message.sender_id}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  ownMessage: {
    justifyContent: 'flex-end',
  },
  otherMessage: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
  },
  messageWrapper: {
    maxWidth: '80%',
  },
  bubble: {
    padding: 12,
    borderRadius: 18,
    marginBottom: 4,
    maxWidth: '100%',
  },
  ownBubble: {
    backgroundColor: '#6366F1',
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    backgroundColor: '#F1F5F9',
    borderBottomLeftRadius: 4,
  },
  mediaBubble: {
    padding: 2,
    overflow: 'hidden',
  },
  flaggedBubble: {
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#FFFFFF',
  },
  otherMessageText: {
    color: '#334155',
  },
  flaggedText: {
    fontStyle: 'italic',
    opacity: 0.8,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 18,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  locationText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6366F1',
  },
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  documentName: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6366F1',
  },
  metadataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  timestamp: {
    fontSize: 11,
    marginRight: 4,
  },
  ownTimestamp: {
    color: '#94A3B8',
  },
  otherTimestamp: {
    color: '#94A3B8',
  },
  flagIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reportButton: {
    padding: 4,
  },
  sentimentIndicator: {
    marginLeft: 4,
  },
  agreementContent: {
    padding: 4,
  },
  agreementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  agreementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0369A1',
    marginLeft: 8,
  },
  agreementText: {
    fontSize: 14,
    color: '#334155',
    marginBottom: 12,
  },
  agreementButton: {
    backgroundColor: '#0EA5E9',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  agreementButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
