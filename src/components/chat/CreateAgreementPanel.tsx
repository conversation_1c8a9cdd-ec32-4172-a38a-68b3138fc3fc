import React, { useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { FileText, ArrowRight, CheckCircle, X } from 'lucide-react-native';
;
import { matchChatService } from '@services';
import { createAgreementFromChatAndNavigate } from '@utils/navigationUtils';
import { logger } from '@services/loggerService';

interface CreateAgreementPanelProps {
  chatRoomId: string;
  currentUserId: string;
  otherUserId: string;
  existingAgreementId?: string;
  onSuccess?: (agreementId: string) => void;
  onError?: (error: string) => void;
}

// Available agreement templates
const AGREEMENT_TEMPLATES = [
  {
    id: 'standard',
    name: 'Standard Roommate Agreement',
    description:
      'Comprehensive agreement covering rent, utilities, chores, guests, and house rules',
  },
  {
    id: 'basic',
    name: 'Basic Roommate Agreement',
    description: 'Simple agreement with just the essential rules for living together',
  },
  {
    id: 'custom',
    name: 'Custom Agreement Template',
    description: 'Start from scratch and customize every aspect of your agreement',
  },
];

/**
 * A component that provides UI for initiating agreement creation from a chat
 * Can be displayed within chat screens to guide users through creating a roommate agreement
 */
export function CreateAgreementPanel({
  chatRoomId,
  currentUserId,
  otherUserId,
  existingAgreementId,
  onSuccess,
  onError,
}: CreateAgreementPanelProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('standard');

  // Handle agreement creation
  const handleCreateAgreement = async () => {
    setIsCreating(true);
    setError(null);

    try {
      // Use the standardized navigation utility for agreement creation
      const success = await createAgreementFromChatAndNavigate(
        chatRoomId,
        currentUserId,
        otherUserId,
        selectedTemplate as 'standard' | 'basic' | 'custom'
      );

      if (!success) {
        throw new Error('Failed to create agreement');
      }

      // Call success callback if provided
      if (onSuccess && existingAgreementId) {
        onSuccess(existingAgreementId);
      }
    } catch (error) {
      logger.error('Failed to create agreement from chat panel', 'CreateAgreementPanel', {
        error: error instanceof Error ? error.message : String(error),
        chatRoomId,
        currentUserId,
        otherUserIdRedacted: otherUserId.slice(-4),
      });

      // Get user-friendly error message
      const { message } = matchChatService.handleMatchChatError(error, 'agreement');
      setError(message);

      // Call error callback if provided
      if (onError) {
        onError(message);
      }

      // Show alert with error message
      Alert.alert('Error', message, [{ text: 'OK' }]);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle template selection
  const handleSelectTemplate = (templateId: string) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    setSelectedTemplate(templateId);
    setShowTemplateModal(false);
  };

  // Template selection modal
  const renderTemplateModal = () => (
    <Modal
      visible={showTemplateModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowTemplateModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Agreement Type</Text>
            <TouchableOpacity onPress={() => setShowTemplateModal(false)}>
              <X size={24} color={theme.colors.gray} />
            </TouchableOpacity>
          </View>

          {AGREEMENT_TEMPLATES.map(template => (
            <TouchableOpacity
              key={template.id}
              style={[
                styles.templateOption,
                selectedTemplate === template.id && styles.selectedTemplateOption,
              ]}
              onPress={() => handleSelectTemplate(template.id)}
            >
              <View style={styles.templateOptionContent}>
                <Text style={styles.templateName}>{template.name}</Text>
                <Text style={styles.templateDescription}>{template.description}</Text>
              </View>
              {selectedTemplate === template.id && (
                <CheckCircle size={20} color={theme.colors.primary} />
              )}
            </TouchableOpacity>
          ))}

          <TouchableOpacity style={styles.applyButton} onPress={() => setShowTemplateModal(false)}>
            <Text style={styles.applyButtonText}>Apply Template</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  // If there's already an agreement, show view button instead
  if (existingAgreementId) {
    return (
      <View style={styles.container}>
        <View style={styles.existingAgreementContainer}>
          <View style={styles.existingIconContainer}>
            <CheckCircle size={18} color={theme.colors.success} />
          </View>
          <Text style={styles.existingText}>
            You already have a roommate agreement for this conversation
          </Text>
          <TouchableOpacity
            style={styles.viewButton}
            onPress={() => {
              import('expo-router').then(({ router }) => {
                router.push({
                  pathname: '/agreement/details/[id]',
                  params: { id: existingAgreementId },
                });
              });
            }}
          >
            <Text style={styles.viewButtonText}>View</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Otherwise show the agreement creation panel
  return (
    <View style={styles.container}>
      {renderTemplateModal()}

      {isExpanded ? (
        <View style={styles.expandedContainer}>
          <Text style={styles.title}>Create a Roommate Agreement</Text>
          <Text style={styles.description}>
            Turn your conversation into a formal roommate agreement to establish clear expectations
            and avoid future conflicts.
          </Text>

          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsTitle}>Benefits:</Text>
            <View style={styles.benefitItem}>
              <View style={styles.bulletPoint} />
              <Text style={styles.benefitText}>Clearly define responsibilities and rules</Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={styles.bulletPoint} />
              <Text style={styles.benefitText}>Prevent misunderstandings and conflicts</Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={styles.bulletPoint} />
              <Text style={styles.benefitText}>Easily track rent payments and expenses</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.templateSelector}
            onPress={() => setShowTemplateModal(true)}
          >
            <View>
              <Text style={styles.templateSelectorLabel}>Agreement Template</Text>
              <Text style={styles.selectedTemplateName}>
                {AGREEMENT_TEMPLATES.find(t => t.id === selectedTemplate)?.name ||
                  'Standard Roommate Agreement'}
              </Text>
            </View>
            <ArrowRight size={16} color={theme.colors.primary} />
          </TouchableOpacity>

          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setIsExpanded(false)}
              disabled={isCreating}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.createButton}
              onPress={handleCreateAgreement}
              disabled={isCreating}
            >
              {isCreating ? (
                <ActivityIndicator size="small" color={theme.colors.background} />
              ) : (
                <>
                  <FileText size={16} color={theme.colors.background} />
                  <Text style={styles.createButtonText}>Create Agreement</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.collapsedButton}
          onPress={() => setIsExpanded(true)}
          disabled={isCreating}
        >
          <FileText size={18} color={theme.colors.primary} />
          <Text style={styles.collapsedButtonText}>Create a Roommate Agreement</Text>
          <ArrowRight size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginVertical: 8,
  },
  collapsedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  collapsedButtonText: {
    flex: 1,
    marginHorizontal: 12,
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  expandedContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.gray,
    shadowColor: theme.colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.gray,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: theme.colors.gray,
    marginBottom: 16,
    lineHeight: 20,
  },
  benefitsContainer: {
    backgroundColor: theme.colors.gray,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  benefitsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.gray,
    marginBottom: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: theme.colors.primary,
    marginRight: 8,
  },
  benefitText: {
    fontSize: 14,
    color: theme.colors.gray,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.gray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.gray,
  },
  createButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  createButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.white,
  },
  existingAgreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.success,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.success,
  },
  existingIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: theme.colors.success,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  existingText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.success,
  },
  viewButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.success,
  },
  // Template selection modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: theme.colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.gray,
  },
  templateOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.gray,
    marginBottom: 12,
  },
  selectedTemplateOption: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  templateOptionContent: {
    flex: 1,
    marginRight: 8,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray,
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 14,
    color: theme.colors.gray,
  },
  applyButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  applyButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  templateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.gray,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.gray,
  },
  templateSelectorLabel: {
    fontSize: 14,
    color: theme.colors.gray,
    marginBottom: 4,
  },
  selectedTemplateName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
});

export default CreateAgreementPanel;
