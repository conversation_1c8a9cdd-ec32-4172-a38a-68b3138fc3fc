import React, { useState, useEffect, useCallback } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { TouchableOpacity, Text, StyleSheet, View, ActivityIndicator } from 'react-native';
import { FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@hooks/useAuth';
import { matchingToAgreementFlow } from '@services/flows/MatchingToAgreementFlowService';
import { AgreementProposalModal } from '@components/matching/AgreementProposalModal';
import type { FlowProgress, FlowStage } from '@types/matching-flow';

interface AgreementInitiationButtonProps {
  chatRoomId: string;
  participantIds: string[];
}

export function AgreementInitiationButton({ chatRoomId, participantIds }: AgreementInitiationButtonProps) {
  const router = useRouter();
  const theme = useTheme();
  const { authState } = useAuth();
  const user = authState?.user;
  const styles = createStyles(theme);
  
  // State management
  const [flowProgress, setFlowProgress] = useState<FlowProgress | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showProposalModal, setShowProposalModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the other user ID from participants
  const otherUserId = participantIds.find(id => id !== user?.id);
  const otherUserName = 'your roommate'; // This could be enhanced to fetch actual name

  // Load flow progress when component mounts
  useEffect(() => {
    if (user?.id && otherUserId) {
      loadFlowProgress();
    }
  }, [user?.id, otherUserId]);

  const loadFlowProgress = async () => {
    if (!user?.id || !otherUserId) return;

    try {
      const progress = await matchingToAgreementFlow.getFlowProgress(user.id, otherUserId);
      setFlowProgress(progress);
    } catch (error) {
      console.error('Error loading flow progress:', error);
    }
  };

  const handleCreateAgreement = useCallback(async () => {
    if (!user?.id || participantIds.length === 0) {
      console.warn('Missing required data for agreement creation');
      return;
    }

    try {
      setIsLoading(true);
      
      // Navigate to agreement template selection screen with chat room context
      router.push({
        pathname: '/agreement/create',
        params: {
          chatRoomId: chatRoomId,
          participantIds: JSON.stringify(participantIds),
          source: 'chat'
        }
      });
      
    } catch (error) {
      console.error('Error navigating to agreement creation:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, participantIds, chatRoomId, router]);

  const handleProposeAgreement = useCallback(async () => {
    if (!user?.id || participantIds.length === 0) {
      console.warn('Missing required data for agreement proposal');
      return;
    }

    try {
      setIsLoading(true);
      
      // Use the MatchingToAgreementFlowService to propose agreement
      const result = await matchingToAgreementFlow.proposeAgreement(
        chatRoomId,
        user.id,
        participantIds[0], // The other participant
        {
          source: 'chat_button',
          timestamp: new Date().toISOString()
        }
      );

      if (result.success) {
        // Navigate to agreement customization
        router.push({
          pathname: '/agreement/customize',
          params: {
            agreementId: result.agreementId,
            source: 'chat'
          }
        });
      } else {
        console.error('Failed to propose agreement:', result.error);
      }
      
    } catch (error) {
      console.error('Error proposing agreement:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, participantIds, chatRoomId, router]);

  // Determine button state and action based on flow progress
  const getButtonConfig = () => {
    if (!flowProgress) {
      return {
        text: 'Create Roommate Agreement',
        icon: FileText,
        action: handleCreateAgreement,
        disabled: false
      };
    }

    switch (flowProgress.currentStage) {
      case 'discovery':
      case 'matching':
      case 'mutual_interest':
      case 'chat_initiated':
        return {
          text: 'Create Roommate Agreement',
          icon: FileText,
          action: handleCreateAgreement,
          disabled: false
        };
      
      case 'agreement_proposed':
        return {
          text: 'View Agreement Proposal',
          icon: Clock,
          action: () => router.push(`/agreement/${flowProgress.agreementId}`),
          disabled: false
        };
      
      case 'agreement_customizing':
        return {
          text: 'Continue Customizing',
          icon: FileText,
          action: () => router.push({
            pathname: '/agreement/customize',
            params: { agreementId: flowProgress.agreementId }
          }),
          disabled: false
        };
      
      case 'agreement_ready':
        return {
          text: 'Review & Sign Agreement',
          icon: CheckCircle,
          action: () => router.push(`/agreement/${flowProgress.agreementId}`),
          disabled: false
        };
      
      case 'signatures_pending':
        return {
          text: 'Waiting for Signatures',
          icon: Clock,
          action: () => router.push(`/agreement/${flowProgress.agreementId}`),
          disabled: true
        };
      
      case 'agreement_active':
        return {
          text: 'View Active Agreement',
          icon: CheckCircle,
          action: () => router.push(`/agreement/${flowProgress.agreementId}`),
          disabled: false
        };
      
      default:
        return {
          text: 'Create Roommate Agreement',
          icon: FileText,
          action: handleCreateAgreement,
          disabled: false
        };
    }
  };

  const buttonConfig = getButtonConfig();
  const ButtonIcon = buttonConfig.icon;

  // Don't show if no participants or user not available
  if (!user?.id || participantIds.length === 0 || !otherUserId) {
    return null;
  }

  return (
    <>
      <TouchableOpacity 
        style={[styles.button, { backgroundColor: theme.colors?.primary || '#3B82F6' }]}
        onPress={buttonConfig.action}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color={theme.colors?.background || '#FFFFFF'} style={styles.icon} />
        ) : (
          <ButtonIcon size={18} color={theme.colors?.background || '#FFFFFF'} style={styles.icon} />
        )}
        <Text style={styles.text}>{buttonConfig.text}</Text>
      </TouchableOpacity>

      {/* Flow Progress Indicator */}
      {flowProgress && (
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            Progress: {flowProgress.completed_stages.length} of 4 steps completed
          </Text>
          
          {/* Next Actions */}
          {flowProgress.next_actions.length > 0 && (
            <View style={styles.nextActionsContainer}>
              <Text style={styles.nextActionsTitle}>Next Steps:</Text>
              {flowProgress.next_actions.slice(0, 2).map((action, index) => (
                <Text key={index} style={styles.nextActionText}>
                  • {action.description}
                </Text>
              ))}
            </View>
          )}

          {/* Blocking Issues */}
          {flowProgress.blocking_issues.length > 0 && (
            <View style={styles.issuesContainer}>
              <AlertCircle size={16} color={theme.colors?.error || '#EF4444'} />
              <Text style={styles.issuesText}>
                {flowProgress.blocking_issues[0]}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={16} color={theme.colors?.error || '#EF4444'} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Agreement Proposal Modal */}
      <AgreementProposalModal
        visible={showProposalModal}
        onClose={() => setShowProposalModal(false)}
        onPropose={handleProposeAgreement}
        recipientName={otherUserName}
      />
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors?.primary || '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 12,
    alignSelf: 'center',
    shadowColor: theme.colors?.text || '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  icon: {
    marginRight: 8,
  },
  text: {
    color: theme.colors?.background || '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  progressContainer: {
    backgroundColor: theme.colors?.surface || '#F8F9FA',
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E2E8F0',
  },
  progressText: {
    fontSize: 12,
    color: theme.colors?.textSecondary || '#64748B',
    fontWeight: '500',
    marginBottom: 8,
  },
  nextActionsContainer: {
    marginTop: 8,
  },
  nextActionsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors?.text || '#1F2937',
    marginBottom: 4,
  },
  nextActionText: {
    fontSize: 11,
    color: theme.colors?.textSecondary || '#64748B',
    marginBottom: 2,
  },
  issuesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: theme.colors?.errorBackground || '#FEF2F2',
    borderRadius: 6,
  },
  issuesText: {
    marginLeft: 8,
    fontSize: 12,
    color: theme.colors?.error || '#EF4444',
    flex: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: theme.colors?.errorBackground || '#FEF2F2',
    borderRadius: 8,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: theme.colors?.error || '#EF4444',
  },
  errorText: {
    marginLeft: 8,
    fontSize: 12,
    color: theme.colors?.error || '#EF4444',
    flex: 1,
  },
});

export default AgreementInitiationButton;
