import React, { forwardRef } from 'react';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { platformInputStyles, platformBorder } from '@utils/crossPlatformStyles';

export interface SimpleInputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
}

/**
 * A simplified input component that doesn't rely on complex theme structure.
 * Used as a fallback when the main Input component encounters theme issues.
 * Updated with cross-platform styling for consistent appearance.
 */
const SimpleInput = forwardRef<TextInput, SimpleInputProps>(
  ({ label, error, helperText, style, ...rest }, ref) => {
    const theme = useTheme();
    const styles = createStyles(theme);
    return (
      <View style={styles.container}>
        {label && <Text style={styles.label}>{label}</Text>}
        <TextInput
          ref={ref}
          style={[styles.input, error ? styles.inputError : null, style]}
          placeholderTextColor={Platform.select({
            ios: '#4B5563', // Much darker placeholder for better iOS visibility
            android: '#6B7280',
            default: '#4B5563'
          })}
          {...rest}
        />
        {error ? (
          <Text style={styles.errorText}>{error}</Text>
        ) : helperText ? (
          <Text style={styles.helperText}>{helperText}</Text>
        ) : null}
      </View>
    );
  }
);

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    ...platformInputStyles.container,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text || '#374151',
    ...platformInputStyles.label,
  },
  input: {
    ...platformInputStyles.input,
    ...platformBorder(),
    borderColor: theme.colors.border || '#D1D5DB',
    color: '#111827', // Ensure dark text for visibility
    backgroundColor: Platform.select({
      ios: '#FFFFFF', // Pure white background for iOS for better contrast
      android: '#F9FAFB',
      default: '#FFFFFF'
    }),
    fontSize: 16,
    paddingHorizontal: Platform.select({
      ios: 16, // More padding on iOS for better touch targets
      android: 12,
      default: 16
    }),
    paddingVertical: Platform.select({
      ios: 14, // Increased padding on iOS for better visibility
      android: 10,
      default: 14
    }),
    borderRadius: Platform.select({
      ios: 10, // Slightly more rounded on iOS
      android: 8,
      default: 10
    }),
    borderWidth: Platform.select({
      ios: 1.5, // Slightly thicker border on iOS for better definition
      android: 1,
      default: 1.5
    }),
    // Add shadow for iOS to improve visual separation
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      default: {}
    }),
  },
  inputError: {
    borderColor: '#DC2626', // Red color for errors
    backgroundColor: '#FEF2F2', // Light red background
    ...platformBorder(2), // Thicker border for errors
  },
  errorText: {
    fontSize: Platform.select({
      ios: 14,
      android: 13,
      default: 14
    }),
    color: '#DC2626',
    marginTop: Platform.select({
      ios: 4,
      android: 2,
      default: 4
    }),
  },
  helperText: {
    fontSize: Platform.select({
      ios: 14,
      android: 13,
      default: 14
    }),
    color: '#6B7280',
    marginTop: Platform.select({
      ios: 4,
      android: 2,
      default: 4
    }),
  },
});

export default SimpleInput;
