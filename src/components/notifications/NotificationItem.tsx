import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { MessageSquare, Heart, Home, BellRing, Shield } from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Notification } from "@utils/notificationUtils";

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
  onMarkAsRead: (id: string) => void;
}

export default function NotificationItem({
  notification,
  onPress,
  onMarkAsRead,
}: NotificationItemProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const getIcon = () => {
    switch (notification.type) {
      case 'message':
        return <MessageSquare size={20} color={theme.colors.primary} />;
      case 'match':
        return <Heart size={20} color={theme.colors.success} />;
      case 'roomUpdate':
        return <Home size={20} color={theme.colors.info} />;
      case 'suspicious_profile':
        return <Shield size={20} color={theme.colors.error} />;
      case 'system':
        return <BellRing size={20} color={theme.colors.warning} />;
      default:
        return <BellRing size={20} color={theme.colors.textMuted} />;
    }
  };

  // Format the time difference with error handling
  const getFormattedTime = () => {
    try {
      if (!notification.created_at) {
        return 'recently';
      }
      const date = new Date(notification.created_at);
      if (isNaN(date.getTime())) {
        return 'recently';
      }
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'recently'; // Fallback value
    }
  };

  const formattedTime = getFormattedTime();

  const handlePress = () => {
    onPress(notification);
    if (!notification.is_read) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: notification.is_read ? theme.colors.surface : theme.colors.scales.primary[theme.isDark ? 900 : 50],
        },
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        {getIcon()}
        {!notification.is_read && (
          <View style={styles.unreadIndicator} />
        )}
      </View>

      <View style={styles.contentContainer}>
        <Text
          style={[
            styles.title,
            {
              fontWeight: notification.is_read ? '500' : '600',
            },
          ]}
          numberOfLines={1}
        >
          {notification.title}
        </Text>

        <Text style={styles.body} numberOfLines={2}>
          {notification.body}
        </Text>

        <Text style={styles.time}>{formattedTime}</Text>
      </View>
    </TouchableOpacity>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    marginVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  iconContainer: {
    position: 'relative',
    marginRight: theme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  unreadIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
    color: theme.colors.text,
  },
  body: {
    fontSize: 14,
    marginBottom: theme.spacing.sm,
    color: theme.colors.textSecondary,
  },
  time: {
    fontSize: 12,
    color: theme.colors.textMuted,
  },
});
