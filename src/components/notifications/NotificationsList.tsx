import React, { useState, useEffect } from 'react';

import { Bell } from 'lucide-react-native';
import { View, FlatList, StyleSheet, Text, ActivityIndicator, RefreshControl } from 'react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';
import { Notification } from '@utils/notificationUtils';
import { getUserNotifications, markNotificationAsRead } from '@utils/notificationUtils';
// Import standardized navigation utilities
import { navigateToChat, navigateToMatches, navigateToProfile } from '@utils/navigationUtils';

import NotificationItem from '@components/notifications/NotificationItem';

export default function NotificationsList() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const { data: user } = await supabase.auth.getUser();

      if (user?.user?.id) {
        const notificationsData = await getUserNotifications(user.user.id);
        setNotifications(notificationsData);
      } else {
        // User not logged in
        setNotifications([]);
        logger.warn('User not logged in when fetching notifications', 'Notifications');
      }
    } catch (error) {
      logger.error('Failed to load notifications', 'Notifications', {
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadNotifications();
  };

  const handleNotificationPress = (notification: Notification) => {
    // Handle navigation based on notification type
    try {
      if (!notification || !notification.type) {
        logger.warn('Invalid notification object', 'Notifications');
        return;
      }

      switch (notification.type) {
        case 'message':
          // Navigate to conversation with user_id from notification data using standardized navigation
          if (notification.data?.userId && notification.data?.roomId) {
            try {
              // Use standardized utility for chat navigation
              navigateToChat(
                notification.data.roomId,
                {
                  id: notification.data.userId,
                  name: notification.data.userName || 'User',
                  avatar: notification.data.userAvatar,
                },
                {
                  source: 'notification',
                  trackEvent: true,
                }
              );
            } catch (navError) {
              logger.error('Navigation error to Chat', 'Notifications', {
                error: navError instanceof Error ? navError.message : String(navError),
              });
            }
          } else if (notification.data?.userId) {
            // If we have only the user ID but no room ID, navigate to profile
            try {
              navigateToProfile(notification.data.userId, {
                source: 'notification',
                trackEvent: true,
              });
            } catch (navError) {
              logger.error('Navigation error to Profile', 'Notifications', {
                error: navError instanceof Error ? navError.message : String(navError),
              });
            }
          }
          break;

        case 'match':
          // Navigate to matches screen or specific match using standardized navigation
          if (notification.data?.matchId) {
            try {
              // Use standardized utility for match navigation
              navigateToMatches({
                highlightMatchId: notification.data.matchId,
                source: 'notification',
                trackEvent: true,
              });
            } catch (navError) {
              logger.error('Navigation error to Matches', 'Notifications', {
                error: navError instanceof Error ? navError.message : String(navError),
              });
            }
          } else {
            try {
              // Use standardized utility for general matches navigation
              navigateToMatches({
                source: 'notification',
                trackEvent: true,
              });
            } catch (navError) {
              logger.error('Navigation error to Matches screen', 'Notifications', {
                error: navError instanceof Error ? navError.message : String(navError),
              });
            }
          }
          break;

        case 'roomUpdate':
          // Use standardized navigation for room updates
          if (notification.data?.roomId) {
            try {
              // For now, we'll navigate to a chat if it's a room update
              // This could be refined further when we implement room-specific navigation utilities
              navigateToChat(
                notification.data.roomId,
                notification.data.userId
                  ? {
                      id: notification.data.userId,
                      name: notification.data.userName || 'User',
                    }
                  : undefined,
                {
                  source: 'notification',
                  context: 'agreement',
                  trackEvent: true,
                }
              );
            } catch (navError) {
              logger.error('Navigation error to Room', 'Notifications', {
                error: navError instanceof Error ? navError.message : String(navError),
              });
            }
          }
          break;
        default:
          // Default navigation or handling for other notification types
          logger.info('Notification pressed with type: ' + notification.type, 'Notifications');
      }
    } catch (error) {
      logger.error('General notification press error', 'Notifications', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  };

  const handleMarkAsRead = async (id: string) => {
    try {
      if (!id) {
        logger.warn('Invalid notification ID for marking as read', 'Notifications');
        return;
      }

      const success = await markNotificationAsRead(id);

      if (!success) {
        logger.error(`Failed to mark notification ${id} as read`, 'Notifications', {
          error: 'Operation failed',
        });
        return;
      }

      // Update local state to mark notification as read
      setNotifications(
        notifications.map(notification =>
          notification.id === id ? { ...notification, is_read: true } : notification
        )
      );
    } catch (error) {
      logger.error('Failed to mark notification as read', 'Notifications', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  };

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (notifications.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Bell size={48} color={theme.colors.textMuted} />
        <Text style={styles.emptyText}>
          No notifications yet
        </Text>
        <Text style={styles.emptySubtext}>
          We'll notify you when something important happens
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={notifications}
      keyExtractor={item => item.id}
      renderItem={({ item }) => (
        <NotificationItem
          notification={item}
          onPress={handleNotificationPress}
          onMarkAsRead={handleMarkAsRead}
        />
      )}
      contentContainerStyle={styles.listContainer}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
    />
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  listContainer: {
    flexGrow: 1,
    paddingVertical: theme.spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textMuted,
    textAlign: 'center',
    maxWidth: '80%',
  },
});
