/**
 * Notification Bell Component
 * 
 * A compact notification bell button that displays:
 * - Real-time unread notification count
 * - Visual notification indicator
 * - Opens EnhancedNotificationCenter on press
 * - Professional animation and styling
 * 
 * Features:
 * - Auto-updating unread count
 * - Animated notification appearance
 * - Badge styling with count limits
 * - Theme integration
 * - Touch feedback
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Bell, BellRing } from 'lucide-react-native';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { 
  unifiedNotificationService, 
  UnreadCounts 
} from '@services/enhanced/UnifiedNotificationService';
import { EnhancedNotificationCenter } from './EnhancedNotificationCenter';

// =====================================================
// INTERFACES
// =====================================================

interface NotificationBellProps {
  size?: number;
  showBadge?: boolean;
  onPress?: () => void;
  style?: any;
}

// =====================================================
// NOTIFICATION BELL COMPONENT
// =====================================================

export const NotificationBell: React.FC<NotificationBellProps> = ({
  size = 24,
  showBadge = true,
  onPress,
  style
}) => {
  const { user } = useSupabaseUser();
  const theme = useTheme();
  const styles = createStyles(theme);
  
  // State
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({});
  const [notificationCenterVisible, setNotificationCenterVisible] = useState(false);
  const [bellAnimation] = useState(new Animated.Value(0));
  const [badgeAnimation] = useState(new Animated.Value(0));

  const totalUnread = unreadCounts.total || 0;
  const hasUnread = totalUnread > 0;

  // Initialize notification service and subscriptions
  useEffect(() => {
    if (!user?.id) return;

    const initializeNotifications = async () => {
      // Initialize service
      await unifiedNotificationService.initialize(user.id);
      
      // Subscribe to unread count updates
      const unsubscribe = unifiedNotificationService.subscribeToUnreadCounts(
        user.id,
        (counts) => {
          const previousTotal = unreadCounts.total || 0;
          const newTotal = counts.total || 0;
          
          setUnreadCounts(counts);
          
          // Animate bell if new notifications arrived
          if (newTotal > previousTotal) {
            animateBell();
          }
          
          // Animate badge appearance/update
          if (newTotal !== previousTotal) {
            animateBadge();
          }
        }
      );
      
      return unsubscribe;
    };

    const cleanup = initializeNotifications();
    
    return () => {
      cleanup.then(fn => fn?.());
    };
  }, [user?.id]);

  // Animate bell when new notifications arrive
  const animateBell = () => {
    Animated.sequence([
      Animated.timing(bellAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(bellAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Animate badge when count changes
  const animateBadge = () => {
    Animated.sequence([
      Animated.timing(badgeAnimation, {
        toValue: 1.2,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(badgeAnimation, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Handle bell press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      setNotificationCenterVisible(true);
    }
  };

  // Bell rotation animation
  const bellRotation = bellAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '20deg'],
  });

  // Badge scale animation
  const badgeScale = badgeAnimation.interpolate({
    inputRange: [0, 1, 1.2],
    outputRange: [1, 1, 1.2],
  });

  // Format badge count
  const formatCount = (count: number): string => {
    if (count > 99) return '99+';
    return count.toString();
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.container, style]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Animated.View
          style={[
            styles.bellContainer,
            {
              transform: [{ rotate: bellRotation }],
            },
          ]}
        >
          {hasUnread ? (
            <BellRing 
              size={size} 
              color={theme.colors.primary} 
            />
          ) : (
            <Bell 
              size={size} 
              color={theme.colors.text} 
            />
          )}
        </Animated.View>

        {showBadge && hasUnread && (
          <Animated.View
            style={[
              styles.badge,
              {
                backgroundColor: theme.colors.error,
                transform: [{ scale: badgeScale }],
              },
            ]}
          >
            <Text style={styles.badgeText}>
              {formatCount(totalUnread)}
            </Text>
          </Animated.View>
        )}

        {hasUnread && (
          <View style={styles.indicator} />
        )}
      </TouchableOpacity>

      <EnhancedNotificationCenter
        visible={notificationCenterVisible}
        onClose={() => setNotificationCenterVisible(false)}
      />
    </>
  );
};

// =====================================================
// NOTIFICATION BELL WITH CUSTOM TRIGGER
// =====================================================

interface NotificationBellTriggerProps {
  children: React.ReactNode;
  onUnreadCountChange?: (count: number) => void;
}

export const NotificationBellTrigger: React.FC<NotificationBellTriggerProps> = ({
  children,
  onUnreadCountChange
}) => {
  const { user } = useSupabaseUser();
  const [notificationCenterVisible, setNotificationCenterVisible] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Subscribe to unread counts
  useEffect(() => {
    if (!user?.id) return;

    const initializeNotifications = async () => {
      await unifiedNotificationService.initialize(user.id);
      
      const unsubscribe = unifiedNotificationService.subscribeToUnreadCounts(
        user.id,
        (counts) => {
          const total = counts.total || 0;
          setUnreadCount(total);
          onUnreadCountChange?.(total);
        }
      );
      
      return unsubscribe;
    };

    const cleanup = initializeNotifications();
    
    return () => {
      cleanup.then(fn => fn?.());
    };
  }, [user?.id, onUnreadCountChange]);

  return (
    <>
      <TouchableOpacity
        onPress={() => setNotificationCenterVisible(true)}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>

      <EnhancedNotificationCenter
        visible={notificationCenterVisible}
        onClose={() => setNotificationCenterVisible(false)}
      />
    </>
  );
};

// =====================================================
// STYLES
// =====================================================

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    padding: theme.spacing.sm,
  },
  bellContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    minWidth: 20,
    height: 20,
    borderRadius: theme.borderRadius.round,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xs,
    borderWidth: 2,
    borderColor: theme.colors.background,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.background,
  },
  indicator: {
    position: 'absolute',
    top: theme.spacing.xs,
    right: theme.spacing.xs,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary,
  },
}); 