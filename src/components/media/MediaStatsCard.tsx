import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useColorScheme } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Card } from '@components/ui';
import { BarChart3, Eye, Heart, TrendingUp, Image as ImageIcon, VideoIcon } from 'lucide-react-native';

interface MediaItem {
  id: string;
  type: 'photo' | 'video';
  uri: string;
  thumbnail?: string;
  duration?: number;
  size?: number;
  isProfile?: boolean;
  createdAt: Date;
}

interface MediaStatsCardProps {
  mediaItems: MediaItem[];
  totalViews?: number;
  totalLikes?: number;
  profileViews?: number;
}

const MediaStatsCard = React.memo(({
  mediaItems,
  totalViews = 0,
  totalLikes = 0,
  profileViews = 0,
}: MediaStatsCardProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const photos = mediaItems.filter(item => item.type === 'photo');
  const videos = mediaItems.filter(item => item.type === 'video');
  const hasProfilePhoto = photos.some(photo => photo.isProfile);

  const stats = [
    {
      icon: ImageIcon,
      label: 'Photos',
      value: photos.length,
      subtext: hasProfilePhoto ? 'Profile photo set' : 'No profile photo',
      color: theme.colors.primary,
    },
    {
      icon: VideoIcon,
      label: 'Videos',
      value: videos.length,
      subtext: videos.length > 0 ? 'Introduction ready' : 'No introduction',
      color: theme.colors.success,
    },
    {
      icon: Eye,
      label: 'Profile Views',
      value: profileViews,
      subtext: 'This month',
      color: theme.colors.warning,
    },
    {
      icon: TrendingUp,
      label: 'Media Score',
      value: Math.round(((photos.length * 15) + (videos.length * 25) + (hasProfilePhoto ? 10 : 0))),
      subtext: 'Completion rating',
      color: theme.colors.primary,
    },
  ];

  const completionPercentage = Math.min(100, Math.round(
    ((photos.length / 3) * 50) + // Up to 50% for photos (3 photos = 50%)
    (videos.length > 0 ? 30 : 0) + // 30% for video
    (hasProfilePhoto ? 20 : 0) // 20% for profile photo
  ));

  return (
    <Card style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.header}>
        <BarChart3 size={20} color={theme.colors.primary} />
        <Text style={[styles.title, { color: theme.colors.text }]}>Media Statistics</Text>
      </View>

      {/* Completion Score */}
      <View style={[styles.completionCard, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.completionTitle, { color: theme.colors.text }]}>
          Profile Completion
        </Text>
        <View style={styles.completionContent}>
          <Text style={[styles.completionScore, { color: theme.colors.primary }]}>
            {completionPercentage}%
          </Text>
          <View style={[styles.completionBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={[
                styles.completionFill,
                { 
                  width: `${completionPercentage}%`, 
                  backgroundColor: completionPercentage >= 70 ? theme.colors.success : theme.colors.warning 
                }
              ]}
            />
          </View>
        </View>
        <Text style={[styles.completionText, { color: theme.colors.textSecondary }]}>
          {completionPercentage >= 70 
            ? 'Great! Your media profile looks complete'
            : 'Add more photos and videos to improve your profile'
          }
        </Text>
      </View>

      {/* Stats Grid */}
      <View style={styles.statsGrid}>
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <View key={index} style={[styles.statItem, { backgroundColor: theme.colors.background }]}>
              <IconComponent size={16} color={stat.color} />
              <Text style={[styles.statValue, { color: theme.colors.text }]}>
                {stat.value}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                {stat.label}
              </Text>
              <Text style={[styles.statSubtext, { color: theme.colors.textSecondary }]}>
                {stat.subtext}
              </Text>
            </View>
          );
        })}
      </View>

      {/* Recommendations */}
      <View style={styles.recommendations}>
        <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>
          Recommendations
        </Text>
        {photos.length === 0 && (
          <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
            • Add your first photo to make a great first impression
          </Text>
        )}
        {!hasProfilePhoto && photos.length > 0 && (
          <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
            • Set one of your photos as your profile picture
          </Text>
        )}
        {videos.length === 0 && (
          <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
            • Record a video introduction to stand out
          </Text>
        )}
        {photos.length < 3 && (
          <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
            • Add {3 - photos.length} more photo{3 - photos.length > 1 ? 's' : ''} for a complete gallery
          </Text>
        )}
      </View>
    </Card>
  );
});

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  completionCard: {
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  completionTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  completionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  completionScore: {
    fontSize: 20,
    fontWeight: '700',
    minWidth: 50,
  },
  completionBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  completionFill: {
    height: '100%',
    borderRadius: 3,
  },
  completionText: {
    fontSize: 12,
    lineHeight: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    gap: 4,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statSubtext: {
    fontSize: 10,
    textAlign: 'center',
  },
  recommendations: {
    gap: 8,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  recommendationText: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default MediaStatsCard; 