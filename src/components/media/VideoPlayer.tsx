import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Text,
  Dimensions,
  Platform,
} from 'react-native';
import { Video, ResizeMode, AVPlaybackStatus } from 'expo-av';
import { MaterialIcons } from '@expo/vector-icons';
import * as ScreenOrientation from 'expo-screen-orientation';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface VideoPlayerProps {
  uri: string;
  thumbnailUri?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  onError?: (error: string) => void;
  style?: any;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  uri,
  thumbnailUri,
  autoPlay = false,
  showControls = true,
  onError,
  style,
}) => {
  const videoRef = useRef<Video>(null);
  const [status, setStatus] = useState<AVPlaybackStatus | null>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    return () => {
      // Clean up
      if (isFullScreen) {
        ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
      }
    };
  }, [isFullScreen]);

  const onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    if (status.isLoaded) {
      setStatus(status);
      setIsPlaying(status.isPlaying);
      setCurrentTime(status.positionMillis / 1000);
      setDuration(status.durationMillis ? status.durationMillis / 1000 : 0);
      setIsLoading(false);
    } else {
      if (status.error) {
        setError(`An error occurred: ${status.error}`);
        if (onError) onError(status.error);
      }
    }
  };

  const togglePlayPause = async () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      await videoRef.current.pauseAsync();
    } else {
      await videoRef.current.playAsync();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = async () => {
    if (!videoRef.current) return;
    
    await videoRef.current.setIsMutedAsync(!isMuted);
    setIsMuted(!isMuted);
  };

  const toggleFullScreen = async () => {
    if (!videoRef.current) return;

    if (isFullScreen) {
      await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
    } else {
      await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
    }
    
    setIsFullScreen(!isFullScreen);
  };

  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const seekVideo = async (value: number) => {
    if (!videoRef.current || !status?.isLoaded) return;
    
    const seekPosition = Math.round(value * (status.durationMillis || 0));
    await videoRef.current.setPositionAsync(seekPosition);
  };

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <View style={styles.controlsContainer}>
        <View style={styles.progressContainer}>
          <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progress, 
                { width: `${(currentTime / (duration || 1)) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.timeText}>{formatTime(duration)}</Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={togglePlayPause} style={styles.controlButton}>
            <MaterialIcons
              name={isPlaying ? 'pause' : 'play-arrow'}
              size={28}
              color="#FFFFFF"
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={toggleMute} style={styles.controlButton}>
            <MaterialIcons
              name={isMuted ? 'volume-off' : 'volume-up'}
              size={24}
              color="#FFFFFF"
            />
          </TouchableOpacity>

          {Platform.OS !== 'web' && (
            <TouchableOpacity onPress={toggleFullScreen} style={styles.controlButton}>
              <MaterialIcons
                name={isFullScreen ? 'fullscreen-exit' : 'fullscreen'}
                size={24}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  if (error) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity 
        activeOpacity={0.8} 
        style={styles.videoContainer} 
        onPress={showControls ? togglePlayPause : undefined}
      >
        <Video
          ref={videoRef}
          style={styles.video}
          source={{ uri }}
          resizeMode={ResizeMode.CONTAIN}
          isLooping
          onPlaybackStatusUpdate={onPlaybackStatusUpdate}
          posterSource={thumbnailUri ? { uri: thumbnailUri } : undefined}
          usePoster={!!thumbnailUri}
          useNativeControls={false}
        />
        
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FFFFFF" />
          </View>
        )}
        
        {renderControls()}
      </TouchableOpacity>
    </View>
  );
};

const { width } = Dimensions.get('window');

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.text,
    borderRadius: 8,
    overflow: 'hidden',
    width: '100%',
    aspectRatio: 16 / 9,
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.shadow,
  },
  controlsContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
    padding: 10,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 8,
    borderRadius: 2,
  },
  progress: {
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 2,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  controlButton: {
    marginHorizontal: 15,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#FFFFFF',
    textAlign: 'center',
    padding: 20,
  },
});

export default VideoPlayer;