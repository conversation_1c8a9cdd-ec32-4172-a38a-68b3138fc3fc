/**
 * Clean Supabase Media Uploader Component
 * Following React Native + Expo best practices
 */

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { supabaseMediaService, VerificationDocumentType, UploadProgress } from '@services/SupabaseMediaService';
import { logger } from '@utils/logger';

interface SupabaseUploaderProps {
  userId: string;
  uploadType: 'avatar' | 'service_profile' | 'service_gallery' | 'room' | 'verification';
  verificationDocumentType?: VerificationDocumentType;
  onUploadComplete?: (result: { success: boolean; url?: string; error?: string }) => void;
  style?: any;
  buttonText?: string;
  disabled?: boolean;
}

export const SupabaseUploader: React.FC<SupabaseUploaderProps> = ({
  userId,
  uploadType,
  verificationDocumentType,
  onUploadComplete,
  style,
  buttonText,
  disabled = false
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);

  const getButtonText = (): string => {
    if (buttonText) return buttonText;
    
    switch (uploadType) {
      case 'avatar':
        return 'Upload Avatar';
      case 'service_profile':
        return 'Upload Profile Image';
      case 'service_gallery':
        return 'Upload Gallery Image';
      case 'room':
        return 'Upload Room Image';
      case 'verification':
        return `Upload ${verificationDocumentType?.replace('_', ' ').toUpperCase() || 'Document'}`;
      default:
        return 'Upload Image';
    }
  };

  const handleUpload = async () => {
    try {
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Camera roll access is needed to upload images.');
        return;
      }

      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: [['images']],
        allowsEditing: true,
        aspect: uploadType === 'avatar' ? [1, 1] : [4, 3],
        quality: 0.8,
      });

      if (result.canceled || !result.assets[0]) {
        return;
      }

      const imageUri = result.assets[0].uri;
      setIsUploading(true);
      setUploadProgress({ progress: 0, stage: 'preparing' });

      logger.info(`Starting ${uploadType} upload for user ${userId}`, 'SupabaseUploader');

      let uploadResult;

      // Call appropriate upload method based on type
      switch (uploadType) {
        case 'avatar':
          uploadResult = await supabaseMediaService.uploadAvatar(
            userId,
            imageUri,
            (progress) => setUploadProgress(progress)
          );
          
          // Update user profile if avatar upload successful
          if (uploadResult.success && uploadResult.url) {
            await supabaseMediaService.updateUserAvatar(userId, uploadResult.url);
          }
          break;

        case 'service_profile':
          uploadResult = await supabaseMediaService.uploadServiceProviderProfile(
            userId,
            imageUri,
            (progress) => setUploadProgress(progress)
          );
          break;

        case 'service_gallery':
          uploadResult = await supabaseMediaService.uploadServiceProviderGallery(
            userId,
            imageUri,
            (progress) => setUploadProgress(progress)
          );
          break;

        case 'room':
          uploadResult = await supabaseMediaService.uploadRoomImage(
            `listing_${Date.now()}`,
            imageUri,
            (progress) => setUploadProgress(progress)
          );
          break;

        case 'verification':
          if (!verificationDocumentType) {
            throw new Error('Verification document type is required for verification uploads');
          }
          
          uploadResult = await supabaseMediaService.uploadVerificationDocument(
            userId,
            imageUri,
            verificationDocumentType,
            (progress) => setUploadProgress(progress)
          );
          
          // Update user profile if verification upload successful
          if (uploadResult.success && uploadResult.url) {
            await supabaseMediaService.updateVerificationDocument(
              userId, 
              verificationDocumentType, 
              uploadResult.url
            );
          }
          break;

        default:
          throw new Error(`Unknown upload type: ${uploadType}`);
      }

      // Handle result
      if (uploadResult.success) {
        Alert.alert('Success', 'Image uploaded successfully!');
        logger.info(`${uploadType} upload completed successfully`, 'SupabaseUploader');
      } else {
        Alert.alert('Upload Failed', uploadResult.error || 'Unknown error occurred');
        logger.error(`${uploadType} upload failed: ${uploadResult.error}`, 'SupabaseUploader');
      }

      // Notify parent component
      onUploadComplete?.(uploadResult);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Upload error: ${errorMessage}`, 'SupabaseUploader');
      Alert.alert('Error', `Upload failed: ${errorMessage}`);
      
      onUploadComplete?.({ success: false, error: errorMessage });
    } finally {
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  const getProgressText = (): string => {
    if (!uploadProgress) return '';
    
    const percentage = Math.round(uploadProgress.progress * 100);
    return `${percentage}% - ${uploadProgress.stage}`;
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.uploadButton,
          disabled && styles.disabledButton,
          isUploading && styles.uploadingButton
        ]}
        onPress={handleUpload}
        disabled={disabled || isUploading}
      >
        {isUploading ? (
          <View style={styles.uploadingContainer}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={styles.uploadingText}>Uploading...</Text>
          </View>
        ) : (
          <Text style={styles.buttonText}>{getButtonText()}</Text>
        )}
      </TouchableOpacity>
      
      {uploadProgress && (
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>{getProgressText()}</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { width: `${uploadProgress.progress * 100}%` }
              ]} 
            />
          </View>
        </View>
      )}
    </View>
  );
};

/**
 * Specific verification document uploader
 */
interface VerificationUploaderProps {
  userId: string;
  documentType: VerificationDocumentType;
  onUploadComplete?: (result: { success: boolean; url?: string; error?: string }) => void;
  style?: any;
}

export const VerificationUploader: React.FC<VerificationUploaderProps> = ({
  userId,
  documentType,
  onUploadComplete,
  style
}) => {
  return (
    <SupabaseUploader
      userId={userId}
      uploadType="verification"
      verificationDocumentType={documentType}
      onUploadComplete={onUploadComplete}
      style={style}
      buttonText={`Upload ${documentType.replace('_', ' ').toUpperCase()}`}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  uploadButton: {
    backgroundColor: '#2563EB',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
  },
  uploadingButton: {
    backgroundColor: '#1D4ED8',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploadingText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontSize: 16,
  },
  progressContainer: {
    marginTop: 10,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 5,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#10B981',
  },
});

export default SupabaseUploader; 