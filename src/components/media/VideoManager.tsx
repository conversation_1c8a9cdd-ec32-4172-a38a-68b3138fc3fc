import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { Card } from '@components/ui';
import { Button } from '@design-system';
import { VideoIcon, Play, Pause, RotateCcw, Trash2, Upload, Camera } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface MediaItem {
  id: string;
  type: 'photo' | 'video';
  uri: string;
  thumbnail?: string;
  duration?: number;
  size?: number;
  isProfile?: boolean;
  createdAt: Date;
}

interface VideoManagerProps {
  videoIntroduction: string | null;
  videoItem?: MediaItem;
  onRecordNew: () => void;
  onSelectFromGallery: () => void;
  onDeleteVideo: () => void;
  onPreviewVideo?: (item: MediaItem) => void;
}

const VideoManager = React.memo(({
  videoIntroduction,
  videoItem,
  onRecordNew,
  onSelectFromGallery,
  onDeleteVideo,
  onPreviewVideo,
}: VideoManagerProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleDeleteVideo = () => {
    Alert.alert(
      'Delete Video',
      'Are you sure you want to delete your video introduction?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: onDeleteVideo },
      ]
    );
  };

  const formatDuration = (durationMs: number) => {
    const seconds = Math.round(durationMs / 1000);
    return `${seconds}s`;
  };

  const renderVideoPlayer = () => {
    if (!videoIntroduction) return null;

    return (
      <Card style={[styles.videoCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.videoTitle, { color: theme.colors.text }]}>Video Introduction</Text>
        
        <Video
          source={{ uri: videoIntroduction }}
          style={styles.videoPlayer}
          resizeMode={ResizeMode.CONTAIN}
          useNativeControls
          shouldPlay={isPlaying}
          onPlaybackStatusUpdate={(status: any) => {
            if (status.didJustFinish) {
              setIsPlaying(false);
            }
          }}
        />

        <View style={styles.videoInfo}>
          {videoItem?.duration && (
            <Text style={[styles.videoDuration, { color: theme.colors.textSecondary }]}>
              Duration: {formatDuration(videoItem.duration)}
            </Text>
          )}
          {videoItem?.createdAt && (
            <Text style={[styles.videoDate, { color: theme.colors.textSecondary }]}>
              Created: {videoItem.createdAt.toLocaleDateString()}
            </Text>
          )}
        </View>

        <View style={styles.videoActions}>
          <Button
            onPress={() => onPreviewVideo?.(videoItem!)}
            style={styles.videoActionButton}
            variant="outlined"
            leftIcon={<Play size={16} color={theme.colors.primary} />}
          >
            Preview
          </Button>
          
          <Button
            onPress={onRecordNew}
            style={styles.videoActionButton}
            variant="outlined"
            leftIcon={<RotateCcw size={16} color={theme.colors.primary} />}
          >
            Re-record
          </Button>
          
          <Button
            onPress={handleDeleteVideo}
            style={styles.videoActionButton}
            variant="outlined"
            leftIcon={<Trash2 size={16} color={theme.colors.error} />}
            color="error"
          >
            Delete
          </Button>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <Card style={[styles.emptyVideoCard, { backgroundColor: theme.colors.surface }]}>
      <VideoIcon size={48} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Video Introduction</Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Record a 60-second video to introduce yourself to potential roommates. 
        Share your personality, interests, and what you're looking for in a living situation.
      </Text>
      
      <View style={styles.emptyActions}>
        <Button 
          onPress={onRecordNew} 
          style={styles.emptyActionButton}
          leftIcon={<Camera size={16} color="#FFFFFF" />}
        >
          Record Video
        </Button>
        
        <Button 
          onPress={onSelectFromGallery} 
          style={styles.emptyActionButton}
          variant="outlined"
          leftIcon={<Upload size={16} color={theme.colors.primary} />}
        >
          Upload Video
        </Button>
      </View>
    </Card>
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Video Introduction</Text>
      <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
        Let potential roommates get to know you with a personal video
      </Text>
      
      {videoIntroduction ? renderVideoPlayer() : renderEmptyState()}
    </View>
  );
});

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  sectionSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  videoCard: {
    padding: 16,
    gap: 16,
    borderRadius: 12,
  },
  videoTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  videoPlayer: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  videoInfo: {
    gap: 4,
  },
  videoDuration: {
    fontSize: 14,
  },
  videoDate: {
    fontSize: 14,
  },
  videoActions: {
    flexDirection: 'row',
    gap: 12,
  },
  videoActionButton: {
    flex: 1,
  },
  emptyVideoCard: {
    padding: 32,
    alignItems: 'center',
    gap: 16,
    borderRadius: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  emptyActions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  emptyActionButton: {
    flex: 1,
  },
});

export default VideoManager; 