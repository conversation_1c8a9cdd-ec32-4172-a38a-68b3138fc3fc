import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Image, Dimensions } from 'react-native';
import { useColorScheme } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Video, ResizeMode } from 'expo-av';
import { X, Download, Share2, Trash2, Edit3 } from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MediaItem {
  id: string;
  type: 'photo' | 'video';
  uri: string;
  thumbnail?: string;
  duration?: number;
  size?: number;
  isProfile?: boolean;
  createdAt: Date;
}

interface MediaPreviewProps {
  visible: boolean;
  mediaItem: MediaItem | null;
  onClose: () => void;
  onDelete?: (item: MediaItem) => void;
  onSetProfile?: (item: MediaItem) => void;
  onShare?: (item: MediaItem) => void;
  onDownload?: (item: MediaItem) => void;
}

const MediaPreview = React.memo(({
  visible,
  mediaItem,
  onClose,
  onDelete,
  onSetProfile,
  onShare,
  onDownload,
}: MediaPreviewProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [videoStatus, setVideoStatus] = useState({});

  if (!mediaItem) return null;

  const handleAction = (action: () => void) => {

    action();
    onClose();
  };

  const renderMediaContent = () => {
    if (mediaItem.type === 'photo') {
      return (
        <Image
          source={{ uri: mediaItem.uri }}
          style={styles.previewImage}
          resizeMode="contain"
        />
      );
    } else {
      return (
        <Video
          source={{ uri: mediaItem.uri }}
          style={styles.previewVideo}
          resizeMode={ResizeMode.CONTAIN}
          useNativeControls
          shouldPlay={false}
          onPlaybackStatusUpdate={setVideoStatus}
        />
      );
    }
  };

  const renderActions = () => (
    <View style={styles.actionsContainer}>
      {onDownload && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
          onPress={() => handleAction(() => onDownload(mediaItem))}
        >
          <Download size={20} color="#FFFFFF" />
        </TouchableOpacity>
      )}

      {onShare && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
          onPress={() => handleAction(() => onShare(mediaItem))}
        >
          <Share2 size={20} color="#FFFFFF" />
        </TouchableOpacity>
      )}

      {onSetProfile && !mediaItem.isProfile && mediaItem.type === 'photo' && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
          onPress={() => handleAction(() => onSetProfile(mediaItem))}
        >
          <Edit3 size={20} color="#FFFFFF" />
        </TouchableOpacity>
      )}

      {onDelete && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
          onPress={() => handleAction(() => onDelete(mediaItem))}
        >
          <Trash2 size={20} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderMediaInfo = () => (
    <View style={styles.mediaInfo}>
      <Text style={[styles.mediaTitle, { color: theme.colors.text }]}>
        {mediaItem.type === 'photo' ? 'Photo' : 'Video Introduction'}
      </Text>
      
      {mediaItem.isProfile && (
        <View style={[styles.profileBadge, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.profileBadgeText}>PROFILE PHOTO</Text>
        </View>
      )}
      
      <Text style={[styles.mediaDate, { color: theme.colors.textSecondary }]}>
        {mediaItem.createdAt.toLocaleDateString()}
      </Text>
      
      {mediaItem.type === 'video' && mediaItem.duration && (
        <Text style={[styles.mediaDuration, { color: theme.colors.textSecondary }]}>
          Duration: {Math.round(mediaItem.duration / 1000)}s
        </Text>
      )}
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      statusBarTranslucent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.overlay }]}>
        <View style={styles.previewContainer}>
          {/* Header */}
          <View style={styles.previewHeader}>
            <TouchableOpacity
              style={[styles.closeButton, { backgroundColor: theme.colors.overlay }]}
              onPress={onClose}
            >
              <X size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {/* Media Content */}
          <View style={styles.mediaContainer}>
            {renderMediaContent()}
          </View>

          {/* Media Info */}
          {renderMediaInfo()}

          {/* Actions */}
          {renderActions()}
        </View>
      </View>
    </Modal>
  );
});

const createStyles = (theme: any) => StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'space-between',
  },
  previewHeader: {
    position: 'absolute',
    top: 50,
    right: 16,
    zIndex: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  previewImage: {
    width: screenWidth - 32,
    height: screenHeight * 0.7,
  },
  previewVideo: {
    width: screenWidth - 32,
    height: screenHeight * 0.7,
  },
  mediaInfo: {
    position: 'absolute',
    bottom: 120,
    left: 16,
    right: 16,
    alignItems: 'center',
    gap: 8,
  },
  mediaTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  profileBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
  },
  profileBadgeText: {
    color: theme.colors.background,
    fontSize: 12,
    fontWeight: '600',
  },
  mediaDate: {
    fontSize: 14,
    textAlign: 'center',
  },
  mediaDuration: {
    fontSize: 14,
    textAlign: 'center',
  },
  actionsContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingHorizontal: 16,
  },
  actionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MediaPreview; 