import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { ImageIcon, Plus, Eye, Trash2, Edit3 } from 'lucide-react-native';
import { Dimensions } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface MediaItem {
  id: string;
  type: 'photo' | 'video';
  uri: string;
  thumbnail?: string;
  duration?: number;
  size?: number;
  isProfile?: boolean;
  createdAt: Date;
}

interface MediaGalleryProps {
  mediaItems: MediaItem[];
  onAddPhoto: () => void;
  onPreviewMedia: (item: MediaItem) => void;
  onDeleteMedia: (item: MediaItem) => void;
  onSetProfilePhoto: (item: MediaItem) => void;
}

const MediaGallery = React.memo(({
  mediaItems,
  onAddPhoto,
  onPreviewMedia,
  onDeleteMedia,
  onSetProfilePhoto,
}: MediaGalleryProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const photos = mediaItems.filter(item => item.type === 'photo');

  const handleDeletePhoto = (item: MediaItem) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => onDeleteMedia(item) },
      ]
    );
  };

  const handleSetProfilePhoto = (item: MediaItem) => {
    Alert.alert(
      'Set Profile Photo',
      'Set this as your main profile photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Set', onPress: () => onSetProfilePhoto(item) },
      ]
    );
  };

  const renderEmptyGallery = () => (
    <View style={[styles.emptyCard, { backgroundColor: theme.colors.surface }]}>
      <ImageIcon size={48} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Photos Yet</Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
        Add photos to your profile to help potential roommates get to know you better
      </Text>
      <TouchableOpacity
        style={[styles.emptyButton, { backgroundColor: theme.colors.primary }]}
        onPress={onAddPhoto}
      >
        <Text style={styles.emptyButtonText}>Add Your First Photo</Text>
      </TouchableOpacity>
    </View>
  );

  if (photos.length === 0) {
    return renderEmptyGallery();
  }

  return (
    <View style={styles.galleryContainer}>
      <View style={styles.galleryHeader}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Photos ({photos.length})
        </Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={onAddPhoto}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
        Showcase your personality and lifestyle with photos
      </Text>

      <View style={styles.photoGrid}>
        {photos.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.photoItem, { backgroundColor: theme.colors.border }]}
            onPress={() => onPreviewMedia(item)}
          >
            <Image source={{ uri: item.uri }} style={styles.photoImage} />
            
            {item.isProfile && (
              <View style={[styles.profileBadge, { backgroundColor: theme.colors.primary }]}>
                <Text style={styles.profileBadgeText}>PROFILE</Text>
              </View>
            )}
            
            <View style={styles.photoActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
                onPress={() => onPreviewMedia(item)}
              >
                <Eye size={14} color="#FFFFFF" />
              </TouchableOpacity>
              
              {!item.isProfile && (
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
                  onPress={() => handleSetProfilePhoto(item)}
                >
                  <Edit3 size={14} color="#FFFFFF" />
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.overlay }]}
                onPress={() => handleDeletePhoto(item)}
              >
                <Trash2 size={14} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
});

const createStyles = (theme: any) => StyleSheet.create({
  galleryContainer: {
    gap: 16,
  },
  galleryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  sectionSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  photoItem: {
    width: (screenWidth - 56) / 3, // 3 columns with gaps
    aspectRatio: 1,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  profileBadge: {
    position: 'absolute',
    top: 4,
    left: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  profileBadgeText: {
    color: theme.colors.background,
    fontSize: 10,
    fontWeight: '600',
  },
  photoActions: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    flexDirection: 'row',
    gap: 4,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCard: {
    padding: 32,
    alignItems: 'center',
    gap: 12,
    borderRadius: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  emptyButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  emptyButtonText: {
    color: theme.colors.background,
    fontSize: 14,
    fontWeight: '600',
  },
});

export default MediaGallery; 