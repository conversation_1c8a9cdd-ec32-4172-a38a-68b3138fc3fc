import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Easing } from 'react-native';
import { Shield } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface BackgroundCheckBadgeProps {
  size?: 'small' | 'medium' | 'large';
  showTooltip?: boolean;
  style?: any;
  checkLevel?: 'basic' | 'standard' | 'comprehensive';
  animate?: boolean;
  onPress?: () => void;
}

/**
 * An enhanced background check badge component with animation and check levels
 */
export default function BackgroundCheckBadge({ 
  size = 'medium', 
  showTooltip = false, 
  style,
  checkLevel = 'standard',
  animate = false,
  onPress
}: BackgroundCheckBadgeProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const pulseAnim = React.useRef(new Animated.Value(1)).current;
  
  // Define size configurations
  const sizeConfig = {
    small: {
      iconSize: 16,
      badgeSize: 22,
      borderWidth: 1.5,
    },
    medium: {
      iconSize: 20,
      badgeSize: 28,
      borderWidth: 2,
    },
    large: {
      iconSize: 24,
      badgeSize: 34,
      borderWidth: 2.5,
    },
  };
  
  const { iconSize, badgeSize, borderWidth } = sizeConfig[size];
  
  // Get color based on check level
  const getCheckLevelColor = () => {
    switch (checkLevel) {
      case 'comprehensive':
        return theme.colors.success; // Dark green for comprehensive
      case 'standard':
        return theme.colors.success; // Standard green
      case 'basic':
      default:
        return theme.colors.successLight; // Light green for basic
    }
  };
  
  // Get tooltip text based on check level
  const getTooltipText = () => {
    switch (checkLevel) {
      case 'comprehensive':
        return 'Comprehensive background check including criminal, credit, and employment history';
      case 'standard':
        return 'Standard background check including criminal history verification';
      case 'basic':
      default:
        return 'Basic background check verification';
    }
  };
  
  // Start pulse animation if animate is true
  React.useEffect(() => {
    if (animate) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
    
    return () => {
      pulseAnim.stopAnimation();
    };
  }, [animate, pulseAnim]);
  
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (showTooltip) {
      // Navigate to background check info page
      router.push('/verification');
    }
  };
  
  const badgeColor = getCheckLevelColor();
  
  return (
    <TouchableOpacity 
      onPress={handlePress}
      disabled={!showTooltip && !onPress}
      style={[
        styles.container,
        style
      ]}
      activeOpacity={0.7}
    >
      <Animated.View 
        style={[
          styles.badgeContainer,
          { 
            height: badgeSize, 
            width: badgeSize,
            borderWidth,
            borderColor: badgeColor,
            transform: [{ scale: animate ? pulseAnim : 1 }]
          }
        ]}
      >
        <Shield size={iconSize} color={badgeColor} />
      </Animated.View>
      
      {showTooltip && (
        <View style={styles.tooltip}>
          <Text style={styles.tooltipText}>
            {getTooltipText()}
          </Text>
          <View style={styles.tooltipArrow} />
        </View>
      )}
    </TouchableOpacity>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    backgroundColor: theme.colors.background,
    ...theme.shadows.sm,
  },
  tooltip: {
    position: 'absolute',
    bottom: '100%',
    left: -95,
    width: 200,
    backgroundColor: theme.colors.tooltipBackground,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    ...theme.shadows.md,
  },
  tooltipText: {
    color: theme.colors.background,
    fontSize: 12,
    textAlign: 'center',
  },
  tooltipArrow: {
    position: 'absolute',
    bottom: -6,
    left: '50%',
    marginLeft: -6,
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6,
    borderStyle: 'solid',
    backgroundColor: 'transparent',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: theme.colors.tooltipBackground,
  },
});