import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView, Alert, Modal } from 'react-native';
import { Text } from 'react-native';
import { Button } from '@design-system/components';
import {
  CheckSquare,
  Square,
  AlertCircle,
  CheckCircle,
  Clock,
  MoreHorizontal,
  Plus,
  DollarSign,
  Home,
  Zap,
  MoreVertical,
  X,
} from 'lucide-react-native';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';
import { formatDistanceToNow } from 'date-fns';
import { useColorFix } from '@hooks/useColorFix';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface AgreementComplianceProps {
  agreementId: string;
  onRaiseDispute: (sectionId: string, title: string) => void;
}

interface ComplianceItem {
  id: string;
  section_id: string;
  section_title: string;
  description: string;
  due_date?: string;
  is_completed: boolean;
  assigned_to: string[];
  assigned_to_names?: string[];
  created_at: string;
  updated_at: string;
  priority: string;
}

interface ComplianceSummary {
  total: number;
  completed: number;
  overdue: number;
  upcoming: number;
}

export default function AgreementCompliance({
  agreementId,
  onRaiseDispute,
}: AgreementComplianceProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const { state, actions } = useAuth();
  const userId = state.user?.id;
  const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>([]);
  const [complianceSummary, setComplianceSummary] = useState<ComplianceSummary>({
    total: 0,
    completed: 0,
    overdue: 0,
    upcoming: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddItem, setShowAddItem] = useState(false);
  const [newItemType, setNewItemType] = useState<'custom' | 'template'>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  useEffect(() => {
    fetchComplianceItems();
  }, [agreementId]);

  const fetchComplianceItems = async () => {
    try {
      setLoading(true);

      // Fetch compliance items from the permanent agreement_compliance_items table
      const { data, error } = await supabase
        .from('agreement_compliance_items')
        .select(
          `
          *,
          section:agreement_sections(section_title, section_key)
        `
        )
        .eq('agreement_id', agreementId)
        .order('due_date', { ascending: true });

      if (error) {
        throw error;
      }

      // Fetch assigned user names
      if (data && data.length > 0) {
        const formattedData = await Promise.all(
          data.map(async item => {
            let assignedToNames: string[] = [];
            
            if (item.assigned_to && item.assigned_to.length > 0) {
              const { data: userData, error: userError } = await supabase
                .from('user_profiles')
                .select('id, first_name, last_name')
                .in('id', item.assigned_to);

              if (!userError && userData) {
                assignedToNames = userData.map(u => `${u.first_name} ${u.last_name}`.trim());
              }
            }

            return {
              ...item,
              section_title: item.section?.section_title || 'General Compliance',
              assigned_to_names: assignedToNames,
            };
          })
        );

        setComplianceItems(formattedData);
        
        // Calculate compliance summary
        const now = new Date();
        const completed = formattedData.filter(item => item.is_completed).length;
        const overdue = formattedData.filter(item => 
          !item.is_completed && item.due_date && new Date(item.due_date) < now
        ).length;
        const upcoming = formattedData.filter(item => {
          if (item.is_completed || !item.due_date) return false;
          const dueDate = new Date(item.due_date);
          const daysDiff = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
          return daysDiff >= 0 && daysDiff <= 7; // Due within next 7 days
        }).length;
        
        setComplianceSummary({
          total: formattedData.length,
          completed,
          overdue,
          upcoming
        });
      } else {
        setComplianceItems([]);
        setComplianceSummary({ total: 0, completed: 0, overdue: 0, upcoming: 0 });
      }
    } catch (err) {
      console.error('Error fetching compliance items:', err);
      Alert.alert('Error', 'Failed to load compliance items');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleToggleCompleted = async (itemId: string, currentStatus: boolean) => {
    try {
      // Update the item status in the agreement_compliance_items table
      const { error } = await supabase
        .from('agreement_compliance_items')
        .update({
          is_completed: !currentStatus,
          updated_at: new Date().toISOString(),
        })
        .eq('id', itemId);

      if (error) {
        throw error;
      }

      // Update local state
      setComplianceItems(prevItems =>
        prevItems.map(item =>
          item.id === itemId
            ? { 
                ...item, 
                is_completed: !currentStatus, 
                updated_at: new Date().toISOString() 
              }
            : item
        )
      );
    } catch (err) {
      console.error('Error updating compliance item:', err);
      Alert.alert('Error', 'Failed to update item status');
    }
  };

  const handleDeleteItem = async (itemId: string, itemDescription: string) => {
    Alert.alert(
      'Delete Task',
      `Are you sure you want to delete "${itemDescription}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('agreement_compliance_items')
                .delete()
                .eq('id', itemId);

              if (error) throw error;

              // Update local state
              setComplianceItems(prevItems => 
                prevItems.filter(item => item.id !== itemId)
              );
            } catch (err) {
              console.error('Error deleting compliance item:', err);
              Alert.alert('Error', 'Failed to delete task');
            }
          }
        }
      ]
    );
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchComplianceItems();
  };

  const formatDueDate = (dateString?: string) => {
    if (!dateString) return 'No due date';

    const dueDate = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (dueDate < today) {
      return `Overdue by ${formatDistanceToNow(dueDate, { addSuffix: false })}`;
    }

    return `Due ${formatDistanceToNow(dueDate, { addSuffix: true })}`;
  };

  const isItemAssignedToUser = (item: ComplianceItem) => {
    return item.assigned_to.includes(userId || '');
  };

  const complianceTemplates = {
    rent_payment: {
      title: 'Monthly Rent Payment',
      description: 'Pay monthly rent by the due date',
      priority: 'critical' as const,
      recurring: 'monthly',
      dueInDays: 30,
    },
    utilities_payment: {
      title: 'Utilities Payment',
      description: 'Split and pay utility bills',
      priority: 'high' as const,
      recurring: 'monthly',
      dueInDays: 15,
    },
    kitchen_cleaning: {
      title: 'Kitchen Deep Clean',
      description: 'Complete weekly kitchen deep cleaning',
      priority: 'medium' as const,
      recurring: 'weekly',
      dueInDays: 7,
    },
    common_area_cleaning: {
      title: 'Common Area Maintenance',
      description: 'Clean and organize common living spaces',
      priority: 'medium' as const,
      recurring: 'weekly',
      dueInDays: 7,
    },
    guest_notification: {
      title: 'Guest Notification',
      description: 'Notify roommates 24 hours before guests arrive',
      priority: 'low' as const,
      recurring: 'as_needed',
      dueInDays: 1,
    },
    maintenance_request: {
      title: 'Maintenance Issues',
      description: 'Report and coordinate repairs promptly',
      priority: 'high' as const,
      recurring: 'as_needed',
      dueInDays: 3,
    },
  };

  const handleAddComplianceItem = async (templateKey?: string) => {
    if (!userId) return;

    const template = templateKey ? complianceTemplates[templateKey] : null;
    
    if (template) {
      // Calculate due date based on template
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + template.dueInDays);

      const newItem = {
        agreement_id: agreementId,
        description: template.description,
        priority: template.priority,
        due_date: dueDate.toISOString(),
        assigned_to: [userId], // Assign to current user initially
        is_completed: false,
      };

      try {
        const { data, error } = await supabase
          .from('agreement_compliance_items')
          .insert([newItem])
          .select()
          .single();

        if (error) throw error;

        // Refresh compliance items
        await fetchComplianceItems();
        setShowAddItem(false);
        setSelectedTemplate('');
      } catch (error) {
        console.error('Error adding compliance item:', error);
      }
    }
  };

  const handleCustomItemSubmit = async (customData: {
    description: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    dueDate: Date;
  }) => {
    if (!userId) return;

    const newItem = {
      agreement_id: agreementId,
      description: customData.description,
      priority: customData.priority,
      due_date: customData.dueDate.toISOString(),
      assigned_to: [userId],
      is_completed: false,
    };

    try {
      const { data, error } = await supabase
        .from('agreement_compliance_items')
        .insert([newItem])
        .select()
        .single();

      if (error) throw error;

      await fetchComplianceItems();
      setShowAddItem(false);
    } catch (error) {
      console.error('Error adding custom compliance item:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return theme.colors.error;
      case 'high': return '#F59E0B';
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.textSecondary;
    }
  };

   const getStatusColor = (item: ComplianceItem) => {
     if (item.is_completed) return theme.colors.success;
     
     const dueDate = new Date(item.due_date);
     const now = new Date();
     const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
     
     if (diffDays < 0) return theme.colors.error; // Overdue
     if (diffDays <= 1) return theme.colors.warning; // Due soon
     return theme.colors.textSecondary; // Normal
   };

  const renderComplianceItem = (item: ComplianceItem) => {
    const isAssignedToMe = isItemAssignedToUser(item);
    const isOverdue = item.due_date && new Date(item.due_date) < new Date() && !item.is_completed;

    return (
      <View key={item.id} style={styles.complianceItem}>
        <TouchableOpacity
          style={styles.checkboxContainer}
          onPress={() => handleToggleCompleted(item.id, item.is_completed)}
          disabled={!isAssignedToMe}
        >
          {item.is_completed ? (
            <CheckSquare size={22} color={theme.colors.success} />
          ) : (
            <Square size={22} color={isOverdue ? theme.colors.error : theme.colors.textSecondary} />
          )}
        </TouchableOpacity>

        <View style={styles.contentContainer}>
          <View style={styles.headerRow}>
            <Text style={[styles.itemTitle, item.is_completed && styles.completedText]}>
              {item.description}
            </Text>

            {isOverdue && !item.is_completed && (
              <View style={styles.overdueTag}>
                <AlertCircle size={14} color={theme.colors.background} />
                <Text style={styles.overdueText}>Overdue</Text>
              </View>
            )}
          </View>

          <Text style={styles.sectionTitle}>{item.section_title}</Text>

          <View style={styles.metaRow}>
            <View style={styles.timeContainer}>
              <Clock size={14} color={theme.colors.textSecondary} />
              <Text style={styles.dueText}>{formatDueDate(item.due_date)}</Text>
            </View>

            {item.assigned_to_names && item.assigned_to_names.length > 0 && (
              <Text style={styles.assigneeText}>
                Assigned to: {item.assigned_to_names.join(', ')}
              </Text>
            )}
          </View>
        </View>

        <TouchableOpacity
          style={styles.actionsButton}
          onPress={() => {
            Alert.alert(
              'Task Actions',
              'What would you like to do with this task?',
              [
                { text: 'Cancel', style: 'cancel' },
                { 
                  text: 'Delete Task', 
                  style: 'destructive',
                  onPress: () => handleDeleteItem(item.id, item.description)
                },
                { 
                  text: 'Raise Dispute', 
                  onPress: () => onRaiseDispute(item.section_id, item.description)
                }
              ]
            );
          }}
        >
          <MoreHorizontal size={18} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <CheckCircle size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyStateTitle}>No Compliance Items</Text>
      <Text style={styles.emptyStateText}>
        No compliance tasks have been set up for this agreement yet. Compliance items can be added to track agreement obligations and responsibilities.
      </Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Compliance Summary */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Compliance Overview</Text>
        <View style={styles.summaryCards}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryNumber}>{complianceSummary.completed}</Text>
            <Text style={styles.summaryLabel}>Completed</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryNumber, { color: theme.colors.warning }]}>
              {complianceSummary.upcoming}
            </Text>
            <Text style={styles.summaryLabel}>Upcoming</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryNumber, { color: theme.colors.error }]}>
              {complianceSummary.overdue}
            </Text>
            <Text style={styles.summaryLabel}>Overdue</Text>
          </View>
        </View>
      </View>

      {/* Compliance Items */}
      <View style={styles.itemsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Compliance Tasks</Text>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={() => setShowAddItem(true)}
          >
            <Plus size={16} color={theme.colors.background} />
            <Text style={styles.addButtonText}>Add Task</Text>
          </TouchableOpacity>
        </View>

        {complianceItems.length === 0 ? (
          <View style={styles.emptyState}>
            <CheckSquare size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>No Compliance Items</Text>
            <Text style={styles.emptyStateSubtext}>
              Add tasks to track agreement obligations and responsibilities
            </Text>
            
            {/* Quick Template Options */}
            <View style={styles.quickTemplates}>
              <Text style={styles.quickTemplatesTitle}>Quick Add:</Text>
              <View style={styles.templateButtons}>
                <TouchableOpacity 
                  style={styles.templateButton}
                  onPress={() => handleAddComplianceItem('rent_payment')}
                >
                  <DollarSign size={16} color={theme.colors.primary} />
                  <Text style={styles.templateButtonText}>Rent</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.templateButton}
                  onPress={() => handleAddComplianceItem('kitchen_cleaning')}
                >
                  <Home size={16} color={theme.colors.primary} />
                  <Text style={styles.templateButtonText}>Cleaning</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.templateButton}
                  onPress={() => handleAddComplianceItem('utilities_payment')}
                >
                  <Zap size={16} color={theme.colors.primary} />
                  <Text style={styles.templateButtonText}>Utilities</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.itemsList}>
            {complianceItems.map((item) => renderComplianceItem(item))}
          </View>
        )}
      </View>

      {/* Add Item Modal */}
      {showAddItem && (
        <Modal visible={showAddItem} transparent animationType="slide">
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add Compliance Task</Text>
                <TouchableOpacity onPress={() => setShowAddItem(false)}>
                  <X size={24} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
              
              {/* Template Selection */}
              <View style={styles.templateSelection}>
                <Text style={styles.templateSelectionTitle}>Choose a template or create custom:</Text>
                
                <ScrollView style={styles.templatesContainer} showsVerticalScrollIndicator={false}>
                  {Object.entries(complianceTemplates).map(([key, template]) => (
                    <TouchableOpacity
                      key={key}
                      style={[
                        styles.templateOption,
                        selectedTemplate === key && styles.selectedTemplate
                      ]}
                      onPress={() => setSelectedTemplate(key)}
                    >
                      <View style={styles.templateOptionContent}>
                        <Text style={styles.templateOptionTitle}>{template.title}</Text>
                        <Text style={styles.templateOptionDescription}>{template.description}</Text>
                        <View style={styles.templateOptionMeta}>
                          <View style={[
                            styles.templatePriority,
                            { backgroundColor: `${getPriorityColor(template.priority)}20` }
                          ]}>
                            <Text style={[
                              styles.templatePriorityText,
                              { color: getPriorityColor(template.priority) }
                            ]}>
                              {template.priority.toUpperCase()}
                            </Text>
                          </View>
                          <Text style={styles.templateDue}>Due in {template.dueInDays} days</Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
                
                <View style={styles.modalActions}>
                  <TouchableOpacity 
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowAddItem(false);
                      setSelectedTemplate('');
                    }}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={[
                      styles.confirmButton,
                      !selectedTemplate && styles.disabledButton
                    ]}
                    onPress={() => handleAddComplianceItem(selectedTemplate)}
                    disabled={!selectedTemplate}
                  >
                    <Text style={styles.confirmButtonText}>Add Task</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </ScrollView>
  );
}

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  refreshButton: {
    paddingHorizontal: 12,
  },
  scrollContainer: {
    flex: 1,
  },
  complianceItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  checkboxContainer: {
    paddingRight: 12,
    paddingTop: 2,
  },
  contentContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
    marginRight: 8,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: theme.colors.textSecondary,
  },
  sectionTitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    flexWrap: 'wrap',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dueText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  assigneeText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  actionsButton: {
    padding: 4,
  },
  overdueTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.error,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  overdueText: {
    fontSize: 12,
    color: theme.colors.background,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyState: {
    alignItems: 'center',
    padding: 24,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    maxWidth: '80%',
  },
  summaryContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.lg,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  summaryCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryCard: {
    alignItems: 'center',
    flex: 1,
  },
  summaryNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.success,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  itemsSection: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    gap: theme.spacing.xs,
  },
  addButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
    fontSize: 14,
  },
  quickTemplates: {
    marginTop: theme.spacing.lg,
    alignItems: 'center',
  },
  quickTemplatesTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  templateButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  templateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
    gap: theme.spacing.xs,
  },
  templateButtonText: {
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: 12,
  },
     itemsList: {
     gap: theme.spacing.sm,
   },
   complianceItem: {
     flexDirection: 'row',
     backgroundColor: theme.colors.surface,
     padding: theme.spacing.md,
     borderRadius: theme.borderRadius.md,
     borderWidth: 1,
     borderColor: theme.colors.border,
     alignItems: 'flex-start',
   },
  checkbox: {
    marginRight: theme.spacing.md,
    marginTop: 2,
  },
  itemContent: {
    flex: 1,
  },
     itemDescription: {
     fontSize: 16,
     fontWeight: '500',
     color: theme.colors.text,
     marginBottom: theme.spacing.sm,
   },
   completedText: {
     textDecorationLine: 'line-through',
     color: theme.colors.textSecondary,
   },
  itemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  priorityBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.xs,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  dueDate: {
    fontSize: 12,
    fontWeight: '500',
  },
  assignedUsers: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  itemActions: {
    padding: theme.spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: theme.borderRadius.lg,
    borderTopRightRadius: theme.borderRadius.lg,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  templateSelection: {
    padding: theme.spacing.lg,
  },
  templateSelectionTitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  templatesContainer: {
    maxHeight: 300,
    marginBottom: theme.spacing.lg,
  },
  templateOption: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedTemplate: {
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}10`,
  },
  templateOptionContent: {},
  templateOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  templateOptionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  templateOptionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  templatePriority: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.xs,
  },
  templatePriorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  templateDue: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  modalActions: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.text,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: theme.colors.textSecondary,
  },
  confirmButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
  },
});
