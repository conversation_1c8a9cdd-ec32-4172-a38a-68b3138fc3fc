import React, { useRef, useState } from 'react';
import { View, StyleSheet, TextInput } from 'react-native';
import { createClientComponentClient } from '@supabase/ssr';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import SignatureCanvas from 'react-native-signature-canvas';
import * as ImagePicker from 'expo-image-picker';
import { format } from 'date-fns';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SignatureCaptureProps {
  agreementId: string;
  onSignatureComplete: (signatureData: SignatureData) => void;
  signatureType?: 'draw' | 'type' | 'upload';
}

export interface SignatureData {
  type: 'draw' | 'type' | 'upload';
  data: string;
  timestamp: string;
  userId: string;
}

export default function SignatureCapture({
  agreementId, 
  onSignatureComplete,
  signatureType = 'draw' 
}: SignatureCaptureProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const supabase = createClientComponentClient();
  const { authState } = useAuthCompat();
  const user = authState.user;
  const [activeType, setActiveType] = useState(signatureType);
  const [typedSignature, setTypedSignature] = useState('');
  const signaturePadRef = useRef<SignatureCanvas>(null);
  const [error, setError] = useState<string | null>(null);

  const handleDrawSignature = async () => {
    if (!signaturePadRef.current) return;
    
    try {
      // SignatureCanvas uses readSignature method
      signaturePadRef.current.readSignature();
    } catch (error) {
      setError('Please draw your signature');
    }
  };

  const handleSignature = (signature: string) => {
    if (!signature) {
      setError('Please draw your signature');
      return;
    }

    const signatureData: SignatureData = {
      type: 'draw',
      data: signature,
      timestamp: new Date().toISOString(),
      userId: authState.user?.id || '',
    };

    onSignatureComplete(signatureData);
  };

  const handleEmpty = () => {
    setError('Please draw your signature');
  };

  const handleTypeSignature = () => {
    if (!typedSignature.trim()) {
      setError('Please type your signature');
      return;
    }

    const signatureData: SignatureData = {
      type: 'type',
      data: typedSignature,
      timestamp: new Date().toISOString(),
      userId: authState.user?.id || '',
    };

    onSignatureComplete(signatureData);
  };

  const handleUploadSignature = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'], // ✅ Modern API - no deprecated enum
        allowsEditing: true,
        aspect: [4, 1],
        quality: 1,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const signatureData: SignatureData = {
          type: 'upload',
          data: result.assets[0].uri,
          timestamp: new Date().toISOString(),
          userId: authState.user?.id || '',
        };

        onSignatureComplete(signatureData);
      }
    } catch (error) {
      setError('Error uploading signature image');
    }
  };

  const renderSignatureInput = () => {
    switch (activeType) {
      case 'draw':
        return (
          <View style={styles.signaturePadContainer}>
            <SignatureCanvas
              ref={signaturePadRef}
              style={styles.signaturePad}
              onOK={handleSignature}
              onEmpty={handleEmpty}
              onClear={() => setError(null)}
              descriptionText="Sign here"
              clearText="Clear"
              confirmText="Save"
              webStyle={`
                .m-signature-pad--footer {
                  display: none;
                }
                .m-signature-pad {
                  box-shadow: none;
                  border: 2px solid ${AgreementTheme.colors.border};
                  border-radius: 8px;
                }
              `}
            />
            <Button 
              onPress={handleDrawSignature}
              variant="primary"
              style={styles.submitButton}
            >
              Save Signature
            </Button>
          </View>
        );

      case 'type':
        return (
          <View style={styles.typeContainer}>
            <TextInput
              style={styles.typeInput}
              value={typedSignature}
              onChangeText={setTypedSignature}
              placeholder="Type your full name"
              placeholderTextColor={AgreementTheme.colors.textSecondary}
            />
            <Text style={styles.timestamp}>
              {format(new Date(), "MMMM d, yyyy 'at' h:mm a")}
            </Text>
            <Button 
              onPress={handleTypeSignature}
              variant="primary"
              style={styles.submitButton}
            >
              Confirm Signature
            </Button>
          </View>
        );

      case 'upload':
        return (
          <View style={styles.uploadContainer}>
            <Button 
              onPress={handleUploadSignature}
              variant="secondary"
              style={styles.uploadButton}
            >
              Upload Signature Image
            </Button>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.typeSelector}>
        <Button
          variant={activeType === 'draw' ? 'primary' : 'secondary'}
          onPress={() => setActiveType('draw')}
          style={styles.typeSelectorButton}
        >
          Draw
        </Button>
        <Button
          variant={activeType === 'type' ? 'primary' : 'secondary'}
          onPress={() => setActiveType('type')}
          style={styles.typeSelectorButton}
        >
          Type
        </Button>
        <Button
          variant={activeType === 'upload' ? 'primary' : 'secondary'}
          onPress={() => setActiveType('upload')}
          style={styles.typeSelectorButton}
        >
          Upload
        </Button>
      </View>

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {renderSignatureInput()}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: AgreementTheme.spacing.md,
  },
  typeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: AgreementTheme.spacing.md,
  },
  typeSelectorButton: {
    flex: 1,
    marginHorizontal: AgreementTheme.spacing.xs,
  },
  signaturePadContainer: {
    height: 200,
    borderWidth: 1,
    borderColor: AgreementTheme.colors.border,
    borderRadius: AgreementTheme.borderRadius.md,
    overflow: 'hidden',
  },
  signaturePad: {
    flex: 1,
  },
  typeContainer: {
    marginTop: AgreementTheme.spacing.md,
  },
  typeInput: {
    borderWidth: 1,
    borderColor: AgreementTheme.colors.border,
    borderRadius: AgreementTheme.borderRadius.sm,
    padding: AgreementTheme.spacing.sm,
    fontSize: 18,
    color: AgreementTheme.colors.text,
  },
  timestamp: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
    marginTop: AgreementTheme.spacing.xs,
  },
  uploadContainer: {
    alignItems: 'center',
    marginTop: AgreementTheme.spacing.md,
  },
  uploadButton: {
    width: '100%',
  },
  submitButton: {
    marginTop: AgreementTheme.spacing.md,
  },
  errorText: {
    color: AgreementTheme.colors.error,
    marginBottom: AgreementTheme.spacing.sm,
  },
});