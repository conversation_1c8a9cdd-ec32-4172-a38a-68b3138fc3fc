import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text } from '@components/ui';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { format } from 'date-fns';
import type { SignatureData } from './SignatureCapture';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SignatureVerificationProps {
  signature: SignatureData;
  userName: string;
}

export default function SignatureVerification({
  signature,
  userName,
}: SignatureVerificationProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const renderSignature = () => {
    switch (signature.type) {
      case 'draw':
      case 'upload':
        return (
          <Image
            source={{ uri: signature.data }}
            style={styles.signatureImage}
            resizeMode="contain"
          />
        );
      case 'type':
        return (
          <Text style={styles.typedSignature}>
            {signature.data}
          </Text>
        );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Signed by</Text>
      <Text style={styles.name}>{userName}</Text>
      
      <View style={styles.signatureContainer}>
        {renderSignature()}
      </View>
      
      <Text style={styles.timestamp}>
        Signed on {format(new Date(signature.timestamp), "MMMM d, yyyy 'at' h:mm a")}
      </Text>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: AgreementTheme.spacing.md,
    borderWidth: 1,
    borderColor: AgreementTheme.colors.border,
    borderRadius: AgreementTheme.borderRadius.md,
    backgroundColor: AgreementTheme.colors.surface,
  },
  label: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
  },
  name: {
    ...AgreementTheme.typography.subtitle,
    color: AgreementTheme.colors.text,
    marginTop: AgreementTheme.spacing.xs,
  },
  signatureContainer: {
    marginVertical: AgreementTheme.spacing.md,
    minHeight: 60,
    justifyContent: 'center',
  },
  signatureImage: {
    width: '100%',
    height: 60,
  },
  typedSignature: {
    ...AgreementTheme.typography.signature,
    color: AgreementTheme.colors.text,
    textAlign: 'center',
  },
  timestamp: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
  },
});