import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
  Dimensions,
  LayoutChangeEvent,
} from 'react-native';
import { ChevronDown, ChevronUp, Edit2, Save, Trash2, Plus, Users, Clock } from 'lucide-react-native';
import CollaborationIndicator from '@components/agreement/CollaborationIndicator';
import LiveCursorIndicator from '@components/agreement/LiveCursorIndicator';
import { useAuth } from '@context/AuthContext';
import { Collaborator } from '@hooks/useAgreementCollaboration';
import { router } from 'expo-router';
import { useColorFix } from '@hooks/useColorFix';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface Field {
  name: string;
  type: string;
  required: boolean;
  options?: string[];
  value?: any;
}

interface Section {
  id?: string;
  key: string;
  title: string;
  order: number;
  is_required: boolean;
  content: {
    description: string;
    fields: Field[];
  };
  isExpanded?: boolean;
  isEditing?: boolean;
  order_index?: number;
  section_key?: string;
  section_title?: string;
}

interface AgreementSectionEditorProps {
  sections: Section[];
  onSectionsChange: (sections: Section[]) => void;
  collaborators?: Collaborator[];
  readOnly?: boolean;
  onStartEditing?: (sectionId: string) => void;
  onStopEditing?: () => void;
  canEditSection?: (sectionId: string) => boolean;
  getEditorForSection?: (sectionId: string) => Collaborator | undefined;
  notifyUpdate?: (sectionId: string, content: any) => void;
  agreementId?: string;
  cursors?: Array<{
    userId: string;
    displayName: string;
    position: { x: number; y: number };
    color: string;
    lastUpdated: number;
  }>;
  updateCursorPosition?: (x: number, y: number) => Promise<boolean>;
}

export default function AgreementSectionEditor({
  sections: initialSections,
  onSectionsChange,
  collaborators = [],
  readOnly = false,
  onStartEditing,
  onStopEditing,
  canEditSection,
  getEditorForSection,
  notifyUpdate,
  agreementId,
  cursors = [],
  updateCursorPosition
}: AgreementSectionEditorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const { authState } = useAuth();
  const user = authState?.user;
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(false);

  // Use a ref to track if sections have been initialized to prevent unnecessary re-renders
  const sectionsInitialized = useRef(false);
  const inProgress = useRef(false);

  // Add container measurements
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef<ScrollView>(null);
  
  // Add touch tracking for cursor position updates
  const handleTouchMove = (event: any) => {
    if (!updateCursorPosition) return;
    
    const { pageX, pageY } = event.nativeEvent;
    updateCursorPosition(pageX, pageY);
  };
  
  const handleLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  // Convert initialSections to a stable reference for dependency comparison
  const initialSectionsJSON = useMemo(() => JSON.stringify(
    initialSections.map(s => ({ 
      id: s.id,
      title: s.title || s.section_title,
      order: s.order || s.order_index,
      content: s.content
    }))
  ), [initialSections]);

  useEffect(() => {
    // Only initialize sections if they haven't been initialized or if initialSections has changed significantly
    if (!sectionsInitialized.current || !sections.length) {
      // Initialize sections with UI state properties
      setSections(
        initialSections.map((section) => ({
          ...section,
          isExpanded: false,
          isEditing: false,
        }))
      );
      sectionsInitialized.current = true;
    }
  }, [initialSectionsJSON, sections.length]);

  // Memoized derived data
  const memoizedSections = useMemo(() => sections, [sections]);

  // Memoized handler functions
  const handleSectionToggle = useCallback((index: number) => {
    if (inProgress.current) return;
    inProgress.current = true;
    
    setSections(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        isExpanded: !updated[index].isExpanded,
      };
      
      inProgress.current = false;
      return updated;
    });
  }, []);

  const handleEditToggle = useCallback((index: number) => {
    if (readOnly || inProgress.current) return;
    inProgress.current = true;
    
    const section = sections[index];
    const isCurrentlyEditing = section.isEditing;
    
    // If trying to start editing, check if the section is already being edited
    if (!isCurrentlyEditing && section.id) {
      if (canEditSection && !canEditSection(section.id)) {
        const editor = getEditorForSection && getEditorForSection(section.id);
        Alert.alert(
          "Section Being Edited",
          `This section is currently being edited by ${editor?.display_name || 'another user'}.`,
          [{ text: "OK" }]
        );
        inProgress.current = false;
        return;
      }
      
      if (onStartEditing) {
        onStartEditing(section.id);
      }
    }
    
    if (isCurrentlyEditing && onStopEditing) {
      onStopEditing();
    }
    
    setSections(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        isEditing: !updated[index].isEditing,
      };
      
      inProgress.current = false;
      return updated;
    });
  }, [readOnly, sections, canEditSection, getEditorForSection, onStartEditing, onStopEditing]);

  // Memoize all the other handler functions similarly
  const handleSectionTitleChange = useCallback((index: number, title: string) => {
    if (inProgress.current) return;
    inProgress.current = true;
    
    setSections(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        title,
        section_title: title,
      };
      
      inProgress.current = false;
      return updated;
    });
  }, []);

  const handleSectionDescriptionChange = (index: number, description: string) => {
    setSections((prev) => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        content: {
          ...updated[index].content,
          description,
        },
      };
      return updated;
    });
  };

  const handleFieldValueChange = (sectionIndex: number, fieldIndex: number, value: any) => {
    setSections((prev) => {
      const updated = [...prev];
      const fields = [...updated[sectionIndex].content.fields];
      fields[fieldIndex] = {
        ...fields[fieldIndex],
        value,
      };
      updated[sectionIndex] = {
        ...updated[sectionIndex],
        content: {
          ...updated[sectionIndex].content,
          fields,
        },
      };
      return updated;
    });
  };

  const handleFieldRequiredToggle = (sectionIndex: number, fieldIndex: number) => {
    if (readOnly) return;
    
    setSections((prev) => {
      const updated = [...prev];
      const fields = [...updated[sectionIndex].content.fields];
      fields[fieldIndex] = {
        ...fields[fieldIndex],
        required: !fields[fieldIndex].required,
      };
      updated[sectionIndex] = {
        ...updated[sectionIndex],
        content: {
          ...updated[sectionIndex].content,
          fields,
        },
      };
      return updated;
    });
  };

  const handleAddField = (sectionIndex: number) => {
    if (readOnly) return;
    
    setSections((prev) => {
      const updated = [...prev];
      const fields = [...updated[sectionIndex].content.fields];
      fields.push({
        name: `field_${fields.length + 1}`,
        type: 'text',
        required: false,
      });
      updated[sectionIndex] = {
        ...updated[sectionIndex],
        content: {
          ...updated[sectionIndex].content,
          fields,
        },
      };
      return updated;
    });
  };

  const handleRemoveField = (sectionIndex: number, fieldIndex: number) => {
    if (readOnly) return;
    
    setSections((prev) => {
      const updated = [...prev];
      const fields = [...updated[sectionIndex].content.fields];
      fields.splice(fieldIndex, 1);
      updated[sectionIndex] = {
        ...updated[sectionIndex],
        content: {
          ...updated[sectionIndex].content,
          fields,
        },
      };
      return updated;
    });
  };

  const handleSaveSection = useCallback((index: number) => {
    if (inProgress.current) return;
    inProgress.current = true;
    
    const section = sections[index];
    
    if (section.id && notifyUpdate) {
      // Use setTimeout to defer the update to the next tick
      setTimeout(() => {
        notifyUpdate(section.id!, section.content);
        inProgress.current = false;
      }, 0);
    }
    
    handleEditToggle(index);
  }, [sections, notifyUpdate, handleEditToggle]);
  
  // When sections change, we need to notify parent - but do it with debounce
  useEffect(() => {
    if (!sectionsInitialized.current || inProgress.current) return;
    
    const timer = setTimeout(() => {
      onSectionsChange(sections);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [sections, onSectionsChange]);

  const isSectionBeingEdited = (sectionId: string) => {
    if (getEditorForSection) {
      return !!getEditorForSection(sectionId);
    }
    
    return collaborators.some(
      collab => collab.is_editing && collab.section_id === sectionId && collab.user_id !== user?.id
    );
  };

  const getSectionCollaborators = (sectionId: string) => {
    return collaborators.filter(
      collab => collab.is_editing && collab.section_id === sectionId
    );
  };

  const renderFieldEditor = (field: Field, sectionIndex: number, fieldIndex: number) => {
    const isEditing = sections[sectionIndex].isEditing;

    switch (field.type) {
      case 'text':
      case 'textarea':
        return (
          <TextInput
            style={[
              styles.fieldInput,
              field.type === 'textarea' && styles.textareaInput,
              !isEditing && styles.readOnlyInput,
            ]}
            value={field.value || ''}
            onChangeText={(value) => handleFieldValueChange(sectionIndex, fieldIndex, value)}
            placeholder={`Enter ${field.name.replace(/_/g, ' ')}`}
            multiline={field.type === 'textarea'}
            editable={isEditing || !readOnly}
          />
        );
        
      case 'currency':
        return (
          <TextInput
            style={[styles.fieldInput, !isEditing && styles.readOnlyInput]}
            value={field.value || ''}
            onChangeText={(value) => handleFieldValueChange(sectionIndex, fieldIndex, value)}
            placeholder="$0.00"
            keyboardType="numeric"
            editable={isEditing || !readOnly}
          />
        );
        
      case 'select':
        if (!isEditing && field.value) {
          return (
            <Text style={styles.fieldValue}>{field.value}</Text>
          );
        }
        
        return (
          <View style={styles.selectContainer}>
            {field.options?.map((option, optionIndex) => (
              <TouchableOpacity
                key={optionIndex}
                style={[
                  styles.selectOption,
                  field.value === option && styles.selectedOption,
                  !isEditing && styles.readOnlyOption,
                ]}
                onPress={() => isEditing && handleFieldValueChange(sectionIndex, fieldIndex, option)}
                disabled={!isEditing || readOnly}
              >
                <Text
                  style={[
                    styles.selectOptionText,
                    field.value === option && styles.selectedOptionText,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );
        
      case 'multiselect':
        const selectedValues = Array.isArray(field.value) ? field.value : [];
        
        if (!isEditing && selectedValues.length > 0) {
          return (
            <Text style={styles.fieldValue}>{selectedValues.join(', ')}</Text>
          );
        }
        
        return (
          <View style={styles.selectContainer}>
            {field.options?.map((option, optionIndex) => (
              <TouchableOpacity
                key={optionIndex}
                style={[
                  styles.selectOption,
                  selectedValues.includes(option) && styles.selectedOption,
                  !isEditing && styles.readOnlyOption,
                ]}
                onPress={() => {
                  if (!isEditing || readOnly) return;
                  
                  const newValues = selectedValues.includes(option)
                    ? selectedValues.filter((v) => v !== option)
                    : [...selectedValues, option];
                    
                  handleFieldValueChange(sectionIndex, fieldIndex, newValues);
                }}
                disabled={!isEditing || readOnly}
              >
                <Text
                  style={[
                    styles.selectOptionText,
                    selectedValues.includes(option) && styles.selectedOptionText,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'period':
        if (!isEditing && field.value?.duration && field.value?.unit) {
          return (
            <Text style={styles.fieldValue}>{`${field.value.duration} ${field.value.unit}`}</Text>
          );
        }
        
        return (
          <View style={styles.periodContainer}>
            <TextInput
              style={[styles.fieldInput, styles.periodInput]}
              value={field.value?.duration || ''}
              onChangeText={duration => handleFieldValueChange(sectionIndex, fieldIndex, { 
                ...field.value, 
                duration: duration 
              })}
              placeholder="12"
              keyboardType="numeric"
              editable={isEditing && !readOnly}
            />
            <View style={styles.selectContainer}>
              {['months', 'weeks', 'days'].map((unit, unitIndex) => (
                <TouchableOpacity
                  key={unitIndex}
                  style={[
                    styles.selectOption,
                    field.value?.unit === unit && styles.selectedOption,
                    !isEditing && styles.readOnlyOption,
                  ]}
                  onPress={() => isEditing && !readOnly && handleFieldValueChange(sectionIndex, fieldIndex, { 
                    ...field.value, 
                    unit: unit 
                  })}
                  disabled={!isEditing || readOnly}
                >
                  <Text
                    style={[
                      styles.selectOptionText,
                      field.value?.unit === unit && styles.selectedOptionText,
                    ]}
                  >
                    {unit}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'day_of_month':
        if (!isEditing && field.value) {
          return (
            <Text style={styles.fieldValue}>{`Day ${field.value} of month`}</Text>
          );
        }
        
        return (
          <View style={styles.dayPickerContainer}>
            <View style={styles.dayPickerGrid}>
              {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                <TouchableOpacity
                  key={day}
                  style={[
                    styles.dayOption,
                    field.value === day && styles.selectedDayOption,
                    !isEditing && styles.readOnlyOption,
                  ]}
                  onPress={() => isEditing && !readOnly && handleFieldValueChange(sectionIndex, fieldIndex, day)}
                  disabled={!isEditing || readOnly}
                >
                  <Text
                    style={[
                      styles.dayOptionText,
                      field.value === day && styles.selectedDayOptionText,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'time_range':
        if (!isEditing && field.value?.start && field.value?.end) {
          return (
            <Text style={styles.fieldValue}>{`${field.value.start} - ${field.value.end}`}</Text>
          );
        }
        
        return (
          <View style={styles.timeRangeContainer}>
            <View style={styles.timeInputRow}>
              <Text style={styles.timeLabel}>From:</Text>
              <TextInput
                style={styles.timeInput}
                value={field.value?.start || ''}
                onChangeText={start => handleFieldValueChange(sectionIndex, fieldIndex, { 
                  ...field.value, 
                  start: start 
                })}
                placeholder="10:00 PM"
                editable={isEditing && !readOnly}
              />
            </View>
            <View style={styles.timeInputRow}>
              <Text style={styles.timeLabel}>To:</Text>
              <TextInput
                style={styles.timeInput}
                value={field.value?.end || ''}
                onChangeText={end => handleFieldValueChange(sectionIndex, fieldIndex, { 
                  ...field.value, 
                  end: end 
                })}
                placeholder="7:00 AM"
                editable={isEditing && !readOnly}
              />
            </View>
          </View>
        );
        
      default:
        return (
          <TextInput
            style={[styles.fieldInput, !isEditing && styles.readOnlyInput]}
            value={field.value || ''}
            onChangeText={(value) => handleFieldValueChange(sectionIndex, fieldIndex, value)}
            placeholder={`Enter ${field.name.replace(/_/g, ' ')}`}
            editable={isEditing || !readOnly}
          />
        );
    }
  };

  // Add handler to view edit history
  const handleViewHistory = useCallback((sectionId: string) => {
    if (!agreementId) return;
    
    router.push(`/agreement/edit-history?id=${agreementId}&sectionId=${sectionId}`);
  }, [agreementId]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading sections...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      ref={containerRef}
      style={styles.container}
      onLayout={handleLayout}
      onTouchMove={handleTouchMove}
    >
      {/* Add cursor indicators */}
      {cursors && cursors.length > 0 && user && (
        <LiveCursorIndicator
          cursors={cursors}
          containerWidth={containerDimensions.width}
          containerHeight={containerDimensions.height}
          currentUserId={user.id}
        />
      )}
      
      {sections.map((section, sectionIndex) => (
        <View 
          key={section.key || `section-${sectionIndex}`} 
          style={[
            styles.sectionContainer,
            section.isExpanded && styles.expandedSection,
            section.isEditing && styles.editingSection
          ]}
        >
          <TouchableOpacity
            style={styles.sectionHeader}
            onPress={() => handleSectionToggle(sectionIndex)}
          >
            <Text style={styles.sectionTitle}>
              {section.title || `Section ${sectionIndex + 1}`}
              {section.is_required && <Text style={styles.requiredBadge}> (Required)</Text>}
            </Text>
            
            <View style={styles.sectionHeaderActions}>
              {!readOnly && section.id && (
                <TouchableOpacity
                  style={[
                    styles.editButton,
                    (section.id && isSectionBeingEdited(section.id) && !section.isEditing) && styles.disabledEditButton
                  ]}
                  onPress={() => handleEditToggle(sectionIndex)}
                  disabled={section.id && isSectionBeingEdited(section.id) && !section.isEditing}
                >
                  {section.isEditing ? (
                    <Text style={styles.editButtonText}>Cancel</Text>
                  ) : (
                    <>
                      <Edit2 size={16} color={section.id && isSectionBeingEdited(section.id) && !section.isEditing ? "#94A3B8" : "#6366F1"} />
                      <Text style={[
                        styles.editButtonText,
                        (section.id && isSectionBeingEdited(section.id) && !section.isEditing) && styles.disabledEditButtonText
                      ]}>
                        Edit
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              )}
              
              {/* Add history button */}
              {!readOnly && agreementId && section.id && (
                <TouchableOpacity
                  style={styles.historyButton}
                  onPress={() => handleViewHistory(section.id!)}
                >
                  <Clock size={16} color={theme.colors.textSecondary} />
                  <Text style={styles.historyButtonText}>History</Text>
                </TouchableOpacity>
              )}
              
              {section.isExpanded ? (
                <ChevronUp size={20} color={theme.colors.textSecondary} />
              ) : (
                <ChevronDown size={20} color={theme.colors.textSecondary} />
              )}
            </View>
          </TouchableOpacity>
          
          {section.id && isSectionBeingEdited(section.id) && (
            <CollaborationIndicator 
              collaborators={getSectionCollaborators(section.id)}
              currentUserId={user?.id || ''}
              isCompact={true}
            />
          )}
          
          {section.isExpanded && (
            <View style={styles.sectionContent}>
              {section.isEditing ? (
                <View style={styles.editContainer}>
                  <Text style={styles.editLabel}>Section Title</Text>
                  <TextInput
                    style={styles.editTitleInput}
                    value={section.title || section.section_title}
                    onChangeText={(text) => handleSectionTitleChange(sectionIndex, text)}
                    placeholder="Enter section title"
                  />
                  
                  <Text style={styles.editLabel}>Description</Text>
                  <TextInput
                    style={styles.editDescriptionInput}
                    value={section.content.description}
                    onChangeText={(text) => handleSectionDescriptionChange(sectionIndex, text)}
                    placeholder="Enter section description"
                    multiline
                  />
                </View>
              ) : (
                <Text style={styles.sectionDescription}>{section.content.description}</Text>
              )}
              
              <View style={styles.fieldsContainer}>
                {section.content.fields.map((field, fieldIndex) => (
                  <View key={field.name} style={styles.fieldContainer}>
                    <View style={styles.fieldHeader}>
                      <View>
                        <Text style={styles.fieldLabel}>
                          {field.name.replace(/_/g, ' ')}
                          {field.required && <Text style={styles.requiredIndicator}> *</Text>}
                        </Text>
                      </View>
                      
                      {section.isEditing && (
                        <View style={styles.fieldActions}>
                          <View style={styles.requiredToggleContainer}>
                            <Text style={styles.requiredToggleLabel}>Required</Text>
                            <Switch
                              value={field.required}
                              onValueChange={() => handleFieldRequiredToggle(sectionIndex, fieldIndex)}
                              trackColor={{ false: theme.colors.border, true: '#A5B4FC' }}
                              thumbColor={field.required ? '#6366F1' : '#F3F4F6'}
                            />
                          </View>
                          
                          <TouchableOpacity
                            style={styles.removeFieldButton}
                            onPress={() => handleRemoveField(sectionIndex, fieldIndex)}
                          >
                            <Trash2 size={16} color={theme.colors.error} />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                    
                    {renderFieldEditor(field, sectionIndex, fieldIndex)}
                  </View>
                ))}
                
                {section.isEditing && !readOnly && (
                  <TouchableOpacity
                    style={styles.addFieldButton}
                    onPress={() => handleAddField(sectionIndex)}
                  >
                    <Plus size={16} color="#6366F1" />
                    <Text style={styles.addFieldButtonText}>Add Field</Text>
                  </TouchableOpacity>
                )}
              </View>
              
              {section.isEditing && !readOnly && (
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={() => handleSaveSection(sectionIndex)}
                >
                  <Save size={16} color={theme.colors.background} />
                  <Text style={styles.saveButtonText}>Save Changes</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      ))}
    </ScrollView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  sectionContainer: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  expandedSection: {
    borderColor: '#CBD5E1',
  },
  editingSection: {
    borderColor: '#A5B4FC',
    borderWidth: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButton: {
    marginRight: 12,
    padding: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 8,
  },
  requiredBadge: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  requiredBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6366F1',
  },
  sectionContent: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
  },
  fieldsContainer: {
    gap: 20,
  },
  fieldContainer: {
    marginBottom: 4,
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fieldLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#334155',
  },
  requiredIndicator: {
    color: theme.colors.error,
    marginLeft: 4,
    fontWeight: '700',
  },
  fieldActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requiredToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  requiredToggleLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginRight: 4,
  },
  removeFieldButton: {
    padding: 4,
  },
  fieldInput: {
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    color: '#334155',
  },
  readOnlyInput: {
    backgroundColor: '#F1F5F9',
    borderColor: theme.colors.border,
    color: theme.colors.textSecondary,
  },
  textareaInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  fieldValue: {
    fontSize: 14,
    color: '#334155',
    padding: 12,
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
  },
  selectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectOption: {
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: theme.colors.background,
  },
  selectedOption: {
    borderColor: '#6366F1',
    backgroundColor: '#EEF2FF',
  },
  readOnlyOption: {
    opacity: 0.7,
  },
  selectOptionText: {
    fontSize: 14,
    color: '#334155',
  },
  selectedOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  editContainer: {
    marginBottom: 16,
  },
  editLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#334155',
    marginBottom: 4,
  },
  editTitleInput: {
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    marginBottom: 12,
  },
  editDescriptionInput: {
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 16,
  },
  addFieldButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 6,
    borderStyle: 'dashed',
    paddingVertical: 12,
    marginTop: 8,
  },
  addFieldButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6366F1',
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#6366F1',
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 16,
  },
  saveButtonText: {
    color: theme.colors.background,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6366F1',
    fontWeight: '500',
  },
  requiredToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  disabledEditButton: {
    opacity: 0.5,
  },
  disabledEditButtonText: {
    color: '#94A3B8',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    padding: 4,
  },
  historyButtonText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  // Period field styles
  periodContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 6,
    padding: 12,
  },
  periodInput: {
    borderWidth: 0,
    padding: 0,
    marginBottom: 12,
  },
  // Day picker styles
  dayPickerContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 6,
    padding: 12,
  },
  dayPickerGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dayOption: {
    width: 35,
    height: 35,
    backgroundColor: '#F1F5F9',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedDayOption: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  dayOptionText: {
    fontSize: 12,
    color: '#475569',
  },
  selectedDayOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  // Time range styles
  timeRangeContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 6,
    padding: 12,
  },
  timeInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeLabel: {
    fontSize: 14,
    color: theme.colors.text,
    width: 50,
    marginRight: 12,
  },
  timeInput: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
    color: theme.colors.text,
  },
});