import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { router } from 'expo-router';
import { unifiedChatService } from '@services/unified/UnifiedChatService';
import { useAuth } from '@hooks/useAuth';
import { logger } from '@utils/logger';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface AgreementChatButtonProps {
  agreementId: string;
  participantIds: string[];
  title?: string;
  style?: object;
}

/**
 * A button component that creates a chat room between agreement participants
 * and navigates to the chat screen.
 */
export default function AgreementChatButton({
  agreementId,
  participantIds,
  title = 'Discuss Agreement',
  style = {},
}: AgreementChatButtonProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [isLoading, setIsLoading] = useState(false);
  const { state, actions } = useAuth();
  const user = state?.user;

  // Create a unique log context for tracking
  const logContext = {
    component: 'AgreementChatButton',
    agreementId,
    participantCount: participantIds?.length,
  };

  const handlePress = async () => {
    if (!user?.id || !participantIds || participantIds.length === 0) {
      Alert.alert('Error', 'Missing user or participant information');
      return;
    }

    setIsLoading(true);

    try {
      logger.info('Creating chat room for agreement participants', logContext);

      // Filter out current user from participants (they'll be added as the creator)
      const otherParticipants = participantIds.filter(id => id !== user.id);

      if (otherParticipants.length === 0) {
        logger.warn('No other participants besides current user', logContext);
        Alert.alert('Cannot Create Chat', 'There are no other participants to chat with.');
        setIsLoading(false);
        return;
      }

      // For now, we'll just support 1:1 chats for agreements
      // For multi-participant chats, we would need to modify this logic
      if (otherParticipants.length > 1) {
        logger.info('Multiple participants found, creating group chat', logContext);

        // Here we would create a group chat, but for now we'll just use the first participant
        // In a future implementation, you could add support for group chats
      }

      // Get the first participant to create a 1:1 chat
      const otherUserId = otherParticipants[0];

      // Create chat room using the MessagingService
      const roomId = await unifiedChatService.createChatRoom(user.id, otherUserId);

      if (!roomId) {
        throw new Error('Failed to create chat room');
      }

      logger.info('Successfully created chat room', { ...logContext, roomId });

      // Check if this is a mock room
      const isMockRoom = roomId.startsWith('mock_');
      if (isMockRoom) {
        logger.warn('Created mock room as fallback', { ...logContext, roomId });

        // Show a warning to the user about temporary chat
        Alert.alert(
          'Temporary Chat Created',
          'The chat room was created in temporary mode. Some features may be limited.',
          [{ text: 'Continue', style: 'default' }]
        );
      }

      // Navigate to the chat screen with query parameters instead of params object
      const queryParams = new URLSearchParams({
        roomId: String(roomId),
        context: 'agreement',
        agreementId: String(agreementId),
        title: 'Agreement Discussion',
      });
      
      router.push(`/chat?${queryParams.toString()}`);
    } catch (error) {
      logger.error('Error creating chat room', { ...logContext, error });

      // Try to create mock room as a last resort
      try {
        // Dynamically import ChatService to avoid circular dependency
        const { ChatService } = await import('../../services/standardized/ChatService');

        if (ChatService && typeof ChatService.createMockRoom === 'function') {
          const otherUserId = participantIds.filter(id => id !== user.id)[0];
          if (otherUserId) {
            const mockRoomId = ChatService.createMockRoom(user.id, otherUserId);

            if (mockRoomId) {
              logger.info('Created emergency mock room', { ...logContext, mockRoomId });

              // Show a warning to the user
              Alert.alert(
                'Limited Chat Mode',
                'We were unable to create a full chat room. A temporary chat has been created instead.',
                [
                  {
                    text: 'Continue',
                    onPress: () => {
                      // Use query parameters instead of params object
                      const queryParams = new URLSearchParams({
                        roomId: String(mockRoomId),
                        context: 'agreement',
                        agreementId: String(agreementId),
                        title: 'Agreement Discussion (Limited)',
                      });
                      
                      router.push(`/chat?${queryParams.toString()}`);
                    },
                  },
                  {
                    text: 'Cancel',
                    style: 'cancel',
                  },
                ]
              );
              setIsLoading(false);
              return;
            }
          }
        }
      } catch (fallbackError) {
        logger.error('Fallback also failed', { ...logContext, fallbackError });
      }

      // Show error message if all attempts fail
      Alert.alert(
        'Chat Error',
        'Unable to create chat room. Please try again later or contact support if this issue persists.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TouchableOpacity style={[styles.button, style]} onPress={handlePress} disabled={isLoading}>
      {isLoading ? (
        <ActivityIndicator color={theme.colors.background} size="small" />
      ) : (
        <Text style={styles.buttonText}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  button: {
    backgroundColor: '#4a90e2',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: theme.colors.background,
    fontWeight: '600',
    fontSize: 16,
  },
});
