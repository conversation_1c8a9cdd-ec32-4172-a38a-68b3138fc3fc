import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { Clock, ChevronDown, ChevronUp, User, Flag } from 'lucide-react-native';
import { supabase } from "@utils/supabaseUtils";
import { formatDistanceToNow } from 'date-fns';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface EditHistoryEntry {
  id: string;
  agreement_id: string;
  section_id: string;
  user_id: string;
  version_number: number;
  changes: {
    previous: any;
    updated: any;
  };
  created_at: string;
  user?: {
    email: string;
    user_metadata?: {
      name?: string;
    };
  };
}

interface EditHistoryViewerProps {
  agreementId: string;
  sectionId?: string;
}

export default function EditHistoryViewer({
  agreementId, sectionId }: EditHistoryViewerProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [editHistory, setEditHistory] = useState<EditHistoryEntry[]>([]);
  const [expandedEntries, setExpandedEntries] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchEditHistory();
  }, [agreementId, sectionId]);

  const fetchEditHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('agreement_edit_history')
        .select(`
          *,
          user:user_id (
            email,
            user_metadata
          )
        `)
        .eq('agreement_id', agreementId)
        .order('created_at', { ascending: false });

      if (sectionId) {
        query = query.eq('section_id', sectionId);
      }

      const { data, error } = await query;

      if (error) throw error;

      setEditHistory(data || []);
    } catch (err) {
      console.error('Error fetching edit history:', err);
      setError('Failed to load edit history');
    } finally {
      setLoading(false);
    }
  };

  const toggleEntry = (id: string) => {
    setExpandedEntries(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const getUserDisplayName = (user?: any) => {
    if (!user) return 'Unknown User';
    return user.user_metadata?.name || user.email || 'Unknown User';
  };

  const getTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (err) {
      return 'Invalid date';
    }
  };

  const renderChangeDiff = (entry: EditHistoryEntry) => {
    if (!entry.changes) return null;

    const { previous, updated } = entry.changes;
    if (!previous || !updated) return <Text style={styles.noChangesText}>No change details available</Text>;

    // First handle content field which is most common for sections
    if (updated.content !== undefined) {
      // Extract the keys that have changed in the content
      const contentChanges = [];
      const previousContent = previous.content || {};
      const updatedContent = updated.content || {};

      // Add fields that have been changed or added
      const allKeys = new Set([
        ...Object.keys(previousContent),
        ...Object.keys(updatedContent)
      ]);

      // Handle fields array specially if it exists
      if (Array.isArray(previousContent.fields) || Array.isArray(updatedContent.fields)) {
        const prevFields = Array.isArray(previousContent.fields) ? previousContent.fields : [];
        const updFields = Array.isArray(updatedContent.fields) ? updatedContent.fields : [];
        
        // Compare field arrays by checking additions or removals
        const prevFieldCount = prevFields.length;
        const updFieldCount = updFields.length;
        
        if (prevFieldCount !== updFieldCount) {
          contentChanges.push(
            <View key="fields-count" style={styles.changeItem}>
              <Text style={styles.changeLabel}>Fields Count:</Text>
              <Text style={styles.oldValue}>{prevFieldCount} fields</Text>
              <Text style={styles.newValue}>{updFieldCount} fields</Text>
            </View>
          );
        }
        
        // Check for modified fields
        const prevFieldsMap = new Map(
          prevFields.map((f: any) => [f.name || f.id, f])
        );
        
        updFields.forEach((field: any, index: number) => {
          const fieldName = field.name || field.id || `Field ${index+1}`;
          const prevField = prevFieldsMap.get(fieldName);
          
          if (!prevField) {
            contentChanges.push(
              <View key={`field-${fieldName}-added`} style={styles.changeItem}>
                <Text style={styles.changeLabel}>Added Field:</Text>
                <Text style={styles.newValue}>{fieldName}</Text>
              </View>
            );
          } else if (JSON.stringify(prevField) !== JSON.stringify(field)) {
            contentChanges.push(
              <View key={`field-${fieldName}-modified`} style={styles.changeItem}>
                <Text style={styles.changeLabel}>Modified Field:</Text>
                <Text style={styles.fieldName}>{fieldName}</Text>
              </View>
            );
          }
        });
      }
      
      // Handle other content properties
      Array.from(allKeys).forEach(key => {
        if (key === 'fields') return; // Already handled above
        
        const prevValue = previousContent[key];
        const updValue = updatedContent[key];
        
        if (JSON.stringify(prevValue) !== JSON.stringify(updValue)) {
          contentChanges.push(
            <View key={`content-${key}`} style={styles.changeItem}>
              <Text style={styles.changeLabel}>{key}:</Text>
              <Text style={styles.oldValue}>{JSON.stringify(prevValue)}</Text>
              <Text style={styles.newValue}>{JSON.stringify(updValue)}</Text>
            </View>
          );
        }
      });
      
      if (contentChanges.length === 0) {
        return <Text style={styles.noChangesText}>No visible content changes found</Text>;
      }
      
      return (
        <View style={styles.changesContainer}>
          <Text style={styles.changesTitle}>Content Changes:</Text>
          {contentChanges}
        </View>
      );
    }
    
    // For other types of changes
    const changesArray = [];
    const allKeys = new Set([
      ...Object.keys(previous),
      ...Object.keys(updated)
    ]);
    
    // Skip metadata and system fields
    const skipFields = ['id', 'created_at', 'updated_at', 'agreement_id', 'section_id', 'version_number', 'last_updated_by'];
    
    Array.from(allKeys).forEach(key => {
      if (skipFields.includes(key)) return;
      
      const prevValue = previous[key];
      const updValue = updated[key];
      
      if (JSON.stringify(prevValue) !== JSON.stringify(updValue)) {
        changesArray.push(
          <View key={key} style={styles.changeItem}>
            <Text style={styles.changeLabel}>{key}:</Text>
            <Text style={styles.oldValue}>{JSON.stringify(prevValue)}</Text>
            <Text style={styles.newValue}>{JSON.stringify(updValue)}</Text>
          </View>
        );
      }
    });
    
    if (changesArray.length === 0) {
      return <Text style={styles.noChangesText}>No visible changes found</Text>;
    }
    
    return (
      <View style={styles.changesContainer}>
        <Text style={styles.changesTitle}>Changes:</Text>
        {changesArray}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading edit history...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Button title="Retry" onPress={fetchEditHistory} />
      </View>
    );
  }

  if (editHistory.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No edit history found for this {sectionId ? 'section' : 'agreement'}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Edit History</Text>
      
      {editHistory.map(entry => (
        <View key={entry.id} style={styles.entryContainer}>
          <TouchableOpacity 
            style={styles.entryHeader} 
            onPress={() => toggleEntry(entry.id)}
          >
            <View style={styles.entryHeaderLeft}>
              <Clock size={16} color="#6366F1" />
              <Text style={styles.timestamp}>{getTimestamp(entry.created_at)}</Text>
            </View>
            
            <View style={styles.entryHeaderRight}>
              <User size={16} color={theme.colors.textSecondary} />
              <Text style={styles.userName}>{getUserDisplayName(entry.user)}</Text>
              {expandedEntries[entry.id] ? <ChevronUp size={16} color={theme.colors.textSecondary} /> : <ChevronDown size={16} color={theme.colors.textSecondary} />}
            </View>
          </TouchableOpacity>
          
          {expandedEntries[entry.id] && (
            <View style={styles.entryDetails}>
              <View style={styles.metadataRow}>
                <View style={styles.metadataItem}>
                  <Text style={styles.metadataLabel}>Version</Text>
                  <Text style={styles.metadataValue}>{entry.version_number}</Text>
                </View>
                <View style={styles.metadataItem}>
                  <Text style={styles.metadataLabel}>Date</Text>
                  <Text style={styles.metadataValue}>
                    {new Date(entry.created_at).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.metadataItem}>
                  <Text style={styles.metadataLabel}>Time</Text>
                  <Text style={styles.metadataValue}>
                    {new Date(entry.created_at).toLocaleTimeString()}
                  </Text>
                </View>
              </View>
              
              {renderChangeDiff(entry)}
            </View>
          )}
        </View>
      ))}
    </ScrollView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 16,
    fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  entryContainer: {
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F8FAFC',
  },
  entryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.text,
  },
  userName: {
    marginLeft: 4,
    marginRight: 8,
    fontSize: 14,
    color: theme.colors.text,
  },
  entryDetails: {
    padding: 12,
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  metadataRow: {
    flexDirection: 'row',
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  metadataItem: {
    flex: 1,
  },
  metadataLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  metadataValue: {
    fontSize: 14,
    color: theme.colors.text,
  },
  changesContainer: {
    marginTop: 8,
  },
  changesTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  changeItem: {
    marginBottom: 12,
    paddingLeft: 8,
    borderLeftWidth: 2,
    borderLeftColor: '#6366F1',
  },
  changeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  fieldName: {
    fontSize: 14,
    color: '#6366F1',
    fontWeight: '500',
  },
  oldValue: {
    fontSize: 14,
    color: theme.colors.error,
    textDecorationLine: 'line-through',
    marginBottom: 4,
  },
  newValue: {
    fontSize: 14,
    color: theme.colors.success,
  },
  noChangesText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
});