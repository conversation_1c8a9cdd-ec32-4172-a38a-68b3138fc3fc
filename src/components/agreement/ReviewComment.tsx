import React from 'react';
import { View, StyleSheet } from 'react-native';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { format } from 'date-fns';
import { Avatar, Text } from '@components/ui';
import { Button } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
interface ReviewCommentProps {
  comment: {
    id: string;
    reviewerId: string;
    reviewerName: string;
    reviewerAvatar?: string;
    comment: string;
    status: 'pending' | 'approved' | 'rejected' | 'resolved';
    createdAt: string;
    resolvedAt?: string;
  };
  onResolve?: (commentId: string) => void;
  onApprove?: (commentId: string) => void;
  onReject?: (commentId: string) => void;
}

export default function ReviewComment({
  comment,
  onResolve,
  onApprove,
  onReject,
}: ReviewCommentProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return AgreementTheme.colors.success;
      case 'rejected':
        return AgreementTheme.colors.error;
      case 'resolved':
        return AgreementTheme.colors.textSecondary;
      default:
        return AgreementTheme.colors.warning;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Avatar
            size="sm"
            source={comment.reviewerAvatar ? { uri: comment.reviewerAvatar } : undefined}
            fallback={comment.reviewerName[0]?.toUpperCase() || '?'}
          />
          <Text style={styles.userName}>{comment.reviewerName}</Text>
        </View>
        <Text style={[styles.status, { color: getStatusColor(comment.status) }]}>
          {comment.status.charAt(0).toUpperCase() + comment.status.slice(1)}
        </Text>
      </View>

      <Text style={styles.comment}>{comment.comment}</Text>

      <View style={styles.footer}>
        <Text style={styles.timestamp}>
          {format(new Date(comment.createdAt), "MMM d, yyyy 'at' h:mm a")}
        </Text>

        {comment.status === 'pending' && (
          <View style={styles.actions}>
            {onApprove && (
              <Button
                onPress={() => onApprove(comment.id)}
                variant="success"
                size="sm"
                style={styles.actionButton}
              >
                Approve
              </Button>
            )}
            {onReject && (
              <Button
                onPress={() => onReject(comment.id)}
                variant="error"
                size="sm"
                style={styles.actionButton}
              >
                Reject
              </Button>
            )}
            {onResolve && (
              <Button
                onPress={() => onResolve(comment.id)}
                variant="secondary"
                size="sm"
                style={styles.actionButton}
              >
                Resolve
              </Button>
            )}
          </View>
        )}

        {comment.resolvedAt && (
          <Text style={styles.resolvedText}>
            Resolved on {format(new Date(comment.resolvedAt), "MMM d, yyyy 'at' h:mm a")}
          </Text>
        )}
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: AgreementTheme.colors.surface,
    borderRadius: AgreementTheme.borderRadius.md,
    padding: AgreementTheme.spacing.md,
    marginBottom: AgreementTheme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: AgreementTheme.spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: AgreementTheme.spacing.sm,
  },
  userName: {
    ...AgreementTheme.typography.subtitle,
    color: AgreementTheme.colors.text,
  },
  status: {
    ...AgreementTheme.typography.caption,
    fontWeight: '600',
  },
  comment: {
    ...AgreementTheme.typography.body,
    color: AgreementTheme.colors.text,
    marginBottom: AgreementTheme.spacing.md,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: AgreementTheme.colors.border,
    paddingTop: AgreementTheme.spacing.sm,
  },
  timestamp: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: AgreementTheme.spacing.sm,
    marginTop: AgreementTheme.spacing.sm,
  },
  actionButton: {
    minWidth: 80,
  },
  resolvedText: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
    fontStyle: 'italic',
    marginTop: AgreementTheme.spacing.sm,
  },
});