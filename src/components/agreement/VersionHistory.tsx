import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
;
;
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { format } from 'date-fns';
import VersionDiff from '@components/agreement/VersionDiff';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Text } from '@components/ui';
import { Button } from '@design-system';
interface Version {
  id: string;
  versionNumber: number;
  createdAt: string;
  createdBy: {
    id: string;
    fullName: string;
  };
  changesSummary: string;
  content: any;
}

interface VersionHistoryProps {
  versions: Version[];
  onRestoreVersion?: (versionId: string) => void;
  currentVersionNumber: number;
}

export default function VersionHistory({
  versions,
  onRestoreVersion,
  currentVersionNumber,
}: VersionHistoryProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [selectedVersions, setSelectedVersions] = useState<[number, number] | null>(null);

  const sortedVersions = [...versions].sort((a, b) => b.versionNumber - a.versionNumber);

  const compareVersions = (version1: Version, version2: Version) => {
    const changes: any[] = [];
    
    // Compare content objects recursively
    const compareObjects = (obj1: any, obj2: any, path: string = '') => {
      if (typeof obj1 !== typeof obj2) {
        changes.push({
          type: 'modified',
          field: path,
          oldValue: obj1,
          newValue: obj2,
        });
        return;
      }

      if (typeof obj1 !== 'object') {
        if (obj1 !== obj2) {
          changes.push({
            type: 'modified',
            field: path,
            oldValue: obj1,
            newValue: obj2,
          });
        }
        return;
      }

      const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);

      for (const key of allKeys) {
        const currentPath = path ? `${path}.${key}` : key;

        if (!(key in obj1)) {
          changes.push({
            type: 'added',
            field: currentPath,
            newValue: obj2[key],
          });
        } else if (!(key in obj2)) {
          changes.push({
            type: 'removed',
            field: currentPath,
            oldValue: obj1[key],
          });
        } else {
          compareObjects(obj1[key], obj2[key], currentPath);
        }
      }
    };

    compareObjects(version1.content, version2.content);
    return changes;
  };

  const handleVersionClick = (versionNumber: number) => {
    if (!selectedVersions) {
      setSelectedVersions([versionNumber, versionNumber]);
    } else {
      const [v1] = selectedVersions;
      if (v1 === versionNumber) {
        setSelectedVersions(null);
      } else {
        setSelectedVersions([Math.max(v1, versionNumber), Math.min(v1, versionNumber)]);
      }
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Version History</Text>
      
      <ScrollView style={styles.versionsContainer}>
        {sortedVersions.map((version, index) => {
          const isSelected = selectedVersions?.includes(version.versionNumber);
          const isLatest = index === 0;
          const isCurrent = version.versionNumber === currentVersionNumber;

          return (
            <TouchableOpacity
              key={version.id}
              style={[
                styles.versionItem,
                isSelected && styles.selectedVersion,
                isCurrent && styles.currentVersion,
              ]}
              onPress={() => handleVersionClick(version.versionNumber)}
            >
              <View style={styles.versionHeader}>
                <Text style={styles.versionNumber}>
                  Version {version.versionNumber}
                  {isLatest && ' (Latest)'}
                  {isCurrent && ' (Current)'}
                </Text>
                <Text style={styles.versionDate}>
                  {format(new Date(version.createdAt), "MMM d, yyyy 'at' h:mm a")}
                </Text>
              </View>
              
              <Text style={styles.versionAuthor}>
                by {version.createdBy.fullName}
              </Text>
              
              {version.changesSummary && (
                <Text style={styles.changesSummary}>
                  {version.changesSummary}
                </Text>
              )}

              {onRestoreVersion && !isCurrent && (
                <Button
                  onPress={() => onRestoreVersion(version.id)}
                  variant="secondary"
                  size="sm"
                  style={styles.restoreButton}
                >
                  Restore this version
                </Button>
              )}
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {selectedVersions && selectedVersions[0] !== selectedVersions[1] && (
        <View style={styles.comparisonSection}>
          <Text style={styles.comparisonTitle}>Version Comparison</Text>
          <VersionDiff
            changes={compareVersions(
              versions.find(v => v.versionNumber === selectedVersions[0])!,
              versions.find(v => v.versionNumber === selectedVersions[1])!
            )}
            oldVersion={selectedVersions[1]}
            newVersion={selectedVersions[0]}
            createdAt={versions.find(v => v.versionNumber === selectedVersions[0])!.createdAt}
            createdByName={versions.find(v => v.versionNumber === selectedVersions[0])!.createdBy.fullName}
          />
        </View>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    ...AgreementTheme.typography.h2,
    color: AgreementTheme.colors.text,
    marginBottom: AgreementTheme.spacing.md,
  },
  versionsContainer: {
    maxHeight: 400,
  },
  versionItem: {
    backgroundColor: AgreementTheme.colors.surface,
    borderRadius: AgreementTheme.borderRadius.md,
    padding: AgreementTheme.spacing.md,
    marginBottom: AgreementTheme.spacing.md,
  },
  selectedVersion: {
    borderColor: AgreementTheme.colors.primary.main,
    borderWidth: 2,
  },
  currentVersion: {
    backgroundColor: AgreementTheme.colors.primary.main + '10',
  },
  versionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: AgreementTheme.spacing.xs,
  },
  versionNumber: {
    ...AgreementTheme.typography.subtitle,
    color: AgreementTheme.colors.text,
    fontWeight: '600',
  },
  versionDate: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
  },
  versionAuthor: {
    ...AgreementTheme.typography.caption,
    color: AgreementTheme.colors.textSecondary,
    marginBottom: AgreementTheme.spacing.xs,
  },
  changesSummary: {
    ...AgreementTheme.typography.body,
    color: AgreementTheme.colors.text,
    marginTop: AgreementTheme.spacing.xs,
  },
  restoreButton: {
    marginTop: AgreementTheme.spacing.sm,
  },
  comparisonSection: {
    marginTop: AgreementTheme.spacing.lg,
  },
  comparisonTitle: {
    ...AgreementTheme.typography.subtitle,
    color: AgreementTheme.colors.text,
    marginBottom: AgreementTheme.spacing.md,
  },
});