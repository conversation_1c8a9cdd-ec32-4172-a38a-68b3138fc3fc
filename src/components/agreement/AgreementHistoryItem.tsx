import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from '@components/ui';
import { Calendar, User, FileText, ChevronRight } from 'lucide-react-native';
import { Agreement } from '@utils/agreement';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface AgreementHistoryItemProps {
  agreement: Agreement;
  isCreator: boolean;
  participantCount: number;
  signatureCount: number;
  onPress: () => void;
}

export default function AgreementHistoryItem({
  agreement,
  isCreator,
  participantCount,
  signatureCount,
  onPress,
}: AgreementHistoryItemProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft': return 'Draft';
      case 'pending_review': return 'Pending Review';
      case 'review': return 'Ready for Signature';
      case 'active': return 'Active';
      case 'archived': return 'Archived';
      default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return '#94A3B8';
      case 'pending_review': return theme.colors.warning;
      case 'review': return theme.colors.primary;
      case 'active': return theme.colors.success;
      case 'archived': return theme.colors.textSecondary;
      default: return '#94A3B8';
    }
  };

  const statusColor = getStatusColor(agreement.status);

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
          <Text style={[styles.statusText, { color: statusColor }]}>
            {getStatusLabel(agreement.status)}
          </Text>
        </View>
        
        {isCreator && (
          <View style={styles.creatorBadge}>
            <Text style={styles.creatorText}>Creator</Text>
          </View>
        )}
      </View>
      
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <FileText size={24} color="#6366F1" />
        </View>
        
        <View style={styles.detailsContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {agreement.title || 'Untitled Agreement'}
          </Text>
          
          <View style={styles.metadataRow}>
            <View style={styles.metadataItem}>
              <Calendar size={14} color={theme.colors.textSecondary} />
              <Text style={styles.metadataText}>
                {formatDate(agreement.created_at)}
              </Text>
            </View>
            
            <View style={styles.metadataItem}>
              <User size={14} color={theme.colors.textSecondary} />
              <Text style={styles.metadataText}>
                {signatureCount}/{participantCount} signed
              </Text>
            </View>
          </View>
        </View>
        
        <ChevronRight size={20} color="#94A3B8" />
      </View>
    </TouchableOpacity>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    padding: 12,
    paddingBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  creatorBadge: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4F46E5',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingTop: 0,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailsContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metadataText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
});