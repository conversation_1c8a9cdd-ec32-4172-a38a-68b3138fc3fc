import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowRight, CheckCircle, FileText, Info } from 'lucide-react-native';

import { supabase } from '@utils/supabaseUtils';
import { useToast } from '@core/errors';
import { trackAgreementOperation } from '@utils/performance/agreementFlowMonitor';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface AgreementTemplate {
  id: string;
  name: string;
  description: string;
  sections: any;
}

// Template cache to prevent multiple API calls
let templateCache: { data: AgreementTemplate[]; timestamp: number } | null = null;
const TEMPLATE_CACHE_TTL = 300000; // 5 minutes

interface AgreementTemplateSelectorProps {
  onSelectTemplate: (template: AgreementTemplate) => void;
  suggestedTemplateId?: string | null;
}

export default function AgreementTemplateSelector({
  onSelectTemplate,
  suggestedTemplateId = null,
}: AgreementTemplateSelectorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [templates, setTemplates] = useState<AgreementTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [showingSuggestion, setShowingSuggestion] = useState<boolean>(!!suggestedTemplateId);
  const router = useRouter();
  const { showToast } = useToast();

  // Memoize the loadTemplates function to prevent infinite loops
  const loadTemplates = useCallback(async () => {
    try {
      // Start performance tracking
      trackAgreementOperation.templateLoading();

      // Check cache first
      if (templateCache && Date.now() - templateCache.timestamp < TEMPLATE_CACHE_TTL) {
        console.log('✅ Using cached templates');
        setTemplates(templateCache.data);
        setLoading(false);
        trackAgreementOperation.templateLoadingComplete(templateCache.data.length);
        return;
      }

      console.log('🔄 Loading templates from database...');
      const { data, error } = await supabase
        .from('agreement_templates')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      const templatesData = data || [];

      // Update cache
      templateCache = {
        data: templatesData,
        timestamp: Date.now(),
      };

      console.log(`✅ Loaded ${templatesData.length} templates`);
      setTemplates(templatesData);

      // End performance tracking
      trackAgreementOperation.templateLoadingComplete(templatesData.length);
    } catch (error) {
      console.error('Error loading agreement templates:', error);
      showToast('Failed to load agreement templates', 'error');

      // End performance tracking with error
      trackAgreementOperation.templateLoadingComplete(0);
    } finally {
      setLoading(false);
    }
  }, []); // Remove showToast dependency to prevent infinite loop

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]); // Use the memoized function

  // Auto-select the suggested template if provided
  useEffect(() => {
    if (!loading && suggestedTemplateId && templates.length > 0) {
      const suggestedTemplate = templates.find(t => t.id === suggestedTemplateId);
      if (suggestedTemplate) {
        setSelectedTemplate(suggestedTemplateId);

        // Show a toast to explain the suggestion
        showToast('Template suggested based on your conversation', 'info');
      }
    }
  }, [loading, suggestedTemplateId, templates]); // Remove showToast dependency

  const handleSelectTemplate = (template: AgreementTemplate) => {
    setSelectedTemplate(template.id);
    onSelectTemplate(template);
  };

  const handleContinue = () => {
    if (!selectedTemplate) {
      showToast('Please select a template to continue', 'info');
      return;
    }

    // This would normally navigate to the next step in the agreement creation flow
    // Since we're just implementing components at this stage, we'll just show a success toast
    showToast('Template selected successfully', 'success');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading templates...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Select Agreement Template</Text>
      <Text style={styles.subtitle}>
        {suggestedTemplateId
          ? "We've suggested a template based on your conversation"
          : 'Choose a template that best fits your roommate situation'}
      </Text>

      {suggestedTemplateId && (
        <View style={styles.suggestionBanner}>
          <Info size={16} color="#6366F1" />
          <Text style={styles.suggestionText}>
            Based on your conversation, we recommend the template highlighted below
          </Text>
        </View>
      )}

      <ScrollView style={styles.templateList} showsVerticalScrollIndicator={false}>
        {templates.map(template => (
          <TouchableOpacity
            key={template.id}
            style={[
              styles.templateCard,
              selectedTemplate === template.id && styles.selectedTemplate,
              suggestedTemplateId === template.id && !selectedTemplate && styles.suggestedTemplate,
            ]}
            onPress={() => handleSelectTemplate(template)}
          >
            <View style={styles.templateHeader}>
              <View style={styles.templateIconContainer}>
                <FileText size={24} color="#6366F1" />
              </View>
              <View style={styles.templateTitleContainer}>
                <Text style={styles.templateName}>{template.name}</Text>
                <Text style={styles.templateDescription}>{template.description}</Text>

                {suggestedTemplateId === template.id && !selectedTemplate && (
                  <Text style={styles.suggestedLabel}>Suggested</Text>
                )}
              </View>
              {selectedTemplate === template.id && <CheckCircle size={24} color="#6366F1" />}
              {suggestedTemplateId === template.id && !selectedTemplate && (
                <Info size={20} color="#6366F1" />
              )}
            </View>

            <View style={styles.templateDetails}>
              <Text style={styles.sectionTitle}>Includes sections for:</Text>
              {template.sections?.sections?.slice(0, 3).map((section: any, index: number) => (
                <View key={index} style={styles.sectionItem}>
                  <View style={styles.bulletPoint} />
                  <Text style={styles.sectionName}>{section.section_title || section.title}</Text>
                </View>
              ))}
              {(template.sections?.sections?.length || 0) > 3 && (
                <Text style={styles.moreSections}>
                  +{(template.sections?.sections?.length || 0) - 3} more sections
                </Text>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={styles.infoBox}>
        <Info size={16} color="#6366F1" />
        <Text style={styles.infoText}>
          You'll be able to customize all sections after selecting a template
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.continueButton, !selectedTemplate && styles.disabledButton]}
        onPress={handleContinue}
        disabled={!selectedTemplate}
      >
        <Text style={styles.continueButtonText}>Continue</Text>
        <ArrowRight size={20} color={theme.colors.background} />
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8FAFC',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  templateList: {
    flex: 1,
    marginBottom: 16,
  },
  templateCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedTemplate: {
    borderColor: '#6366F1',
    borderWidth: 2,
    backgroundColor: '#EEF2FF',
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  templateIconContainer: {
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
    padding: 8,
    marginRight: 12,
  },
  templateTitleContainer: {
    flex: 1,
  },
  templateName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  templateDetails: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#6366F1',
    marginRight: 8,
  },
  sectionName: {
    fontSize: 14,
    color: '#334155',
  },
  moreSections: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#334155',
    marginLeft: 8,
  },
  continueButton: {
    backgroundColor: '#6366F1',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.background,
    marginRight: 8,
  },
  disabledButton: {
    backgroundColor: '#CBD5E1',
  },
  suggestionBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#CBD5FF',
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    color: '#4338CA',
    marginLeft: 8,
  },
  suggestedTemplate: {
    borderColor: '#6366F1',
    borderStyle: 'dashed',
    borderWidth: 1,
    backgroundColor: '#F8FAFC',
  },
  suggestedLabel: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
    marginTop: 4,
  },
});
