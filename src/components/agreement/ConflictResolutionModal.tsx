import React, { useState, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Modal, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { Text, Avatar } from '@components/ui';
import { Button } from '@design-system';
import { X, AlertTriangle, Check, RefreshCw, MessageCircle, Users, Shield } from 'lucide-react-native';
import { showToast } from '@utils/toast';
;
import { conflictResolutionService } from '@services/conflictResolutionService';
import { useColorFix } from '@hooks/useColorFix';

export interface AgreementConflictData {
  sectionId: string;
  sectionTitle: string;
  yourContent: any;
  theirContent: any;
  editor: {
    userId: string;
    displayName: string;
  };
  userId: string;
  displayName: string;
}

interface ConflictResolutionModalProps {
  visible: boolean;
  conflict: AgreementConflictData | null;
  onResolve: (resolution: 'yours' | 'theirs' | 'merge') => void;
  onCancel: () => void;
}

export default function ConflictResolutionModal({
  visible,
  conflict,
  onResolve,
  onCancel
}: ConflictResolutionModalProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const [selectedOption, setSelectedOption] = useState<'yours' | 'theirs' | 'merge' | null>(null);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Helper function to display content differences
  const renderContentDiff = () => {
    if (!conflict) return null;
    
    // Get all unique field names
    const allFields = new Set([
      ...Object.keys(conflict.yourContent || {}),
      ...Object.keys(conflict.theirContent || {})
    ]);
    
    return Array.from(allFields).map(field => {
      const yourValue = conflict.yourContent?.[field];
      const theirValue = conflict.theirContent?.[field];
      const isDifferent = JSON.stringify(yourValue) !== JSON.stringify(theirValue);
      
      return (
        <View key={field} style={styles.diffItem}>
          <Text style={styles.diffField}>{field}:</Text>
          
          <View style={styles.diffValues}>
            <View style={[styles.diffValue, isDifferent && styles.diffValueHighlight]}>
              <Text style={styles.diffLabel}>Your version:</Text>
              <Text>{typeof yourValue === 'object' ? JSON.stringify(yourValue) : String(yourValue || '')}</Text>
            </View>
            
            <View style={[styles.diffValue, isDifferent && styles.diffValueHighlight]}>
              <Text style={styles.diffLabel}>Their version:</Text>
              <Text>{typeof theirValue === 'object' ? JSON.stringify(theirValue) : String(theirValue || '')}</Text>
            </View>
          </View>
        </View>
      );
    });
  };

  // Generate AI suggestions for resolving the conflict
  const handleGenerateAISuggestions = async () => {
    if (!conflict) return;
    
    try {
      setIsLoadingSuggestions(true);
      
      // Convert agreement conflict to a format the AI can understand
      const mockConflictData = {
        id: conflict.sectionId,
        household_id: 'mock-household-id',
        reported_by: conflict.userId,
        conflict_type: 'other',
        involved_users: [conflict.userId, conflict.editor.userId],
        description: `Agreement section conflict on ${conflict.sectionTitle}`,
        resolution_approach: 'discussion',
        status: 'reported',
      };
      
      const suggestions = await conflictResolutionService.generateAISuggestions(mockConflictData as any);
      
      if (suggestions && suggestions.ai_suggestions) {
        setAiSuggestions(suggestions.ai_suggestions);
        setShowSuggestions(true);
      } else {
        showToast('Failed to generate suggestions', 'error');
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error);
      showToast('Failed to generate suggestions', 'error');
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleResolve = () => {
    if (!selectedOption) {
      showToast('Please select a resolution option', 'error');
      return;
    }
    
    onResolve(selectedOption);
    setSelectedOption(null);
    setShowSuggestions(false);
    setAiSuggestions([]);
  };

  const handleCancel = () => {
    setSelectedOption(null);
    setShowSuggestions(false);
    setAiSuggestions([]);
    onCancel();
  };

  if (!conflict) return null;

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={handleCancel}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.modalHeader}>
            <AlertTriangle size={20} color={theme.colors.warning} />
            <Text style={styles.modalTitle}>Edit Conflict Detected</Text>
            <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
              <X size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
          
          <Text style={styles.modalSubtitle}>
            {conflict.editor.displayName} has edited this section while you were making changes.
          </Text>
          
          <ScrollView style={styles.diffContainer}>
            <Text style={styles.sectionTitle}>{conflict.sectionTitle}</Text>
            {renderContentDiff()}
          </ScrollView>
          
          <Text style={styles.resolutionTitle}>How would you like to resolve this?</Text>
          
          <View style={styles.resolutionOptions}>
            <TouchableOpacity
              style={[
                styles.resolutionOption,
                selectedOption === 'yours' && styles.selectedOption
              ]}
              onPress={() => setSelectedOption('yours')}
            >
              <View style={styles.resolutionOptionHeader}>
                <Check size={20} color={theme.colors.success} />
                <Text style={styles.resolutionOptionTitle}>Keep your changes</Text>
              </View>
              <Text style={styles.resolutionOptionDescription}>
                Your edits will be kept and their changes will be discarded.
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.resolutionOption,
                selectedOption === 'theirs' && styles.selectedOption
              ]}
              onPress={() => setSelectedOption('theirs')}
            >
              <View style={styles.resolutionOptionHeader}>
                <Users size={20} color={theme.colors.info} />
                <Text style={styles.resolutionOptionTitle}>Use their changes</Text>
              </View>
              <Text style={styles.resolutionOptionDescription}>
                Their edits will be kept and your changes will be discarded.
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.resolutionOption,
                selectedOption === 'merge' && styles.selectedOption
              ]}
              onPress={() => setSelectedOption('merge')}
            >
              <View style={styles.resolutionOptionHeader}>
                <RefreshCw size={20} color={theme.colors.primary} />
                <Text style={styles.resolutionOptionTitle}>Merge changes</Text>
              </View>
              <Text style={styles.resolutionOptionDescription}>
                System will attempt to merge both sets of changes.
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* AI Suggestions Section */}
          <View style={styles.aiSuggestionsContainer}>
            <View style={styles.aiSuggestionsHeader}>
              <Shield size={20} color={theme.colors.warning} />
              <Text style={styles.aiSuggestionsTitle}>AI Conflict Resolution</Text>
            </View>
            
            {!showSuggestions && !isLoadingSuggestions && (
              <TouchableOpacity
                style={styles.generateButton}
                onPress={handleGenerateAISuggestions}
              >
                <Text style={styles.generateButtonText}>
                  Get AI suggestions for resolving this conflict
                </Text>
              </TouchableOpacity>
            )}
            
            {isLoadingSuggestions && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Generating suggestions...</Text>
              </View>
            )}
            
            {showSuggestions && aiSuggestions.length > 0 && (
              <View style={styles.suggestionsList}>
                <Text style={styles.suggestionsIntro}>
                  Here are some suggestions to help resolve this conflict:
                </Text>
                {aiSuggestions.map((suggestion, index) => (
                  <View key={index} style={styles.suggestionItem}>
                    <View style={styles.suggestionNumber}>
                      <Text style={styles.suggestionNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
          
          <View style={styles.actionButtons}>
            <Button
              variant="outlined"
              onPress={handleCancel}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
            <Button
              variant="filled"
              onPress={handleResolve}
              disabled={!selectedOption}
              style={styles.resolveButton}
            >
              Apply Resolution
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.overlay,
    padding: 20,
  },
  modalView: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    padding: 0,
    shadowColor: theme.colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#4B5563',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
  },
  diffContainer: {
    maxHeight: 300,
    padding: 16,
    backgroundColor: '#F8FAFC',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  diffItem: {
    marginBottom: 16,
  },
  diffField: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 4,
  },
  diffValues: {
    borderLeftWidth: 2,
    borderLeftColor: '#6366F1',
    paddingLeft: 12,
  },
  diffValue: {
    padding: 8,
    backgroundColor: theme.colors.background,
    borderRadius: 4,
    marginBottom: 8,
  },
  diffValueHighlight: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#D1D5FE',
  },
  diffLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6366F1',
    marginBottom: 4,
  },
  resolutionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    padding: 16,
  },
  resolutionOptions: {
    paddingHorizontal: 16,
  },
  resolutionOption: {
    padding: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedOption: {
    borderColor: '#6366F1',
    backgroundColor: '#EEF2FF',
  },
  resolutionOptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resolutionOptionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  resolutionOptionDescription: {
    fontSize: 13,
    color: theme.colors.textSecondary,
    marginLeft: 28,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    marginRight: 8,
  },
  resolveButton: {
    minWidth: 140,
  },
  aiSuggestionsContainer: {
    marginTop: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: theme.colors.surface,
  },
  aiSuggestionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  aiSuggestionsTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  generateButton: {
    backgroundColor: theme.colors.warning,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  generateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.warning,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  loadingText: {
    fontSize: 14,
    color: theme.colors.gray,
    marginLeft: 8,
  },
  suggestionsList: {
    marginTop: 8,
  },
  suggestionsIntro: {
    fontSize: 14,
    color: theme.colors.gray,
    marginBottom: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  suggestionNumber: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: theme.colors.warning,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginTop: 2,
  },
  suggestionNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.warning,
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.gray,
    lineHeight: 20,
  },
});