import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Modal, TextInput, ActivityIndicator, Alert } from 'react-native';
import { Text } from '@components/ui';
import {
  Calendar,
  Clock,
  ChevronDown,
  ChevronUp,
  Users,
  Edit2,
  MessageCircle,
  Plus,
  DollarSign,
  Home,
  PenTool,
  ChevronRight,
  FileText,
  CheckSquare,
  Pencil,
  X,
  FileSignature,
} from 'lucide-react-native';
import { router } from 'expo-router';
import AgreementActionMenu from '@components/agreement/AgreementActionMenu';
import AgreementChatButton from '@components/agreement/AgreementChatButton';
import { Agreement, AgreementSection } from '@utils/agreement';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { AgreementService } from '@services/agreementService';
import { showToast } from '@utils/toast';
import { supabase } from '@utils/supabaseUtils';

interface AgreementDetailsProps {
  agreement: Agreement;
  sections: AgreementSection[];
  participants: any[];
  isCreator: boolean;
  onGeneratePdf: () => void;
  onEditSection?: (sectionId: string) => void;
  onRefresh?: () => void;
}

export default function AgreementDetails({
  agreement,
  sections,
  participants,
  isCreator,
  onGeneratePdf,
  onEditSection,
  onRefresh,
}: AgreementDetailsProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const agreementService = new AgreementService();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>(
    // Initialize with all sections collapsed
    sections.reduce((acc, section) => ({ ...acc, [section.id]: false }), {})
  );

  const [showParticipants, setShowParticipants] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [showAddParticipant, setShowAddParticipant] = useState(false);
  const [participantEmail, setParticipantEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [creatingSection, setCreatingSection] = useState(false);
  const [showCustomModal, setShowCustomModal] = useState(false);
  const [customSection, setCustomSection] = useState({
    title: '',
    content: '',
    type: 'general'
  });

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  const toggleParticipants = () => setShowParticipants(!showParticipants);
  const toggleActions = () => setShowActions(!showActions);

  const handleAddParticipant = async () => {
    if (!participantEmail.trim()) return;
    
    setLoading(true);
    try {
      // First, find the user by email
      const { data: userData, error: userError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', participantEmail.trim())
        .single();

      if (userError && userError.code !== 'PGRST116') {
        throw new Error('Failed to find user');
      }

      if (!userData) {
        Alert.alert(
          'User Not Found',
          'No user found with this email address. They need to create an account first.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Check if user is already a participant
      const isAlreadyParticipant = participants.some(p => p.user_id === userData.id);
      if (isAlreadyParticipant) {
        Alert.alert(
          'Already Added',
          'This user is already a participant in this agreement.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Add participant using Supabase
      const { error: participantError } = await supabase
        .from('agreement_participants')
        .insert({
          agreement_id: agreement.id,
          user_id: userData.id,
          role: 'roommate',
          status: 'invited'
        });

      if (participantError) {
        throw participantError;
      }

      Alert.alert(
        'Participant Added!',
        `${participantEmail} has been added to the agreement and will receive a notification.`,
        [{ 
          text: 'OK',
          onPress: () => {
            // Trigger refresh to show new participant
            if (onRefresh) {
              onRefresh();
            }
          }
        }]
      );

      setParticipantEmail('');
      setShowAddParticipant(false);
    } catch (error) {
      console.error('Error adding participant:', error);
      Alert.alert(
        'Error',
        'Failed to add participant. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const createTemplateSection = (type: 'rent' | 'rules' | 'utilities' | 'chores' | 'guests') => {
    const templates = {
      rent: {
        title: 'Rent & Financial Responsibilities',
        content: `Monthly Rent: $______
Due Date: _____ of each month
Security Deposit: $______
Utilities Split: _____% each
Late Fee: $_____ after _____ days
Payment Method: _____________`,
        type: 'financial'
      },
      rules: {
        title: 'House Rules & Behavior',
        content: `Quiet Hours: _____ PM to _____ AM
Guests Policy: Max _____ overnight guests per week
Common Area Cleaning: _____________
Personal Belongings: _____________
Smoking Policy: _____________
Pet Policy: _____________`,
        type: 'behavioral'
      },
      utilities: {
        title: 'Utilities & Bills',
        content: `Electricity: Split _____% each
Internet: Split _____% each
Water/Gas: Split _____% each
Garbage: Split _____% each
Responsible Party for Setup: _____________
Bill Due Dates: _____________`,
        type: 'financial'
      },
      chores: {
        title: 'Cleaning & Maintenance',
        content: `Kitchen Cleaning: _____________
Bathroom Cleaning: _____________
Living Room: _____________
Trash & Recycling: _____________
Yard/Outdoor Maintenance: _____________
Deep Cleaning Schedule: _____________`,
        type: 'maintenance'
      },
      guests: {
        title: 'Guest & Visitor Policy',
        content: `Overnight Guests: Max _____ nights per month
Guest Notification: _____ hours advance notice
Common Area Guest Use: _____________
Guest Parking: _____________
Guest Key Policy: _____________
Long-term Guest Policy (>7 days): _____________`,
        type: 'social'
      }
    };

    return templates[type];
  };

  const handleCreateSection = async (type: 'rent' | 'rules' | 'utilities' | 'chores' | 'guests') => {
    if (creatingSection) return; // Prevent multiple clicks
    
    setCreatingSection(true);
    setSelectedTemplate(type);
    
    try {
      const template = createTemplateSection(type);
      console.log('Creating section:', template);
      
      // Create section via AgreementService
      const sectionData = {
        agreement_id: agreement.id,
        version_number: 1, // Default version
        section_key: type, // Use the template type as section key
        section_title: template.title,
        content: { text: template.content, type: template.type }, // Store as JSON
        order_index: sections.length, // Add at the end
        is_required: true,
      };

      const response = await agreementService.addAgreementSection(sectionData);

      if (response.error) {
        throw new Error(response.error);
      }

      // Show success feedback
      Alert.alert(
        'Section Added!',
        `${template.title} has been added to your agreement.`,
        [{ 
          text: 'OK',
          onPress: () => {
            // Trigger refresh to show new section
            if (onRefresh) {
              onRefresh();
            }
          }
        }]
      );

      // Reset visual state after success
      setTimeout(() => {
        setSelectedTemplate('');
      }, 1000);

    } catch (error) {
      console.error('Error creating section:', error);
      Alert.alert(
        'Error',
        'Failed to create section. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setCreatingSection(false);
    }
  };

  const handleCreateCustomSection = async () => {
    if (!customSection.title.trim() || !customSection.content.trim()) {
      Alert.alert('Error', 'Please fill in both title and content.');
      return;
    }

    setCreatingSection(true);
    
    try {
      const sectionData = {
        agreement_id: agreement.id,
        version_number: 1, // Default version
        section_key: `custom_${Date.now()}`, // Unique key for custom sections
        section_title: customSection.title,
        content: { text: customSection.content, type: customSection.type }, // Store as JSON
        order_index: sections.length,
        is_required: false,
      };

      const response = await agreementService.addAgreementSection(sectionData);

      if (response.error) {
        throw new Error(response.error);
      }

      Alert.alert(
        'Custom Section Added!',
        `${customSection.title} has been added to your agreement.`,
        [{ 
          text: 'OK',
          onPress: () => {
            // Trigger refresh to show new section
            if (onRefresh) {
              onRefresh();
            }
          }
        }]
      );

      // Reset form and close modal
      setCustomSection({ title: '', content: '', type: 'general' });
      setShowCustomModal(false);

    } catch (error) {
      console.error('Error creating custom section:', error);
      Alert.alert('Error', 'Failed to create custom section. Please try again.');
    } finally {
      setCreatingSection(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'pending_review':
        return 'Pending Review';
      case 'review':
        return 'Review';
      case 'active':
        return 'Active';
      case 'archived':
        return 'Archived';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return '#94A3B8';
      case 'pending_review':
        return theme.colors.warning;
      case 'review':
        return theme.colors.primary;
      case 'active':
        return theme.colors.success;
      case 'archived':
        return theme.colors.textSecondary;
      default:
        return '#94A3B8';
    }
  };

  const getParticipantStatusLabel = (status: string) => {
    switch (status) {
      case 'invited':
        return 'Invited';
      case 'reviewing':
        return 'Reviewing';
      case 'approved':
        return 'Approved';
      case 'signed':
        return 'Signed';
      case 'declined':
        return 'Declined';
      default:
        return status;
    }
  };

  const getParticipantStatusColor = (status: string) => {
    switch (status) {
      case 'invited':
        return theme.colors.warning;
      case 'reviewing':
        return theme.colors.primary;
      case 'approved':
        return theme.colors.success;
      case 'signed':
        return theme.colors.success;
      case 'declined':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const navigateToEditor = () => {
    router.push({
      pathname: '/agreement/editor',
      params: { id: agreement.id },
    });
  };

  const handleSetUpCompliance = () => {
    // Navigate to the current agreement but highlight the compliance tab
    Alert.alert(
      'Set Up Compliance Tracking',
      'Tap the "Compliance" tab above to add tracking tasks and deadlines for your agreement items.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Go to Compliance Tab', 
          onPress: () => {
            // Show toast to guide user to the tab
            showToast('Switch to the Compliance tab to set up tracking', 'info');
          }
        }
      ]
    );
  };

  const handleSendForSignatures = () => {
    // Check if there are participants first
    if (participants.length === 0) {
      Alert.alert(
        'No Participants',
        'Please add participants to this agreement before sending for signatures.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Navigate to signature workflow
    Alert.alert(
      'Send for Signatures',
      'This will notify all participants to review and sign the agreement. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send', 
          onPress: () => {
            // TODO: Implement signature sending logic
            showToast('Signature requests sent to all participants', 'success');
          }
        }
      ]
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{agreement.title || 'Untitled Agreement'}</Text>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: `${getStatusColor(agreement.status)}20` },
            ]}
          >
            <Text style={[styles.statusText, { color: getStatusColor(agreement.status) }]}>
              {getStatusLabel(agreement.status)}
            </Text>
          </View>
        </View>

        <View style={styles.headerActions}>
          {isCreator && ['draft', 'pending_review'].includes(agreement.status) && (
            <TouchableOpacity style={styles.editAgreementButton} onPress={navigateToEditor}>
              <Edit2 size={18} color={theme.colors.background} />
              <Text style={styles.editAgreementButtonText}>Edit</Text>
            </TouchableOpacity>
          )}
          <AgreementActionMenu
            agreementId={agreement.id}
            status={agreement.status}
            isCreator={isCreator}
            onGeneratePdf={onGeneratePdf}
          />
        </View>
      </View>

      <View style={styles.metadataContainer}>
        <View style={styles.metadataItem}>
          <Calendar size={16} color={theme.colors.textSecondary} />
          <Text style={styles.metadataText}>Created on {formatDate(agreement.created_at)}</Text>
        </View>

        {agreement.effective_date && (
          <View style={styles.metadataItem}>
            <Clock size={16} color={theme.colors.textSecondary} />
            <Text style={styles.metadataText}>
              Effective from {formatDate(agreement.effective_date)}
              {agreement.expiration_date ? ` to ${formatDate(agreement.expiration_date)}` : ''}
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity style={styles.participantsHeader} onPress={toggleParticipants}>
        <View style={styles.sectionTitleContainer}>
          <Users size={20} color={theme.colors.text} />
          <Text style={styles.sectionTitle}>Participants ({participants.length})</Text>
        </View>
        {showParticipants ? (
          <ChevronUp size={20} color={theme.colors.textSecondary} />
        ) : (
          <ChevronDown size={20} color={theme.colors.textSecondary} />
        )}
      </TouchableOpacity>

      {showParticipants && (
        <View style={styles.participantsContainer}>
          {participants.length === 0 ? (
            <View style={styles.emptyParticipants}>
              <Users size={32} color={theme.colors.textSecondary} />
              <Text style={styles.emptyParticipantsText}>No participants added yet</Text>
              <Text style={styles.emptyParticipantsSubtext}>
                Add roommates to this agreement to get started
              </Text>
              <TouchableOpacity 
                style={styles.addParticipantButton}
                onPress={() => setShowAddParticipant(true)}
              >
                <Plus size={16} color={theme.colors.background} />
                <Text style={styles.addParticipantButtonText}>Add Participant</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              {participants.map(participant => (
                                  <View key={participant.user_id} style={styles.participantItem}>
                  <View style={styles.participantInfo}>
                    <Text style={styles.participantName}>
                      {participant.user_profiles ? 
                        `${participant.user_profiles.first_name} ${participant.user_profiles.last_name}`.trim() :
                        participant.profiles ? 
                          `${participant.profiles.first_name} ${participant.profiles.last_name}`.trim() :
                          'Unknown User'
                      }
                      {participant.is_creator && ' (Creator)'}
                    </Text>
                    <Text style={styles.participantEmail}>
                      {participant.user_profiles?.email || participant.profiles?.email || ''}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.participantStatusBadge,
                      { backgroundColor: `${getParticipantStatusColor(participant.status)}20` },
                    ]}
                  >
                    <Text
                      style={[
                        styles.participantStatusText,
                        { color: getParticipantStatusColor(participant.status) },
                      ]}
                    >
                      {getParticipantStatusLabel(participant.status)}
                    </Text>
                  </View>
                </View>
              ))}
              
              <TouchableOpacity 
                style={styles.addMoreParticipantsButton}
                onPress={() => setShowAddParticipant(true)}
              >
                <Plus size={16} color={theme.colors.primary} />
                <Text style={styles.addMoreParticipantsText}>Add Another Participant</Text>
              </TouchableOpacity>
            </>
          )}

          {/* Chat button - only show if participants exist */}
          {participants.length > 0 && (
            <AgreementChatButton
              agreementId={agreement.id}
              participantIds={participants.map(p => p.user_id)}
              title="Discuss with Participants"
              style={styles.chatButton}
            />
          )}
        </View>
      )}

      {/* Agreement Content Section */}
      <View style={styles.contentSection}>
        <Text style={styles.sectionTitle}>Agreement Contents</Text>
        
        {sections.length === 0 ? (
          <View style={styles.emptyContent}>
            <FileText size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyContentTitle}>Ready to create your agreement?</Text>
            <Text style={styles.emptyContentSubtext}>
              Choose from pre-built templates or create custom sections
            </Text>
            
            {/* Quick Action Templates */}
            <View style={styles.templateGrid}>
              <TouchableOpacity 
                style={[
                  styles.templateCard,
                  selectedTemplate === 'rent' && styles.selectedTemplateCard,
                  creatingSection && selectedTemplate === 'rent' && styles.creatingTemplateCard
                ]}
                onPress={() => handleCreateSection('rent')}
                disabled={creatingSection}
              >
                {creatingSection && selectedTemplate === 'rent' ? (
                  <ActivityIndicator size={24} color={theme.colors.primary} />
                ) : (
                  <DollarSign size={24} color={theme.colors.primary} />
                )}
                <Text style={styles.templateTitle}>Rent & Bills</Text>
                <Text style={styles.templateSubtitle}>Monthly payments, utilities</Text>
                {selectedTemplate === 'rent' && !creatingSection && (
                  <View style={styles.templateSelectedIndicator}>
                    <CheckSquare size={16} color={theme.colors.success} />
                  </View>
                )}
              </TouchableOpacity>

              <TouchableOpacity 
                style={[
                  styles.templateCard,
                  selectedTemplate === 'rules' && styles.selectedTemplateCard,
                  creatingSection && selectedTemplate === 'rules' && styles.creatingTemplateCard
                ]}
                onPress={() => handleCreateSection('rules')}
                disabled={creatingSection}
              >
                {creatingSection && selectedTemplate === 'rules' ? (
                  <ActivityIndicator size={24} color={theme.colors.primary} />
                ) : (
                  <Home size={24} color={theme.colors.primary} />
                )}
                <Text style={styles.templateTitle}>House Rules</Text>
                <Text style={styles.templateSubtitle}>Quiet hours, guests, pets</Text>
                {selectedTemplate === 'rules' && !creatingSection && (
                  <View style={styles.templateSelectedIndicator}>
                    <CheckSquare size={16} color={theme.colors.success} />
                  </View>
                )}
              </TouchableOpacity>

              <TouchableOpacity 
                style={[
                  styles.templateCard,
                  selectedTemplate === 'chores' && styles.selectedTemplateCard,
                  creatingSection && selectedTemplate === 'chores' && styles.creatingTemplateCard
                ]}
                onPress={() => handleCreateSection('chores')}
                disabled={creatingSection}
              >
                {creatingSection && selectedTemplate === 'chores' ? (
                  <ActivityIndicator size={24} color={theme.colors.primary} />
                ) : (
                  <CheckSquare size={24} color={theme.colors.primary} />
                )}
                <Text style={styles.templateTitle}>Cleaning</Text>
                <Text style={styles.templateSubtitle}>Chores, maintenance</Text>
                {selectedTemplate === 'chores' && !creatingSection && (
                  <View style={styles.templateSelectedIndicator}>
                    <CheckSquare size={16} color={theme.colors.success} />
                  </View>
                )}
              </TouchableOpacity>

              <TouchableOpacity 
                style={[
                  styles.templateCard,
                  selectedTemplate === 'guests' && styles.selectedTemplateCard,
                  creatingSection && selectedTemplate === 'guests' && styles.creatingTemplateCard
                ]}
                onPress={() => handleCreateSection('guests')}
                disabled={creatingSection}
              >
                {creatingSection && selectedTemplate === 'guests' ? (
                  <ActivityIndicator size={24} color={theme.colors.primary} />
                ) : (
                  <Users size={24} color={theme.colors.primary} />
                )}
                <Text style={styles.templateTitle}>Guests</Text>
                <Text style={styles.templateSubtitle}>Visitor policies</Text>
                {selectedTemplate === 'guests' && !creatingSection && (
                  <View style={styles.templateSelectedIndicator}>
                    <CheckSquare size={16} color={theme.colors.success} />
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity 
              style={[
                styles.customSectionButton,
                creatingSection && styles.disabledButton
              ]}
              onPress={() => setShowCustomModal(true)}
              disabled={creatingSection}
            >
              <Plus size={16} color={theme.colors.background} />
              <Text style={styles.customSectionButtonText}>Create Custom Section</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.sectionsContainer}>
            {sections.map((section) => (
              <View key={section.id} style={styles.sectionCard}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionCardTitle}>{section.section_title}</Text>
                  <TouchableOpacity onPress={() => onEditSection?.(section.id)}>
                    <Pencil size={16} color={theme.colors.primary} />
                  </TouchableOpacity>
                </View>
                <Text style={styles.sectionContent} numberOfLines={3}>
                  {typeof section.content === 'string' ? section.content : section.content?.text || 'No content available'}
                </Text>
              </View>
            ))}
            
            <TouchableOpacity 
              style={styles.addSectionButton}
              onPress={() => setShowCustomModal(true)}
            >
              <Plus size={16} color={theme.colors.primary} />
              <Text style={styles.addSectionText}>Add Another Section</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Next Steps - Show when content exists */}
      {sections.length > 0 && (
        <View style={styles.nextStepsSection}>
          <Text style={styles.sectionTitle}>Next Steps</Text>
          
          <TouchableOpacity style={styles.nextStepCard} onPress={handleSetUpCompliance}>
            <CheckSquare size={20} color={theme.colors.success} />
            <View style={styles.nextStepInfo}>
              <Text style={styles.nextStepTitle}>Set Up Compliance Tracking</Text>
              <Text style={styles.nextStepSubtitle}>Add tasks and deadlines for agreement items</Text>
            </View>
            <ChevronRight size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.nextStepCard} 
            onPress={() => router.push({
              pathname: '/agreement/signature-flow',
              params: { agreementId: agreement.id }
            })}
          >
            <FileSignature size={20} color={theme.colors.warning} />
            <View style={styles.nextStepInfo}>
              <Text style={styles.nextStepTitle}>Signature Workflow</Text>
              <Text style={styles.nextStepSubtitle}>Review approval status and collect signatures</Text>
            </View>
            <ChevronRight size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.nextStepCard} onPress={onGeneratePdf}>
            <FileText size={20} color={theme.colors.primary} />
            <View style={styles.nextStepInfo}>
              <Text style={styles.nextStepTitle}>Generate Final PDF</Text>
              <Text style={styles.nextStepSubtitle}>Download and share the completed agreement</Text>
            </View>
            <ChevronRight size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Add Participant Modal */}
      {showAddParticipant && (
        <Modal visible={showAddParticipant} transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Add Participant</Text>
              <Text style={styles.modalSubtitle}>
                Enter the email of the person you want to add to this agreement
              </Text>
              
              <TextInput
                style={styles.emailInput}
                placeholder="<EMAIL>"
                value={participantEmail}
                onChangeText={setParticipantEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={() => {
                    setShowAddParticipant(false);
                    setParticipantEmail('');
                  }}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.addButton}
                  onPress={handleAddParticipant}
                  disabled={loading || !participantEmail.trim()}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={theme.colors.background} />
                  ) : (
                    <Text style={styles.addButtonText}>Add Participant</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* Custom Section Modal */}
      {showCustomModal && (
        <Modal visible={showCustomModal} transparent animationType="slide">
          <View style={styles.modalOverlay}>
            <View style={styles.customModalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Create Custom Section</Text>
                <TouchableOpacity onPress={() => setShowCustomModal(false)}>
                  <X size={24} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.customModalBody}>
                <Text style={styles.inputLabel}>Section Title</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="e.g., Parking Rules, Pet Policy"
                  value={customSection.title}
                  onChangeText={(text) => setCustomSection(prev => ({ ...prev, title: text }))}
                />
                
                <Text style={styles.inputLabel}>Section Content</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  placeholder="Enter the details for this section..."
                  value={customSection.content}
                  onChangeText={(text) => setCustomSection(prev => ({ ...prev, content: text }))}
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                />
                
                <Text style={styles.inputLabel}>Section Type</Text>
                <View style={styles.typeSelector}>
                  {['general', 'financial', 'behavioral', 'maintenance', 'social'].map((type) => (
                    <TouchableOpacity
                      key={type}
                      style={[
                        styles.typeOption,
                        customSection.type === type && styles.selectedTypeOption
                      ]}
                      onPress={() => setCustomSection(prev => ({ ...prev, type }))}
                    >
                      <Text style={[
                        styles.typeOptionText,
                        customSection.type === type && styles.selectedTypeOptionText
                      ]}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={() => {
                    setShowCustomModal(false);
                    setCustomSection({ title: '', content: '', type: 'general' });
                  }}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.addButton,
                    creatingSection && styles.disabledButton
                  ]}
                  onPress={handleCreateCustomSection}
                  disabled={creatingSection || !customSection.title.trim() || !customSection.content.trim()}
                >
                  {creatingSection ? (
                    <ActivityIndicator size="small" color={theme.colors.background} />
                  ) : (
                    <Text style={styles.addButtonText}>Create Section</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </ScrollView>
  );
}

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 6,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editAgreementButton: {
    backgroundColor: '#6366F1',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  editAgreementButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
    marginLeft: 6,
  },
  metadataContainer: {
    marginBottom: 20,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  metadataText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  participantsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  participantsContainer: {
    marginBottom: 20,
  },
  participantItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  participantEmail: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  participantStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  participantStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  sectionsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 16,
  },
  emptyParticipants: {
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  emptyParticipantsText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
  },
  emptyParticipantsSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
    marginBottom: theme.spacing.md,
  },
  addParticipantButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    gap: theme.spacing.xs,
  },
  addParticipantButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
  },
  addMoreParticipantsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.sm,
    borderStyle: 'dashed',
    marginTop: theme.spacing.sm,
    gap: theme.spacing.xs,
  },
  addMoreParticipantsText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  sectionsContainer: {
    marginTop: theme.spacing.md,
  },
  sectionCard: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  sectionCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  sectionContent: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  addSectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderStyle: 'dashed',
    gap: theme.spacing.xs,
  },
  addSectionText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  nextStepsSection: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  nextStepCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  nextStepInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  nextStepTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  nextStepSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    width: '100%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  modalSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
  },
  emailInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: 16,
    marginBottom: theme.spacing.lg,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.text,
    fontWeight: '500',
  },
  addButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
  },
  addButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
  },
  templateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    justifyContent: 'space-between',
  },
  templateCard: {
    width: '47%',
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
  },
  templateSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  customSectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.xs,
     },
   customSectionButtonText: {
     color: theme.colors.background,
     fontWeight: '600',
     fontSize: 16,
   },
   
   // Content section styles
   contentSection: {
     marginTop: theme.spacing.lg,
   },
   emptyContent: {
     alignItems: 'center',
     padding: theme.spacing.xl,
     backgroundColor: theme.colors.surface,
     borderRadius: theme.borderRadius.lg,
     marginTop: theme.spacing.md,
   },
   emptyContentTitle: {
     fontSize: 18,
     fontWeight: '600',
     color: theme.colors.text,
     marginTop: theme.spacing.md,
   },
   emptyContentSubtext: {
     fontSize: 14,
     color: theme.colors.textSecondary,
     textAlign: 'center',
     marginTop: theme.spacing.xs,
     marginBottom: theme.spacing.lg,
   },
  chatButton: {
    minWidth: 220,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: '#6366F1',
    borderRadius: 8,
  },
  // Enhanced template card styles
  selectedTemplateCard: {
    borderColor: theme.colors.success,
    backgroundColor: `${theme.colors.success}10`,
    borderWidth: 2,
  },
  creatingTemplateCard: {
    opacity: 0.7,
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}05`,
  },
  templateSelectedIndicator: {
    position: 'absolute',
    top: theme.spacing.xs,
    right: theme.spacing.xs,
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.sm,
    padding: 2,
  },
  disabledButton: {
    opacity: 0.5,
  },

  // Custom modal styles
  customModalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: theme.borderRadius.lg,
    borderTopRightRadius: theme.borderRadius.lg,
    maxHeight: '90%',
    minHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  customModalBody: {
    padding: theme.spacing.lg,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.md,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: 16,
    color: theme.colors.text,
    backgroundColor: theme.colors.background,
  },
  textArea: {
    minHeight: 120,
    paddingTop: theme.spacing.md,
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
  },
  typeOption: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  selectedTypeOption: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  typeOptionText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  selectedTypeOptionText: {
    color: theme.colors.background,
    fontWeight: '600',
  },
});
