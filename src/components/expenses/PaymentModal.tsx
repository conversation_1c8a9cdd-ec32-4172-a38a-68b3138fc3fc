import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface PaymentModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (amount: number, method: 'cash' | 'transfer' | 'card' | 'other', notes?: string) => void;
  amountDue: number;
  expenseName: string;
}

/**
 * PaymentModal Component
 * Modal for recording a payment towards an expense
 */
export default function PaymentModal({
  visible,
  onClose,
  onSubmit,
  amountDue,
  expenseName
}: PaymentModalProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [amount, setAmount] = useState(amountDue.toString());
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'transfer' | 'card' | 'other'>('cash');
  const [notes, setNotes] = useState('');
  const [isValid, setIsValid] = useState(false);
  
  useEffect(() => {
    // Reset form when modal opens
    if (visible) {
      setAmount(amountDue.toString());
      setPaymentMethod('cash');
      setNotes('');
    }
  }, [visible, amountDue]);
  
  // Validate amount input
  useEffect(() => {
    const numAmount = parseFloat(amount);
    setIsValid(
      !isNaN(numAmount) && 
      numAmount > 0 && 
      numAmount <= amountDue
    );
  }, [amount, amountDue]);
  
  // Handle amount input change
  const handleAmountChange = (text: string) => {
    // Only allow numeric input with up to 2 decimal places
    if (text === '' || /^\d+(\.\d{0,2})?$/.test(text)) {
      setAmount(text);
    }
  };
  
  // Handle submit
  const handleSubmit = () => {
    const numAmount = parseFloat(amount);
    if (isValid) {
      onSubmit(numAmount, paymentMethod, notes.trim() || undefined);
    }
  };
  
  // Payment method options
  const paymentMethods = [
    { id: 'cash', label: 'Cash', icon: 'dollar-sign' },
    { id: 'transfer', label: 'Transfer', icon: 'repeat' },
    { id: 'card', label: 'Card', icon: 'credit-card' },
    { id: 'other', label: 'Other', icon: 'more-horizontal' }
  ] as const;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.overlay}>
          <View style={styles.modalContent}>
            <View style={styles.header}>
              <Text style={styles.title}>Pay Your Share</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Feather name="x" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.scrollContent}>
              <Text style={styles.expenseName}>{expenseName}</Text>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Amount to Pay</Text>
                <Text style={styles.amountDue}>Amount due: ${amountDue.toFixed(2)}</Text>
                
                <View style={styles.amountInputContainer}>
                  <Text style={styles.dollarSign}>$</Text>
                  <TextInput
                    style={styles.amountInput}
                    value={amount}
                    onChangeText={handleAmountChange}
                    keyboardType="decimal-pad"
                    placeholder="0.00"
                    autoFocus
                    selectTextOnFocus
                  />
                </View>
                
                <TouchableOpacity
                  style={styles.fullAmountButton}
                  onPress={() => setAmount(amountDue.toString())}
                >
                  <Text style={styles.fullAmountText}>Pay Full Amount</Text>
                </TouchableOpacity>
              </View>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Payment Method</Text>
                
                <View style={styles.paymentMethodOptions}>
                  {paymentMethods.map((method) => (
                    <TouchableOpacity
                      key={method.id}
                      style={[
                        styles.methodOption,
                        paymentMethod === method.id && styles.selectedMethod
                      ]}
                      onPress={() => setPaymentMethod(method.id)}
                    >
                      <Feather
                        name={method.icon as any}
                        size={20}
                        color={paymentMethod === method.id ? '#6366F1' : '#6B7280'}
                      />
                      <Text
                        style={[
                          styles.methodText,
                          paymentMethod === method.id && styles.selectedMethodText
                        ]}
                      >
                        {method.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Notes (Optional)</Text>
                <TextInput
                  style={styles.notesInput}
                  value={notes}
                  onChangeText={setNotes}
                  placeholder="Add any additional details about this payment"
                  multiline
                  numberOfLines={3}
                  maxLength={200}
                />
              </View>
            </ScrollView>
            
            <View style={styles.footer}>
              <TouchableOpacity
                style={[styles.submitButton, !isValid && styles.disabledButton]}
                onPress={handleSubmit}
                disabled={!isValid}
              >
                <Text style={styles.submitText}>Record Payment</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    height: '80%',
    maxHeight: 600,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    position: 'relative',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    top: 12,
    padding: 4,
  },
  scrollContent: {
    flex: 1,
    padding: 16,
  },
  expenseName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  amountDue: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 56,
    marginBottom: 12,
  },
  dollarSign: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4B5563',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 20,
    color: '#1F2937',
    height: 56,
  },
  fullAmountButton: {
    alignSelf: 'flex-end',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#EEF2FF',
  },
  fullAmountText: {
    color: '#6366F1',
    fontSize: 14,
    fontWeight: '500',
  },
  paymentMethodOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  methodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginHorizontal: 4,
    marginBottom: 8,
    minWidth: '45%',
  },
  selectedMethod: {
    borderColor: '#6366F1',
    backgroundColor: '#EEF2FF',
  },
  methodText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
  },
  selectedMethodText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1F2937',
    height: 100,
    textAlignVertical: 'top',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  submitButton: {
    backgroundColor: '#6366F1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#A5B4FC',
    opacity: 0.7,
  },
  submitText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
});
