/**
 * Database Performance Monitor
 * 
 * Real-time monitoring dashboard for database performance metrics,
 * connection pool status, and query analytics
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { createLogger } from '@utils/loggerUtils';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

const performanceLogger = createLogger('DatabasePerformanceMonitor');

// Performance metrics interfaces
interface QueryMetrics {
  totalQueries: number;
  avgResponseTime: number;
  slowQueries: number;
  errorRate: number;
  cacheHitRatio: number;
}

interface ConnectionPoolMetrics {
  activeConnections: number;
  maxConnections: number;
  queuedRequests: number;
  timeouts: number;
  utilization: number;
}

interface DatabaseHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  diskUsage: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface PerformanceAlert {
  id: string;
  type: 'slow_query' | 'high_error_rate' | 'connection_pool_full' | 'memory_high';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  resolved: boolean;
}

interface DatabaseMetrics {
  queryMetrics: QueryMetrics;
  connectionPool: ConnectionPoolMetrics;
  health: DatabaseHealth;
  alerts: PerformanceAlert[];
  lastUpdated: string;
}

// Thresholds for performance alerts
const PERFORMANCE_THRESHOLDS = {
  SLOW_QUERY_MS: 1000,
  HIGH_ERROR_RATE: 0.05, // 5%
  HIGH_CONNECTION_UTILIZATION: 0.8, // 80%
  HIGH_MEMORY_USAGE: 0.85, // 85%
  HIGH_CPU_USAGE: 0.8, // 80%
};

export const DatabasePerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<DatabaseMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '6h' | '24h' | '7d'>('1h');
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  /**
   * Fetch real-time database metrics
   */
  const fetchDatabaseMetrics = useCallback(async (): Promise<DatabaseMetrics> => {
    const startTime = Date.now();
    
    try {
      // Simulate comprehensive database metrics collection
      // In production, these would be actual queries to monitoring tables
      
      // Query performance metrics
      const queryMetrics: QueryMetrics = await fetchQueryMetrics();
      
      // Connection pool status
      const connectionPool: ConnectionPoolMetrics = await fetchConnectionPoolMetrics();
      
      // Overall database health
      const health: DatabaseHealth = await fetchDatabaseHealth();
      
      // Recent performance alerts
      const alerts: PerformanceAlert[] = await fetchPerformanceAlerts();
      
      const metrics: DatabaseMetrics = {
        queryMetrics,
        connectionPool,
        health,
        alerts,
        lastUpdated: new Date().toISOString(),
      };
      
      // Log performance of the monitoring query itself
      const monitoringTime = Date.now() - startTime;
      performanceLogger.debug('Database metrics collected', {
        collectionTime: monitoringTime,
        metricsCount: Object.keys(metrics).length
      });
      
      return metrics;
      
    } catch (error) {
      performanceLogger.error('Failed to fetch database metrics', error as Error);
      throw error;
    }
  }, [selectedTimeRange]);

  /**
   * Fetch query performance metrics
   */
  const fetchQueryMetrics = async (): Promise<QueryMetrics> => {
    try {
      // Simulate query metrics - in production, query pg_stat_statements
      const mockMetrics: QueryMetrics = {
        totalQueries: 1247,
        avgResponseTime: 145, // ms
        slowQueries: 12,
        errorRate: 0.023, // 2.3%
        cacheHitRatio: 0.94, // 94%
      };
      
      // Add some realistic variation
      const variation = (Math.random() - 0.5) * 0.2;
      mockMetrics.avgResponseTime *= (1 + variation);
      mockMetrics.slowQueries += Math.floor(Math.random() * 5);
      mockMetrics.errorRate *= (1 + variation);
      mockMetrics.cacheHitRatio = Math.min(0.99, mockMetrics.cacheHitRatio * (1 + variation * 0.1));
      
      return mockMetrics;
    } catch (error) {
      performanceLogger.error('Failed to fetch query metrics', error as Error);
      throw error;
    }
  };

  /**
   * Fetch connection pool metrics
   */
  const fetchConnectionPoolMetrics = async (): Promise<ConnectionPoolMetrics> => {
    try {
      // Get actual connection pool metrics if available
      // const poolStatus = connectionPoolManager.getMetrics();
      
      // Mock metrics for demonstration
      const mockMetrics: ConnectionPoolMetrics = {
        activeConnections: 7,
        maxConnections: 20,
        queuedRequests: 2,
        timeouts: 0,
        utilization: 0.35,
      };
      
      return mockMetrics;
    } catch (error) {
      performanceLogger.error('Failed to fetch connection pool metrics', error as Error);
      throw error;
    }
  };

  /**
   * Fetch overall database health
   */
  const fetchDatabaseHealth = async (): Promise<DatabaseHealth> => {
    try {
      // Test database connectivity and performance
      const healthCheckStart = Date.now();
      
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      const responseTime = Date.now() - healthCheckStart;
      
      // Determine health status based on response time
      let status: DatabaseHealth['status'] = 'healthy';
      if (responseTime > 1000) status = 'critical';
      else if (responseTime > 500) status = 'warning';
      
      const health: DatabaseHealth = {
        status,
        uptime: 99.97, // percentage
        diskUsage: 0.65, // 65%
        memoryUsage: 0.72, // 72%
        cpuUsage: 0.45, // 45%
      };
      
      return health;
    } catch (error) {
      performanceLogger.error('Database health check failed', error as Error);
      return {
        status: 'critical',
        uptime: 0,
        diskUsage: 0,
        memoryUsage: 0,
        cpuUsage: 0,
      };
    }
  };

  /**
   * Fetch recent performance alerts
   */
  const fetchPerformanceAlerts = async (): Promise<PerformanceAlert[]> => {
    try {
      // Mock alerts - in production, fetch from monitoring system
      const alerts: PerformanceAlert[] = [
        {
          id: '1',
          type: 'slow_query',
          message: 'Query execution time exceeded 1000ms',
          severity: 'medium',
          timestamp: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
          resolved: false,
        },
        {
          id: '2',
          type: 'high_error_rate',
          message: 'Error rate increased to 3.2%',
          severity: 'high',
          timestamp: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
          resolved: true,
        },
      ];
      
      return alerts;
    } catch (error) {
      performanceLogger.error('Failed to fetch performance alerts', error as Error);
      return [];
    }
  };

  /**
   * Load metrics and handle state updates
   */
  const loadMetrics = useCallback(async (isRefresh = false) => {
    if (!mountedRef.current) return;
    
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setIsLoading(true);
      }
      
      const newMetrics = await fetchDatabaseMetrics();
      
      if (mountedRef.current) {
        setMetrics(newMetrics);
        
        // Check for critical alerts
        const criticalAlerts = newMetrics.alerts.filter(
          alert => alert.severity === 'critical' && !alert.resolved
        );
        
        if (criticalAlerts.length > 0) {
          Alert.alert(
            'Critical Database Alert',
            `${criticalAlerts.length} critical issue(s) detected. Please review immediately.`,
            [{ text: 'OK', style: 'default' }]
          );
        }
      }
    } catch (error) {
      if (mountedRef.current) {
        performanceLogger.error('Failed to load database metrics', error as Error);
        Alert.alert('Error', 'Failed to load database metrics. Please try again.');
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
        setRefreshing(false);
      }
    }
  }, [fetchDatabaseMetrics]);

  /**
   * Setup auto-refresh
   */
  useEffect(() => {
    loadMetrics();
    
    if (autoRefresh) {
      intervalRef.current = setInterval(() => {
        loadMetrics();
      }, 30000); // Refresh every 30 seconds
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [loadMetrics, autoRefresh]);

  /**
   * Get status color based on health
   */
  const getStatusColor = (status: string) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'critical': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  /**
   * Get severity color for alerts
   */
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return '#2196F3';
      case 'medium': return '#FF9800';
      case 'high': return '#F44336';
      case 'critical': return '#D32F2F';
      default: return '#9E9E9E';
    }
  };

  if (isLoading && !metrics) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading database metrics...</Text>
        </View>
      </View>
    );
  }

  if (!metrics) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load database metrics</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadMetrics()}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={() => loadMetrics(true)} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Database Performance</Text>
        <View style={styles.headerControls}>
          <TouchableOpacity
            style={[
              styles.autoRefreshButton,
              { backgroundColor: autoRefresh ? '#4CAF50' : '#9E9E9E' }
            ]}
            onPress={() => setAutoRefresh(!autoRefresh)}
          >
            <Text style={styles.autoRefreshText}>
              Auto: {autoRefresh ? 'ON' : 'OFF'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Overall Health Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Database Health</Text>
        <View style={styles.healthCard}>
          <View style={styles.healthHeader}>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: getStatusColor(metrics.health.status) }
              ]}
            />
            <Text style={[styles.statusText, { color: getStatusColor(metrics.health.status) }]}>
              {metrics.health.status.toUpperCase()}
            </Text>
          </View>
          
          <View style={styles.healthMetrics}>
            <View style={styles.healthMetric}>
              <Text style={styles.healthMetricLabel}>Uptime</Text>
              <Text style={styles.healthMetricValue}>{metrics.health.uptime.toFixed(2)}%</Text>
            </View>
            <View style={styles.healthMetric}>
              <Text style={styles.healthMetricLabel}>CPU</Text>
              <Text style={styles.healthMetricValue}>
                {(metrics.health.cpuUsage * 100).toFixed(1)}%
              </Text>
            </View>
            <View style={styles.healthMetric}>
              <Text style={styles.healthMetricLabel}>Memory</Text>
              <Text style={styles.healthMetricValue}>
                {(metrics.health.memoryUsage * 100).toFixed(1)}%
              </Text>
            </View>
            <View style={styles.healthMetric}>
              <Text style={styles.healthMetricLabel}>Disk</Text>
              <Text style={styles.healthMetricValue}>
                {(metrics.health.diskUsage * 100).toFixed(1)}%
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Query Performance */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Query Performance</Text>
        <View style={styles.metricsGrid}>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{metrics.queryMetrics.avgResponseTime.toFixed(0)}ms</Text>
            <Text style={styles.metricLabel}>Avg Response Time</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{metrics.queryMetrics.totalQueries}</Text>
            <Text style={styles.metricLabel}>Total Queries</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{metrics.queryMetrics.slowQueries}</Text>
            <Text style={styles.metricLabel}>Slow Queries</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>
              {(metrics.queryMetrics.cacheHitRatio * 100).toFixed(1)}%
            </Text>
            <Text style={styles.metricLabel}>Cache Hit Ratio</Text>
          </View>
        </View>
      </View>

      {/* Connection Pool */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Connection Pool</Text>
        <View style={styles.connectionPoolCard}>
          <View style={styles.connectionPoolHeader}>
            <Text style={styles.connectionPoolText}>
              {metrics.connectionPool.activeConnections} / {metrics.connectionPool.maxConnections} Active
            </Text>
            <Text style={styles.utilizationText}>
              {(metrics.connectionPool.utilization * 100).toFixed(1)}% Utilization
            </Text>
          </View>
          
          <View style={styles.connectionPoolBar}>
            <View
              style={[
                styles.connectionPoolFill,
                {
                  width: `${metrics.connectionPool.utilization * 100}%`,
                  backgroundColor: metrics.connectionPool.utilization > 0.8 ? '#F44336' : '#4CAF50'
                }
              ]}
            />
          </View>
          
          <View style={styles.connectionPoolDetails}>
            <Text style={styles.connectionPoolDetail}>
              Queued: {metrics.connectionPool.queuedRequests}
            </Text>
            <Text style={styles.connectionPoolDetail}>
              Timeouts: {metrics.connectionPool.timeouts}
            </Text>
          </View>
        </View>
      </View>

      {/* Performance Alerts */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Performance Alerts ({metrics.alerts.filter(a => !a.resolved).length} active)
        </Text>
        
        {metrics.alerts.length === 0 ? (
          <View style={styles.noAlertsCard}>
            <Text style={styles.noAlertsText}>No performance alerts</Text>
          </View>
        ) : (
          metrics.alerts.map((alert) => (
            <View
              key={alert.id}
              style={[
                styles.alertCard,
                { borderLeftColor: getSeverityColor(alert.severity) }
              ]}
            >
              <View style={styles.alertHeader}>
                <Text style={[styles.alertSeverity, { color: getSeverityColor(alert.severity) }]}>
                  {alert.severity.toUpperCase()}
                </Text>
                <Text style={styles.alertTime}>
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </Text>
              </View>
              <Text style={styles.alertMessage}>{alert.message}</Text>
              {alert.resolved && (
                <Text style={styles.alertResolved}>✓ Resolved</Text>
              )}
            </View>
          ))
        )}
      </View>

      {/* Last Updated */}
      <View style={styles.footer}>
        <Text style={styles.lastUpdated}>
          Last updated: {new Date(metrics.lastUpdated).toLocaleTimeString()}
        </Text>
      </View>
    </ScrollView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  autoRefreshButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  autoRefreshText: {
    color: theme.colors.background,
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    margin: 15,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  healthCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 20,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  healthHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  healthMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  healthMetric: {
    alignItems: 'center',
  },
  healthMetricLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  healthMetricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 20,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  connectionPoolCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 20,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  connectionPoolHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  connectionPoolText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  utilizationText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  connectionPoolBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginBottom: 12,
  },
  connectionPoolFill: {
    height: '100%',
    borderRadius: 4,
  },
  connectionPoolDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  connectionPoolDetail: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  noAlertsCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  noAlertsText: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '500',
  },
  alertCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertSeverity: {
    fontSize: 12,
    fontWeight: '600',
  },
  alertTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  alertMessage: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  alertResolved: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
    marginTop: 8,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  lastUpdated: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default DatabasePerformanceMonitor; 