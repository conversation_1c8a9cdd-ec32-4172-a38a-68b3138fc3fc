import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Mail, AlertTriangle, CheckCircle2, ExternalLink } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { supabase } from '@utils/supabaseUtils';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { logger } from '@services/loggerService';

/**
 * Component that displays the user's email verification status
 * and provides quick actions to manage verification
 */
const VerificationStatusBar = () => {
  const theme = useTheme();
  
  // Debug logging to see what theme object we're getting
  console.log('🔍 VerificationStatusBar theme:', {
    hasSpacing: !!theme.spacing,
    spacingSm: theme.spacing?.sm,
    themeKeys: Object.keys(theme),
    fullSpacing: theme.spacing
  });
  
  const styles = createStyles(theme);
  const router = useRouter();
  const { authState, resendVerificationEmail } = useAuthCompat();
  const user = authState.user;
  const [loading, setLoading] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cooldown, setCooldown] = useState(0);

  // Check user verification status
  useEffect(() => {
    const checkVerificationStatus = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!user) {
          setLoading(false);
          return;
        }

        // Get the user's email verification status with proper error handling
        const { data, error } = await supabase.auth.getUser();

        if (error) {
          // Handle specific error types more gracefully
          const errorMessage = error.message || 'Failed to check verification status';
          console.error('🟥 [VerificationStatusBar] Error checking email verification status');
          console.error('Metadata:', { error });
          
          logger.error('Error checking email verification status', 'VerificationStatusBar', {
            error: errorMessage,
            errorCode: error.status || 'unknown',
            userExists: !!user
          });
          
          setError('Unable to check verification status. Please try again later.');
          return;
        }

        if (data?.user) {
          const verified = data.user.email_confirmed_at !== null;
          setEmailVerified(verified);

          // Only log if verification status changes or on initial load
          if (verified !== emailVerified || loading) {
            logger.info('Email verification status checked', 'VerificationStatusBar', {
              verified,
              email: data.user.email,
            });
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error('[VerificationStatusBar] Error:', errorMessage);
        
        logger.error('Unexpected error checking verification status', 'VerificationStatusBar', {
          error: errorMessage,
          userExists: !!user
        });
        
        setError('Verification check failed. Please refresh the app.');
      } finally {
        setLoading(false);
      }
    };

    // Only check if we have a user and haven't checked yet, or if user ID changes
    if (user?.id && (loading || !emailVerified)) {
      checkVerificationStatus();
    }

    // Set up a refresh interval only if user is not verified
    let intervalId: NodeJS.Timeout | null = null;
    if (user?.id && !emailVerified && !error) {
      intervalId = setInterval(checkVerificationStatus, 60000); // Check every 60 seconds (reduced frequency)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [user?.id, emailVerified]); // Only depend on user ID and verification status

  // Handle resend verification email
  const handleResendEmail = async () => {
    if (!user?.email || sendingEmail || cooldown > 0) return;

    setSendingEmail(true);
    setError(null);
    setEmailSent(false);

    try {
      const error = await resendVerificationEmail(user.email);

      if (error) {
        setError(error);
        logger.error('Failed to resend verification email', 'VerificationStatusBar', { error });
      } else {
        setEmailSent(true);
        logger.info('Verification email resent successfully', 'VerificationStatusBar', {
          email: user.email,
        });

        // Set cooldown timer (60 seconds)
        setCooldown(60);
        const timer = setInterval(() => {
          setCooldown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    } catch (err) {
      logger.error('Unexpected error resending verification email', 'VerificationStatusBar', {
        error: err instanceof Error ? err.message : String(err),
      });
      setError('An unexpected error occurred');
    } finally {
      setSendingEmail(false);
    }
  };

  // Navigate to verification instructions page
  const goToVerificationPage = () => {
    if (user?.email) {
      router.push({
        pathname: '/(auth)/verification-notice',
        params: { email: user.email },
      });
    }
  };

  // Don't show anything if loading and no error
  if (loading && !error) {
    return null;
  }

  // Don't show the banner if the user is verified and no error
  if (emailVerified && !error) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        {error ? (
          <AlertTriangle size={20} color="#FFFFFF" />
        ) : emailSent ? (
          <CheckCircle2 size={20} color="#FFFFFF" />
        ) : (
          <Mail size={20} color="#FFFFFF" />
        )}
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>
          {error 
            ? 'Verification Error' 
            : emailSent 
            ? 'Verification email sent!' 
            : 'Please verify your email'}
        </Text>
        <Text style={styles.description}>
          {error
            ? error
            : emailSent
            ? 'Check your inbox and click the verification link.'
            : 'Verify your email to access all features.'}
        </Text>
      </View>

      <View style={styles.actionContainer}>
        {sendingEmail ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : cooldown > 0 ? (
          <Text style={styles.cooldownText}>{cooldown}s</Text>
        ) : (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleResendEmail}
            disabled={cooldown > 0}
          >
            <Text style={styles.actionText}>Resend</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity style={styles.actionButton} onPress={goToVerificationPage}>
          <Text style={styles.actionText}>View</Text>
          <ExternalLink size={16} color="#FFFFFF" style={styles.linkIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (theme: any) => {
  // Safety check for theme structure
  if (!theme || !theme.spacing) {
    console.error('🔴 VerificationStatusBar: Invalid theme object received:', theme);
    // Fallback values
    const fallbackSpacing = { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 };
    const fallbackColors = { primary: '#0284c7', primaryLight: '#38bdf8', primaryDark: '#0369a1' };
    const fallbackBorderRadius = { sm: 4 };
    theme = { 
      spacing: fallbackSpacing, 
      colors: fallbackColors,
      borderRadius: fallbackBorderRadius
    };
  }
  
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.primary,
      flexDirection: 'row',
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      alignItems: 'center',
      width: '100%',
    },
    iconContainer: {
      marginRight: theme.spacing.sm,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      fontSize: 14,
      fontWeight: '600',
      color: '#FFFFFF',
      marginBottom: 2,
    },
    description: {
      fontSize: 12,
      color: theme.colors.primaryLight || '#FFFFFF',
    },
    actionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: theme.spacing.xs,
    },
    actionButton: {
      backgroundColor: theme.colors.primaryDark || '#0369a1',
      paddingVertical: 6,
      paddingHorizontal: 10,
      borderRadius: theme.borderRadius?.sm || 4,
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: theme.spacing.xs,
    },
    actionText: {
      fontSize: 12,
      fontWeight: '500',
      color: '#FFFFFF',
    },
    cooldownText: {
      fontSize: 12,
      color: theme.colors.primaryLight || '#FFFFFF',
      marginHorizontal: theme.spacing.xs,
    },
    linkIcon: {
      marginLeft: theme.spacing.xs,
    },
    errorText: {
      fontSize: 12,
      color: theme.colors.errorLight || '#ef4444',
      marginTop: theme.spacing.xs,
    },
  });
};

export default VerificationStatusBar;
