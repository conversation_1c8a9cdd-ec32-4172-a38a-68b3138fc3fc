import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { ASYNC_STORAGE_KEYS } from '@utils/constants';
import VerificationConfirmation from '@components/auth/VerificationConfirmation';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface EmailVerificationHandlerProps {
  redirectTo?: string;
}

/**
 * A component that handles the email verification process
 * It checks if the user's email is verified and shows a confirmation screen if it is
 */
const EmailVerificationHandler = ({ redirectTo = '/(tabs)' }: EmailVerificationHandlerProps) => {
  const router = useRouter();
  const [isVerified, setIsVerified] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Check if the user's email is verified
  useEffect(() => {
    const checkVerificationStatus = async () => {
      try {
        // Get the pending verification email from storage
        const pendingEmail = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL);
        
        if (!pendingEmail) {
          return;
        }
        
        setEmail(pendingEmail);
        
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          logger.error('Error checking session', 'EmailVerificationHandler', { error: sessionError });
          return;
        }
        
        // If we have a session and the email matches, the user is verified
        if (session?.user && session.user.email === pendingEmail && session.user.email_confirmed_at) {
          logger.info('Email verified successfully', 'EmailVerificationHandler', { email: pendingEmail });
          
          // Clear the pending verification email
          await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL);
          
          setIsVerified(true);
          setShowConfirmation(true);
        }
      } catch (error) {
        logger.error('Error checking verification status', 'EmailVerificationHandler', { error });
      }
    };
    
    checkVerificationStatus();
    
    // Set up a refresh interval to periodically check status
    const intervalId = setInterval(checkVerificationStatus, 5000); // Check every 5 seconds
    
    return () => clearInterval(intervalId);
  }, []);

  // Handle dismissing the confirmation
  const handleDismiss = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    setShowConfirmation(false);
  };

  // If the user is verified and the confirmation is shown, render the confirmation
  if (isVerified && showConfirmation) {
    return (
      <View style={styles.container}>
        <VerificationConfirmation 
          email={email || undefined} 
          onDismiss={handleDismiss}
        />
      </View>
    );
  }

  // Otherwise, render nothing
  return null;
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
});

export default EmailVerificationHandler;
