import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Check, ArrowRight, Shield, AlertTriangle, RefreshCw } from 'lucide-react-native';
import { Button } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import * as Haptics from 'expo-haptics';
import { verificationService as emailVerificationService } from '@services';
import { logger } from '@services/loggerService';

interface EnhancedEmailVerificationConfirmationProps {
  email: string;
  token: string;
  onDismiss?: () => void;
  onVerificationComplete?: (success: boolean) => void;
  redirectPath?: string;
}

/**
 * Enhanced email verification confirmation component
 * Handles the verification process and displays appropriate UI based on the result
 */
const EnhancedEmailVerificationConfirmation = ({
  email,
  token,
  onDismiss,
  onVerificationComplete,
  redirectPath = '/(tabs)'
}: EnhancedEmailVerificationConfirmationProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const [status, setStatus] = React.useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = React.useState<string | undefined>();

  // Verify email on component mount
  useEffect(() => {
    const verifyEmail = async () => {
      try {
        logger.info('Verifying email', 'EnhancedEmailVerificationConfirmation', { email });
        
        // Call the email verification service
        const { success, error } = await emailVerificationService.verifyEmail(token, email);
        
        if (success) {
          logger.info('Email verification successful', 'EnhancedEmailVerificationConfirmation', { email });
          setStatus('success');
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          if (onVerificationComplete) onVerificationComplete(true);
        } else {
          logger.error('Email verification failed', 'EnhancedEmailVerificationConfirmation', { error, email });
          setStatus('error');
          setErrorMessage(error);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          if (onVerificationComplete) onVerificationComplete(false);
        }
      } catch (err) {
        logger.error('Unexpected error during email verification', 'EnhancedEmailVerificationConfirmation', { err, email });
        setStatus('error');
        setErrorMessage('An unexpected error occurred');
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        if (onVerificationComplete) onVerificationComplete(false);
      }
    };
    
    verifyEmail();
  }, [email, token, onVerificationComplete]);

  const handleContinue = () => {
    // Navigate to specified path or dashboard
    if (redirectPath === '/(tabs)') {
      router.replace('/(tabs)');
    } else if (redirectPath === '/(auth)/login') {
      router.replace('/(auth)/login');
    } else {
      // For other paths, try to use push instead of replace
      router.push(redirectPath as any);
    }
    if (onDismiss) onDismiss();
  };
  
  const handleRetry = async () => {
    setStatus('loading');
    setErrorMessage(undefined);
    
    try {
      // Resend verification email
      const { success, error } = await emailVerificationService.resendVerificationEmail(email);
      
      if (success) {
        logger.info('Verification email resent', 'EnhancedEmailVerificationConfirmation', { email });
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Show success message but keep in error state since we need to wait for user to click the new link
        setErrorMessage('Verification email resent. Please check your inbox and click the link.');
      } else {
        logger.error('Failed to resend verification email', 'EnhancedEmailVerificationConfirmation', { error, email });
        setStatus('error');
        setErrorMessage(error || 'Failed to resend verification email');
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    } catch (err) {
      logger.error('Unexpected error resending verification email', 'EnhancedEmailVerificationConfirmation', { err, email });
      setStatus('error');
      setErrorMessage('An unexpected error occurred');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
            <Text style={styles.title}>Verifying Email</Text>
            <Text style={styles.description}>
              Please wait while we verify your email address...
            </Text>
          </>
        );

      case 'error':
        return (
          <>
            <View style={[styles.statusContainer, styles.errorContainer]}>
              <AlertTriangle size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.title}>Verification Failed</Text>
            <Text style={styles.description}>
              {errorMessage || 'We encountered an issue verifying your email address.'}
            </Text>
            <View style={styles.divider} />
            <Button
              onPress={handleRetry}
              style={styles.retryButton}
              leftIcon={RefreshCw}
            >
              Resend Verification Email
            </Button>
            <Button
              onPress={handleContinue}
              style={styles.continueButton}
              variant="outlined"
            >
              Continue Anyway
            </Button>
          </>
        );

      case 'success':
      default:
        return (
          <>
            <View style={[styles.statusContainer, styles.successContainer]}>
              <Check size={32} color="#FFFFFF" />
            </View>
            <Text style={styles.title}>Email Verified</Text>
            <Text style={styles.description}>
              Your email <Text style={styles.emailText}>{email}</Text> has been successfully verified. 
              Your account is now fully activated.
            </Text>
            <View style={styles.divider} />
            <Text style={styles.securityNote}>
              This verification helps ensure the security of your account and allows you to access all features.
            </Text>
            <Button
              onPress={handleContinue}
              style={styles.continueButton}
              rightIcon={ArrowRight}
            >
              Continue to Dashboard
            </Button>
          </>
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Shield size={24} color="#FFFFFF" />
      </View>

      <View style={styles.contentWrapper}>
        {renderContent()}

        {onDismiss && status !== 'loading' && (
          <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
            <Text style={styles.dismissText}>Dismiss</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    width: '90%',
    maxWidth: 500,
    alignSelf: 'center',
    marginVertical: 20,
    ...theme.shadows.md,
  },
  iconContainer: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  contentWrapper: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  loadingContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  statusContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  successContainer: {
    backgroundColor: theme.colors.success,
  },
  errorContainer: {
    backgroundColor: theme.colors.error,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  emailText: {
    fontWeight: '600',
    color: theme.colors.text,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.border,
    width: '100%',
    marginVertical: theme.spacing.md,
  },
  securityNote: {
    fontSize: 14,
    color: theme.colors.textMuted,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    fontStyle: 'italic',
  },
  continueButton: {
    width: '100%',
  },
  retryButton: {
    width: '100%',
    marginBottom: theme.spacing.sm,
  },
  dismissButton: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.xs,
  },
  dismissText: {
    fontSize: 14,
    color: theme.colors.textMuted,
    fontWeight: '500',
  },
});

export default EnhancedEmailVerificationConfirmation;
