import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface PasswordStrengthMeterProps {
  strength: number; // 0-5 scale
}

export default function PasswordStrengthMeter({ strength }: PasswordStrengthMeterProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  // Determine strength level text and color
  const getStrengthText = () => {
    switch (strength) {
      case 0: return { text: 'No Password', color: theme.colors.textMuted };
      case 1: return { text: 'Very Weak', color: theme.colors.error };
      case 2: return { text: 'Weak', color: theme.colors.warning };
      case 3: return { text: 'Moderate', color: theme.colors.warning };
      case 4: return { text: 'Strong', color: theme.colors.success };
      case 5: return { text: 'Very Strong', color: theme.colors.success };
      default: return { text: 'No Password', color: theme.colors.textMuted };
    }
  };
  
  const strengthInfo = getStrengthText();
  
  return (
    <View style={styles.container}>
      <View style={styles.meterContainer}>
        {[1, 2, 3, 4, 5].map((level) => (
          <View
            key={level}
            style={[
              styles.meterSegment,
              { 
                backgroundColor: level <= strength ? strengthInfo.color : theme.colors.border,
                width: `${100 / 5}%`,
              }
            ]}
          />
        ))}
      </View>
      <Text style={[styles.strengthText, { color: strengthInfo.color }]}>
        {strengthInfo.text}
      </Text>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginTop: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  meterContainer: {
    flexDirection: 'row',
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    backgroundColor: theme.colors.border,
  },
  meterSegment: {
    height: '100%',
  },
  strengthText: {
    fontSize: 12,
    marginTop: theme.spacing.xs,
    textAlign: 'right',
  },
});
