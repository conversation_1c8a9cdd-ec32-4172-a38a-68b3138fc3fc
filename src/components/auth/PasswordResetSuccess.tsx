import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Check, ArrowRight, Lock } from 'lucide-react-native';
import { Button } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface PasswordResetSuccessProps {
  onDismiss?: () => void;
}

const PasswordResetSuccess = ({ onDismiss }: PasswordResetSuccessProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const handleContinue = () => {
    router.replace('/(auth)/login');
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Lock size={24} color="#FFFFFF" />
      </View>

      <View style={styles.contentWrapper}>
        <View style={styles.checkContainer}>
          <Check size={32} color={theme.colors.success} />
        </View>

        <Text style={styles.title}>Password Reset Successful</Text>
        
        <Text style={styles.description}>
          Your password has been successfully reset. You can now log in with your new password.
        </Text>

        <View style={styles.divider} />

        <Text style={styles.securityNote}>
          For security reasons, you will be signed out of all devices. Please log in again with your new password.
        </Text>

        <Button
          onPress={handleContinue}
          variant="filled"
          color="primary"
          style={styles.continueButton}
          rightIcon={ArrowRight}
        >
          Continue to Login
        </Button>

        {onDismiss && (
          <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
            <Text style={styles.dismissText}>Dismiss</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    width: '100%',
    maxWidth: 500,
    alignSelf: 'center',
    marginVertical: theme.spacing.lg,
    ...theme.shadows.md,
  },
  iconContainer: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  contentWrapper: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  checkContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.successLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 24,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.border,
    width: '100%',
    marginVertical: theme.spacing.md,
  },
  securityNote: {
    fontSize: 14,
    color: theme.colors.textMuted,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    fontStyle: 'italic',
  },
  continueButton: {
    width: '100%',
  },
  dismissButton: {
    marginTop: theme.spacing.md,
    padding: theme.spacing.xs,
  },
  dismissText: {
    fontSize: 14,
    color: theme.colors.textMuted,
    fontWeight: '500',
  },
});

export default PasswordResetSuccess;
