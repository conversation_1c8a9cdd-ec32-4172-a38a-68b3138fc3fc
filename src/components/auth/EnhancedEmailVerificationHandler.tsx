import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { ASYNC_STORAGE_KEYS } from '@utils/constants';
import { verificationService as emailVerificationService } from '@services';
import EnhancedVerificationConfirmation from '@components/auth/EnhancedVerificationConfirmation';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface EnhancedEmailVerificationHandlerProps {
  redirectTo?: string;
  token?: string;
  email?: string;
  autoVerify?: boolean;
}

/**
 * Enhanced component that handles the email verification process
 * It can either:
 * 1. Check if the user's email is verified based on session data
 * 2. Actively verify an email using a token (when autoVerify is true)
 */
const EnhancedEmailVerificationHandler = ({ 
  redirectTo = '/(tabs)', 
  token, 
  email: providedEmail,
  autoVerify = false
}: EnhancedEmailVerificationHandlerProps) => {
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [email, setEmail] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Verify email using token (for direct verification from link)
  const verifyEmailWithToken = useCallback(async (token: string, email: string) => {
    try {
      setStatus('loading');
      
      // Use EmailVerificationService to verify email and update database
      const { success, error } = await emailVerificationService.verifyEmail(token, email);

      if (!success) {
        logger.error('Email verification failed', 'EnhancedEmailVerificationHandler', { error });
        setErrorMessage(error || 'Email verification failed. Please try again.');
        setStatus('error');
      } else {
        setStatus('success');
        logger.info('Email verified successfully', 'EnhancedEmailVerificationHandler', { email });
        
        // Store that we've verified this email
        await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.EMAIL_VERIFIED, 'true');
      }
      
      setShowConfirmation(true);
    } catch (error) {
      logger.error('Unexpected error during email verification', 'EnhancedEmailVerificationHandler', { error });
      setErrorMessage('An unexpected error occurred. Please try again later.');
      setStatus('error');
      setShowConfirmation(true);
    }
  }, []);

  // Check if the user's email is verified from session
  const checkVerificationStatus = useCallback(async () => {
    try {
      // Get the pending verification email from storage
      const pendingEmail = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL);
      
      if (!pendingEmail) {
        return;
      }
      
      setEmail(pendingEmail);
      setStatus('loading');
      
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        logger.error('Error checking session', 'EnhancedEmailVerificationHandler', { error: sessionError });
        return;
      }
      
      // If we have a session and the email matches, the user is verified
      if (session?.user && session.user.email === pendingEmail && session.user.email_confirmed_at) {
        logger.info('Email verified successfully', 'EnhancedEmailVerificationHandler', { email: pendingEmail });
        
        // Clear the pending verification email
        await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.PENDING_VERIFICATION_EMAIL);
        await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.EMAIL_VERIFIED, 'true');
        
        setStatus('success');
        setShowConfirmation(true);
      }
    } catch (error) {
      logger.error('Error checking verification status', 'EnhancedEmailVerificationHandler', { error });
      setStatus('error');
      setErrorMessage('Failed to check verification status. Please try again later.');
    }
  }, []);

  // Handle retry for verification
  const handleRetry = useCallback(() => {
    if (autoVerify && token && (providedEmail || email)) {
      verifyEmailWithToken(token, providedEmail || email || '');
    } else {
      checkVerificationStatus();
    }
  }, [autoVerify, token, providedEmail, email, verifyEmailWithToken, checkVerificationStatus]);

  // Initial verification check
  useEffect(() => {
    if (autoVerify && token && providedEmail) {
      // If we have a token and email, verify directly
      setEmail(providedEmail);
      verifyEmailWithToken(token, providedEmail);
    } else {
      // Otherwise check if email is verified from session
      checkVerificationStatus();
      
      // Set up a refresh interval to periodically check status
      const intervalId = setInterval(checkVerificationStatus, 5000); // Check every 5 seconds
      
      return () => clearInterval(intervalId);
    }
  }, [autoVerify, token, providedEmail, verifyEmailWithToken, checkVerificationStatus]);

  // Handle dismissing the confirmation
  const handleDismiss = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    setShowConfirmation(false);
  };

  // If the confirmation is shown, render the confirmation
  if (showConfirmation) {
    return (
      <View style={styles.container}>
        <EnhancedVerificationConfirmation 
          email={email || undefined} 
          onDismiss={handleDismiss}
          status={status}
          errorMessage={errorMessage || undefined}
          onRetry={handleRetry}
          redirectPath={redirectTo}
        />
      </View>
    );
  }

  // Otherwise, render nothing
  return null;
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
});

export default EnhancedEmailVerificationHandler;
