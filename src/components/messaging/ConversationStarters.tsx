import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  ActivityIndicator 
} from 'react-native';
import { Lightbulb, ChevronRight, X } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { hapticFeedback } from '@utils/hapticFeedback';

interface ConversationStartersProps {
  profileData?: any;
  onSelect: (template: string) => void;
  onDismiss: () => void;
}

// Predefined conversation starters
const DEFAULT_STARTERS = [
  "Hi! I noticed we matched. I'm looking for a roommate in {{area}}. What areas are you considering?",
  "Hey there! I like your profile. What are you looking for in a potential roommate?",
  "Hello! I see we have similar living preferences. What's your timeline for moving?",
  "Hi! What kind of apartment/house setup are you looking for?",
  "Hey! I'm curious about your daily routine - are you a night owl or early bird?"
];

export default function ConversationStarters({ 
  profileData, 
  onSelect, 
  onDismiss 
}: ConversationStartersProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [starters, setStarters] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Generate personalized starters based on profile data
    const generateStarters = () => {
      setLoading(true);
      
      // Start with default templates
      let personalizedStarters = [...DEFAULT_STARTERS];
      
      // If we have profile data, add some personalized options
      if (profileData) {
        const area = profileData.location?.area || 'your preferred area';
        const interests = profileData.interests || [];
        const movingDate = profileData.movingDate || 'soon';
        
        // Personalize existing templates
        personalizedStarters = personalizedStarters.map(starter => {
          return starter.replace('{{area}}', area);
        });
        
        // Add interest-based starters if available
        if (interests.length > 0) {
          const interestStarter = `I see you're interested in ${interests[0]}! That's something I enjoy too. Would you want a roommate who shares that interest?`;
          personalizedStarters.push(interestStarter);
        }
        
        // Add move-in date starter
        if (movingDate) {
          personalizedStarters.push(`Hi! I see you're looking to move ${movingDate}. That works well with my timeline too!`);
        }
      }
      
      setStarters(personalizedStarters);
      setLoading(false);
    };
    
    generateStarters();
  }, [profileData]);

  const handleSelect = (starter: string) => {
    hapticFeedback.selection();
    onSelect(starter);
  };

  const handleDismiss = () => {
    hapticFeedback.light();
    onDismiss();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Lightbulb size={18} color={theme.colors.primary} />
          <Text style={styles.headerText}>Conversation Starters</Text>
          <TouchableOpacity onPress={handleDismiss} hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}>
            <X size={18} color={theme.colors.textMuted} />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Generating starters...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Lightbulb size={18} color={theme.colors.primary} />
        <Text style={styles.headerText}>Conversation Starters</Text>
        <TouchableOpacity 
          onPress={handleDismiss}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <X size={18} color={theme.colors.textMuted} />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {starters.map((starter, index) => (
          <TouchableOpacity 
            key={index} 
            style={styles.starterItem}
            onPress={() => handleSelect(starter)}
            activeOpacity={0.7}
          >
            <Text style={styles.starterText} numberOfLines={3}>
              {starter}
            </Text>
            <ChevronRight size={16} color={theme.colors.primary} />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surfaceVariant,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingBottom: theme.spacing.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  scrollContent: {
    paddingHorizontal: theme.spacing.sm,
    paddingBottom: theme.spacing.xs,
  },
  starterItem: {
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    marginHorizontal: theme.spacing.xs,
    width: 220,
    ...theme.shadows.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  starterText: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  loadingContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    fontSize: 14,
    color: theme.colors.textMuted,
    marginLeft: theme.spacing.sm,
  },
});
