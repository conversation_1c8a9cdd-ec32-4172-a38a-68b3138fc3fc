import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { MessageCircle, Clock, Users } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';

// Enhanced chat room interface based on our new database function
interface EnhancedChatRoom {
  room_id: string;
  created_by: string;
  created_at: string;
  last_message: string | null;
  last_message_at: string | null;
  participant_count: number;
  unread_count: number;
  other_participant_id: string | null;
  other_participant_name: string | null;
  other_participant_avatar: string | null;
}

interface EnhancedMessageListProps {
  onChatPress?: (roomId: string, participantName: string) => void;
}

export function EnhancedMessageList({ onChatPress }: EnhancedMessageListProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { authState } = useAuth();
  const router = useRouter();
  const user = authState?.user;

  // State management
  const [chatRooms, setChatRooms] = useState<EnhancedChatRoom[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch enhanced chat rooms using our new database function
  const fetchChatRooms = useCallback(async () => {
    if (!user?.id) return;

    try {
      setError(null);
      
      // Use our enhanced function that includes participant details and unread counts
      const { data, error: fetchError } = await supabase.rpc(
        'get_user_chat_rooms_enhanced',
        { user_id_param: user.id }
      );

      if (fetchError) throw fetchError;

      setChatRooms(data || []);
    } catch (err) {
      console.error('Error fetching enhanced chat rooms:', err);
      setError(err instanceof Error ? err.message : 'Failed to load messages');
    }
  }, [user?.id]);

  // Initial load
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      await fetchChatRooms();
      setLoading(false);
    };

    if (user?.id) {
      loadInitialData();
    }
  }, [user?.id, fetchChatRooms]);

  // Pull to refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchChatRooms();
    setRefreshing(false);
  }, [fetchChatRooms]);

  // Navigate to chat room
  const handleChatPress = useCallback((room: EnhancedChatRoom) => {
    if (onChatPress) {
      onChatPress(room.room_id, room.other_participant_name || 'Unknown User');
    } else {
      // Default navigation
      router.push({
        pathname: '/chat/[id]',
        params: {
          id: room.room_id,
          recipientName: room.other_participant_name || 'Unknown User',
          recipientId: room.other_participant_id || '',
        },
      });
    }
  }, [onChatPress, router]);

  // Mark messages as read when entering a chat
  const markMessagesAsRead = useCallback(async (roomId: string) => {
    if (!user?.id) return;

    try {
      await supabase.rpc('mark_messages_read', {
        room_id_param: roomId,
        user_id_param: user.id,
      });
      
      // Update local state to reflect read status
      setChatRooms(prev => 
        prev.map(room => 
          room.room_id === roomId 
            ? { ...room, unread_count: 0 }
            : room
        )
      );
    } catch (err) {
      console.error('Error marking messages as read:', err);
    }
  }, [user?.id]);

  // Format timestamp for display
  const formatMessageTime = useCallback((timestamp: string | null) => {
    if (!timestamp) return '';

    const messageDate = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      // Within 24 hours - show time
      return messageDate.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffInHours < 168) {
      // Within a week - show day
      return messageDate.toLocaleDateString([], { weekday: 'short' });
    } else {
      // Older - show date
      return messageDate.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  }, []);

  // Calculate total unread count
  const totalUnreadCount = useMemo(() => {
    return chatRooms.reduce((total, room) => total + room.unread_count, 0);
  }, [chatRooms]);

  // Render individual chat room item
  const renderChatRoom = useCallback(({ item }: { item: EnhancedChatRoom }) => {
    const hasUnread = item.unread_count > 0;
    const timeDisplay = formatMessageTime(item.last_message_at);

    return (
      <TouchableOpacity
        style={[styles.chatRoomItem, hasUnread && styles.unreadChatRoom]}
        onPress={() => {
          handleChatPress(item);
          if (hasUnread) {
            markMessagesAsRead(item.room_id);
          }
        }}
        activeOpacity={0.7}
      >
        {/* Avatar */}
        <View style={styles.avatarContainer}>
          {item.other_participant_avatar ? (
            <Image
              source={{ uri: item.other_participant_avatar }}
              style={styles.avatar}
              defaultSource={require('../../../assets/images/default-avatar.png')}
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {(item.other_participant_name || 'U')[0].toUpperCase()}
              </Text>
            </View>
          )}
          
          {/* Online status indicator (could be enhanced with real-time presence) */}
          <View style={styles.statusIndicator} />
        </View>

        {/* Chat Info */}
        <View style={styles.chatInfo}>
          <View style={styles.chatHeader}>
            <Text style={[styles.participantName, hasUnread && styles.unreadText]}>
              {item.other_participant_name || 'Unknown User'}
            </Text>
            
            <View style={styles.metaInfo}>
              {timeDisplay && (
                <Text style={[styles.timeText, hasUnread && styles.unreadTimeText]}>
                  {timeDisplay}
                </Text>
              )}
              
              {item.participant_count > 2 && (
                <View style={styles.groupIndicator}>
                  <Users size={12} color={theme.colors.textMuted} />
                  <Text style={styles.groupText}>{item.participant_count}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.messagePreview}>
            <Text
              style={[styles.lastMessage, hasUnread && styles.unreadMessage]}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {item.last_message || 'No messages yet'}
            </Text>

            {hasUnread && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>
                  {item.unread_count > 99 ? '99+' : item.unread_count}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [formatMessageTime, handleChatPress, markMessagesAsRead]);

  // Empty state component
  const EmptyState = () => (
    <View style={styles.emptyContainer}>
      <MessageCircle size={64} color={theme.colors.textMuted} />
      <Text style={styles.emptyTitle}>No conversations yet</Text>
      <Text style={styles.emptyDescription}>
        When you match with someone or respond to a room listing, your conversations will appear here.
      </Text>
    </View>
  );

  // Error state component
  const ErrorState = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorTitle}>Unable to load messages</Text>
      <Text style={styles.errorDescription}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
        <Text style={styles.retryText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  // Loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading conversations...</Text>
      </View>
    );
  }

  // Error state
  if (error && chatRooms.length === 0) {
    return <ErrorState />;
  }

  return (
    <View style={styles.container}>
      {/* Header with unread count */}
      {totalUnreadCount > 0 && (
        <View style={styles.unreadSummary}>
          <Text style={styles.unreadSummaryText}>
            {totalUnreadCount} unread message{totalUnreadCount !== 1 ? 's' : ''}
          </Text>
        </View>
      )}

      {/* Chat rooms list */}
      <FlatList
        data={chatRooms}
        keyExtractor={(item) => item.room_id}
        renderItem={renderChatRoom}
        ListEmptyComponent={EmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={[
          styles.listContent,
          chatRooms.length === 0 && styles.emptyListContent,
        ]}
        showsVerticalScrollIndicator={false}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews={true}
      />
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  unreadSummary: {
    backgroundColor: theme.colors.primarySurface,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  unreadSummaryText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  listContent: {
    flexGrow: 1,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
  },
  chatRoomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background,
  },
  unreadChatRoom: {
    backgroundColor: theme.colors.surfaceVariant,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.surfaceVariant,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.background,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.success,
    borderWidth: 2,
    borderColor: theme.colors.background,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  unreadText: {
    color: theme.colors.text,
    fontWeight: '700',
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeText: {
    fontSize: 12,
    color: theme.colors.textMuted,
  },
  unreadTimeText: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  groupIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  groupText: {
    fontSize: 11,
    color: theme.colors.textMuted,
  },
  messagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  lastMessage: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textMuted,
    lineHeight: 18,
  },
  unreadMessage: {
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    paddingHorizontal: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: 'bold',
    color: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.textMuted,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: theme.colors.background,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: theme.colors.textMuted,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    backgroundColor: theme.colors.background,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.error,
    marginBottom: 8,
  },
  errorDescription: {
    fontSize: 14,
    color: theme.colors.textMuted,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.background,
  },
});

export default EnhancedMessageList; 