/**
 * MessageListItem.tsx
 * List item component for displaying a chat room in the list
 */
import React from 'react';
import { StyleSheet, TouchableOpacity, View, Text, Image } from 'react-native';
import { formatDistanceToNow } from 'date-fns';
import { ChatRoom } from '@services/unified/types';
import { navigateToChat } from '@utils/navigationUtils';
import { Ionicons } from '@expo/vector-icons';
import Avatar from '@components/common/Avatar';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface MessageListItemProps {
  room: ChatRoom;
}

export const MessageListItem: React.FC<MessageListItemProps> = ({ room }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Navigate to chat room
  const handlePress = () => {
    // Debug the room.id to see what's being passed
    console.log('🔍 MessageListItem navigation:', {
      roomId: room.id,
      roomIdType: typeof room.id,
      isString: typeof room.id === 'string',
      isObject: typeof room.id === 'object',
      stringified: JSON.stringify(room.id),
      room: room,
      roomKeys: Object.keys(room),
    });

    // Ensure we're passing a string ID
    const roomIdString = typeof room.id === 'string' ? room.id : String(room.id);
    console.log('🔍 Navigation roomId string:', {
      roomIdString,
      type: typeof roomIdString,
    });

    // Get recipient info from participants
    const otherParticipant = room.participants?.[0];
    const recipientInfo = otherParticipant
      ? {
          id: otherParticipant.userId || otherParticipant.user_id || '',
          name: otherParticipant.display_name || 'User',
          avatar: otherParticipant.avatar_url,
        }
      : undefined;

    // Use standardized navigation
    navigateToChat(roomIdString, recipientInfo, {
      source: 'messages',
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string | undefined) => {
    if (!timestamp) return '';
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (e) {
      return '';
    }
  };

  // Get other user info
  const otherUser = room.participants[0] || {};
  const displayName = otherUser.display_name || 'User';
  const avatarUrl = otherUser.avatar_url || 'https://via.placeholder.com/50';

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.avatarContainer}>
        <Image source={{ uri: avatarUrl }} style={styles.avatar} />
        {room.unread_count > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {room.unread_count > 99 ? '99+' : room.unread_count}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.headerContainer}>
          <Text style={styles.name} numberOfLines={1}>
            {displayName}
          </Text>
          <Text style={styles.time}>{formatTimestamp(room.last_message_at)}</Text>
        </View>
        <Text style={styles.message} numberOfLines={1}>
          {room.last_message || 'No messages yet'}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: theme.spacing.sm,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  badge: {
    position: 'absolute',
    right: -5,
    top: -5,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.round,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: theme.colors.background,
    fontSize: 10,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.xs,
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  time: {
    fontSize: 12,
    color: theme.colors.textMuted,
  },
  message: {
    fontSize: 14,
    color: theme.colors.text,
  },
});

export default MessageListItem;
