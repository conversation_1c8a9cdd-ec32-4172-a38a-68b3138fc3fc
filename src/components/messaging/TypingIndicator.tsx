import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface TypingIndicatorProps {
  isTyping: boolean;
  userName?: string;
}

export default function TypingIndicator({ isTyping, userName = 'User' }: TypingIndicatorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Don't render if not typing
  if (!isTyping) return null;
  
  // Animation references for the dots
  const fadeAnim1 = useRef(new Animated.Value(0.3)).current;
  const fadeAnim2 = useRef(new Animated.Value(0.3)).current;
  const fadeAnim3 = useRef(new Animated.Value(0.3)).current;
  
  // Function to create the animation sequence
  const runAnimation = () => {
    // Reset values
    fadeAnim1.setValue(0.3);
    fadeAnim2.setValue(0.3);
    fadeAnim3.setValue(0.3);
    
    // Create animation sequence
    Animated.sequence([
      // First dot
      Animated.timing(fadeAnim1, {
        toValue: 1,
        duration: 400,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      // Second dot
      Animated.timing(fadeAnim2, {
        toValue: 1,
        duration: 400,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      // Third dot
      Animated.timing(fadeAnim3, {
        toValue: 1,
        duration: 400,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Restart animation when complete
      setTimeout(() => {
        if (isTyping) runAnimation();
      }, 300);
    });
  };
  
  // Start animation when component mounts or isTyping changes
  useEffect(() => {
    if (isTyping) {
      runAnimation();
    }
  }, [isTyping]);
  
  return (
    <View style={styles.container}>
      <Text style={styles.text}>{userName} is typing</Text>
      <View style={styles.dotsContainer}>
        <Animated.View 
          style={[
            styles.dot, 
            { opacity: fadeAnim1 }
          ]} 
        />
        <Animated.View 
          style={[
            styles.dot, 
            { opacity: fadeAnim2 }
          ]} 
        />
        <Animated.View 
          style={[
            styles.dot, 
            { opacity: fadeAnim3 }
          ]} 
        />
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    backgroundColor: theme.colors.surfaceVariant,
  },
  text: {
    fontSize: 12,
    color: theme.colors.textMuted,
    fontStyle: 'italic',
    marginRight: theme.spacing.xs,
  },
  dotsContainer: {
    flexDirection: 'row',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.textMuted,
    marginHorizontal: 2,
  },
});
