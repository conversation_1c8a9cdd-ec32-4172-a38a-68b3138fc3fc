import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from '@design-system/ThemeProvider';
import { SmartConversationIntelligence } from '@services/messaging/SmartConversationIntelligence';
import { AIMessageModeration } from '@services/messaging/AIMessageModeration';
import { ConversationOptimizer } from '@services/messaging/ConversationOptimizer';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';

// Types
interface Message {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  room_id: string;
  message_type?: string;
  metadata?: any;
}

interface AIInsight {
  type: 'suggestion' | 'warning' | 'optimization' | 'health';
  message: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  action?: string;
  timestamp: string;
}

interface ConversationMetrics {
  healthScore: number;
  engagementLevel: 'low' | 'medium' | 'high';
  riskLevel: 'safe' | 'caution' | 'warning' | 'critical';
  suggestions: string[];
  lastAnalysis: string;
}

interface AIEnhancedMessageListProps {
  messages: Message[];
  currentUserId: string;
  otherUserId: string;
  roomId: string;
  onSendMessage?: (content: string) => void;
  isTyping?: boolean;
  enableAIInsights?: boolean;
  enableRealTimeModeration?: boolean;
  enableOptimizationSuggestions?: boolean;
}

export const AIEnhancedMessageList: React.FC<AIEnhancedMessageListProps> = ({
  messages,
  currentUserId,
  otherUserId,
  roomId,
  onSendMessage,
  isTyping = false,
  enableAIInsights = true,
  enableRealTimeModeration = true,
  enableOptimizationSuggestions = true,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // State
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [conversationMetrics, setConversationMetrics] = useState<ConversationMetrics>({
    healthScore: 85,
    engagementLevel: 'medium',
    riskLevel: 'safe',
    suggestions: [],
    lastAnalysis: new Date().toISOString(),
  });
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showInsights, setShowInsights] = useState(false);
  const [insightAnimation] = useState(new Animated.Value(0));

  // Services
  const conversationIntelligence = useMemo(() => SmartConversationIntelligence.getInstance(), []);
  const messageModeration = useMemo(() => AIMessageModeration.getInstance(), []);
  const conversationOptimizer = useMemo(() => ConversationOptimizer.getInstance(), []);

  // Real-time AI Analysis
  const analyzeConversation = useCallback(async () => {
    if (!enableAIInsights || messages.length === 0) return;

    setIsAnalyzing(true);
    try {
      // Parallel AI analysis
      const [intelligenceData, moderationResults, optimizationData] = await Promise.all([
        conversationIntelligence.analyzeConversation(roomId, currentUserId, otherUserId),
        enableRealTimeModeration ? 
          messageModeration.analyzeConversationSafety(roomId, currentUserId, otherUserId) : 
          Promise.resolve(null),
        enableOptimizationSuggestions ? 
          conversationOptimizer.optimizeConversation(roomId, currentUserId, otherUserId) : 
          Promise.resolve(null),
      ]);

      // Process insights
      const newInsights: AIInsight[] = [];

      // Conversation Intelligence Insights
      if (intelligenceData.smartSuggestions) {
        intelligenceData.smartSuggestions.forEach(suggestion => {
          newInsights.push({
            type: 'suggestion',
            message: suggestion.content,
            priority: suggestion.priority as 'low' | 'medium' | 'high',
            action: suggestion.action,
            timestamp: new Date().toISOString(),
          });
        });
      }

      // Safety Moderation Insights
      if (moderationResults && moderationResults.riskLevel !== 'low') {
        newInsights.push({
          type: 'warning',
          message: `Safety Alert: ${moderationResults.detectedIssues.join(', ')}`,
          priority: moderationResults.riskLevel === 'critical' ? 'critical' : 'high',
          action: 'review_conversation',
          timestamp: new Date().toISOString(),
        });
      }

      // Optimization Insights
      if (optimizationData && optimizationData.recommendations) {
        optimizationData.recommendations.forEach(rec => {
          newInsights.push({
            type: 'optimization',
            message: rec.suggestion,
            priority: rec.priority as 'low' | 'medium' | 'high',
            action: rec.action,
            timestamp: new Date().toISOString(),
          });
        });
      }

      // Update metrics
      setConversationMetrics({
        healthScore: intelligenceData.conversationHealth?.overallScore || 85,
        engagementLevel: optimizationData?.engagementLevel || 'medium',
        riskLevel: moderationResults?.riskLevel === 'low' ? 'safe' : 
                  moderationResults?.riskLevel === 'medium' ? 'caution' :
                  moderationResults?.riskLevel === 'high' ? 'warning' : 'critical',
        suggestions: newInsights.map(insight => insight.message),
        lastAnalysis: new Date().toISOString(),
      });

      setAiInsights(newInsights);

      // Show insights if there are important ones
      const hasImportantInsights = newInsights.some(insight => 
        insight.priority === 'high' || insight.priority === 'critical'
      );
      
      if (hasImportantInsights && !showInsights) {
        setShowInsights(true);
        Animated.spring(insightAnimation, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
      }

    } catch (error) {
      console.error('AI conversation analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [
    messages, 
    roomId, 
    currentUserId, 
    otherUserId, 
    enableAIInsights, 
    enableRealTimeModeration, 
    enableOptimizationSuggestions,
    conversationIntelligence,
    messageModeration,
    conversationOptimizer,
    showInsights,
    insightAnimation
  ]);

  // Trigger analysis when messages change
  useEffect(() => {
    const timeoutId = setTimeout(analyzeConversation, 2000); // Debounce analysis
    return () => clearTimeout(timeoutId);
  }, [messages.length, analyzeConversation]);

  // Handle insight actions
  const handleInsightAction = useCallback((insight: AIInsight) => {
    switch (insight.action) {
      case 'suggest_response':
        // Could integrate with message composer
        Alert.alert('AI Suggestion', insight.message);
        break;
      case 'review_conversation':
        Alert.alert('Safety Review', insight.message);
        break;
      case 'improve_engagement':
        Alert.alert('Engagement Tip', insight.message);
        break;
      default:
        Alert.alert('AI Insight', insight.message);
    }
  }, []);

  // Toggle insights panel
  const toggleInsights = useCallback(() => {
    setShowInsights(!showInsights);
    Animated.spring(insightAnimation, {
      toValue: showInsights ? 0 : 1,
      useNativeDriver: true,
    }).start();
  }, [showInsights, insightAnimation]);

  // Render message with AI enhancements
  const renderMessage = useCallback(({ item: message, index }: { item: Message; index: number }) => {
    const isOwnMessage = message.sender_id === currentUserId;
    
    return (
      <View style={styles.messageContainer}>
        <MessageBubble
          message={message}
          isOwnMessage={isOwnMessage}
          showTimestamp={true}
        />
        
        {/* AI Enhancement Indicators */}
        {enableAIInsights && (
          <View style={styles.aiIndicators}>
            {/* Safety indicator */}
            {conversationMetrics.riskLevel !== 'safe' && (
              <View style={[styles.riskIndicator, styles[`risk${conversationMetrics.riskLevel}`]]}>
                <Text style={styles.riskText}>
                  {conversationMetrics.riskLevel === 'critical' ? '⚠️' : 
                   conversationMetrics.riskLevel === 'warning' ? '⚡' : '⚪'}
                </Text>
              </View>
            )}
            
            {/* Engagement indicator */}
            {conversationMetrics.engagementLevel === 'high' && (
              <View style={styles.engagementIndicator}>
                <Text style={styles.engagementText}>🔥</Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  }, [currentUserId, conversationMetrics, enableAIInsights, styles]);

  // Render AI insights panel
  const renderInsightsPanel = () => {
    if (!showInsights || aiInsights.length === 0) return null;

    return (
      <Animated.View 
        style={[
          styles.insightsPanel,
          {
            transform: [{
              translateY: insightAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [100, 0],
              }),
            }],
            opacity: insightAnimation,
          }
        ]}
      >
        <View style={styles.insightsHeader}>
          <Text style={styles.insightsTitle}>AI Insights</Text>
          <TouchableOpacity onPress={toggleInsights} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.metricsRow}>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Health</Text>
            <Text style={[styles.metricValue, { color: getHealthColor(conversationMetrics.healthScore) }]}>
              {conversationMetrics.healthScore}%
            </Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Engagement</Text>
            <Text style={[styles.metricValue, { color: getEngagementColor(conversationMetrics.engagementLevel) }]}>
              {conversationMetrics.engagementLevel}
            </Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Safety</Text>
            <Text style={[styles.metricValue, { color: getRiskColor(conversationMetrics.riskLevel) }]}>
              {conversationMetrics.riskLevel}
            </Text>
          </View>
        </View>

        <FlatList
          data={aiInsights.slice(0, 3)} // Show top 3 insights
          keyExtractor={(item, index) => `insight-${index}`}
          renderItem={({ item: insight }) => (
            <TouchableOpacity 
              style={[styles.insightItem, styles[`priority${insight.priority}`]]}
              onPress={() => handleInsightAction(insight)}
            >
              <View style={styles.insightContent}>
                <Text style={styles.insightType}>
                  {insight.type === 'suggestion' ? '💡' : 
                   insight.type === 'warning' ? '⚠️' : 
                   insight.type === 'optimization' ? '⚡' : '📊'}
                </Text>
                <Text style={styles.insightMessage} numberOfLines={2}>
                  {insight.message}
                </Text>
              </View>
            </TouchableOpacity>
          )}
          style={styles.insightsList}
        />
      </Animated.View>
    );
  };

  // Helper functions for colors
  const getHealthColor = (score: number) => {
    if (score >= 80) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  };

  const getEngagementColor = (level: string) => {
    switch (level) {
      case 'high': return theme.colors.success;
      case 'medium': return theme.colors.warning;
      default: return theme.colors.error;
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'safe': return theme.colors.success;
      case 'caution': return theme.colors.warning;
      case 'warning': return theme.colors.error;
      default: return theme.colors.error;
    }
  };

  return (
    <View style={styles.container}>
      {/* AI Status Bar */}
      {enableAIInsights && (
        <TouchableOpacity style={styles.aiStatusBar} onPress={toggleInsights}>
          <View style={styles.aiStatusContent}>
            <Text style={styles.aiStatusText}>
              AI: {isAnalyzing ? 'Analyzing...' : 'Active'}
            </Text>
            <View style={styles.aiStatusIndicators}>
              <View style={[styles.statusDot, { backgroundColor: getHealthColor(conversationMetrics.healthScore) }]} />
              <View style={[styles.statusDot, { backgroundColor: getEngagementColor(conversationMetrics.engagementLevel) }]} />
              <View style={[styles.statusDot, { backgroundColor: getRiskColor(conversationMetrics.riskLevel) }]} />
            </View>
            {aiInsights.length > 0 && (
              <View style={styles.insightsBadge}>
                <Text style={styles.insightsBadgeText}>{aiInsights.length}</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      )}

      {/* Messages List */}
      <FlatList
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderMessage}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        inverted
      />

      {/* Typing Indicator */}
      {isTyping && (
        <View style={styles.typingContainer}>
          <TypingIndicator />
        </View>
      )}

      {/* AI Insights Panel */}
      {renderInsightsPanel()}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  aiStatusBar: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  aiStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  aiStatusText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  aiStatusIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  insightsBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.xs,
  },
  insightsBadgeText: {
    color: theme.colors.background,
    fontSize: 10,
    fontWeight: 'bold',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  messageContainer: {
    marginVertical: theme.spacing.xs,
  },
  aiIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
    gap: theme.spacing.xs,
  },
  riskIndicator: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  riskcaution: {
    backgroundColor: theme.colors.warning + '20',
  },
  riskwarning: {
    backgroundColor: theme.colors.error + '20',
  },
  riskcritical: {
    backgroundColor: theme.colors.error + '40',
  },
  riskText: {
    fontSize: 10,
  },
  engagementIndicator: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
    backgroundColor: theme.colors.success + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  engagementText: {
    fontSize: 10,
  },
  typingContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  insightsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: theme.borderRadius.lg,
    borderTopRightRadius: theme.borderRadius.lg,
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: theme.colors.border,
    maxHeight: Dimensions.get('window').height * 0.4,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  insightsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  insightsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    backgroundColor: theme.colors.border,
  },
  closeButtonText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    fontWeight: 'bold',
  },
  metricsRow: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    justifyContent: 'space-around',
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  insightsList: {
    maxHeight: 120,
  },
  insightItem: {
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderLeftWidth: 3,
  },
  prioritylow: {
    backgroundColor: theme.colors.surface,
    borderLeftColor: theme.colors.textSecondary,
  },
  prioritymedium: {
    backgroundColor: theme.colors.warning + '10',
    borderLeftColor: theme.colors.warning,
  },
  priorityhigh: {
    backgroundColor: theme.colors.error + '10',
    borderLeftColor: theme.colors.error,
  },
  prioritycritical: {
    backgroundColor: theme.colors.error + '20',
    borderLeftColor: theme.colors.error,
  },
  insightContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  insightType: {
    fontSize: 16,
  },
  insightMessage: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 18,
  },
}); 