/**
 * MessageList.tsx
 * Component for displaying a list of messages in a chat
 */
import React, { useRef, useEffect } from 'react';
import { StyleSheet, View, FlatList, ActivityIndicator, Text } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import MessageBubble from '@components/messaging/MessageBubble';
import { Message } from '@services/unified/types';

interface MessageListProps {
  messages: Message[];
  userId: string;
  loading: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
  isOffline?: boolean; // Add prop to receive offline state from parent
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  userId,
  loading,
  onRefresh,
  refreshing = false,
  isOffline = false, // Initialize default value
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const flatListRef = useRef<FlatList>(null);

  // Scroll to bottom when new messages come in
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages.length]);

  // Handle rendering each message
  const renderItem = ({ item }: { item: Message }) => (
    <MessageBubble message={item} isOwnMessage={item.sender_id === userId} />
  );

  // Extract key from message
  const keyExtractor = (item: Message) => item.id;

  // Empty component when no messages
  const EmptyComponent = () => {
    // Use the parent-provided offline state first
    // Fall back to local detection for backward compatibility
    const [localOfflineMode, setLocalOfflineMode] = React.useState(false);

    useEffect(() => {
      if (loading && !refreshing && !isOffline) {
        const timer = setTimeout(() => {
          if (loading) {
            setLocalOfflineMode(true);
          }
        }, 5000);

        return () => clearTimeout(timer);
      } else if (!loading) {
        setLocalOfflineMode(false);
      }
    }, [loading, refreshing, isOffline]);

    // Use passed prop first, fallback to local detection
    const effectiveOfflineMode = isOffline || localOfflineMode;

    return (
      <View style={styles.emptyContainer}>
        {loading && !effectiveOfflineMode ? (
          <ActivityIndicator size="large" color={theme.colors.primary} />
        ) : effectiveOfflineMode ? (
          <View style={styles.offlineContainer}>
            <Text style={styles.offlineTitle}>Offline Mode</Text>
            <Text style={styles.emptyText}>
              You can start a conversation, but messages will be sent when you're back online.
            </Text>
          </View>
        ) : (
          <Text style={styles.emptyText}>No messages yet. Start the conversation!</Text>
        )}
      </View>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      data={messages}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListEmptyComponent={EmptyComponent}
      onRefresh={onRefresh}
      refreshing={refreshing}
      inverted={false} // Set to true to have newest messages at the bottom
      initialNumToRender={15}
      maxToRenderPerBatch={10}
    />
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    flexGrow: 1,
    paddingVertical: theme.spacing.sm,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    color: theme.colors.textMuted,
    fontSize: 16,
    textAlign: 'center',
  },
  offlineContainer: {
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  offlineTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
});

export default MessageList;
