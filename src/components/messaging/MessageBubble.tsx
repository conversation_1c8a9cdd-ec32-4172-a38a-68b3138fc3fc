/**
 * MessageBubble.tsx
 * Component for rendering individual message bubbles in a chat
 */
import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image } from 'react-native';
import { format } from 'date-fns';
import { <PERSON><PERSON><PERSON>, Feather } from '@expo/vector-icons';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Avatar } from '@components/common/Avatar';
import { Message } from '@services/unified/types';
import { useAuthCompat } from '@hooks/useAuthCompat';

interface MessageBubbleProps {
  message: Message;
  isOwnMessage: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isOwnMessage }) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Format timestamp to display time
  const formatTime = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'h:mm a');
    } catch (e) {
      return '';
    }
  };

  return (
    <View
      style={[
        styles.container,
        isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer,
      ]}
    >
      <View
        style={[
          styles.bubble,
          isOwnMessage ? styles.ownBubble : styles.otherBubble,
          message.is_offline && styles.offlineBubble,
        ]}
      >
        <Text
          style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
          ]}
        >
          {message.content}
        </Text>
        <View style={styles.statusContainer}>
          {message.is_offline && (
            <View style={styles.offlineIndicator}>
              <Ionicons
                name="time-outline"
                size={10}
                color={isOwnMessage ? theme.colors.white + '70' : theme.colors.textMuted}
              />
              <Text
                style={[
                  styles.offlineText,
                  isOwnMessage ? styles.ownOfflineText : styles.otherOfflineText,
                ]}
              >
                Sending when online
              </Text>
            </View>
          )}
          <Text
            style={[styles.timestamp, isOwnMessage ? styles.ownTimestamp : styles.otherTimestamp]}
          >
            {formatTime(message.timestamp)}
          </Text>
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    paddingHorizontal: theme.spacing.sm,
    marginVertical: theme.spacing.xs,
    flexDirection: 'row',
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.sm,
    paddingBottom: 24, // Space for timestamp at bottom
  },
  ownBubble: {
    backgroundColor: theme.colors.primary,
  },
  otherBubble: {
    backgroundColor: theme.colors.surfaceVariant,
  },
  offlineBubble: {
    opacity: 0.8,
    borderWidth: 1,
    borderColor: theme.colors.warning + '30', // Subtle orange border
  },
  messageText: {
    fontSize: 16,
  },
  ownMessageText: {
    color: theme.colors.white,
  },
  otherMessageText: {
    color: theme.colors.text,
  },
  statusContainer: {
    position: 'absolute',
    bottom: 6,
    right: theme.spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 4,
  },
  offlineText: {
    fontSize: 8,
    marginLeft: 2,
  },
  ownOfflineText: {
    color: theme.colors.white + '70',
  },
  otherOfflineText: {
    color: theme.colors.textMuted,
  },
  timestamp: {
    fontSize: 10,
  },
  ownTimestamp: {
    color: theme.colors.white + '70',
  },
  otherTimestamp: {
    color: theme.colors.textMuted,
  },
});

export default MessageBubble;
