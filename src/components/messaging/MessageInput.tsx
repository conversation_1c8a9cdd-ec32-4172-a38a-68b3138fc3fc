/**
 * MessageInput.tsx
 * Input component for sending messages in a chat
 */
import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Text,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface MessageInputProps {
  onSend: (message: string) => Promise<void>;
  disabled?: boolean;
  isOffline?: boolean;
  onTyping?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSend,
  disabled = false,
  isOffline = false,
  onTyping,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  const handleSend = async () => {
    if (!message.trim() || sending || disabled) return;

    const messageToSend = message.trim();
    setMessage('');
    setSending(true);

    try {
      await onSend(messageToSend);
    } catch (error) {
      console.error('Error sending message:', error);
      // Could restore the message text here if send fails
      // setMessage(messageToSend);
    } finally {
      setSending(false);
    }
  };

  return (
    <View style={styles.inputWrapper}>
      {isOffline && (
        <View style={styles.offlineIndicator}>
          <Ionicons name="cloud-offline" size={16} color={theme.colors.white} />
          <Text style={styles.offlineText}>
            Offline - Messages will be sent when connection is restored
          </Text>
        </View>
      )}
      <View style={styles.container}>
        <TextInput
          style={[styles.input, isOffline && styles.offlineInput]}
          placeholder={isOffline ? 'Message (offline mode)' : 'Type a message...'}
          placeholderTextColor={theme.colors.textMuted}
          value={message}
          onChangeText={(text) => {
            setMessage(text);
            // Trigger typing event when user types
            if (onTyping && text.trim() !== '') {
              onTyping();
            }
          }}
          multiline
          maxLength={1000}
          autoCapitalize="sentences"
          editable={!disabled}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!message.trim() || sending || disabled) && styles.disabledButton,
            isOffline && styles.offlineSendButton,
          ]}
          onPress={handleSend}
          disabled={!message.trim() || sending || disabled}
        >
          {sending ? (
            <ActivityIndicator size="small" color={theme.colors.white} />
          ) : isOffline ? (
            <Ionicons name="timer-outline" size={20} color={theme.colors.white} />
          ) : (
            <Ionicons name="send" size={20} color={theme.colors.white} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  inputWrapper: {
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  offlineIndicator: {
    backgroundColor: theme.colors.warning,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
  },
  offlineText: {
    color: theme.colors.white,
    fontSize: 12,
    marginLeft: 4,
  },
  container: {
    flexDirection: 'row',
    padding: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.pill,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    maxHeight: 100,
    fontSize: 16,
    color: theme.colors.text,
  },
  offlineInput: {
    backgroundColor: theme.colors.surfaceVariant,
    borderWidth: 1,
    borderColor: theme.colors.warning,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.pill,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: theme.spacing.sm,
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
  },
  offlineSendButton: {
    backgroundColor: theme.colors.warning,
  },
});

export default MessageInput;
