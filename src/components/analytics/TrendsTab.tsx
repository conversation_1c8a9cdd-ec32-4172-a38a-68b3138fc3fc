import React from 'react';

import { View, Text, StyleSheet } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

import SentimentTrendChart from '@components/chat/SentimentTrendChart';
import type { SentimentPeriod } from '@hooks/useSentimentAnalytics';
import type { SentimentComparison, UserSentimentMetrics } from '@services/sentimentTrendService';

import PeriodSelector from '@components/analytics/PeriodSelector';

interface TrendsTabProps {
  comparisons: SentimentComparison[] | null;
  userMetrics: UserSentimentMetrics | null;
  selectedPeriod: SentimentPeriod;
  onSelectPeriod: (period: SentimentPeriod) => void;
  getSentimentDescription: (score: number) => string;
}

export default function TrendsTab({
  comparisons,
  userMetrics,
  selectedPeriod,
  onSelectPeriod,
  getSentimentDescription,
}: TrendsTabProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <View style={styles.tabContent}>
      <PeriodSelector selectedPeriod={selectedPeriod} onSelectPeriod={onSelectPeriod} />

      {comparisons && comparisons.length > 0 ? (
        <SentimentTrendChart
          trends={comparisons.filter(c => c.period_type === selectedPeriod)}
          title="Overall Sentiment Trend"
          height={250}
        />
      ) : (
        <View style={styles.chartPlaceholder}>
          <Text style={styles.noDataText}>No trend data available</Text>
        </View>
      )}

      <Text style={styles.sectionTitle}>Conversation Health</Text>
      <View style={styles.conversationHealthCard}>
        {userMetrics ? (
          <Text style={styles.conversationDescription}>
            Based on your messaging patterns, your conversations are generally{' '}
            {getSentimentDescription(userMetrics.average_sentiment_score)}
          </Text>
        ) : (
          <Text style={styles.noDataText}>Not enough conversation data to analyze health</Text>
        )}
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  tabContent: {
    flex: 1,
  },
  chartPlaceholder: {
    height: 250,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  noDataText: {
    color: theme.colors.textMuted,
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.xs,
  },
  conversationHealthCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  conversationDescription: {
    fontSize: 15,
    lineHeight: 22,
    color: theme.colors.textSecondary,
  },
});
