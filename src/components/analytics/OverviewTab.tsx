import React from 'react';

import { BarChart2, TrendingUp, Zap, Clock } from 'lucide-react-native';
import { View, Text, StyleSheet } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

import type { UserSentimentMetrics } from '@services/sentimentTrendService';

interface OverviewTabProps {
  userMetrics: UserSentimentMetrics | null;
  formatResponseTime: (minutes: number) => string;
  hasVolatileSentiment: () => boolean;
}

export default function OverviewTab({
  userMetrics,
  formatResponseTime,
  hasVolatileSentiment,
}: OverviewTabProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  if (!userMetrics) {
    return (
      <View style={styles.noDataContainer}>
        <Text style={styles.noDataText}>No sentiment data available yet</Text>
        <Text style={styles.noDataDescription}>
          Sentiment metrics will appear after you have exchanged messages with other users
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.tabContent}>
      <View style={styles.metricCard}>
        <View style={styles.metricHeader}>
          <BarChart2 size={20} color={theme.colors.primary} />
          <Text style={styles.metricTitle}>Sentiment Distribution</Text>
        </View>
        <View style={styles.distributionContainer}>
          <View style={[styles.distributionBar, { flexDirection: 'row' }]}>
            <View
              style={[
                styles.distributionSegment,
                {
                  backgroundColor: theme.colors.success,
                  flex: userMetrics.positive_conversation_ratio,
                },
              ]}
            />
            <View
              style={[
                styles.distributionSegment,
                {
                  backgroundColor: theme.colors.textMuted,
                  flex: userMetrics.neutral_conversation_ratio,
                },
              ]}
            />
            <View
              style={[
                styles.distributionSegment,
                {
                  backgroundColor: theme.colors.error,
                  flex: userMetrics.negative_conversation_ratio,
                },
              ]}
            />
          </View>
          <View style={styles.distributionLabels}>
            <View style={styles.labelItem}>
              <View style={[styles.labelDot, { backgroundColor: theme.colors.success }]} />
              <Text style={styles.labelText}>
                Positive ({Math.round(userMetrics.positive_conversation_ratio * 100)}%)
              </Text>
            </View>
            <View style={styles.labelItem}>
              <View style={[styles.labelDot, { backgroundColor: theme.colors.textMuted }]} />
              <Text style={styles.labelText}>
                Neutral ({Math.round(userMetrics.neutral_conversation_ratio * 100)}%)
              </Text>
            </View>
            <View style={styles.labelItem}>
              <View style={[styles.labelDot, { backgroundColor: theme.colors.error }]} />
              <Text style={styles.labelText}>
                Negative ({Math.round(userMetrics.negative_conversation_ratio * 100)}%)
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.metricCard}>
        <View style={styles.metricHeader}>
          <Zap size={20} color={theme.colors.primary} />
          <Text style={styles.metricTitle}>Engagement Score</Text>
        </View>
        <View style={styles.scoreContainer}>
          <View style={styles.scoreBadge}>
            <Text style={styles.scoreValue}>{userMetrics.engagement_score}</Text>
          </View>
          <Text style={styles.scoreDescription}>
            {userMetrics.engagement_score >= 80
              ? 'Highly engaged in conversations'
              : userMetrics.engagement_score >= 60
                ? 'Good engagement levels'
                : userMetrics.engagement_score >= 40
                  ? 'Moderate engagement'
                  : 'Low engagement in conversations'}
          </Text>
        </View>
      </View>

      {userMetrics.response_time_avg && (
        <View style={styles.metricCard}>
          <View style={styles.metricHeader}>
            <Clock size={20} color={theme.colors.primary} />
            <Text style={styles.metricTitle}>Response Time</Text>
          </View>
          <View style={styles.responseTimeContainer}>
            <Text style={styles.responseTimeValue}>
              {formatResponseTime(userMetrics.response_time_avg)}
            </Text>
            <Text style={styles.responseTimeLabel}>Average response time</Text>
          </View>
        </View>
      )}

      <View style={styles.metricCard}>
        <View style={styles.metricHeader}>
          <TrendingUp size={20} color={theme.colors.primary} />
          <Text style={styles.metricTitle}>Sentiment Volatility</Text>
        </View>
        <View style={styles.volatilityContainer}>
          <Text style={styles.volatilityDescription}>
            {hasVolatileSentiment()
              ? 'Your sentiment varies significantly across conversations'
              : 'Your sentiment is relatively stable across conversations'}
          </Text>
          <View style={styles.volatilityBar}>
            <View
              style={[
                styles.volatilityFill,
                {
                  width: `${Math.min(100, userMetrics.sentiment_volatility * 100)}%`,
                  backgroundColor: hasVolatileSentiment() ? theme.colors.warning : theme.colors.success,
                },
              ]}
            />
          </View>
          <View style={styles.volatilityLabels}>
            <Text style={styles.volatilityLabel}>Stable</Text>
            <Text style={styles.volatilityLabel}>Volatile</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  tabContent: {
    flex: 1,
  },
  metricCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  metricTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  distributionContainer: {
    marginTop: theme.spacing.xs,
  },
  distributionBar: {
    height: 20,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: theme.spacing.sm,
  },
  distributionSegment: {
    height: '100%',
  },
  distributionLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  labelItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  labelText: {
    fontSize: 12,
    color: theme.colors.textMuted,
  },
  scoreContainer: {
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  scoreBadge: {
    backgroundColor: theme.colors.primary,
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  scoreValue: {
    color: theme.colors.surface,
    fontSize: 24,
    fontWeight: '700',
  },
  scoreDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  responseTimeContainer: {
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  responseTimeValue: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  responseTimeLabel: {
    fontSize: 14,
    color: theme.colors.textMuted,
  },
  volatilityContainer: {
    marginTop: theme.spacing.xs,
  },
  volatilityDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  volatilityBar: {
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: theme.spacing.xs,
  },
  volatilityFill: {
    height: '100%',
    borderRadius: 4,
  },
  volatilityLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  volatilityLabel: {
    fontSize: 12,
    color: theme.colors.textMuted,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  noDataDescription: {
    fontSize: 14,
    color: theme.colors.textMuted,
    textAlign: 'center',
  },
});
