import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { 
  Circle, 
  Path, 
  Defs, 
  LinearGradient as SvgLinearGradient, 
  Stop,
  Ellipse,
  Rect,
  Polygon,
  Line
} from 'react-native-svg';

const { width, height } = Dimensions.get('window');

const MatchingSlide: React.FC = () => {
  // Animation values
  const pulseAnim1 = useRef(new Animated.Value(0.8)).current;
  const pulseAnim2 = useRef(new Animated.Value(0.8)).current;
  const connectAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Pulse animations for people
    const createPulseAnimation = (animValue: Animated.Value) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1.1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0.9,
            duration: 1500,
            useNativeDriver: true,
          }),
        ])
      );
    };

    // Connection line animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(connectAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(connectAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Sparkle animation
    Animated.loop(
      Animated.timing(sparkleAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      })
    ).start();

    // Background rotation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 25000,
        useNativeDriver: true,
      })
    ).start();

    createPulseAnimation(pulseAnim1).start();
    createPulseAnimation(pulseAnim2).start();
  }, []);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const sparkleOpacity = sparkleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 1, 0.3],
  });

  const connectionOpacity = connectAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.2, 1, 0.2],
  });

  return (
    <View style={styles.container}>
      {/* Background Organic Shapes */}
      <Animated.View 
        style={[
          styles.backgroundShapes,
          {
            transform: [{ scale: scaleAnim }, { rotate }],
          },
        ]}
      >
        <Svg width={width} height={height * 0.6} viewBox="0 0 400 400">
          <Defs>
            <SvgLinearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#fef3c7" stopOpacity="0.6" />
              <Stop offset="100%" stopColor="#fed7aa" stopOpacity="0.3" />
            </SvgLinearGradient>
            <SvgLinearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ddd6fe" stopOpacity="0.4" />
              <Stop offset="100%" stopColor="#c7d2fe" stopOpacity="0.2" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Organic AI brain shapes */}
          <Path
            d="M80,180 Q180,80 280,180 Q380,280 280,380 Q180,480 80,380 Q-20,280 80,180 Z"
            fill="url(#bgGradient1)"
          />
          <Path
            d="M120,220 Q220,120 320,220 Q420,320 320,420 Q220,520 120,420 Q20,320 120,220 Z"
            fill="url(#bgGradient2)"
          />
        </Svg>
      </Animated.View>

      {/* Main AI Matching Graphics */}
      <Animated.View 
        style={[
          styles.mainGraphics,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Svg width={300} height={300} viewBox="0 0 300 300">
          <Defs>
            <SvgLinearGradient id="person1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#6366f1" stopOpacity="1" />
              <Stop offset="100%" stopColor="#4f46e5" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="person2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ec4899" stopOpacity="1" />
              <Stop offset="100%" stopColor="#db2777" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#10b981" stopOpacity="1" />
              <Stop offset="100%" stopColor="#059669" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#f59e0b" stopOpacity="1" />
              <Stop offset="100%" stopColor="#d97706" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>

          {/* Connection Lines */}
          <Line
            x1="80"
            y1="150"
            x2="220"
            y2="150"
            stroke="url(#connectionGradient)"
            strokeWidth="4"
            strokeDasharray="10,5"
            opacity={connectionOpacity}
          />
          <Line
            x1="100"
            y1="120"
            x2="200"
            y2="180"
            stroke="url(#connectionGradient)"
            strokeWidth="3"
            strokeDasharray="8,4"
            opacity={connectionOpacity}
          />
          <Line
            x1="100"
            y1="180"
            x2="200"
            y2="120"
            stroke="url(#connectionGradient)"
            strokeWidth="3"
            strokeDasharray="8,4"
            opacity={connectionOpacity}
          />

          {/* AI Brain in Center */}
          <Circle
            cx="150"
            cy="150"
            r="25"
            fill="url(#aiGradient)"
          />
          
          {/* AI Neural Network Pattern */}
          <Circle cx="140" cy="140" r="3" fill="#ffffff" opacity="0.9" />
          <Circle cx="160" cy="140" r="3" fill="#ffffff" opacity="0.9" />
          <Circle cx="150" cy="160" r="3" fill="#ffffff" opacity="0.9" />
          <Circle cx="135" cy="155" r="2" fill="#ffffff" opacity="0.7" />
          <Circle cx="165" cy="155" r="2" fill="#ffffff" opacity="0.7" />
          
          {/* Neural connections */}
          <Line x1="140" y1="140" x2="160" y2="140" stroke="#ffffff" strokeWidth="1" opacity="0.6" />
          <Line x1="140" y1="140" x2="150" y2="160" stroke="#ffffff" strokeWidth="1" opacity="0.6" />
          <Line x1="160" y1="140" x2="150" y2="160" stroke="#ffffff" strokeWidth="1" opacity="0.6" />
          <Line x1="135" y1="155" x2="165" y2="155" stroke="#ffffff" strokeWidth="1" opacity="0.6" />
        </Svg>
      </Animated.View>

      {/* Person 1 (Left) */}
      <Animated.View 
        style={[
          styles.person1,
          {
            transform: [{ scale: pulseAnim1 }],
          },
        ]}
      >
        <Svg width={80} height={80} viewBox="0 0 80 80">
          <Defs>
            <SvgLinearGradient id="person1Fill" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#6366f1" stopOpacity="1" />
              <Stop offset="100%" stopColor="#4f46e5" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Person body */}
          <Circle cx="40" cy="25" r="12" fill="url(#person1Fill)" />
          <Ellipse cx="40" cy="55" rx="18" ry="20" fill="url(#person1Fill)" />
          
          {/* Person features */}
          <Circle cx="36" cy="22" r="2" fill="#ffffff" />
          <Circle cx="44" cy="22" r="2" fill="#ffffff" />
          <Path d="M35,28 Q40,32 45,28" stroke="#ffffff" strokeWidth="2" fill="none" />
        </Svg>
      </Animated.View>

      {/* Person 2 (Right) */}
      <Animated.View 
        style={[
          styles.person2,
          {
            transform: [{ scale: pulseAnim2 }],
          },
        ]}
      >
        <Svg width={80} height={80} viewBox="0 0 80 80">
          <Defs>
            <SvgLinearGradient id="person2Fill" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ec4899" stopOpacity="1" />
              <Stop offset="100%" stopColor="#db2777" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Person body */}
          <Circle cx="40" cy="25" r="12" fill="url(#person2Fill)" />
          <Ellipse cx="40" cy="55" rx="18" ry="20" fill="url(#person2Fill)" />
          
          {/* Person features */}
          <Circle cx="36" cy="22" r="2" fill="#ffffff" />
          <Circle cx="44" cy="22" r="2" fill="#ffffff" />
          <Path d="M35,28 Q40,32 45,28" stroke="#ffffff" strokeWidth="2" fill="none" />
        </Svg>
      </Animated.View>

      {/* Floating AI Elements */}
      <Animated.View 
        style={[
          styles.floatingAI1,
          {
            opacity: sparkleOpacity,
          },
        ]}
      >
        <Svg width={40} height={40} viewBox="0 0 40 40">
          <Defs>
            <SvgLinearGradient id="aiIcon1" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#06b6d4" stopOpacity="1" />
              <Stop offset="100%" stopColor="#0891b2" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* AI chip */}
          <Rect x="8" y="8" width="24" height="24" rx="4" fill="url(#aiIcon1)" />
          <Circle cx="15" cy="15" r="2" fill="#ffffff" />
          <Circle cx="25" cy="15" r="2" fill="#ffffff" />
          <Circle cx="15" cy="25" r="2" fill="#ffffff" />
          <Circle cx="25" cy="25" r="2" fill="#ffffff" />
          <Line x1="15" y1="15" x2="25" y2="25" stroke="#ffffff" strokeWidth="1" />
          <Line x1="25" y1="15" x2="15" y2="25" stroke="#ffffff" strokeWidth="1" />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingAI2,
          {
            opacity: sparkleOpacity,
          },
        ]}
      >
        <Svg width={35} height={35} viewBox="0 0 35 35">
          <Defs>
            <SvgLinearGradient id="aiIcon2" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#8b5cf6" stopOpacity="1" />
              <Stop offset="100%" stopColor="#7c3aed" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* AI brain waves */}
          <Path
            d="M5,17.5 Q10,10 15,17.5 Q20,25 25,17.5 Q30,10 35,17.5"
            stroke="url(#aiIcon2)"
            strokeWidth="3"
            fill="none"
          />
          <Path
            d="M5,20 Q10,13 15,20 Q20,27 25,20 Q30,13 35,20"
            stroke="url(#aiIcon2)"
            strokeWidth="2"
            fill="none"
            opacity="0.7"
          />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingAI3,
          {
            opacity: sparkleOpacity,
          },
        ]}
      >
        <Svg width={30} height={30} viewBox="0 0 30 30">
          <Defs>
            <SvgLinearGradient id="aiIcon3" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#f59e0b" stopOpacity="1" />
              <Stop offset="100%" stopColor="#d97706" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* AI target/match */}
          <Circle cx="15" cy="15" r="12" fill="none" stroke="url(#aiIcon3)" strokeWidth="2" />
          <Circle cx="15" cy="15" r="8" fill="none" stroke="url(#aiIcon3)" strokeWidth="2" />
          <Circle cx="15" cy="15" r="4" fill="url(#aiIcon3)" />
        </Svg>
      </Animated.View>

      {/* Match Percentage Indicator */}
      <Animated.View 
        style={[
          styles.matchIndicator,
          {
            opacity: connectionOpacity,
          },
        ]}
      >
        <Svg width={60} height={30} viewBox="0 0 60 30">
          <Defs>
            <SvgLinearGradient id="matchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#10b981" stopOpacity="1" />
              <Stop offset="100%" stopColor="#059669" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          <Rect x="5" y="10" width="50" height="10" rx="5" fill="url(#matchGradient)" />
          <Rect x="5" y="10" width="42" height="10" rx="5" fill="#ffffff" opacity="0.3" />
        </Svg>
      </Animated.View>

      {/* Decorative Sparkles */}
      <Animated.View 
        style={[
          styles.sparkles,
          {
            opacity: sparkleOpacity,
          },
        ]}
      >
        <Svg width={200} height={150} viewBox="0 0 200 150">
          <Path d="M20,20 L24,16 L28,20 L24,24 Z" fill="#fbbf24" />
          <Path d="M60,30 L63,27 L66,30 L63,33 Z" fill="#f59e0b" />
          <Path d="M120,25 L124,21 L128,25 L124,29 Z" fill="#fbbf24" />
          <Path d="M170,35 L173,32 L176,35 L173,38 Z" fill="#f59e0b" />
          
          <Path d="M40,80 L43,77 L46,80 L43,83 Z" fill="#c084fc" />
          <Path d="M100,90 L104,86 L108,90 L104,94 Z" fill="#a78bfa" />
          <Path d="M160,85 L163,82 L166,85 L163,88 Z" fill="#c084fc" />
        </Svg>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundShapes: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.3,
  },
  mainGraphics: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  person1: {
    position: 'absolute',
    left: '15%',
    top: '40%',
    zIndex: 3,
  },
  person2: {
    position: 'absolute',
    right: '15%',
    top: '40%',
    zIndex: 3,
  },
  floatingAI1: {
    position: 'absolute',
    top: '20%',
    left: '25%',
    zIndex: 3,
  },
  floatingAI2: {
    position: 'absolute',
    top: '25%',
    right: '25%',
    zIndex: 3,
  },
  floatingAI3: {
    position: 'absolute',
    bottom: '25%',
    right: '30%',
    zIndex: 3,
  },
  matchIndicator: {
    position: 'absolute',
    top: '35%',
    alignSelf: 'center',
    zIndex: 4,
  },
  sparkles: {
    position: 'absolute',
    top: '15%',
    left: '10%',
    opacity: 0.6,
  },
});

export default MatchingSlide; 