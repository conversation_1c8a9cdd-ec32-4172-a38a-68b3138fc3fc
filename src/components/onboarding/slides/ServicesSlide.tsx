import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { 
  Circle, 
  Path, 
  Defs, 
  LinearGradient as SvgLinearGradient, 
  Stop,
  Ellipse,
  Rect,
  Polygon,
  Line
} from 'react-native-svg';

const { width, height } = Dimensions.get('window');

const ServicesSlide: React.FC = () => {
  // Animation values
  const orbitAnim1 = useRef(new Animated.Value(0)).current;
  const orbitAnim2 = useRef(new Animated.Value(0)).current;
  const orbitAnim3 = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(0.9)).current;
  const toolsAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Orbit animations for service icons
    const createOrbitAnimation = (animValue: Animated.Value, duration: number) => {
      return Animated.loop(
        Animated.timing(animValue, {
          toValue: 1,
          duration: duration,
          useNativeDriver: true,
        })
      );
    };

    // Pulse animation for center
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.9,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Tools floating animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(toolsAnim, {
          toValue: 1,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(toolsAnim, {
          toValue: 0,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Background rotation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 30000,
        useNativeDriver: true,
      })
    ).start();

    createOrbitAnimation(orbitAnim1, 8000).start();
    createOrbitAnimation(orbitAnim2, 10000).start();
    createOrbitAnimation(orbitAnim3, 12000).start();
  }, []);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const orbit1Rotate = orbitAnim1.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const orbit2Rotate = orbitAnim2.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '-360deg'],
  });

  const orbit3Rotate = orbitAnim3.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const toolsY = toolsAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, -15, 0],
  });

  return (
    <View style={styles.container}>
      {/* Background Organic Shapes */}
      <Animated.View 
        style={[
          styles.backgroundShapes,
          {
            transform: [{ scale: scaleAnim }, { rotate }],
          },
        ]}
      >
        <Svg width={width} height={height * 0.6} viewBox="0 0 400 400">
          <Defs>
            <SvgLinearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ecfdf5" stopOpacity="0.6" />
              <Stop offset="100%" stopColor="#d1fae5" stopOpacity="0.3" />
            </SvgLinearGradient>
            <SvgLinearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#fef3c7" stopOpacity="0.4" />
              <Stop offset="100%" stopColor="#fed7aa" stopOpacity="0.2" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Organic service network shapes */}
          <Path
            d="M70,170 Q170,70 270,170 Q370,270 270,370 Q170,470 70,370 Q-30,270 70,170 Z"
            fill="url(#bgGradient1)"
          />
          <Path
            d="M110,210 Q210,110 310,210 Q410,310 310,410 Q210,510 110,410 Q10,310 110,210 Z"
            fill="url(#bgGradient2)"
          />
        </Svg>
      </Animated.View>

      {/* Main Services Hub */}
      <Animated.View 
        style={[
          styles.mainGraphics,
          {
            transform: [{ scale: scaleAnim }, { scale: pulseAnim }],
          },
        ]}
      >
        <Svg width={120} height={120} viewBox="0 0 120 120">
          <Defs>
            <SvgLinearGradient id="hubGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#6366f1" stopOpacity="1" />
              <Stop offset="100%" stopColor="#4f46e5" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Central Hub */}
          <Circle cx="60" cy="60" r="35" fill="url(#hubGradient)" />
          
          {/* Hub connections */}
          <Circle cx="60" cy="60" r="25" fill="none" stroke="#ffffff" strokeWidth="2" opacity="0.6" />
          <Circle cx="60" cy="60" r="15" fill="none" stroke="#ffffff" strokeWidth="2" opacity="0.8" />
          
          {/* Service symbol */}
          <Circle cx="50" cy="50" r="3" fill="#ffffff" />
          <Circle cx="70" cy="50" r="3" fill="#ffffff" />
          <Circle cx="50" cy="70" r="3" fill="#ffffff" />
          <Circle cx="70" cy="70" r="3" fill="#ffffff" />
          <Circle cx="60" cy="60" r="4" fill="#ffffff" />
          
          {/* Connection lines */}
          <Line x1="50" y1="50" x2="60" y2="60" stroke="#ffffff" strokeWidth="1.5" />
          <Line x1="70" y1="50" x2="60" y2="60" stroke="#ffffff" strokeWidth="1.5" />
          <Line x1="50" y1="70" x2="60" y2="60" stroke="#ffffff" strokeWidth="1.5" />
          <Line x1="70" y1="70" x2="60" y2="60" stroke="#ffffff" strokeWidth="1.5" />
        </Svg>
      </Animated.View>

      {/* Orbiting Service Icons */}
      
      {/* Cleaning Service */}
      <Animated.View 
        style={[
          styles.orbitContainer,
          {
            transform: [{ rotate: orbit1Rotate }],
          },
        ]}
      >
        <View style={styles.orbitingService1}>
          <Svg width={60} height={60} viewBox="0 0 60 60">
            <Defs>
              <SvgLinearGradient id="cleaningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <Stop offset="0%" stopColor="#10b981" stopOpacity="1" />
                <Stop offset="100%" stopColor="#059669" stopOpacity="1" />
              </SvgLinearGradient>
            </Defs>
            
            {/* Cleaning brush */}
            <Rect x="15" y="35" width="30" height="8" rx="4" fill="url(#cleaningGradient)" />
            <Rect x="20" y="20" width="4" height="20" fill="#8b7355" />
            
            {/* Cleaning bubbles */}
            <Circle cx="25" cy="15" r="3" fill="#e0f2fe" opacity="0.8" />
            <Circle cx="35" cy="12" r="2" fill="#e0f2fe" opacity="0.6" />
            <Circle cx="40" cy="18" r="2.5" fill="#e0f2fe" opacity="0.7" />
          </Svg>
        </View>
      </Animated.View>

      {/* Maintenance Service */}
      <Animated.View 
        style={[
          styles.orbitContainer,
          {
            transform: [{ rotate: orbit2Rotate }],
          },
        ]}
      >
        <View style={styles.orbitingService2}>
          <Svg width={60} height={60} viewBox="0 0 60 60">
            <Defs>
              <SvgLinearGradient id="maintenanceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <Stop offset="0%" stopColor="#f59e0b" stopOpacity="1" />
                <Stop offset="100%" stopColor="#d97706" stopOpacity="1" />
              </SvgLinearGradient>
            </Defs>
            
            {/* Wrench */}
            <Rect x="20" y="25" width="20" height="4" rx="2" fill="url(#maintenanceGradient)" />
            <Circle cx="18" cy="27" r="6" fill="url(#maintenanceGradient)" />
            <Circle cx="18" cy="27" r="3" fill="none" stroke="#ffffff" strokeWidth="2" />
            
            {/* Screwdriver */}
            <Rect x="35" y="15" width="3" height="25" fill="#6b7280" />
            <Rect x="33" y="12" width="7" height="6" rx="1" fill="#ef4444" />
          </Svg>
        </View>
      </Animated.View>

      {/* Moving Service */}
      <Animated.View 
        style={[
          styles.orbitContainer,
          {
            transform: [{ rotate: orbit3Rotate }],
          },
        ]}
      >
        <View style={styles.orbitingService3}>
          <Svg width={60} height={60} viewBox="0 0 60 60">
            <Defs>
              <SvgLinearGradient id="movingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <Stop offset="0%" stopColor="#8b5cf6" stopOpacity="1" />
                <Stop offset="100%" stopColor="#7c3aed" stopOpacity="1" />
              </SvgLinearGradient>
            </Defs>
            
            {/* Moving box */}
            <Rect x="15" y="20" width="25" height="20" rx="2" fill="url(#movingGradient)" />
            <Rect x="15" y="20" width="25" height="5" fill="#a855f7" />
            
            {/* Box tape */}
            <Line x1="15" y1="30" x2="40" y2="30" stroke="#ffffff" strokeWidth="2" />
            <Line x1="27.5" y1="20" x2="27.5" y2="40" stroke="#ffffff" strokeWidth="2" />
            
            {/* Handle */}
            <Circle cx="45" cy="30" r="3" fill="none" stroke="#6b7280" strokeWidth="2" />
          </Svg>
        </View>
      </Animated.View>

      {/* Floating Service Tools */}
      <Animated.View 
        style={[
          styles.floatingTool1,
          {
            transform: [{ translateY: toolsY }],
          },
        ]}
      >
        <Svg width={45} height={45} viewBox="0 0 45 45">
          <Defs>
            <SvgLinearGradient id="toolGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#06b6d4" stopOpacity="1" />
              <Stop offset="100%" stopColor="#0891b2" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Shield (security/trust) */}
          <Path
            d="M22.5,5 L35,12 L35,25 Q35,35 22.5,40 Q10,35 10,25 L10,12 Z"
            fill="url(#toolGradient1)"
          />
          <Path
            d="M22.5,10 L30,15 L30,25 Q30,30 22.5,33 Q15,30 15,25 L15,15 Z"
            fill="#ffffff"
            opacity="0.3"
          />
          
          {/* Checkmark */}
          <Path
            d="M18,22 L21,25 L28,18"
            stroke="#ffffff"
            strokeWidth="2"
            fill="none"
          />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingTool2,
          {
            transform: [{ translateY: toolsY }],
          },
        ]}
      >
        <Svg width={40} height={40} viewBox="0 0 40 40">
          <Defs>
            <SvgLinearGradient id="toolGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ec4899" stopOpacity="1" />
              <Stop offset="100%" stopColor="#db2777" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Star rating */}
          <Path
            d="M20,5 L23,15 L33,15 L25,22 L28,32 L20,26 L12,32 L15,22 L7,15 L17,15 Z"
            fill="url(#toolGradient2)"
          />
          <Path
            d="M20,8 L22,14 L28,14 L23,18 L25,24 L20,21 L15,24 L17,18 L12,14 L18,14 Z"
            fill="#ffffff"
            opacity="0.4"
          />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingTool3,
          {
            transform: [{ translateY: toolsY }],
          },
        ]}
      >
        <Svg width={38} height={38} viewBox="0 0 38 38">
          <Defs>
            <SvgLinearGradient id="toolGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ef4444" stopOpacity="1" />
              <Stop offset="100%" stopColor="#dc2626" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          
          {/* 24/7 clock */}
          <Circle cx="19" cy="19" r="15" fill="url(#toolGradient3)" />
          <Circle cx="19" cy="19" r="12" fill="none" stroke="#ffffff" strokeWidth="2" />
          
          {/* Clock hands */}
          <Line x1="19" y1="19" x2="19" y2="12" stroke="#ffffff" strokeWidth="2" />
          <Line x1="19" y1="19" x2="24" y2="19" stroke="#ffffff" strokeWidth="2" />
          
          {/* Clock center */}
          <Circle cx="19" cy="19" r="2" fill="#ffffff" />
        </Svg>
      </Animated.View>

      {/* Quality Indicators */}
      <View style={styles.qualityIndicators}>
        <Svg width={180} height={80} viewBox="0 0 180 80">
          {/* Trust badges */}
          <Circle cx="30" cy="25" r="8" fill="#10b981" opacity="0.8" />
          <Path d="M26,25 L29,28 L34,20" stroke="#ffffff" strokeWidth="2" fill="none" />
          
          <Circle cx="70" cy="30" r="6" fill="#f59e0b" opacity="0.7" />
          <Path d="M70,26 L70,34 M66,30 L74,30" stroke="#ffffff" strokeWidth="1.5" />
          
          <Circle cx="110" cy="20" r="7" fill="#ec4899" opacity="0.8" />
          <Path d="M107,20 L110,23 L113,17" stroke="#ffffff" strokeWidth="1.5" fill="none" />
          
          <Circle cx="150" cy="28" r="6" fill="#8b5cf6" opacity="0.7" />
          <Circle cx="150" cy="28" r="3" fill="#ffffff" />
          
          {/* Connection lines */}
          <Line x1="38" y1="25" x2="62" y2="30" stroke="#d1d5db" strokeWidth="1" opacity="0.5" />
          <Line x1="76" y1="30" x2="103" y2="20" stroke="#d1d5db" strokeWidth="1" opacity="0.5" />
          <Line x1="117" y1="20" x2="144" y2="28" stroke="#d1d5db" strokeWidth="1" opacity="0.5" />
        </Svg>
      </View>

      {/* Service Categories */}
      <View style={styles.serviceCategories}>
        <Svg width={200} height={60} viewBox="0 0 200 60">
          <Rect x="10" y="20" width="35" height="20" rx="10" fill="#ddd6fe" opacity="0.6" />
          <Rect x="55" y="15" width="40" height="25" rx="12" fill="#fef3c7" opacity="0.6" />
          <Rect x="105" y="18" width="38" height="22" rx="11" fill="#ecfdf5" opacity="0.6" />
          <Rect x="153" y="16" width="37" height="24" rx="12" fill="#fef2f2" opacity="0.6" />
        </Svg>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundShapes: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.3,
  },
  mainGraphics: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  orbitContainer: {
    position: 'absolute',
    width: 240,
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
  },
  orbitingService1: {
    position: 'absolute',
    top: 20,
    alignSelf: 'center',
  },
  orbitingService2: {
    position: 'absolute',
    right: 20,
    alignSelf: 'center',
  },
  orbitingService3: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
  },
  floatingTool1: {
    position: 'absolute',
    top: '15%',
    left: '20%',
    zIndex: 3,
  },
  floatingTool2: {
    position: 'absolute',
    top: '20%',
    right: '15%',
    zIndex: 3,
  },
  floatingTool3: {
    position: 'absolute',
    bottom: '25%',
    left: '25%',
    zIndex: 3,
  },
  qualityIndicators: {
    position: 'absolute',
    top: '65%',
    alignSelf: 'center',
    opacity: 0.7,
  },
  serviceCategories: {
    position: 'absolute',
    bottom: '15%',
    alignSelf: 'center',
    opacity: 0.5,
  },
});

export default ServicesSlide; 