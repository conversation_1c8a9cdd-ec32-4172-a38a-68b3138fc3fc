import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { 
  Circle, 
  Path, 
  Defs, 
  LinearGradient as SvgLinearGradient, 
  Stop,
  Ellipse,
  Rect,
  Polygon
} from 'react-native-svg';

const { width, height } = Dimensions.get('window');

const RoomsSlide: React.FC = () => {
  // Animation values
  const floatingAnim1 = useRef(new Animated.Value(0)).current;
  const floatingAnim2 = useRef(new Animated.Value(0)).current;
  const floatingAnim3 = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Floating animations
    const createFloatingAnimation = (animValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: 3000 + delay,
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 3000 + delay,
            useNativeDriver: true,
          }),
        ])
      );
    };

    // Rotation animation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 20000,
        useNativeDriver: true,
      })
    ).start();

    createFloatingAnimation(floatingAnim1, 0).start();
    createFloatingAnimation(floatingAnim2, 1000).start();
    createFloatingAnimation(floatingAnim3, 2000).start();
  }, []);

  const floating1Y = floatingAnim1.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const floating2Y = floatingAnim2.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -15],
  });

  const floating3Y = floatingAnim3.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -25],
  });

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      {/* Background Organic Shapes */}
      <Animated.View 
        style={[
          styles.backgroundShapes,
          {
            transform: [{ scale: scaleAnim }, { rotate }],
          },
        ]}
      >
        <Svg width={width} height={height * 0.6} viewBox="0 0 400 400">
          <Defs>
            <SvgLinearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#f1f5f9" stopOpacity="0.6" />
              <Stop offset="100%" stopColor="#e2e8f0" stopOpacity="0.3" />
            </SvgLinearGradient>
            <SvgLinearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ddd6fe" stopOpacity="0.4" />
              <Stop offset="100%" stopColor="#c7d2fe" stopOpacity="0.2" />
            </SvgLinearGradient>
          </Defs>
          
          {/* Organic background shapes */}
          <Path
            d="M50,150 Q150,50 250,150 Q350,250 250,350 Q150,450 50,350 Q-50,250 50,150 Z"
            fill="url(#bgGradient1)"
          />
          <Path
            d="M100,200 Q200,100 300,200 Q400,300 300,400 Q200,500 100,400 Q0,300 100,200 Z"
            fill="url(#bgGradient2)"
          />
        </Svg>
      </Animated.View>

      {/* Main Room Graphics */}
      <Animated.View 
        style={[
          styles.mainGraphics,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Svg width={280} height={280} viewBox="0 0 280 280">
          <Defs>
            <SvgLinearGradient id="houseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#6366f1" stopOpacity="1" />
              <Stop offset="100%" stopColor="#4f46e5" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="roofGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#4338ca" stopOpacity="1" />
              <Stop offset="100%" stopColor="#3730a3" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="windowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#e0f2fe" stopOpacity="1" />
              <Stop offset="100%" stopColor="#bae6fd" stopOpacity="1" />
            </SvgLinearGradient>
            <SvgLinearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#fbbf24" stopOpacity="1" />
              <Stop offset="100%" stopColor="#f59e0b" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>

          {/* Main House */}
          <Rect
            x="80"
            y="120"
            width="120"
            height="100"
            rx="8"
            fill="url(#houseGradient)"
          />

          {/* Roof */}
          <Polygon
            points="70,120 140,60 210,120"
            fill="url(#roofGradient)"
          />

          {/* Windows */}
          <Rect
            x="95"
            y="140"
            width="25"
            height="25"
            rx="4"
            fill="url(#windowGradient)"
          />
          <Rect
            x="160"
            y="140"
            width="25"
            height="25"
            rx="4"
            fill="url(#windowGradient)"
          />

          {/* Door */}
          <Rect
            x="125"
            y="180"
            width="30"
            height="40"
            rx="15"
            fill="url(#doorGradient)"
          />

          {/* Door knob */}
          <Circle
            cx="148"
            cy="200"
            r="2"
            fill="#d97706"
          />

          {/* Chimney */}
          <Rect
            x="170"
            y="80"
            width="15"
            height="30"
            fill="#6b7280"
          />
        </Svg>
      </Animated.View>

      {/* Floating Room Elements */}
      <Animated.View 
        style={[
          styles.floatingIcon1,
          {
            transform: [{ translateY: floating1Y }],
          },
        ]}
      >
        <Svg width={60} height={60} viewBox="0 0 60 60">
          <Defs>
            <SvgLinearGradient id="bedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#f472b6" stopOpacity="1" />
              <Stop offset="100%" stopColor="#ec4899" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* Bed icon */}
          <Rect x="10" y="25" width="40" height="20" rx="4" fill="url(#bedGradient)" />
          <Rect x="8" y="35" width="44" height="8" rx="2" fill="#be185d" />
          <Circle cx="15" cy="20" r="4" fill="#f9a8d4" />
          <Circle cx="45" cy="20" r="4" fill="#f9a8d4" />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingIcon2,
          {
            transform: [{ translateY: floating2Y }],
          },
        ]}
      >
        <Svg width={50} height={50} viewBox="0 0 50 50">
          <Defs>
            <SvgLinearGradient id="keyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#10b981" stopOpacity="1" />
              <Stop offset="100%" stopColor="#059669" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* Key icon */}
          <Circle cx="15" cy="15" r="8" fill="url(#keyGradient)" />
          <Circle cx="15" cy="15" r="4" fill="none" stroke="#ffffff" strokeWidth="2" />
          <Rect x="20" y="13" width="20" height="4" fill="url(#keyGradient)" />
          <Rect x="35" y="10" width="3" height="4" fill="url(#keyGradient)" />
          <Rect x="35" y="17" width="3" height="4" fill="url(#keyGradient)" />
        </Svg>
      </Animated.View>

      <Animated.View 
        style={[
          styles.floatingIcon3,
          {
            transform: [{ translateY: floating3Y }],
          },
        ]}
      >
        <Svg width={45} height={45} viewBox="0 0 45 45">
          <Defs>
            <SvgLinearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#ef4444" stopOpacity="1" />
              <Stop offset="100%" stopColor="#dc2626" stopOpacity="1" />
            </SvgLinearGradient>
          </Defs>
          {/* Heart icon */}
          <Path
            d="M22.5,35 C22.5,35 5,25 5,15 C5,10 10,5 15,5 C18,5 20,7 22.5,10 C25,7 27,5 30,5 C35,5 40,10 40,15 C40,25 22.5,35 22.5,35 Z"
            fill="url(#heartGradient)"
          />
        </Svg>
      </Animated.View>

      {/* Decorative Dots */}
      <View style={styles.decorativeDots}>
        <Svg width={200} height={100} viewBox="0 0 200 100">
          <Circle cx="20" cy="20" r="3" fill="#c084fc" opacity="0.6" />
          <Circle cx="60" cy="15" r="2" fill="#a78bfa" opacity="0.5" />
          <Circle cx="100" cy="25" r="2.5" fill="#8b5cf6" opacity="0.7" />
          <Circle cx="140" cy="18" r="2" fill="#7c3aed" opacity="0.5" />
          <Circle cx="180" cy="22" r="3" fill="#6d28d9" opacity="0.6" />
          
          <Circle cx="30" cy="70" r="2" fill="#06b6d4" opacity="0.5" />
          <Circle cx="70" cy="75" r="2.5" fill="#0891b2" opacity="0.6" />
          <Circle cx="110" cy="65" r="2" fill="#0e7490" opacity="0.5" />
          <Circle cx="150" cy="78" r="3" fill="#155e75" opacity="0.7" />
        </Svg>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundShapes: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.3,
  },
  mainGraphics: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  floatingIcon1: {
    position: 'absolute',
    top: '15%',
    right: '15%',
  },
  floatingIcon2: {
    position: 'absolute',
    top: '25%',
    left: '10%',
  },
  floatingIcon3: {
    position: 'absolute',
    bottom: '20%',
    right: '20%',
  },
  floatingIcon4: {
    position: 'absolute',
    bottom: '15%',
    left: '15%',
  },
  floatingIcon5: {
    position: 'absolute',
    top: '35%',
    right: '25%',
  },
  floatingIcon6: {
    position: 'absolute',
    bottom: '35%',
    left: '25%',
  },
  decorativeDots: {
    position: 'absolute',
    top: '10%',
    left: '10%',
    opacity: 0.4,
  },
});

export default RoomsSlide; 