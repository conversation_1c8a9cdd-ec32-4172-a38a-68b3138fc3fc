import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { useTheme } from '@design-system/ThemeProvider';
import OnboardingScreen from './OnboardingScreen';

const OnboardingPreview: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  const handleOnboardingComplete = () => {
    console.log('Onboarding completed!');
    // In real app, this would navigate to registration or home
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>WeRoomies Onboarding</Text>
        <Text style={styles.headerSubtitle}>
          Beautiful organic onboarding experience with 3 feature slides
        </Text>
      </View>

      {/* Onboarding Component */}
      <View style={styles.onboardingContainer}>
        <OnboardingScreen onComplete={handleOnboardingComplete} />
      </View>

      {/* Instructions */}
      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>Features:</Text>
        <Text style={styles.instructionText}>
          • Swipe left/right to navigate between slides{'\n'}
          • Skip button to bypass onboarding{'\n'}
          • Animated organic graphics for each feature{'\n'}
          • Smooth transitions and engaging animations{'\n'}
          • Responsive design for all screen sizes
        </Text>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#4f46e5',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 22,
  },
  onboardingContainer: {
    flex: 1,
    backgroundColor: '#f0f9ff',
  },
  instructions: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
} as any);

export default OnboardingPreview; 