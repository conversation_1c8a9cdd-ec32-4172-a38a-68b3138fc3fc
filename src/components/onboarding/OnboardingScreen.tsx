import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  PanResponder,
  StatusBar,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@design-system/ThemeProvider';
import { ChevronRight, ChevronLeft } from 'lucide-react-native';
import { platformSpacing, platformTextStyles, platformButtonStyles } from '@utils/crossPlatformStyles';

// Import individual slide components
import RoomsSlide from './slides/RoomsSlide';
import MatchingSlide from './slides/MatchingSlide';
import ServicesSlide from './slides/ServicesSlide';

const { width, height } = Dimensions.get('window');

interface OnboardingScreenProps {
  onComplete?: () => void;
}

const onboardingData = [
  {
    id: 'rooms',
    title: 'Find Perfect Rooms',
    subtitle: 'Discover amazing spaces that match your lifestyle and budget',
    component: RoomsSlide,
  },
  {
    id: 'matching',
    title: 'AI-Powered Matching',
    subtitle: 'Connect with compatible roommates using smart algorithms',
    component: MatchingSlide,
  },
  {
    id: 'services',
    title: 'Trusted Services',
    subtitle: 'Access verified service providers for all your housing needs',
    component: ServicesSlide,
  },
];

export const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete }) => {
  const theme = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;

  // Debug effect to track current index changes
  useEffect(() => {
    console.log('🟡 [OnboardingScreen] Current index changed to:', currentIndex);
    console.log('🟡 [OnboardingScreen] Current slide:', onboardingData[currentIndex]?.id);
  }, [currentIndex]);

  // Pan responder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dx) > 20 && Math.abs(gestureState.dy) < 100;
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dx > 50 && currentIndex > 0) {
          // Swipe right - go to previous slide
          goToPreviousSlide();
        } else if (gestureState.dx < -50 && currentIndex < onboardingData.length - 1) {
          // Swipe left - go to next slide
          goToNextSlide();
        }
      },
    })
  ).current;

  const goToNextSlide = () => {
    console.log('🟡 [OnboardingScreen] goToNextSlide called, currentIndex:', currentIndex);
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      console.log('🟡 [OnboardingScreen] Moving to slide:', nextIndex);
      setCurrentIndex(nextIndex);
      
      Animated.timing(scrollX, {
        toValue: -nextIndex * width,
        duration: 300,
        useNativeDriver: false,
      }).start(() => {
        console.log('🟢 [OnboardingScreen] Animation completed for slide:', nextIndex);
      });
    } else {
      // Last slide - complete onboarding
      console.log('🟢 [OnboardingScreen] Last slide reached, completing onboarding');
      handleComplete();
    }
  };

  const goToPreviousSlide = () => {
    console.log('🟡 [OnboardingScreen] goToPreviousSlide called, currentIndex:', currentIndex);
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      console.log('🟡 [OnboardingScreen] Moving to slide:', prevIndex);
      setCurrentIndex(prevIndex);
      
      Animated.timing(scrollX, {
        toValue: -prevIndex * width,
        duration: 300,
        useNativeDriver: false,
      }).start(() => {
        console.log('🟢 [OnboardingScreen] Animation completed for slide:', prevIndex);
      });
    }
  };

  const handleComplete = () => {
    console.log('🟢 [OnboardingScreen] Onboarding completed');
    onComplete?.();
  };

  const handleSkip = () => {
    console.log('🟢 [OnboardingScreen] Onboarding skipped');
    onComplete?.();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f0f9ff', '#ffffff', '#e0f2fe']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Skip Button */}
      <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>

      {/* Slides Container */}
      <View style={styles.slidesContainer} {...panResponder.panHandlers}>
        <Animated.View
          style={[
            styles.slidesWrapper,
            {
              transform: [{ translateX: scrollX }],
            },
          ]}
        >
          {onboardingData.map((item, index) => {
            const SlideComponent = item.component;
            console.log('🟡 [OnboardingScreen] Rendering slide:', item.id, 'at index:', index);
            return (
              <View key={item.id} style={styles.slide}>
                <View style={styles.slideContent}>
                  {/* Graphics Section */}
                  <View style={styles.graphicsContainer}>
                    <SlideComponent />
                  </View>
                  
                  {/* Text Content Section */}
                  <View style={styles.contentContainer}>
                    <Text style={styles.title}>{item.title}</Text>
                    <Text style={styles.subtitle}>{item.subtitle}</Text>
                  </View>
                </View>
              </View>
            );
          })}
        </Animated.View>
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomContainer}>
        {/* Progress Indicators */}
        <View style={styles.progressContainer}>
          {onboardingData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                index === currentIndex && styles.progressDotActive,
              ]}
            />
          ))}
        </View>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {/* Previous Button */}
          {currentIndex > 0 && (
            <TouchableOpacity
              style={styles.navButton}
              onPress={goToPreviousSlide}
              activeOpacity={0.7}
            >
              <ChevronLeft size={24} color="#6366f1" />
            </TouchableOpacity>
          )}

          {/* Next/Get Started Button */}
          <TouchableOpacity
            style={[
              styles.nextButton,
              currentIndex === onboardingData.length - 1 && styles.getStartedButton,
            ]}
            onPress={goToNextSlide}
            activeOpacity={0.8}
          >
            {currentIndex === onboardingData.length - 1 ? (
              <Text style={styles.getStartedText}>Get Started</Text>
            ) : (
              <>
                <Text style={styles.nextText}>Next</Text>
                <ChevronRight size={20} color="#ffffff" />
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  skipButton: {
    position: 'absolute',
    top: Platform.select({
      ios: 60,
      android: 40,
      default: 60
    }),
    right: 20,
    zIndex: 10,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
  },
  slidesContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  slidesWrapper: {
    flexDirection: 'row',
    height: '100%',
    width: width * onboardingData.length,
  },
  slide: {
    width: width,
    height: '100%',
    paddingHorizontal: 20,
    paddingTop: Platform.select({
      ios: 100,
      android: 80,
      default: 80
    }),
    paddingBottom: Platform.select({
      ios: 140,
      android: 120,
      default: 120
    }),
  },
  slideContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  graphicsContainer: {
    flex: 0.65,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
  },
  contentContainer: {
    flex: 0.35,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 20,
    width: '100%',
  },
  title: {
    ...platformTextStyles.title,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    ...platformTextStyles.body,
    color: '#6b7280',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: Platform.select({
      ios: 50,
      android: 40,
      default: 50
    }),
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    minHeight: Platform.select({
      ios: 120,
      android: 110,
      default: 120
    }),
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Platform.select({
      ios: 30,
      android: 25,
      default: 30
    }),
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#d1d5db',
    marginHorizontal: 4,
  },
  progressDotActive: {
    backgroundColor: '#6366f1',
    width: 24,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Platform.select({
      ios: 0,
      android: 4,
      default: 0
    }),
  },
  navButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6366f1',
    paddingHorizontal: 24,
    paddingVertical: Platform.select({
      ios: 12,
      android: 14,
      default: 12
    }),
    borderRadius: 25,
    minWidth: Platform.select({
      ios: 120,
      android: 130,
      default: 120
    }),
    justifyContent: 'center',
  },
  getStartedButton: {
    backgroundColor: '#059669',
    minWidth: Platform.select({
      ios: 140,
      android: 150,
      default: 140
    }),
  },
  nextText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  getStartedText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OnboardingScreen; 