import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { logger } from '@utils/logger';

const { width, height } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  gradient: string[];
  features: string[];
  route?: string;
  category: 'my-profile' | 'living-roommates' | 'settings';
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Your New Profile!',
    description: 'We\'ve simplified your profile into 3 easy categories to help you get started faster.',
    icon: 'sparkles',
    gradient: ['#667eea', '#764ba2'],
    features: [
      'Cleaner, easier navigation',
      'Fewer taps to reach what you need',
      'Advanced features when you\'re ready',
    ],
    category: 'my-profile',
  },
  {
    id: 'my-profile',
    title: 'My Profile',
    description: 'Everything about you in one place',
    icon: 'person-circle',
    gradient: ['#f093fb', '#f5576c'],
    features: [
      'Basic info & photos',
      'Living preferences',
      'Verification status',
      'Quick profile setup',
    ],
    route: '/(tabs)/profile/edit',
    category: 'my-profile',
  },
  {
    id: 'living-roommates',
    title: 'Living & Roommates',
    description: 'Manage your household and find the perfect roommates',
    icon: 'home',
    gradient: ['#4facfe', '#00f2fe'],
    features: [
      'Current household status',
      'Roommate relationships',
      'Find new roommates',
      'Household management',
    ],
    route: '/(tabs)/profile/household',
    category: 'living-roommates',
  },
  {
    id: 'settings',
    title: 'Settings',
    description: 'Control your account, privacy, and app preferences',
    icon: 'settings',
    gradient: ['#fa709a', '#fee140'],
    features: [
      'Account & security',
      'Notification preferences',
      'Privacy controls',
      'App customization',
    ],
    route: '/(tabs)/profile/settings',
    category: 'settings',
  },
  {
    id: 'advanced',
    title: 'Advanced Features',
    description: 'Power features are available when you\'re ready!',
    icon: 'rocket',
    gradient: ['#a8edea', '#fed6e3'],
    features: [
      'AI compatibility matching',
      'Analytics & insights',
      'Business features',
      'Experimental tools',
    ],
    route: '/(tabs)/profile/advanced',
    category: 'settings',
  },
];

interface SimplifiedProfileOnboardingProps {
  onComplete: () => void;
  skipToProfile?: boolean;
}

export function SimplifiedProfileOnboarding({ 
  onComplete, 
  skipToProfile = false 
}: SimplifiedProfileOnboardingProps) {
  const { colors, isDark } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      scrollViewRef.current?.scrollTo({ x: nextStep * width, animated: true });
      
      // Show advanced features step only if user has shown interest
      if (nextStep === 4 && !showAdvanced) {
        handleComplete();
        return;
      }
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      scrollViewRef.current?.scrollTo({ x: prevStep * width, animated: true });
    }
  };

  const handleComplete = () => {
    logger.info('Profile onboarding completed', 'SimplifiedProfileOnboarding', {
      stepsCompleted: currentStep + 1,
      advancedShown: showAdvanced,
    });
    
    if (skipToProfile) {
      router.push('/(tabs)/profile/edit');
    } else {
      onComplete();
    }
  };

  const handleExploreCategory = (route?: string) => {
    if (route) {
      logger.info('User explored category during onboarding', 'SimplifiedProfileOnboarding', { route });
      router.push(route as any);
    }
  };

  const handleShowAdvanced = () => {
    setShowAdvanced(true);
    logger.info('User requested advanced features during onboarding', 'SimplifiedProfileOnboarding');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    stepContainer: {
      width,
      height: height - 100,
      paddingHorizontal: 24,
      paddingTop: 60,
    },
    gradientBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: height * 0.4,
    },
    blurOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: height * 0.4,
    },
    iconContainer: {
      alignItems: 'center',
      marginTop: 40,
      marginBottom: 32,
    },
    iconCircle: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: colors.surface,
      alignItems: 'center',
      justifyContent: 'center',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.3,
          shadowRadius: 16,
        },
        android: {
          elevation: 8,
        },
      }),
    },
    title: {
      fontSize: 32,
      fontWeight: '700',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    description: {
      fontSize: 18,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 26,
      marginBottom: 32,
      paddingHorizontal: 8,
    },
    featuresContainer: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      padding: 20,
      marginBottom: 32,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
        },
        android: {
          elevation: 4,
        },
      }),
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    featureText: {
      fontSize: 16,
      color: colors.text,
      marginLeft: 12,
      flex: 1,
    },
    bottomContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      padding: 24,
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    button: {
      paddingHorizontal: 24,
      paddingVertical: 14,
      borderRadius: 12,
      minWidth: 100,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    secondaryButton: {
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: colors.primaryContrast,
    },
    secondaryButtonText: {
      color: colors.text,
    },
    exploreButton: {
      backgroundColor: colors.accent,
      marginTop: 8,
    },
    exploreButtonText: {
      color: colors.background,
    },
    progressContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
      backgroundColor: colors.border,
    },
    progressDotActive: {
      backgroundColor: colors.primary,
      width: 24,
    },
    advancedToggle: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 16,
      padding: 12,
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    advancedToggleText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 8,
    },
  });

  const currentStepData = onboardingSteps[currentStep];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        style={styles.scrollView}
      >
        {onboardingSteps.map((step, index) => {
          // Skip advanced step if not requested
          if (step.id === 'advanced' && !showAdvanced) return null;
          
          return (
            <View key={step.id} style={styles.stepContainer}>
              <LinearGradient
                colors={step.gradient}
                style={styles.gradientBackground}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              />
              
              <BlurView
                intensity={isDark ? 20 : 40}
                style={styles.blurOverlay}
              />
              
              <View style={styles.iconContainer}>
                <View style={styles.iconCircle}>
                  <Ionicons
                    name={step.icon}
                    size={56}
                    color={step.gradient[0]}
                  />
                </View>
              </View>
              
              <Text style={styles.title}>{step.title}</Text>
              <Text style={styles.description}>{step.description}</Text>
              
              <View style={styles.featuresContainer}>
                {step.features.map((feature, featureIndex) => (
                  <View key={featureIndex} style={styles.featureItem}>
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={colors.success}
                    />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
                
                {step.route && (
                  <TouchableOpacity
                    style={[styles.button, styles.exploreButton]}
                    onPress={() => handleExploreCategory(step.route)}
                  >
                    <Text style={[styles.buttonText, styles.exploreButtonText]}>
                      Explore Now
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
              
              {step.id === 'settings' && !showAdvanced && (
                <TouchableOpacity
                  style={styles.advancedToggle}
                  onPress={handleShowAdvanced}
                >
                  <Ionicons
                    name="rocket-outline"
                    size={16}
                    color={colors.textSecondary}
                  />
                  <Text style={styles.advancedToggleText}>
                    Show me advanced features
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          );
        })}
      </ScrollView>
      
      <View style={styles.bottomContainer}>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={currentStep === 0 ? handleComplete : handlePrevious}
          >
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              {currentStep === 0 ? 'Skip' : 'Previous'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleNext}
          >
            <Text style={[styles.buttonText, styles.primaryButtonText]}>
              {currentStep === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.progressContainer}>
          {onboardingSteps.map((_, index) => {
            // Skip advanced step dot if not shown
            if (onboardingSteps[index].id === 'advanced' && !showAdvanced) return null;
            
            return (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  index === currentStep && styles.progressDotActive,
                ]}
              />
            );
          })}
        </View>
      </View>
    </SafeAreaView>
  );
}

// Hook for onboarding state management
export function useProfileOnboarding() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const [shouldShowOnboarding, setShouldShowOnboarding] = useState(false);

  React.useEffect(() => {
    checkOnboardingStatus();
  }, []);

  const checkOnboardingStatus = async () => {
    try {
      // Check if user has completed the new simplified profile onboarding
      const completed = await AsyncStorage.getItem('profile_onboarding_v2_completed');
      const showOnboarding = await AsyncStorage.getItem('show_profile_onboarding');
      
      setHasCompletedOnboarding(completed === 'true');
      setShouldShowOnboarding(showOnboarding !== 'false' && completed !== 'true');
    } catch (error) {
      logger.error('Failed to check onboarding status', 'useProfileOnboarding', error);
      setShouldShowOnboarding(true); // Default to showing onboarding
    }
  };

  const completeOnboarding = async () => {
    try {
      await AsyncStorage.setItem('profile_onboarding_v2_completed', 'true');
      await AsyncStorage.setItem('show_profile_onboarding', 'false');
      setHasCompletedOnboarding(true);
      setShouldShowOnboarding(false);
      
      logger.info('Profile onboarding marked as completed', 'useProfileOnboarding');
    } catch (error) {
      logger.error('Failed to mark onboarding as completed', 'useProfileOnboarding', error);
    }
  };

  const resetOnboarding = async () => {
    try {
      await AsyncStorage.removeItem('profile_onboarding_v2_completed');
      await AsyncStorage.setItem('show_profile_onboarding', 'true');
      setHasCompletedOnboarding(false);
      setShouldShowOnboarding(true);
      
      logger.info('Profile onboarding reset', 'useProfileOnboarding');
    } catch (error) {
      logger.error('Failed to reset onboarding', 'useProfileOnboarding', error);
    }
  };

  return {
    hasCompletedOnboarding,
    shouldShowOnboarding,
    completeOnboarding,
    resetOnboarding,
    checkOnboardingStatus,
  };
} 