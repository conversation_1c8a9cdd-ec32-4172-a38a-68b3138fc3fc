import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '@design-system/ThemeProvider';
import { SmartConversationIntelligence } from '@services/messaging/SmartConversationIntelligence';
import { AIMessageModeration } from '@services/messaging/AIMessageModeration';
import { ConversationOptimizer } from '@services/messaging/ConversationOptimizer';
import { RealTimeConversationAnalytics } from '@services/messaging/RealTimeConversationAnalytics';

interface TestResult {
  testName: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  duration?: number;
  error?: string;
  details?: any;
  timestamp: Date;
}

interface PerformanceMetrics {
  aiAnalysisTime: number;
  moderationTime: number;
  optimizationTime: number;
  memoryUsage: number;
  activeSubscriptions: number;
  eventsProcessed: number;
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
}

interface SystemHealth {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  aiServices: 'online' | 'degraded' | 'offline';
  realTimeAnalytics: 'active' | 'inactive' | 'error';
  messageModeration: 'active' | 'inactive' | 'error';
  conversationOptimizer: 'active' | 'inactive' | 'error';
  lastHealthCheck: Date;
}

interface LogEntry {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  service: string;
  message: string;
  data?: any;
}

export const RealTimeAIMessagingDebugger: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // State
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    aiAnalysisTime: 0,
    moderationTime: 0,
    optimizationTime: 0,
    memoryUsage: 0,
    activeSubscriptions: 0,
    eventsProcessed: 0,
    averageResponseTime: 0,
    successRate: 0,
    errorRate: 0,
  });
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall: 'good',
    aiServices: 'online',
    realTimeAnalytics: 'active',
    messageModeration: 'active',
    conversationOptimizer: 'active',
    lastHealthCheck: new Date(),
  });
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'tests' | 'metrics' | 'health' | 'logs'>('tests');
  const [testRoomId, setTestRoomId] = useState('test_room_123');
  const [testUserId1, setTestUserId1] = useState('user_1');
  const [testUserId2, setTestUserId2] = useState('user_2');

  // AI Services
  const conversationIntelligence = useMemo(() => SmartConversationIntelligence.getInstance(), []);
  const messageModeration = useMemo(() => AIMessageModeration.getInstance(), []);
  const conversationOptimizer = useMemo(() => ConversationOptimizer.getInstance(), []);
  const realTimeAnalytics = useMemo(() => RealTimeConversationAnalytics.getInstance(), []);

  // Add log entry
  const addLog = useCallback((level: LogEntry['level'], service: string, message: string, data?: any) => {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      service,
      message,
      data,
    };
    setLogs(prev => [logEntry, ...prev.slice(0, 99)]); // Keep last 100 logs
  }, []);

  // Update test result
  const updateTestResult = useCallback((testName: string, updates: Partial<TestResult>) => {
    setTestResults(prev => {
      const existingIndex = prev.findIndex(t => t.testName === testName);
      const updatedResult = {
        testName,
        status: 'pending' as const,
        timestamp: new Date(),
        ...updates,
      };

      if (existingIndex >= 0) {
        const newResults = [...prev];
        newResults[existingIndex] = { ...newResults[existingIndex], ...updatedResult };
        return newResults;
      } else {
        return [...prev, updatedResult];
      }
    });
  }, []);

  // Test AI Conversation Intelligence
  const testConversationIntelligence = useCallback(async (): Promise<void> => {
    const testName = 'AI Conversation Intelligence';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'ConversationIntelligence', 'Starting intelligence analysis test');

    try {
      const startTime = Date.now();
      const result = await conversationIntelligence.analyzeConversation(
        testRoomId,
        testUserId1,
        testUserId2
      );
      const duration = Date.now() - startTime;

      if (result && result.conversationHealth && result.insights) {
        updateTestResult(testName, {
          status: 'passed',
          duration,
          details: {
            healthScore: result.conversationHealth.overallHealth,
            insightsCount: result.insights.length,
            compatibilityScore: result.compatibilityInsights?.overallCompatibility || 0,
          },
        });
        addLog('info', 'ConversationIntelligence', `Test passed in ${duration}ms`, result);
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'ConversationIntelligence', 'Test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, conversationIntelligence, updateTestResult, addLog]);

  // Test AI Message Moderation
  const testMessageModeration = useCallback(async (): Promise<void> => {
    const testName = 'AI Message Moderation';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'MessageModeration', 'Starting moderation test');

    try {
      const startTime = Date.now();
      const result = await messageModeration.analyzeConversationSafety(
        testRoomId,
        testUserId1,
        testUserId2
      );
      const duration = Date.now() - startTime;

      if (result && result.overallRisk && result.detectedIssues !== undefined) {
        updateTestResult(testName, {
          status: 'passed',
          duration,
          details: {
            riskLevel: result.overallRisk,
            issuesCount: result.detectedIssues.length,
            safetyScore: result.safetyScore || 0,
          },
        });
        addLog('info', 'MessageModeration', `Test passed in ${duration}ms`, result);
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'MessageModeration', 'Test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, messageModeration, updateTestResult, addLog]);

  // Test Conversation Optimizer
  const testConversationOptimizer = useCallback(async (): Promise<void> => {
    const testName = 'Conversation Optimizer';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'ConversationOptimizer', 'Starting optimization test');

    try {
      const startTime = Date.now();
      const result = await conversationOptimizer.optimizeConversation(
        testRoomId,
        testUserId1,
        testUserId2
      );
      const duration = Date.now() - startTime;

      if (result && result.engagementMetrics && result.recommendations) {
        updateTestResult(testName, {
          status: 'passed',
          duration,
          details: {
            engagementScore: result.engagementMetrics.overallEngagement,
            recommendationsCount: result.recommendations.length,
            successPrediction: result.successPrediction || 0,
          },
        });
        addLog('info', 'ConversationOptimizer', `Test passed in ${duration}ms`, result);
      } else {
        throw new Error('Invalid response structure');
      }
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'ConversationOptimizer', 'Test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, conversationOptimizer, updateTestResult, addLog]);

  // Test Real-Time Analytics
  const testRealTimeAnalytics = useCallback(async (): Promise<void> => {
    const testName = 'Real-Time Analytics';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'RealTimeAnalytics', 'Starting analytics test');

    try {
      const startTime = Date.now();
      
      // Start session
      await realTimeAnalytics.startConversationSession(testRoomId, [testUserId1, testUserId2]);
      
      // Track some events
      await realTimeAnalytics.trackEvent(testRoomId, 'message_sent', {
        senderId: testUserId1,
        messageLength: 50,
      });
      
      await realTimeAnalytics.trackEvent(testRoomId, 'message_read', {
        readerId: testUserId2,
        messageId: 'test_msg_1',
      });
      
      // Get metrics
      const metrics = await realTimeAnalytics.getConversationMetrics(testRoomId);
      
      // Stop session
      await realTimeAnalytics.stopConversationSession(testRoomId);
      
      const duration = Date.now() - startTime;

      if (metrics && metrics.conversationId) {
        updateTestResult(testName, {
          status: 'passed',
          duration,
          details: {
            conversationId: metrics.conversationId,
            messageCount: metrics.messageCount,
            engagementScore: metrics.engagementScore,
            healthScore: metrics.healthScore,
          },
        });
        addLog('info', 'RealTimeAnalytics', `Test passed in ${duration}ms`, metrics);
      } else {
        throw new Error('Invalid metrics response');
      }
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'RealTimeAnalytics', 'Test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, realTimeAnalytics, updateTestResult, addLog]);

  // Test Load Performance
  const testLoadPerformance = useCallback(async (): Promise<void> => {
    const testName = 'Load Performance';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'LoadTest', 'Starting load performance test');

    try {
      const startTime = Date.now();
      const concurrentTests = 5;
      const promises = [];

      // Run multiple concurrent operations
      for (let i = 0; i < concurrentTests; i++) {
        const roomId = `load_test_room_${i}`;
        promises.push(
          Promise.all([
            conversationIntelligence.analyzeConversation(roomId, testUserId1, testUserId2),
            messageModeration.analyzeConversationSafety(roomId, testUserId1, testUserId2),
            conversationOptimizer.optimizeConversation(roomId, testUserId1, testUserId2),
          ])
        );
      }

      const results = await Promise.all(promises);
      const duration = Date.now() - startTime;
      const averageTime = duration / concurrentTests;

      updateTestResult(testName, {
        status: 'passed',
        duration,
        details: {
          concurrentOperations: concurrentTests,
          averageTime,
          totalOperations: concurrentTests * 3,
          successfulResults: results.length,
        },
      });
      addLog('info', 'LoadTest', `Load test passed in ${duration}ms`, {
        concurrentTests,
        averageTime,
      });
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'LoadTest', 'Load test failed', error);
    }
  }, [testUserId1, testUserId2, conversationIntelligence, messageModeration, conversationOptimizer, updateTestResult, addLog]);

  // Test Error Handling
  const testErrorHandling = useCallback(async (): Promise<void> => {
    const testName = 'Error Handling';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'ErrorHandling', 'Starting error handling test');

    try {
      const startTime = Date.now();
      let errorsCaught = 0;

      // Test with invalid room ID
      try {
        await conversationIntelligence.analyzeConversation('', testUserId1, testUserId2);
      } catch {
        errorsCaught++;
      }

      // Test with invalid user IDs
      try {
        await messageModeration.analyzeConversationSafety(testRoomId, '', '');
      } catch {
        errorsCaught++;
      }

      // Test with null parameters
      try {
        await conversationOptimizer.optimizeConversation(testRoomId, testUserId1, '');
      } catch {
        errorsCaught++;
      }

      const duration = Date.now() - startTime;

      if (errorsCaught >= 2) {
        updateTestResult(testName, {
          status: 'passed',
          duration,
          details: {
            errorsCaught,
            totalTests: 3,
            errorHandlingRate: (errorsCaught / 3) * 100,
          },
        });
        addLog('info', 'ErrorHandling', `Error handling test passed in ${duration}ms`, {
          errorsCaught,
        });
      } else {
        throw new Error(`Only ${errorsCaught} errors caught out of 3 expected`);
      }
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'ErrorHandling', 'Error handling test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, conversationIntelligence, messageModeration, conversationOptimizer, updateTestResult, addLog]);

  // Test Memory Management
  const testMemoryManagement = useCallback(async (): Promise<void> => {
    const testName = 'Memory Management';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'MemoryTest', 'Starting memory management test');

    try {
      const startTime = Date.now();
      const initialMemory = performance.memory?.usedJSHeapSize || 0;

      // Create multiple sessions and clean them up
      const sessionIds = [];
      for (let i = 0; i < 10; i++) {
        const sessionId = `memory_test_${i}`;
        sessionIds.push(sessionId);
        await realTimeAnalytics.startConversationSession(sessionId, [testUserId1, testUserId2]);
      }

      // Clean up sessions
      for (const sessionId of sessionIds) {
        await realTimeAnalytics.stopConversationSession(sessionId);
      }

      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryDelta = finalMemory - initialMemory;
      const duration = Date.now() - startTime;

      updateTestResult(testName, {
        status: 'passed',
        duration,
        details: {
          sessionsCreated: sessionIds.length,
          initialMemory,
          finalMemory,
          memoryDelta,
          memoryLeakDetected: memoryDelta > 1000000, // 1MB threshold
        },
      });
      addLog('info', 'MemoryTest', `Memory test passed in ${duration}ms`, {
        memoryDelta,
        sessionsCreated: sessionIds.length,
      });
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'MemoryTest', 'Memory test failed', error);
    }
  }, [testUserId1, testUserId2, realTimeAnalytics, updateTestResult, addLog]);

  // Test Integration Flow
  const testIntegrationFlow = useCallback(async (): Promise<void> => {
    const testName = 'Integration Flow';
    updateTestResult(testName, { status: 'running' });
    addLog('info', 'Integration', 'Starting integration flow test');

    try {
      const startTime = Date.now();

      // Start analytics session
      await realTimeAnalytics.startConversationSession(testRoomId, [testUserId1, testUserId2]);

      // Simulate message flow with AI analysis
      const [intelligence, moderation, optimization] = await Promise.all([
        conversationIntelligence.analyzeConversation(testRoomId, testUserId1, testUserId2),
        messageModeration.analyzeConversationSafety(testRoomId, testUserId1, testUserId2),
        conversationOptimizer.optimizeConversation(testRoomId, testUserId1, testUserId2),
      ]);

      // Track events based on AI results
      await realTimeAnalytics.trackEvent(testRoomId, 'ai_analysis_complete', {
        healthScore: intelligence.conversationHealth?.overallHealth || 0,
        riskLevel: moderation.overallRisk,
        engagementScore: optimization.engagementMetrics?.overallEngagement || 0,
      });

      // Get final metrics
      const metrics = await realTimeAnalytics.getConversationMetrics(testRoomId);

      // Clean up
      await realTimeAnalytics.stopConversationSession(testRoomId);

      const duration = Date.now() - startTime;

      updateTestResult(testName, {
        status: 'passed',
        duration,
        details: {
          aiAnalysisComplete: !!(intelligence && moderation && optimization),
          metricsGenerated: !!metrics,
          healthScore: intelligence.conversationHealth?.overallHealth || 0,
          riskLevel: moderation.overallRisk,
          engagementScore: optimization.engagementMetrics?.overallEngagement || 0,
        },
      });
      addLog('info', 'Integration', `Integration test passed in ${duration}ms`, metrics);
    } catch (error) {
      updateTestResult(testName, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      addLog('error', 'Integration', 'Integration test failed', error);
    }
  }, [testRoomId, testUserId1, testUserId2, realTimeAnalytics, conversationIntelligence, messageModeration, conversationOptimizer, updateTestResult, addLog]);

  // Run all tests
  const runAllTests = useCallback(async () => {
    if (isRunningTests) return;

    setIsRunningTests(true);
    addLog('info', 'TestRunner', 'Starting comprehensive test suite');

    try {
      await testConversationIntelligence();
      await testMessageModeration();
      await testConversationOptimizer();
      await testRealTimeAnalytics();
      await testLoadPerformance();
      await testErrorHandling();
      await testMemoryManagement();
      await testIntegrationFlow();

      addLog('info', 'TestRunner', 'All tests completed');
    } catch (error) {
      addLog('error', 'TestRunner', 'Test suite failed', error);
    } finally {
      setIsRunningTests(false);
    }
  }, [
    isRunningTests,
    testConversationIntelligence,
    testMessageModeration,
    testConversationOptimizer,
    testRealTimeAnalytics,
    testLoadPerformance,
    testErrorHandling,
    testMemoryManagement,
    testIntegrationFlow,
    addLog,
  ]);

  // Update performance metrics
  const updatePerformanceMetrics = useCallback(() => {
    const passedTests = testResults.filter(t => t.status === 'passed');
    const failedTests = testResults.filter(t => t.status === 'failed');
    const totalTests = passedTests.length + failedTests.length;

    const avgResponseTime = passedTests.length > 0
      ? passedTests.reduce((sum, t) => sum + (t.duration || 0), 0) / passedTests.length
      : 0;

    setPerformanceMetrics({
      aiAnalysisTime: passedTests.find(t => t.testName === 'AI Conversation Intelligence')?.duration || 0,
      moderationTime: passedTests.find(t => t.testName === 'AI Message Moderation')?.duration || 0,
      optimizationTime: passedTests.find(t => t.testName === 'Conversation Optimizer')?.duration || 0,
      memoryUsage: performance.memory?.usedJSHeapSize || 0,
      activeSubscriptions: 0, // Would be tracked by real-time analytics
      eventsProcessed: logs.length,
      averageResponseTime: avgResponseTime,
      successRate: totalTests > 0 ? (passedTests.length / totalTests) * 100 : 0,
      errorRate: totalTests > 0 ? (failedTests.length / totalTests) * 100 : 0,
    });
  }, [testResults, logs.length]);

  // Update system health
  const updateSystemHealth = useCallback(() => {
    const recentTests = testResults.filter(t => 
      Date.now() - t.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );

    const passedRecent = recentTests.filter(t => t.status === 'passed').length;
    const totalRecent = recentTests.length;
    const healthRatio = totalRecent > 0 ? passedRecent / totalRecent : 1;

    let overall: SystemHealth['overall'] = 'excellent';
    if (healthRatio < 0.9) overall = 'good';
    if (healthRatio < 0.7) overall = 'fair';
    if (healthRatio < 0.5) overall = 'poor';

    setSystemHealth({
      overall,
      aiServices: recentTests.some(t => t.testName.includes('AI') && t.status === 'failed') ? 'degraded' : 'online',
      realTimeAnalytics: recentTests.some(t => t.testName === 'Real-Time Analytics' && t.status === 'failed') ? 'error' : 'active',
      messageModeration: recentTests.some(t => t.testName === 'AI Message Moderation' && t.status === 'failed') ? 'error' : 'active',
      conversationOptimizer: recentTests.some(t => t.testName === 'Conversation Optimizer' && t.status === 'failed') ? 'error' : 'active',
      lastHealthCheck: new Date(),
    });
  }, [testResults]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      updatePerformanceMetrics();
      updateSystemHealth();
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, updatePerformanceMetrics, updateSystemHealth]);

  // Initial metrics update
  useEffect(() => {
    updatePerformanceMetrics();
    updateSystemHealth();
  }, [testResults, updatePerformanceMetrics, updateSystemHealth]);

  // Get status color
  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return theme.colors.success;
      case 'failed': return theme.colors.error;
      case 'running': return theme.colors.warning;
      default: return theme.colors.textSecondary;
    }
  };

  // Get health color
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': case 'online': case 'active': return theme.colors.success;
      case 'good': case 'degraded': return theme.colors.warning;
      case 'fair': case 'inactive': return theme.colors.error;
      case 'poor': case 'offline': case 'error': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  // Render test results tab
  const renderTestsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.testControls}>
        <View style={styles.testInputs}>
          <TextInput
            style={styles.input}
            placeholder="Room ID"
            value={testRoomId}
            onChangeText={setTestRoomId}
          />
          <TextInput
            style={styles.input}
            placeholder="User ID 1"
            value={testUserId1}
            onChangeText={setTestUserId1}
          />
          <TextInput
            style={styles.input}
            placeholder="User ID 2"
            value={testUserId2}
            onChangeText={setTestUserId2}
          />
        </View>
        
        <TouchableOpacity
          style={[styles.button, styles.primaryButton, isRunningTests && styles.disabledButton]}
          onPress={runAllTests}
          disabled={isRunningTests}
        >
          {isRunningTests ? (
            <ActivityIndicator color={theme.colors.white} size="small" />
          ) : (
            <Text style={styles.buttonText}>Run All Tests</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.testResults}>
        {testResults.map((test, index) => (
          <View key={index} style={styles.testResult}>
            <View style={styles.testHeader}>
              <Text style={styles.testName}>{test.testName}</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(test.status) }]}>
                <Text style={styles.statusText}>{test.status.toUpperCase()}</Text>
              </View>
            </View>
            
            {test.duration && (
              <Text style={styles.testDuration}>{test.duration}ms</Text>
            )}
            
            {test.error && (
              <Text style={styles.testError}>{test.error}</Text>
            )}
            
            {test.details && (
              <View style={styles.testDetails}>
                <Text style={styles.detailsText}>
                  {JSON.stringify(test.details, null, 2)}
                </Text>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );

  // Render metrics tab
  const renderMetricsTab = () => (
    <View style={styles.tabContent}>
      <ScrollView style={styles.metricsContainer}>
        <View style={styles.metricCard}>
          <Text style={styles.metricTitle}>Performance Metrics</Text>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>AI Analysis Time:</Text>
            <Text style={styles.metricValue}>{performanceMetrics.aiAnalysisTime}ms</Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Moderation Time:</Text>
            <Text style={styles.metricValue}>{performanceMetrics.moderationTime}ms</Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Optimization Time:</Text>
            <Text style={styles.metricValue}>{performanceMetrics.optimizationTime}ms</Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Average Response:</Text>
            <Text style={styles.metricValue}>{Math.round(performanceMetrics.averageResponseTime)}ms</Text>
          </View>
        </View>

        <View style={styles.metricCard}>
          <Text style={styles.metricTitle}>Success Rates</Text>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Success Rate:</Text>
            <Text style={[styles.metricValue, { color: theme.colors.success }]}>
              {performanceMetrics.successRate.toFixed(1)}%
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Error Rate:</Text>
            <Text style={[styles.metricValue, { color: theme.colors.error }]}>
              {performanceMetrics.errorRate.toFixed(1)}%
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Events Processed:</Text>
            <Text style={styles.metricValue}>{performanceMetrics.eventsProcessed}</Text>
          </View>
        </View>

        <View style={styles.metricCard}>
          <Text style={styles.metricTitle}>System Resources</Text>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Memory Usage:</Text>
            <Text style={styles.metricValue}>
              {(performanceMetrics.memoryUsage / 1024 / 1024).toFixed(1)}MB
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Active Subscriptions:</Text>
            <Text style={styles.metricValue}>{performanceMetrics.activeSubscriptions}</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  // Render health tab
  const renderHealthTab = () => (
    <View style={styles.tabContent}>
      <ScrollView style={styles.healthContainer}>
        <View style={styles.healthCard}>
          <Text style={styles.healthTitle}>System Health</Text>
          <View style={styles.healthRow}>
            <Text style={styles.healthLabel}>Overall:</Text>
            <Text style={[styles.healthValue, { color: getHealthColor(systemHealth.overall) }]}>
              {systemHealth.overall.toUpperCase()}
            </Text>
          </View>
          <Text style={styles.healthTimestamp}>
            Last check: {systemHealth.lastHealthCheck.toLocaleTimeString()}
          </Text>
        </View>

        <View style={styles.healthCard}>
          <Text style={styles.healthTitle}>Service Status</Text>
          <View style={styles.healthRow}>
            <Text style={styles.healthLabel}>AI Services:</Text>
            <Text style={[styles.healthValue, { color: getHealthColor(systemHealth.aiServices) }]}>
              {systemHealth.aiServices.toUpperCase()}
            </Text>
          </View>
          <View style={styles.healthRow}>
            <Text style={styles.healthLabel}>Real-Time Analytics:</Text>
            <Text style={[styles.healthValue, { color: getHealthColor(systemHealth.realTimeAnalytics) }]}>
              {systemHealth.realTimeAnalytics.toUpperCase()}
            </Text>
          </View>
          <View style={styles.healthRow}>
            <Text style={styles.healthLabel}>Message Moderation:</Text>
            <Text style={[styles.healthValue, { color: getHealthColor(systemHealth.messageModeration) }]}>
              {systemHealth.messageModeration.toUpperCase()}
            </Text>
          </View>
          <View style={styles.healthRow}>
            <Text style={styles.healthLabel}>Conversation Optimizer:</Text>
            <Text style={[styles.healthValue, { color: getHealthColor(systemHealth.conversationOptimizer) }]}>
              {systemHealth.conversationOptimizer.toUpperCase()}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  // Render logs tab
  const renderLogsTab = () => (
    <View style={styles.tabContent}>
      <ScrollView style={styles.logsContainer}>
        {logs.map((log, index) => (
          <View key={index} style={styles.logEntry}>
            <View style={styles.logHeader}>
              <Text style={styles.logTimestamp}>
                {log.timestamp.toLocaleTimeString()}
              </Text>
              <Text style={[styles.logLevel, { color: getHealthColor(log.level) }]}>
                {log.level.toUpperCase()}
              </Text>
              <Text style={styles.logService}>{log.service}</Text>
            </View>
            <Text style={styles.logMessage}>{log.message}</Text>
            {log.data && (
              <Text style={styles.logData}>
                {JSON.stringify(log.data, null, 2)}
              </Text>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Real-Time AI Messaging Debugger</Text>
        <View style={styles.headerControls}>
          <View style={styles.autoRefreshContainer}>
            <Text style={styles.autoRefreshLabel}>Auto Refresh</Text>
            <Switch
              value={autoRefresh}
              onValueChange={setAutoRefresh}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            />
          </View>
        </View>
      </View>

      <View style={styles.tabBar}>
        {(['tests', 'metrics', 'health', 'logs'] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, selectedTab === tab && styles.activeTab]}
            onPress={() => setSelectedTab(tab)}
          >
            <Text style={[styles.tabText, selectedTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {selectedTab === 'tests' && renderTestsTab()}
      {selectedTab === 'metrics' && renderMetricsTab()}
      {selectedTab === 'health' && renderHealthTab()}
      {selectedTab === 'logs' && renderLogsTab()}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 18,
    fontFamily: theme.typography.bold.fontFamily,
    color: theme.colors.text,
  },
  headerControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  autoRefreshContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  autoRefreshLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.medium.fontFamily,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.medium.fontFamily,
  },
  activeTabText: {
    color: theme.colors.primary,
    fontFamily: theme.typography.semiBold.fontFamily,
  },
  tabContent: {
    flex: 1,
  },
  testControls: {
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  testInputs: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.sm,
    padding: theme.spacing.sm,
    fontSize: 14,
    color: theme.colors.text,
    backgroundColor: theme.colors.surface,
  },
  button: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 40,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  disabledButton: {
    backgroundColor: theme.colors.textSecondary,
  },
  buttonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontFamily: theme.typography.semiBold.fontFamily,
  },
  testResults: {
    flex: 1,
    padding: theme.spacing.md,
  },
  testResult: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  testName: {
    fontSize: 16,
    fontFamily: theme.typography.semiBold.fontFamily,
    color: theme.colors.text,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.white,
    fontFamily: theme.typography.bold.fontFamily,
  },
  testDuration: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.regular.fontFamily,
    marginBottom: theme.spacing.xs,
  },
  testError: {
    fontSize: 14,
    color: theme.colors.error,
    fontFamily: theme.typography.regular.fontFamily,
    marginBottom: theme.spacing.xs,
  },
  testDetails: {
    backgroundColor: theme.colors.backgroundSecondary,
    borderRadius: theme.borderRadius.sm,
    padding: theme.spacing.sm,
  },
  detailsText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
  },
  metricsContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  metricCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricTitle: {
    fontSize: 16,
    fontFamily: theme.typography.semiBold.fontFamily,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  metricLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.regular.fontFamily,
  },
  metricValue: {
    fontSize: 14,
    color: theme.colors.text,
    fontFamily: theme.typography.semiBold.fontFamily,
  },
  healthContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  healthCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  healthTitle: {
    fontSize: 16,
    fontFamily: theme.typography.semiBold.fontFamily,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  healthRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  healthLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.regular.fontFamily,
  },
  healthValue: {
    fontSize: 14,
    fontFamily: theme.typography.semiBold.fontFamily,
  },
  healthTimestamp: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: theme.typography.regular.fontFamily,
    marginTop: theme.spacing.sm,
  },
  logsContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  logEntry: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.sm,
    padding: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  logHeader: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  logTimestamp: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
  },
  logLevel: {
    fontSize: 12,
    fontFamily: theme.typography.bold.fontFamily,
  },
  logService: {
    fontSize: 12,
    color: theme.colors.primary,
    fontFamily: theme.typography.medium.fontFamily,
  },
  logMessage: {
    fontSize: 13,
    color: theme.colors.text,
    fontFamily: theme.typography.regular.fontFamily,
    marginBottom: theme.spacing.xs,
  },
  logData: {
    fontSize: 11,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
    backgroundColor: theme.colors.backgroundSecondary,
    padding: theme.spacing.xs,
    borderRadius: theme.borderRadius.xs,
  },
});

export default RealTimeAIMessagingDebugger;
