import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { ChatToAgreementFlow } from '@services/unified/chatToAgreement';
import { useAuth } from '@hooks/useAuth';
import { useRouter } from 'expo-router';

export default function AgreementCreationTest() {
  const [isCreating, setIsCreating] = useState(false);
  const [testResult, setTestResult] = useState<string>('');
  const { authState } = useAuth();
  const router = useRouter();

  const testAgreementCreation = async () => {
    if (!authState?.user?.id) {
      Alert.alert('Error', 'Must be logged in to test agreement creation');
      return;
    }

    setIsCreating(true);
    setTestResult('Testing agreement creation...');

    try {
      // Use test IDs for the flow
      const testChatRoomId = 'test-room-123';
      const testOtherUserId = 'test-other-user-456';
      
      console.log('🧪 Testing agreement creation with:', {
        chatRoomId: testChatRoomId,
        userId: authState.user.id,
        otherUserId: testOtherUserId
      });

      const result = await ChatToAgreementFlow.initiateAgreementFromChat(
        testChatRoomId,
        authState.user.id,
        testOtherUserId
      );

      if (result.success && result.agreementId) {
        setTestResult(`✅ Agreement created successfully! ID: ${result.agreementId}`);
        
        // Test navigation
        try {
          router.push({
            pathname: `/agreement/details/${result.agreementId}` as any,
            params: {
              id: result.agreementId,
              source: 'test'
            }
          });
          setTestResult(prev => prev + '\n✅ Navigation successful!');
        } catch (navError) {
          setTestResult(prev => prev + `\n❌ Navigation failed: ${navError}`);
        }
      } else {
        setTestResult(`❌ Agreement creation failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Agreement creation test error:', error);
      setTestResult(`❌ Test error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Agreement Creation Test</Text>
      
      <TouchableOpacity
        style={[styles.button, isCreating && styles.buttonDisabled]}
        onPress={testAgreementCreation}
        disabled={isCreating}
      >
        <Text style={styles.buttonText}>
          {isCreating ? 'Creating Agreement...' : 'Test Agreement Creation'}
        </Text>
      </TouchableOpacity>

      {testResult ? (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Test Result:</Text>
          <Text style={styles.resultText}>{testResult}</Text>
        </View>
      ) : null}

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          This test will:
          {'\n'}• Create a test agreement
          {'\n'}• Test navigation to agreement details
          {'\n'}• Show any errors that occur
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  resultContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  infoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    lineHeight: 20,
  },
}); 