import React, { memo, useState, useEffect, useCallback, useMemo } from 'react';
import { 
  View, 
  ScrollView, 
  StyleSheet, 
  Dimensions, 
  TouchableOpacity,
  Switch,
  Alert
} from 'react-native';
import { Text } from '@components/ui';
import { PerformanceMonitor } from '@utils/performanceMonitor';
import { queryCache } from '@utils/queryCache';
import { MessageListPerformanceMonitor } from '@components/chat/VirtualizedMessageList';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Bar<PERSON><PERSON>, LineChart } from 'react-native-chart-kit';
import { 
  Activity, 
  Database, 
  Zap, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings
} from 'lucide-react-native';

const SCREEN_WIDTH = Dimensions.get('window').width;

interface PerformanceMetrics {
  fcp: number;
  lcp: number;
  fid: number;
  cls: number;
  memoryUsage: number;
  bundleSize: number;
  databaseQueries: number;
  cacheHitRate: number;
  renderCount: number;
  networkRequests: number;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  metric: string;
  message: string;
  timestamp: Date;
  value: number;
  threshold: number;
}

const PerformanceDashboard: React.FC = () => {
  const theme = useTheme();
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics>({
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    memoryUsage: 0,
    bundleSize: 0,
    databaseQueries: 0,
    cacheHitRate: 0,
    renderCount: 0,
    networkRequests: 0,
  });
  const [historicalData, setHistoricalData] = useState<PerformanceMetrics[]>([]);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'detailed' | 'alerts'>('overview');

  // Performance thresholds
  const thresholds = useMemo(() => ({
    fcp: 1.5, // seconds
    lcp: 2.5, // seconds
    fid: 100, // milliseconds
    cls: 0.1, // cumulative layout shift score
    memoryUsage: 100, // MB
    bundleSize: 5, // MB
    databaseQueries: 50, // per minute
    cacheHitRate: 80, // percentage
    renderCount: 100, // per minute
    networkRequests: 30, // per minute
  }), []);

  // Generate performance alert
  const generateAlert = useCallback((
    metric: string, 
    value: number, 
    threshold: number, 
    type: 'warning' | 'error' | 'info' = 'warning'
  ) => {
    const alert: PerformanceAlert = {
      id: `${metric}-${Date.now()}`,
      type,
      metric,
      message: `${metric} exceeded threshold: ${value.toFixed(2)} > ${threshold}`,
      timestamp: new Date(),
      value,
      threshold,
    };

    setAlerts(prev => [alert, ...prev.slice(0, 49)]); // Keep only last 50 alerts
  }, []);

  // Check performance thresholds
  const checkThresholds = useCallback((metrics: PerformanceMetrics) => {
    Object.entries(thresholds).forEach(([key, threshold]) => {
      const value = metrics[key as keyof PerformanceMetrics];
      if (value > threshold) {
        generateAlert(key, value, threshold, value > threshold * 1.5 ? 'error' : 'warning');
      }
    });
  }, [thresholds, generateAlert]);

  // Collect current performance metrics
  const collectMetrics = useCallback(async (): Promise<PerformanceMetrics> => {
    try {
      const memInfo = await PerformanceMonitor.getMemoryInfo();
      const renderStats = PerformanceMonitor.getRenderStats();
      const cacheStats = queryCache.getStats();

      // Simulate some metrics that would come from actual monitoring
      const metrics: PerformanceMetrics = {
        fcp: Math.random() * 3, // Simulated FCP
        lcp: Math.random() * 4, // Simulated LCP
        fid: Math.random() * 200, // Simulated FID
        cls: Math.random() * 0.3, // Simulated CLS
        memoryUsage: memInfo.usedJSHeapSize / (1024 * 1024), // Convert to MB
        bundleSize: 2.5, // Would be calculated from bundle analyzer
        databaseQueries: Math.floor(Math.random() * 100),
        cacheHitRate: (cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100 || 0,
        renderCount: renderStats.totalRenders,
        networkRequests: Math.floor(Math.random() * 50),
      };

      return metrics;
    } catch (error) {
      console.error('Error collecting performance metrics:', error);
      return currentMetrics;
    }
  }, [currentMetrics]);

  // Update metrics periodically
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(async () => {
      const metrics = await collectMetrics();
      setCurrentMetrics(metrics);
      setHistoricalData(prev => [...prev.slice(-29), metrics]); // Keep last 30 data points
      checkThresholds(metrics);
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isMonitoring, collectMetrics, checkThresholds]);

  // Get performance score
  const getPerformanceScore = useCallback((metrics: PerformanceMetrics): number => {
    const scores = {
      fcp: Math.max(0, 100 - (metrics.fcp / thresholds.fcp) * 100),
      lcp: Math.max(0, 100 - (metrics.lcp / thresholds.lcp) * 100),
      fid: Math.max(0, 100 - (metrics.fid / thresholds.fid) * 100),
      cls: Math.max(0, 100 - (metrics.cls / thresholds.cls) * 100),
      memory: Math.max(0, 100 - (metrics.memoryUsage / thresholds.memoryUsage) * 100),
      cache: metrics.cacheHitRate,
    };

    return Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;
  }, [thresholds]);

  // Get status color based on score
  const getStatusColor = useCallback((score: number): string => {
    if (score >= 90) return theme.colors.success;
    if (score >= 70) return theme.colors.warning;
    return theme.colors.error;
  }, [theme]);

  // Performance overview cards
  const renderOverviewCards = () => {
    const score = getPerformanceScore(currentMetrics);
    const statusColor = getStatusColor(score);

    return (
      <View style={styles.cardsContainer}>
        {/* Overall Performance Score */}
        <View style={[styles.card, { borderLeftColor: statusColor }]}>
          <View style={styles.cardHeader}>
            <TrendingUp size={20} color={statusColor} />
            <Text style={styles.cardTitle}>Performance Score</Text>
          </View>
          <Text style={[styles.cardValue, { color: statusColor }]}>
            {score.toFixed(0)}%
          </Text>
          <Text style={styles.cardDescription}>
            {score >= 90 ? 'Excellent' : score >= 70 ? 'Good' : 'Needs Improvement'}
          </Text>
        </View>

        {/* Core Web Vitals */}
        <View style={[styles.card, { borderLeftColor: theme.colors.primary }]}>
          <View style={styles.cardHeader}>
            <Zap size={20} color={theme.colors.primary} />
            <Text style={styles.cardTitle}>Core Web Vitals</Text>
          </View>
          <View style={styles.vitalsContainer}>
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>FCP</Text>
              <Text style={[
                styles.vitalValue,
                { color: currentMetrics.fcp > thresholds.fcp ? theme.colors.error : theme.colors.success }
              ]}>
                {currentMetrics.fcp.toFixed(2)}s
              </Text>
            </View>
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>LCP</Text>
              <Text style={[
                styles.vitalValue,
                { color: currentMetrics.lcp > thresholds.lcp ? theme.colors.error : theme.colors.success }
              ]}>
                {currentMetrics.lcp.toFixed(2)}s
              </Text>
            </View>
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>FID</Text>
              <Text style={[
                styles.vitalValue,
                { color: currentMetrics.fid > thresholds.fid ? theme.colors.error : theme.colors.success }
              ]}>
                {currentMetrics.fid.toFixed(0)}ms
              </Text>
            </View>
          </View>
        </View>

        {/* Memory Usage */}
        <View style={[styles.card, { borderLeftColor: theme.colors.info }]}>
          <View style={styles.cardHeader}>
            <Activity size={20} color={theme.colors.info} />
            <Text style={styles.cardTitle}>Memory Usage</Text>
          </View>
          <Text style={[styles.cardValue, { color: theme.colors.info }]}>
            {currentMetrics.memoryUsage.toFixed(1)} MB
          </Text>
          <Text style={styles.cardDescription}>
            {MessageListPerformanceMonitor.estimateMemoryUsage(1000).recommendation}
          </Text>
        </View>

        {/* Cache Performance */}
        <View style={[styles.card, { borderLeftColor: theme.colors.success }]}>
          <View style={styles.cardHeader}>
            <Database size={20} color={theme.colors.success} />
            <Text style={styles.cardTitle}>Cache Hit Rate</Text>
          </View>
          <Text style={[styles.cardValue, { color: theme.colors.success }]}>
            {currentMetrics.cacheHitRate.toFixed(1)}%
          </Text>
          <Text style={styles.cardDescription}>
            {queryCache.getStats().totalItems} cached items
          </Text>
        </View>
      </View>
    );
  };

  // Render performance charts
  const renderCharts = () => {
    if (historicalData.length < 2) {
      return (
        <View style={styles.emptyChart}>
          <Clock size={40} color={theme.colors.textSecondary} />
          <Text style={styles.emptyChartText}>Collecting data...</Text>
        </View>
      );
    }

    const chartConfig = {
      backgroundColor: theme.colors.background,
      backgroundGradientFrom: theme.colors.background,
      backgroundGradientTo: theme.colors.background,
      decimalPlaces: 2,
      color: (opacity = 1) => `rgba(34, 128, 176, ${opacity})`,
      labelColor: () => theme.colors.text,
      style: {
        borderRadius: 16
      },
      propsForDots: {
        r: "6",
        strokeWidth: "2",
        stroke: theme.colors.primary
      }
    };

    const labels = historicalData.slice(-10).map((_, index) => `${index + 1}`);
    
    return (
      <View style={styles.chartsContainer}>
        {/* Core Web Vitals Chart */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Core Web Vitals Trends</Text>
          <LineChart
            data={{
              labels,
              datasets: [
                {
                  data: historicalData.slice(-10).map(d => d.fcp),
                  color: () => theme.colors.primary,
                  strokeWidth: 2
                },
                {
                  data: historicalData.slice(-10).map(d => d.lcp),
                  color: () => theme.colors.warning,
                  strokeWidth: 2
                }
              ],
              legend: ["FCP", "LCP"]
            }}
            width={SCREEN_WIDTH - 32}
            height={220}
            chartConfig={chartConfig}
          />
        </View>

        {/* Memory Usage Chart */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Memory Usage Over Time</Text>
          <LineChart
            data={{
              labels,
              datasets: [{
                data: historicalData.slice(-10).map(d => d.memoryUsage)
              }]
            }}
            width={SCREEN_WIDTH - 32}
            height={220}
            chartConfig={chartConfig}
          />
        </View>
      </View>
    );
  };

  // Render alerts list
  const renderAlerts = () => {
    if (alerts.length === 0) {
      return (
        <View style={styles.emptyAlerts}>
          <CheckCircle size={40} color={theme.colors.success} />
          <Text style={styles.emptyAlertsText}>No performance alerts</Text>
          <Text style={styles.emptyAlertsSubText}>Your app is performing well!</Text>
        </View>
      );
    }

    return (
      <View style={styles.alertsContainer}>
        {alerts.slice(0, 20).map((alert) => (
          <View 
            key={alert.id} 
            style={[
              styles.alertItem,
              { borderLeftColor: alert.type === 'error' ? theme.colors.error : 
                                alert.type === 'warning' ? theme.colors.warning : theme.colors.info }
            ]}
          >
            <View style={styles.alertHeader}>
              {alert.type === 'error' ? 
                <XCircle size={16} color={theme.colors.error} /> :
                alert.type === 'warning' ?
                <AlertTriangle size={16} color={theme.colors.warning} /> :
                <CheckCircle size={16} color={theme.colors.info} />
              }
              <Text style={styles.alertMetric}>{alert.metric.toUpperCase()}</Text>
              <Text style={styles.alertTime}>
                {alert.timestamp.toLocaleTimeString()}
              </Text>
            </View>
            <Text style={styles.alertMessage}>{alert.message}</Text>
          </View>
        ))}
      </View>
    );
  };

  // Clear all alerts
  const clearAlerts = useCallback(() => {
    Alert.alert(
      'Clear Alerts',
      'Are you sure you want to clear all performance alerts?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: () => setAlerts([]) }
      ]
    );
  }, []);

  // Export performance report
  const exportReport = useCallback(() => {
    const report = {
      timestamp: new Date().toISOString(),
      currentMetrics,
      performanceScore: getPerformanceScore(currentMetrics),
      alerts: alerts.slice(0, 10),
      recommendations: MessageListPerformanceMonitor.getPerformanceRecommendations(1000),
    };

    console.log('📊 Performance Report:', JSON.stringify(report, null, 2));
    Alert.alert('Report Exported', 'Performance report has been logged to console');
  }, [currentMetrics, alerts, getPerformanceScore]);

  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Performance Dashboard</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={exportReport} style={styles.headerButton}>
            <RefreshCw size={20} color={theme.colors.primary} />
          </TouchableOpacity>
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Monitor</Text>
            <Switch
              value={isMonitoring}
              onValueChange={setIsMonitoring}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={isMonitoring ? theme.colors.background : theme.colors.textSecondary}
            />
          </View>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {(['overview', 'detailed', 'alerts'] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, selectedTab === tab && styles.activeTab]}
            onPress={() => setSelectedTab(tab)}
          >
            <Text style={[
              styles.tabText,
              selectedTab === tab && styles.activeTabText
            ]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
              {tab === 'alerts' && alerts.length > 0 && (
                <Text style={styles.alertBadge}> ({alerts.length})</Text>
              )}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && renderOverviewCards()}
        {selectedTab === 'detailed' && renderCharts()}
        {selectedTab === 'alerts' && (
          <>
            {alerts.length > 0 && (
              <TouchableOpacity onPress={clearAlerts} style={styles.clearAlertsButton}>
                <Text style={styles.clearAlertsText}>Clear All Alerts</Text>
              </TouchableOpacity>
            )}
            {renderAlerts()}
          </>
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  switchLabel: {
    fontSize: 14,
    color: theme.colors.text,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    margin: 16,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  activeTabText: {
    color: theme.colors.background,
  },
  alertBadge: {
    fontSize: 12,
    color: theme.colors.error,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  cardsContainer: {
    gap: 16,
  },
  card: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  cardValue: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  vitalsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  vitalItem: {
    alignItems: 'center',
  },
  vitalLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  vitalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  chartsContainer: {
    gap: 24,
  },
  chartContainer: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  emptyChart: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyChartText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  alertsContainer: {
    gap: 12,
  },
  alertItem: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  alertMetric: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  alertTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  alertMessage: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  emptyAlerts: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyAlertsText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 12,
  },
  emptyAlertsSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  clearAlertsButton: {
    backgroundColor: theme.colors.error,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  clearAlertsText: {
    color: theme.colors.background,
    fontWeight: '600',
  },
});

export default memo(PerformanceDashboard); 