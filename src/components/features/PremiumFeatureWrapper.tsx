import React, { ReactNode } from 'react';

import { useRouter } from 'expo-router';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { PremiumBadge } from '@components/badges/PremiumBadge';
import { useSubscription } from '@context/SubscriptionContext';
import { theme } from '@design-system';

interface PremiumFeatureWrapperProps {
  children: ReactNode;
  featureName: string;
  description?: string;
  hideContentWhenLocked?: boolean;
  lockIcon?: ReactNode;
  testID?: string;
}

/**
 * A wrapper component that conditionally renders content based on user's premium status
 * For premium users, it renders the children as normal
 * For non-premium users, it renders an upsell UI with an option to upgrade
 */
export function PremiumFeatureWrapper({
  children,
  featureName,
  description,
  hideContentWhenLocked = true,
  lockIcon,
  testID,
}: PremiumFeatureWrapperProps) {
  const { isPremium } = useSubscription();
  const router = useRouter();

  const handleSubscribe = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    router.push('/subscription');
  };

  // If user is premium, just render the children
  if (isPremium) {
    return <>{children}</>;
  }

  // Else show the premium upsell
  return (
    <View style={styles.container} testID={testID}>
      {!hideContentWhenLocked && <View style={styles.blurredContent}>{children}</View>}
      
      <View style={styles.premiumOverlay}>
        <View style={styles.premiumContent}>
          <PremiumBadge size="large" />
          <Text style={styles.premiumTitle}>Premium Feature</Text>
          <Text style={styles.featureName}>{featureName}</Text>
          
          {description && <Text style={styles.description}>{description}</Text>}
          
          <TouchableOpacity 
            style={styles.upgradeButton} 
            onPress={handleSubscribe}
            testID={`${testID}-upgrade-button`}
          >
            <Text style={styles.upgradeButtonText}>Unlock Premium</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    height: '100%',
  },
  blurredContent: {
    opacity: 0.3,
    filter: 'blur(3px)',
    pointerEvents: 'none',
    width: '100%',
    height: '100%',
  },
  premiumOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  premiumContent: {
    backgroundColor: theme.colors.surface,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    width: '90%',
    maxWidth: 340,
  },
  premiumTitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  featureName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginVertical: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  upgradeButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 8,
    width: '100%',
    alignItems: 'center',
  },
  upgradeButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 
export default PremiumFeatureWrapper;
