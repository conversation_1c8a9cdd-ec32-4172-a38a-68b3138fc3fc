import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { X, AlertTriangle, Shield, Flag } from 'lucide-react-native';

import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ModerationInfoPanelProps {
  messageContent: string;
  moderationResult: {
    id: string;
    severity: number;
    category: string;
    confidence: number;
    flagged: boolean;
  } | null;
  onClose: () => void;
}

export default function ModerationInfoPanel({
  messageContent,
  moderationResult,
  onClose,
}: ModerationInfoPanelProps) {
  const { colors } = useTheme();

  const getSeverityColor = (severity: number) => {
    if (severity >= 4) return colors.error;
    if (severity >= 2) return colors.warning;
    return colors.secondaryText;
  };

  const getSeverityText = (severity: number) => {
    if (severity >= 4) return 'High';
    if (severity >= 2) return 'Medium';
    return 'Low';
  };

  const formatCategory = (category: string) => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Shield size={20} color={colors.primary[500]} />
          <Text style={styles.title}>Content Moderation</Text>
        </View>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <X size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {moderationResult ? (
          <>
            <View style={styles.messageContainer}>
              <Text style={styles.messageLabel}>Flagged Content:</Text>
              <Text style={styles.messageContent}>{messageContent}</Text>
            </View>

            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Flagged:</Text>
                <View style={styles.flagIndicator}>
                  <AlertTriangle size={16} color="#FFFFFF" />
                  <Text style={styles.flagText}>Yes</Text>
                </View>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Category:</Text>
                <Text style={styles.infoValue}>{formatCategory(moderationResult.category)}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Severity:</Text>
                <Text
                  style={[styles.infoValue, { color: getSeverityColor(moderationResult.severity) }]}
                >
                  {getSeverityText(moderationResult.severity)}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Confidence:</Text>
                <Text style={styles.infoValue}>
                  {Math.round(moderationResult.confidence * 100)}%
                </Text>
              </View>
            </View>

            <View style={styles.explainCard}>
              <Text style={styles.explainTitle}>Why was this content flagged?</Text>
              <Text style={styles.explainText}>
                {moderationResult.category === 'harassment'
                  ? 'This content was flagged because it may contain harassment or bullying language that could be harmful to others.'
                  : moderationResult.category === 'hate_speech'
                    ? 'This content was flagged because it may contain hate speech or discriminatory language.'
                    : moderationResult.category === 'threatening'
                      ? 'This content was flagged because it may contain threatening language.'
                      : 'This content was flagged because it may violate community guidelines.'}
              </Text>
            </View>

            <TouchableOpacity style={styles.reportButton}>
              <Flag size={16} color="#FFFFFF" />
              <Text style={styles.reportButtonText}>Report Content</Text>
            </TouchableOpacity>
          </>
        ) : (
          <Text style={styles.noDataText}>No moderation data available.</Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#1E293B',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
    marginBottom: 6,
  },
  messageContent: {
    fontSize: 16,
    color: '#334155',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
  },
  infoCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#64748B',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E293B',
  },
  flagIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  flagText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  explainCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  explainTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  explainText: {
    fontSize: 14,
    color: '#64748B',
    lineHeight: 20,
  },
  reportButton: {
    backgroundColor: '#6366F1',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  reportButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  noDataText: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    marginVertical: 24,
  },
});
