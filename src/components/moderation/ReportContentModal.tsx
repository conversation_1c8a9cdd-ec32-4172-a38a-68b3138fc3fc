import React, { useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

import { X, AlertTriangle, CheckCircle } from 'lucide-react-native';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  ScrollView,
} from 'react-native';

;
import { useContentModeration } from '@hooks/useContentModeration';
import type { ContentType } from '@services/moderationService';

interface ReportContentModalProps {
  visible: boolean;
  onClose: () => void;
  contentId: string;
  contentType: ContentType;
  reportedUserId?: string;
}

const REPORT_REASONS = [
  'Inappropriate content',
  'Harassment or bullying',
  'Hate speech',
  'Scam or fraud',
  'False information',
  'Personal information sharing',
  'Impersonation',
  'Other',
];

export default function ReportContentModal({
  visible,
  onClose,
  contentId,
  contentType,
  reportedUserId,
}: ReportContentModalProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  // Initialize useContentModeration with empty arrays to prevent the TypeError
  const { reportContent } = useContentModeration([], '');
  // Manage loading state locally since the hook doesn't provide it
  const [isLoading, setIsLoading] = useState(false);

  const [selectedReason, setSelectedReason] = useState<string | null>(null);
  const [additionalDetails, setAdditionalDetails] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!selectedReason) {
      setError('Please select a reason for reporting');
      return;
    }

    setError(null);
    setIsLoading(true);
    
    try {
      const result = await reportContent(
        contentId,
        contentType,
        selectedReason,
        additionalDetails,
        reportedUserId
      );

      if (result) {
        setIsSubmitted(true);
      } else {
        setError('Failed to submit report. Please try again.');
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Reset state when closing
    setSelectedReason(null);
    setAdditionalDetails('');
    setIsSubmitted(false);
    setError(null);
    onClose();
  };

  return (
    <Modal visible={visible} transparent={true} animationType="slide" onRequestClose={handleClose}>
      <View style={styles.container}>
        <View style={[styles.modal, { backgroundColor: theme.colors.white }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.neutral }]}>
              {isSubmitted ? 'Report Submitted' : 'Report Content'}
            </Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.neutral} />
            </TouchableOpacity>
          </View>

          {isSubmitted ? (
            <View style={styles.successContainer}>
              <CheckCircle size={48} color={theme.colors.success} />
              <Text style={[styles.successText, { color: theme.colors.neutral }]}>
                Thank you for your report. Our team will review this content.
              </Text>
              <TouchableOpacity
                style={[styles.button, { backgroundColor: theme.colors.primary }]}
                onPress={handleClose}
              >
                <Text style={[styles.buttonText, { color: theme.colors.white }]}>Close</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              <ScrollView style={styles.content}>
                <Text style={[styles.label, { color: theme.colors.neutral }]}>
                  Why are you reporting this content?
                </Text>

                {REPORT_REASONS.map(reason => (
                  <TouchableOpacity
                    key={reason}
                    style={[
                      styles.reasonOption,
                      selectedReason === reason && {
                        backgroundColor: theme.colors.primary,
                        borderColor: theme.colors.primary,
                      },
                    ]}
                    onPress={() => setSelectedReason(reason)}
                  >
                    <Text
                      style={[
                        styles.reasonText,
                        {
                          color: selectedReason === reason ? theme.colors.primary : theme.colors.neutral,
                        },
                      ]}
                    >
                      {reason}
                    </Text>
                  </TouchableOpacity>
                ))}

                <Text style={[styles.label, { color: theme.colors.neutral, marginTop: 16 }]}>
                  Additional details (optional)
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      borderColor: theme.colors.neutral,
                      color: theme.colors.neutral,
                      backgroundColor: theme.colors.neutral,
                    },
                  ]}
                  value={additionalDetails}
                  onChangeText={setAdditionalDetails}
                  placeholder="Provide any additional context..."
                  placeholderTextColor={theme.colors.neutral}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />

                {error && (
                  <View style={[styles.errorContainer, { backgroundColor: theme.colors.error }]}>
                    <AlertTriangle size={16} color={theme.colors.error} />
                    <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
                  </View>
                )}
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity
                  style={[styles.cancelButton, { borderColor: theme.colors.neutral }]}
                  onPress={handleClose}
                  disabled={isLoading}
                >
                  <Text style={[styles.cancelButtonText, { color: theme.colors.neutral }]}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    { backgroundColor: theme.colors.primary },
                    isLoading && { opacity: 0.7 },
                  ]}
                  onPress={handleSubmit}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color={theme.colors.white} />
                  ) : (
                    <Text style={[styles.submitButtonText, { color: theme.colors.white }]}>
                      Submit Report
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  modal: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
    maxHeight: 400,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  reasonOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    marginBottom: 8,
  },
  reasonText: {
    fontSize: 15,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    minHeight: 100,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  successContainer: {
    padding: 24,
    alignItems: 'center',
  },
  successText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
