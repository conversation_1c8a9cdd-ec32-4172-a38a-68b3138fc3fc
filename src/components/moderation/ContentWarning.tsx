import React, { useState } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

import { AlertTriangle, Eye, EyeOff, Flag } from 'lucide-react-native';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

;
import type { ModerationStatus } from '@services/moderationService';

interface ContentWarningProps {
  status: ModerationStatus;
  children: React.ReactNode;
  onReport?: () => void;
}

export default function ContentWarning({
 status, children, onReport }: ContentWarningProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [showContent, setShowContent] = useState(status === 'warning');

  if (status === 'approved') {
    return <>{children}</>;
  }

  // If content is blocked, don't allow showing it
  if (status === 'blocked') {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: theme.colors.error, borderColor: theme.colors.error },
        ]}
      >
        <AlertTriangle size={20} color={theme.colors.error} />
        <Text style={[styles.warningText, { color: theme.colors.error }]}>
          This content has been blocked for violating community guidelines.
        </Text>
        {onReport && (
          <TouchableOpacity
            style={[styles.reportButton, { borderColor: theme.colors.error }]}
            onPress={onReport}
          >
            <Flag size={16} color={theme.colors.error} />
            <Text style={[styles.reportButtonText, { color: theme.colors.error }]}>Report</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  // For warning or flagged content, allow toggling visibility
  return (
    <>
      {!showContent ? (
        <View
          style={[
            styles.container,
            {
              backgroundColor: status === 'flagged' ? theme.colors.warning : theme.colors.gray,
              borderColor: status === 'flagged' ? theme.colors.warning : theme.colors.gray,
            },
          ]}
        >
          <AlertTriangle
            size={20}
            color={status === 'flagged' ? theme.colors.warning : theme.colors.gray}
          />
          <Text
            style={[
              styles.warningText,
              { color: status === 'flagged' ? theme.colors.warning : theme.colors.gray },
            ]}
          >
            {status === 'flagged'
              ? 'This content may violate our community guidelines.'
              : 'This content may contain sensitive material.'}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.viewButton,
                {
                  backgroundColor: status === 'flagged' ? theme.colors.warning : theme.colors.gray,
                  borderColor: status === 'flagged' ? theme.colors.warning : theme.colors.gray,
                },
              ]}
              onPress={() => setShowContent(true)}
            >
              <Eye
                size={16}
                color={status === 'flagged' ? theme.colors.warning : theme.colors.gray}
              />
              <Text
                style={[
                  styles.viewButtonText,
                  { color: status === 'flagged' ? theme.colors.warning : theme.colors.gray },
                ]}
              >
                View
              </Text>
            </TouchableOpacity>

            {onReport && (
              <TouchableOpacity
                style={[
                  styles.reportButton,
                  {
                    borderColor: status === 'flagged' ? theme.colors.warning : theme.colors.gray,
                  },
                ]}
                onPress={onReport}
              >
                <Flag
                  size={16}
                  color={status === 'flagged' ? theme.colors.warning : theme.colors.gray}
                />
                <Text
                  style={[
                    styles.reportButtonText,
                    { color: status === 'flagged' ? theme.colors.warning : theme.colors.gray },
                  ]}
                >
                  Report
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      ) : (
        <View>
          {children}
          <TouchableOpacity
            style={[
              styles.hideButton,
              {
                backgroundColor: theme.colors.gray,
                borderColor: theme.colors.gray,
              },
            ]}
            onPress={() => setShowContent(false)}
          >
            <EyeOff size={14} color={theme.colors.gray} />
            <Text style={[styles.hideButtonText, { color: theme.colors.gray }]}>Hide content</Text>
          </TouchableOpacity>
        </View>
      )}
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginVertical: 8,
  },
  warningText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  reportButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  hideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    borderWidth: 1,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  hideButtonText: {
    fontSize: 12,
    marginLeft: 4,
  },
});
