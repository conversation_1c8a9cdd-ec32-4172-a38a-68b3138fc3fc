/**
 * ChatErrorBoundary Component
 * 
 * A React Error Boundary specifically for chat components that provides
 * user-friendly error messages and recovery options.
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { RefreshCw } from 'lucide-react-native';
import { AppError, ErrorCode } from '@core/errors/types';
import { errorTracker } from '@core/errors/errorTracker';
import { logger } from '@services/loggerService';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error boundary component for chat-related screens and components
 */
class ChatErrorBoundaryClass extends Component<Props & { colors: any }, State> {
  constructor(props: Props & { colors: any }) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our error tracking service
    errorTracker.captureError(error, {
      source: 'ChatErrorBoundary',
      componentStack: errorInfo.componentStack,
      isFatal: false
    });

    // Log to our logging service
    logger.error(
      'Error caught by ChatErrorBoundary', 
      'ChatErrorBoundary', 
      { error: error.message, componentStack: errorInfo.componentStack }
    );

    // Update state with error info
    this.setState({
      errorInfo
    });
  }

  handleReset = (): void => {
    // Reset the error boundary state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });

    // Call the onReset prop if provided
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  /**
   * Get a user-friendly error message based on the error
   */
  getUserFriendlyMessage(): string {
    const { error } = this.state;
    
    if (error instanceof AppError) {
      // Return the user message from the AppError
      return error.userMessage;
    }

    // Default messages based on error type
    if (error instanceof TypeError) {
      return 'Something went wrong with the chat. We\'re working on fixing it.';
    }

    if (error instanceof SyntaxError) {
      return 'We received an invalid response. Please try again later.';
    }

    // Generic fallback message
    return 'An unexpected error occurred. Please try refreshing the chat.';
  }

  render(): ReactNode {
    const { hasError } = this.state;
    const { children, fallback, colors } = this.props;

    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Otherwise, show our default error UI
      return (
        <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
          <View style={styles.errorContainer}>
            <Text style={[styles.errorTitle, { color: colors.text.primary }]}>
              Chat Error
            </Text>
            <Text style={[styles.errorMessage, { color: colors.text.secondary }]}>
              {this.getUserFriendlyMessage()}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.primary[500] }]}
              onPress={this.handleReset}
            >
              <RefreshCw size={18} color="white" style={styles.retryIcon} />
              <Text style={styles.retryText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return children;
  }
}

/**
 * Wrapper component that provides theme colors to the error boundary
 */
export function ChatErrorBoundary(props: Props): JSX.Element {
  const { colors } = useTheme();
  return <ChatErrorBoundaryClass {...props} colors={colors} />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    width: '100%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryIcon: {
    marginRight: 8,
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ChatErrorBoundary;
