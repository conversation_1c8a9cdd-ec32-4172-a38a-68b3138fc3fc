import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from 'react-native';
import { Card } from '@components/common/Card';
import { Button } from '@design-system';
import { CommunityEvent, EventRSVP, CommunityService } from '@services/communityService';
import { useAuth } from '@hooks/useAuth';
import { Ionicons } from '@expo/vector-icons';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface EventCardProps {
  event: CommunityEvent;
  onPress?: (event: CommunityEvent) => void;
  onRSVPUpdate?: (event: CommunityEvent) => void;
  showRSVPButton?: boolean;
  compact?: boolean;
}

export const EventCard: React.FC<EventCardProps> = ({
  event,
  onPress,
  onRSVPUpdate,
  showRSVPButton = true,
  compact = false,
}) => {
  const { authState } = useAuth();
  const user = authState?.user;
  const [isUpdatingRSVP, setIsUpdatingRSVP] = useState(false);
  const communityService = new CommunityService();

  const formatDate = (dateString: string) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isTomorrow = date.toDateString() === new Date(now.getTime() + 86400000).toDateString();

    if (isToday) {
      return `Today at ${date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      })}`;
    } else if (isTomorrow) {
      return `Tomorrow at ${date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      })}`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    }
  };

  const formatDuration = () => {
    const start = new Date(event.start_time);
    const end = new Date(event.end_time);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
    }
    return `${minutes}m`;
  };

  const getStatusColor = (status: EventRSVP['status']) => {
    switch (status) {
      case 'attending':
        return '#4caf50';
      case 'maybe':
        return '#ff9800';
      case 'not_attending':
        return '#f44336';
      case 'waitlisted':
        return '#9c27b0';
      default:
        return '#757575';
    }
  };

  const getStatusText = (status: EventRSVP['status']) => {
    switch (status) {
      case 'attending':
        return 'Going';
      case 'maybe':
        return 'Maybe';
      case 'not_attending':
        return 'Not Going';
      case 'waitlisted':
        return 'Waitlisted';
      default:
        return 'Respond';
    }
  };

  const handleRSVP = async (status: EventRSVP['status']) => {
    if (!user) {
      Alert.alert('Authentication Required', 'Please log in to RSVP to events');
      return;
    }

    setIsUpdatingRSVP(true);
    try {
      await communityService.updateEventRSVP(event.id, status);

      // Update the event object with new RSVP status
      const updatedEvent = {
        ...event,
        user_rsvp: {
          ...event.user_rsvp,
          status,
          responded_at: new Date().toISOString(),
        } as EventRSVP,
      };

      onRSVPUpdate?.(updatedEvent);

      Alert.alert(
        'RSVP Updated',
        `You have marked yourself as "${getStatusText(status)}" for this event.`
      );
    } catch (error) {
      console.error('Error updating RSVP:', error);
      Alert.alert('Error', 'Failed to update RSVP. Please try again.');
    } finally {
      setIsUpdatingRSVP(false);
    }
  };

  const showRSVPOptions = () => {
    const options = [
      { text: 'Going', onPress: () => handleRSVP('attending') },
      { text: 'Maybe', onPress: () => handleRSVP('maybe') },
      { text: 'Not Going', onPress: () => handleRSVP('not_attending') },
      { text: 'Cancel', style: 'cancel' },
    ];

    Alert.alert('RSVP to Event', 'How would you like to respond?', options);
  };

  const isEventPast = new Date(event.end_time) < new Date();
  const isEventFull =
    event.max_attendees &&
    event.rsvp_summary &&
    event.rsvp_summary.attending >= event.max_attendees;

  return (
    <Card style={[styles.container, compact && styles.compactContainer]}>
      <TouchableOpacity onPress={() => onPress?.(event)} activeOpacity={0.7}>
        {/* Event Image */}
        {event.event_image_url && !compact && (
          <Image source={{ uri: event.event_image_url }} style={styles.eventImage} />
        )}

        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Text style={styles.title} numberOfLines={compact ? 1 : 2}>
                {event.title}
              </Text>
              {event.category && (
                <View style={[styles.categoryBadge, { backgroundColor: event.category.color }]}>
                  <Text style={styles.categoryText}>{event.category.name}</Text>
                </View>
              )}
            </View>
            {event.is_private && <Ionicons name="lock-closed" size={16} color="#757575" />}
          </View>

          {/* Event Details */}
          <View style={styles.details}>
            <View style={styles.detailRow}>
              <Ionicons name="time-outline" size={16} color="#757575" />
              <Text style={styles.detailText}>
                {formatDate(event.start_time)} • {formatDuration()}
              </Text>
            </View>

            {event.location && (
              <View style={styles.detailRow}>
                <Ionicons name="location-outline" size={16} color="#757575" />
                <Text style={styles.detailText} numberOfLines={1}>
                  {event.location}
                </Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Ionicons name="person-outline" size={16} color="#757575" />
              <Text style={styles.detailText}>
                {event.organizer?.full_name || 'Unknown Organizer'}
              </Text>
            </View>
          </View>

          {/* Description */}
          {event.description && !compact && (
            <Text style={styles.description} numberOfLines={2}>
              {event.description}
            </Text>
          )}

          {/* RSVP Summary */}
          {event.rsvp_summary && (
            <View style={styles.rsvpSummary}>
              <View style={styles.rsvpStats}>
                <Text style={styles.rsvpCount}>{event.rsvp_summary.attending} going</Text>
                {event.rsvp_summary.maybe > 0 && (
                  <Text style={styles.rsvpCount}>• {event.rsvp_summary.maybe} maybe</Text>
                )}
                {event.max_attendees && (
                  <Text style={styles.rsvpCount}>• {event.max_attendees} max</Text>
                )}
              </View>
            </View>
          )}

          {/* Tags */}
          {event.tags && event.tags.length > 0 && !compact && (
            <View style={styles.tagsContainer}>
              {event.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>#{tag}</Text>
                </View>
              ))}
              {event.tags.length > 3 && (
                <Text style={styles.moreTagsText}>+{event.tags.length - 3} more</Text>
              )}
            </View>
          )}

          {/* Status Indicators */}
          <View style={styles.statusContainer}>
            {isEventPast && (
              <View style={[styles.statusBadge, styles.pastBadge]}>
                <Text style={styles.statusText}>Past Event</Text>
              </View>
            )}
            {isEventFull && !isEventPast && (
              <View style={[styles.statusBadge, styles.fullBadge]}>
                <Text style={styles.statusText}>Full</Text>
              </View>
            )}
            {event.requires_approval && (
              <View style={[styles.statusBadge, styles.approvalBadge]}>
                <Text style={styles.statusText}>Approval Required</Text>
              </View>
            )}
          </View>

          {/* RSVP Button */}
          {showRSVPButton && user && !isEventPast && (
            <View style={styles.rsvpContainer}>
              {event.user_rsvp ? (
                <TouchableOpacity
                  style={[
                    styles.rsvpButton,
                    { backgroundColor: getStatusColor(event.user_rsvp.status) },
                  ]}
                  onPress={showRSVPOptions}
                  disabled={isUpdatingRSVP}
                >
                  <Text style={styles.rsvpButtonText}>{getStatusText(event.user_rsvp.status)}</Text>
                  <Ionicons name="chevron-down" size={16} color={theme.colors.background} />
                </TouchableOpacity>
              ) : (
                <Button
                  title="RSVP"
                  onPress={showRSVPOptions}
                  loading={isUpdatingRSVP}
                  disabled={isEventFull}
                  style={styles.rsvpButtonPrimary}
                />
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Card>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  compactContainer: {
    marginBottom: 8,
  },
  eventImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.background,
  },
  details: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  rsvpSummary: {
    marginBottom: 12,
  },
  rsvpStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rsvpCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  moreTagsText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  pastBadge: {
    backgroundColor: '#ffebee',
  },
  fullBadge: {
    backgroundColor: 'theme.colors.background3e0',
  },
  approvalBadge: {
    backgroundColor: '#f3e5f5',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  rsvpContainer: {
    marginTop: 8,
  },
  rsvpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  rsvpButtonText: {
    color: theme.colors.background,
    fontWeight: '600',
    marginRight: 4,
  },
  rsvpButtonPrimary: {
    backgroundColor: '#1976d2',
  },
});

export default EventCard;
