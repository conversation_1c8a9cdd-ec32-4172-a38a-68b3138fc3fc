import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { CommunityEvent, CommunityService } from '@services/communityService';
import { Ionicons } from '@expo/vector-icons';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface CalendarViewProps {
  events: CommunityEvent[];
  onEventPress?: (event: CommunityEvent) => void;
  onDatePress?: (date: Date) => void;
  selectedDate?: Date;
  viewMode?: 'month' | 'week';
}

const { width: screenWidth } = Dimensions.get('window');
const CELL_WIDTH = screenWidth / 7;

export const CalendarView: React.FC<CalendarViewProps> = ({
  events,
  onEventPress,
  onDatePress,
  selectedDate = new Date(),
  viewMode = 'month',
}) => {
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [view, setView] = useState<'month' | 'week'>(viewMode);

  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Get events for a specific date
  const getEventsForDate = (date: Date): CommunityEvent[] => {
    const dateStr = date.toDateString();
    return events.filter(event => {
      const eventDate = new Date(event.start_time);
      return eventDate.toDateString() === dateStr;
    });
  };

  // Generate calendar days for month view
  const generateMonthDays = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days: Date[] = [];
    const current = new Date(startDate);

    // Generate 6 weeks (42 days) to ensure consistent calendar height
    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  // Generate calendar days for week view
  const generateWeekDays = () => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const days: Date[] = [];
    const current = new Date(startOfWeek);

    for (let i = 0; i < 7; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setDate(newDate.getDate() + 7);
    }
    setCurrentDate(newDate);
  };

  const navigate = (direction: 'prev' | 'next') => {
    if (view === 'month') {
      navigateMonth(direction);
    } else {
      navigateWeek(direction);
    }
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const handleDatePress = (date: Date) => {
    onDatePress?.(date);
  };

  const renderEventDot = (event: CommunityEvent, index: number) => {
    const color = event.category?.color || '#1976d2';
    return (
      <View
        key={event.id}
        style={[styles.eventDot, { backgroundColor: color }, index > 0 && { marginLeft: 2 }]}
      />
    );
  };

  const renderDayCell = (date: Date, index: number) => {
    const dayEvents = getEventsForDate(date);
    const isCurrentMonthDay = isCurrentMonth(date);
    const isTodayDate = isToday(date);
    const isSelectedDate = isSelected(date);

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.dayCell,
          isTodayDate && styles.todayCell,
          isSelectedDate && styles.selectedCell,
        ]}
        onPress={() => handleDatePress(date)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.dayText,
            !isCurrentMonthDay && styles.otherMonthText,
            isTodayDate && styles.todayText,
            isSelectedDate && styles.selectedText,
          ]}
        >
          {date.getDate()}
        </Text>

        {dayEvents.length > 0 && (
          <View style={styles.eventDotsContainer}>
            {dayEvents.slice(0, 3).map((event, eventIndex) => renderEventDot(event, eventIndex))}
            {dayEvents.length > 3 && (
              <Text style={styles.moreEventsText}>+{dayEvents.length - 3}</Text>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderWeekEventsList = () => {
    const weekDays = generateWeekDays();

    return (
      <ScrollView style={styles.weekEventsContainer}>
        {weekDays.map((date, index) => {
          const dayEvents = getEventsForDate(date);
          const isTodayDate = isToday(date);

          return (
            <View key={index} style={styles.weekDayContainer}>
              <View style={styles.weekDayHeader}>
                <Text style={[styles.weekDayName, isTodayDate && styles.todayText]}>
                  {dayNames[date.getDay()]}
                </Text>
                <Text style={[styles.weekDayNumber, isTodayDate && styles.todayText]}>
                  {date.getDate()}
                </Text>
              </View>

              <View style={styles.weekDayEvents}>
                {dayEvents.length === 0 ? (
                  <Text style={styles.noEventsText}>No events</Text>
                ) : (
                  dayEvents.map(event => (
                    <TouchableOpacity
                      key={event.id}
                      style={[
                        styles.weekEventItem,
                        { borderLeftColor: event.category?.color || '#1976d2' },
                      ]}
                      onPress={() => onEventPress?.(event)}
                    >
                      <Text style={styles.weekEventTime}>
                        {new Date(event.start_time).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true,
                        })}
                      </Text>
                      <Text style={styles.weekEventTitle} numberOfLines={1}>
                        {event.title}
                      </Text>
                      {event.location && (
                        <Text style={styles.weekEventLocation} numberOfLines={1}>
                          📍 {event.location}
                        </Text>
                      )}
                    </TouchableOpacity>
                  ))
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>
    );
  };

  const getHeaderTitle = () => {
    if (view === 'month') {
      return `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    } else {
      const weekDays = generateWeekDays();
      const startDate = weekDays[0];
      const endDate = weekDays[6];

      if (startDate.getMonth() === endDate.getMonth()) {
        return `${monthNames[startDate.getMonth()]} ${startDate.getDate()}-${endDate.getDate()}, ${startDate.getFullYear()}`;
      } else {
        return `${monthNames[startDate.getMonth()]} ${startDate.getDate()} - ${monthNames[endDate.getMonth()]} ${endDate.getDate()}, ${startDate.getFullYear()}`;
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigate('prev')} style={styles.navButton}>
          <Ionicons name="chevron-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>{getHeaderTitle()}</Text>
          <View style={styles.viewToggle}>
            <TouchableOpacity
              style={[styles.toggleButton, view === 'month' && styles.activeToggle]}
              onPress={() => setView('month')}
            >
              <Text style={[styles.toggleText, view === 'month' && styles.activeToggleText]}>
                Month
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.toggleButton, view === 'week' && styles.activeToggle]}
              onPress={() => setView('week')}
            >
              <Text style={[styles.toggleText, view === 'week' && styles.activeToggleText]}>
                Week
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity onPress={() => navigate('next')} style={styles.navButton}>
          <Ionicons name="chevron-forward" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {view === 'month' ? (
        <>
          {/* Day Names Header */}
          <View style={styles.dayNamesContainer}>
            {dayNames.map((day, index) => (
              <View key={index} style={styles.dayNameCell}>
                <Text style={styles.dayNameText}>{day}</Text>
              </View>
            ))}
          </View>

          {/* Calendar Grid */}
          <View style={styles.calendarGrid}>
            {generateMonthDays().map((date, index) => renderDayCell(date, index))}
          </View>
        </>
      ) : (
        <>
          {/* Week View Events List */}
          {renderWeekEventsList()}
        </>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  navButton: {
    padding: 8,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    padding: 2,
  },
  toggleButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 18,
  },
  activeToggle: {
    backgroundColor: '#1976d2',
  },
  toggleText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  activeToggleText: {
    color: theme.colors.background,
  },
  dayNamesContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  dayNameCell: {
    width: CELL_WIDTH,
    paddingVertical: 12,
    alignItems: 'center',
  },
  dayNameText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayCell: {
    width: CELL_WIDTH,
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#f0f0f0',
    position: 'relative',
  },
  todayCell: {
    backgroundColor: '#e3f2fd',
  },
  selectedCell: {
    backgroundColor: '#1976d2',
  },
  dayText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  otherMonthText: {
    color: '#ccc',
  },
  todayText: {
    color: '#1976d2',
    fontWeight: 'bold',
  },
  selectedText: {
    color: theme.colors.background,
    fontWeight: 'bold',
  },
  eventDotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    bottom: 4,
  },
  eventDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  moreEventsText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginLeft: 2,
  },
  weekEventsContainer: {
    flex: 1,
  },
  weekDayContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  weekDayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
  },
  weekDayName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 8,
  },
  weekDayNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  weekDayEvents: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  noEventsText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 16,
  },
  weekEventItem: {
    backgroundColor: theme.colors.background,
    borderLeftWidth: 4,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    shadowColor: theme.colors.text,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  weekEventTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
  },
  weekEventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  weekEventLocation: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
});

export default CalendarView;
