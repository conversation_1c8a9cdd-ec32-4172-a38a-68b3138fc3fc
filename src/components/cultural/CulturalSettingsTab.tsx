import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useColorScheme } from 'react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Globe, Users, MessageCircle, Settings } from 'lucide-react-native';

interface CulturalSettings {
  primary_culture: string;
  secondary_cultures: string[];
  communication_style: 'direct' | 'indirect' | 'balanced';
  hierarchy_preference: 'formal' | 'informal' | 'contextual';
  time_orientation: 'punctual' | 'flexible' | 'event_based';
  social_distance: 'close' | 'moderate' | 'formal';
  conflict_resolution: 'direct' | 'mediated' | 'avoidance';
  personal_space: 'close' | 'moderate' | 'distant';
}

interface CulturalSettingsTabProps {
  settings: CulturalSettings;
  updateSettings: (updates: Partial<CulturalSettings>) => void;
}

const CulturalSettingsTab = React.memo(({
  settings,
  updateSettings,
}: CulturalSettingsTabProps) => {
  const isDark = useColorScheme() === 'dark';
  const colors = getColors(isDark);

  const cultures = [
    'Western', 'Eastern', 'Latin', 'African', 'Middle Eastern', 
    'Asian', 'Nordic', 'Mediterranean', 'Indigenous', 'Mixed'
  ];

  const communicationStyles = [
    { value: 'direct', label: 'Direct', description: 'Clear, straightforward communication' },
    { value: 'indirect', label: 'Indirect', description: 'Subtle, context-dependent communication' },
    { value: 'balanced', label: 'Balanced', description: 'Adaptable communication style' },
  ];

  const hierarchyPreferences = [
    { value: 'formal', label: 'Formal', description: 'Respect for authority and structure' },
    { value: 'informal', label: 'Informal', description: 'Egalitarian, casual interactions' },
    { value: 'contextual', label: 'Contextual', description: 'Situational hierarchy awareness' },
  ];

  const timeOrientations = [
    { value: 'punctual', label: 'Punctual', description: 'Time is strictly observed' },
    { value: 'flexible', label: 'Flexible', description: 'Time is more fluid' },
    { value: 'event_based', label: 'Event-based', description: 'Events matter more than time' },
  ];

  const socialDistances = [
    { value: 'close', label: 'Close', description: 'Comfortable with close relationships' },
    { value: 'moderate', label: 'Moderate', description: 'Balanced social interaction' },
    { value: 'formal', label: 'Formal', description: 'Prefers formal boundaries' },
  ];

  const conflictResolutions = [
    { value: 'direct', label: 'Direct', description: 'Address conflicts head-on' },
    { value: 'mediated', label: 'Mediated', description: 'Prefer third-party mediation' },
    { value: 'avoidance', label: 'Avoidance', description: 'Avoid confrontation when possible' },
  ];

  const personalSpaces = [
    { value: 'close', label: 'Close', description: 'Comfortable with physical proximity' },
    { value: 'moderate', label: 'Moderate', description: 'Standard personal space' },
    { value: 'distant', label: 'Distant', description: 'Prefer larger personal space' },
  ];

  const renderSectionCard = (
    title: string,
    icon: React.ComponentType<any>,
    children: React.ReactNode
  ) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    const Icon = icon;
    return (
      <View style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Icon size={20} color={theme.colors.primary} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>{title}</Text>
        </View>
        {children}
      </View>
    );
  };

  const renderCultureSelection = () => (
    renderSectionCard('Cultural Background', Globe, (
      <View style={styles.sectionContent}>
        <Text style={[styles.fieldLabel, { color: theme.colors.textSecondary }]}>
          Primary Culture
        </Text>
        <View style={styles.optionsGrid}>
          {cultures.map((culture) => (
            <TouchableOpacity
              key={culture}
              style={[
                styles.optionButton,
                {
                  backgroundColor: settings.primary_culture === culture 
                    ? theme.colors.primary + '20' 
                    : theme.colors.background,
                  borderColor: settings.primary_culture === culture 
                    ? theme.colors.primary 
                    : theme.colors.border,
                },
              ]}
              onPress={() => updateSettings({ primary_culture: culture })}
            >
              <Text
                style={[
                  styles.optionText,
                  {
                    color: settings.primary_culture === culture 
                      ? theme.colors.primary 
                      : theme.colors.text,
                  },
                ]}
              >
                {culture}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    ))
  );

  const renderPreferenceSection = (
    title: string,
    currentValue: string,
    options: Array<{ value: string; label: string; description: string }>,
    updateKey: keyof CulturalSettings
  ) => (
    <View style={styles.preferenceSection}>
      <Text style={[styles.fieldLabel, { color: theme.colors.textSecondary }]}>
        {title}
      </Text>
      <View style={styles.preferenceOptions}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.preferenceButton,
              {
                backgroundColor: currentValue === option.value 
                  ? theme.colors.primary + '20' 
                  : theme.colors.background,
                borderColor: currentValue === option.value 
                  ? theme.colors.primary 
                  : theme.colors.border,
              },
            ]}
            onPress={() => updateSettings({ [updateKey]: option.value })}
          >
            <Text
              style={[
                styles.preferenceLabel,
                {
                  color: currentValue === option.value 
                    ? theme.colors.primary 
                    : theme.colors.text,
                },
              ]}
            >
              {option.label}
            </Text>
            <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
              {option.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderCultureSelection()}
      
      {renderSectionCard('Communication Preferences', MessageCircle, (
        <View style={styles.sectionContent}>
          {renderPreferenceSection(
            'Communication Style',
            settings.communication_style,
            communicationStyles,
            'communication_style'
          )}
          
          {renderPreferenceSection(
            'Hierarchy Preference',
            settings.hierarchy_preference,
            hierarchyPreferences,
            'hierarchy_preference'
          )}
        </View>
      ))}

      {renderSectionCard('Social Preferences', Users, (
        <View style={styles.sectionContent}>
          {renderPreferenceSection(
            'Time Orientation',
            settings.time_orientation,
            timeOrientations,
            'time_orientation'
          )}
          
          {renderPreferenceSection(
            'Social Distance',
            settings.social_distance,
            socialDistances,
            'social_distance'
          )}
          
          {renderPreferenceSection(
            'Conflict Resolution',
            settings.conflict_resolution,
            conflictResolutions,
            'conflict_resolution'
          )}
          
          {renderPreferenceSection(
            'Personal Space',
            settings.personal_space,
            personalSpaces,
            'personal_space'
          )}
        </View>
      ))}
    </ScrollView>
  );
});

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  sectionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionContent: {
    gap: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  optionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  preferenceSection: {
    gap: 8,
  },
  preferenceOptions: {
    gap: 8,
  },
  preferenceButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  preferenceLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  preferenceDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default CulturalSettingsTab; 