import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import {
  CheckSquare,
  Square,
  X,
  Trash2,
  UserX,
  UserCheck,
  Mail,
  Flag,
  Eye,
  EyeOff,
  Download,
  Upload,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Home,
  Shield,
} from 'lucide-react-native';

import { useTheme } from '../../design-system/ThemeProvider';

export interface BulkAction {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  confirmationRequired?: boolean;
  confirmationMessage?: string;
  destructive?: boolean;
}

export interface BulkOperationsBarProps {
  selectedItems: string[];
  totalItems: number;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onClose: () => void;
  actions: BulkAction[];
  onActionPress: (actionId: string, selectedItems: string[]) => Promise<void>;
  entityType: 'users' | 'rooms' | 'reports' | 'content';
  showProgress?: boolean;
  progressText?: string;
  undoAvailable?: boolean;
  onUndo?: () => void;
}

const BulkOperationsBar: React.FC<BulkOperationsBarProps> = ({
  selectedItems,
  totalItems,
  onSelectAll,
  onDeselectAll,
  onClose,
  actions,
  onActionPress,
  entityType,
  showProgress = false,
  progressText,
  undoAvailable = false,
  onUndo,
}) => {
  const { colors, spacing } = useTheme();
  const styles = createStyles(colors, spacing);

  const [isProcessing, setIsProcessing] = useState(false);
  const [slideAnim] = useState(new Animated.Value(0));
  const [undoTimer, setUndoTimer] = useState<NodeJS.Timeout | null>(null);
  const [showUndoBar, setShowUndoBar] = useState(false);

  const selectedCount = selectedItems.length;
  const allSelected = selectedCount === totalItems && totalItems > 0;

  // Animate bar appearance
  useEffect(() => {
    if (selectedCount > 0) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [selectedCount]);

  // Handle undo timer
  useEffect(() => {
    if (undoAvailable && !showUndoBar) {
      setShowUndoBar(true);
      const timer = setTimeout(() => {
        setShowUndoBar(false);
      }, 10000); // 10 seconds to undo
      setUndoTimer(timer);
    }

    return () => {
      if (undoTimer) {
        clearTimeout(undoTimer);
      }
    };
  }, [undoAvailable]);

  const handleActionPress = async (action: BulkAction) => {
    if (selectedCount === 0) return;

    if (action.confirmationRequired) {
      Alert.alert(
        'Confirm Action',
        action.confirmationMessage || `Are you sure you want to ${action.title.toLowerCase()} ${selectedCount} ${entityType}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: action.destructive ? 'Delete' : 'Confirm',
            style: action.destructive ? 'destructive' : 'default',
            onPress: () => executeAction(action),
          },
        ]
      );
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: BulkAction) => {
    try {
      setIsProcessing(true);
      await onActionPress(action.id, selectedItems);
    } catch (error) {
      console.error('Bulk action error:', error);
      Alert.alert('Error', 'Failed to perform bulk action. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUndo = () => {
    if (onUndo) {
      onUndo();
      setShowUndoBar(false);
      if (undoTimer) {
        clearTimeout(undoTimer);
      }
    }
  };

  const getEntityIcon = () => {
    switch (entityType) {
      case 'users':
        return Users;
      case 'rooms':
        return Home;
      case 'reports':
        return Flag;
      case 'content':
        return Shield;
      default:
        return CheckSquare;
    }
  };

  const EntityIcon = getEntityIcon();

  if (selectedCount === 0 && !showProgress && !showUndoBar) {
    return null;
  }

  return (
    <>
      {/* Main Bulk Operations Bar */}
      {(selectedCount > 0 || showProgress) && (
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [100, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.content}>
            {/* Selection Info */}
            <View style={styles.selectionInfo}>
              <View style={styles.entityIconContainer}>
                <EntityIcon size={20} color={colors.primary} />
              </View>
              <View style={styles.selectionText}>
                {showProgress ? (
                  <>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={styles.progressText}>{progressText}</Text>
                  </>
                ) : (
                  <>
                    <Text style={styles.selectedCount}>
                      {selectedCount} {entityType} selected
                    </Text>
                    <TouchableOpacity
                      style={styles.selectAllButton}
                      onPress={allSelected ? onDeselectAll : onSelectAll}
                    >
                      {allSelected ? (
                        <CheckSquare size={16} color={colors.primary} />
                      ) : (
                        <Square size={16} color={colors.textSecondary} />
                      )}
                      <Text style={styles.selectAllText}>
                        {allSelected ? 'Deselect All' : 'Select All'}
                      </Text>
                    </TouchableOpacity>
                  </>
                )}
              </View>
            </View>

            {/* Actions */}
            {!showProgress && (
              <View style={styles.actions}>
                {actions.slice(0, 3).map((action) => {
                  const ActionIcon = action.icon;
                  return (
                    <TouchableOpacity
                      key={action.id}
                      style={[
                        styles.actionButton,
                        { backgroundColor: action.color + '15' },
                        isProcessing && styles.actionButtonDisabled,
                      ]}
                      onPress={() => handleActionPress(action)}
                      disabled={isProcessing}
                    >
                      <ActionIcon size={18} color={action.color} />
                      <Text style={[styles.actionText, { color: action.color }]}>
                        {action.title}
                      </Text>
                    </TouchableOpacity>
                  );
                })}

                {/* More Actions Menu */}
                {actions.length > 3 && (
                  <TouchableOpacity style={styles.moreButton}>
                    <Text style={styles.moreText}>+{actions.length - 3}</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Close Button */}
            {!showProgress && (
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <X size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      )}

      {/* Undo Bar */}
      {showUndoBar && (
        <Animated.View style={[styles.undoContainer]}>
          <View style={styles.undoContent}>
            <View style={styles.undoInfo}>
              <CheckCircle size={20} color={colors.success} />
              <Text style={styles.undoText}>
                Action completed successfully
              </Text>
            </View>
            <TouchableOpacity style={styles.undoButton} onPress={handleUndo}>
              <RotateCcw size={16} color={colors.primary} />
              <Text style={styles.undoButtonText}>Undo</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
    </>
  );
};

// Predefined bulk actions for different entity types
export const getUserBulkActions = (colors: any): BulkAction[] => [
  {
    id: 'suspend',
    title: 'Suspend',
    icon: UserX,
    color: colors.warning,
    confirmationRequired: true,
    confirmationMessage: 'Suspended users will lose access to the platform.',
  },
  {
    id: 'activate',
    title: 'Activate',
    icon: UserCheck,
    color: colors.success,
  },
  {
    id: 'notify',
    title: 'Notify',
    icon: Mail,
    color: colors.info,
  },
  {
    id: 'delete',
    title: 'Delete',
    icon: Trash2,
    color: colors.error,
    confirmationRequired: true,
    confirmationMessage: 'This action cannot be undone. User data will be permanently deleted.',
    destructive: true,
  },
  {
    id: 'export',
    title: 'Export',
    icon: Download,
    color: colors.textSecondary,
  },
];

export const getRoomBulkActions = (colors: any): BulkAction[] => [
  {
    id: 'approve',
    title: 'Approve',
    icon: CheckCircle,
    color: colors.success,
  },
  {
    id: 'reject',
    title: 'Reject',
    icon: X,
    color: colors.error,
    confirmationRequired: true,
  },
  {
    id: 'flag',
    title: 'Flag',
    icon: Flag,
    color: colors.warning,
  },
  {
    id: 'hide',
    title: 'Hide',
    icon: EyeOff,
    color: colors.textSecondary,
  },
  {
    id: 'delete',
    title: 'Delete',
    icon: Trash2,
    color: colors.error,
    confirmationRequired: true,
    destructive: true,
  },
];

export const getContentBulkActions = (colors: any): BulkAction[] => [
  {
    id: 'approve',
    title: 'Approve',
    icon: CheckCircle,
    color: colors.success,
  },
  {
    id: 'reject',
    title: 'Reject',
    icon: X,
    color: colors.error,
    confirmationRequired: true,
  },
  {
    id: 'flag',
    title: 'Flag',
    icon: Flag,
    color: colors.warning,
  },
  {
    id: 'review',
    title: 'Review',
    icon: Eye,
    color: colors.info,
  },
];

const createStyles = (colors: any, spacing: any) => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1000,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: 60,
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  entityIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  selectionText: {
    flex: 1,
  },
  selectedCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  progressText: {
    fontSize: 14,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    gap: spacing.xs,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  moreButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.textSecondary + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: colors.textSecondary,
  },
  closeButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  undoContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.success + '15',
    borderTopWidth: 1,
    borderTopColor: colors.success + '30',
    zIndex: 999,
  },
  undoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  undoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  undoText: {
    fontSize: 14,
    color: colors.success,
    marginLeft: spacing.sm,
    fontWeight: '500',
  },
  undoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.primary + '15',
    borderRadius: 8,
    gap: spacing.xs,
  },
  undoButtonText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
});

export default BulkOperationsBar; 