/**
 * Free Verification Admin Dashboard
 * 
 * Admin interface for managing zero-cost verification system:
 * - Manual identity verification reviews
 * - API usage monitoring (Google Maps free tier)
 * - Cost savings tracking
 * - Verification queue management
 * 
 * Replaces need for expensive admin tools from paid services
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  FlatList
} from 'react-native';
import { useTheme } from '@design-system/ThemeProvider';
import { supabase } from '@utils/supabaseUtils';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@services/logger';
import { freeVerificationService } from '@services/freeVerificationService';
import { freePhoneVerificationService } from '@services/freePhoneVerificationService';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface VerificationRequest {
  id: string;
  user_id: string;
  status: 'pending' | 'in_review' | 'verified' | 'rejected';
  document_type: string;
  document_url: string;
  selfie_url: string;
  submitted_at: string;
  reviewed_at?: string;
  reviewer_id?: string;
  notes?: string;
  retry_count: number;
  trust_score?: number;
  automated_review_result?: string;
  review_confidence_score?: number;
  processing_time_minutes?: number;
  user_profile?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone_number?: string;
  };
}

interface AdminStats {
  pendingReviews: number;
  completedToday: number;
  averageReviewTime: number;
  verificationSuccessRate: number;
  monthlySavings: number;
  apiUsage: {
    googleMaps: {
      currentUsage: number;
      limit: number;
      percentUsed: number;
    };
  };
}

interface ReviewModalProps {
  visible: boolean;
  request: VerificationRequest | null;
  onClose: () => void;
  onApprove: (requestId: string, notes?: string) => void;
  onReject: (requestId: string, reason: string) => void;
}

// ============================================================================
// REVIEW MODAL COMPONENT
// ============================================================================

const ReviewModal: React.FC<ReviewModalProps> = ({
  visible,
  request,
  onClose,
  onApprove,
  onReject
}) => {
  const theme = useTheme();
  const [notes, setNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionInput, setShowRejectionInput] = useState(false);

  const handleApprove = () => {
    if (request) {
      onApprove(request.id, notes);
      setNotes('');
      onClose();
    }
  };

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for rejection');
      return;
    }
    if (request) {
      onReject(request.id, rejectionReason);
      setRejectionReason('');
      setShowRejectionInput(false);
      onClose();
    }
  };

  if (!request) return null;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            Identity Verification Review
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={[styles.closeButtonText, { color: theme.colors.primary }]}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* User Information */}
          <View style={[styles.infoSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              User Information
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Name: {request.user_profile?.first_name} {request.user_profile?.last_name}
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Email: {request.user_profile?.email}
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Phone: {request.user_profile?.phone_number}
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Document Type: {request.document_type.replace('_', ' ').toUpperCase()}
            </Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              Submitted: {new Date(request.submitted_at).toLocaleString()}
            </Text>
          </View>

          {/* Document Images */}
          <View style={[styles.infoSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Document Images
            </Text>
            <View style={styles.imageContainer}>
              <Image source={{ uri: request.document_url }} style={styles.documentImage} />
              {request.selfie_url && request.selfie_url !== request.document_url && (
                <Image source={{ uri: request.selfie_url }} style={styles.documentImage} />
              )}
            </View>
          </View>

          {/* Review Notes */}
          <View style={[styles.infoSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Review Notes (Optional)
            </Text>
            <TextInput
              style={[
                styles.notesInput,
                {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text
                }
              ]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add notes about this verification..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Rejection Reason */}
          {showRejectionInput && (
            <View style={[styles.infoSection, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.error }]}>
                Rejection Reason *
              </Text>
              <TextInput
                style={[
                  styles.notesInput,
                  {
                    backgroundColor: theme.colors.background,
                    borderColor: theme.colors.error,
                    color: theme.colors.text
                  }
                ]}
                value={rejectionReason}
                onChangeText={setRejectionReason}
                placeholder="Explain why this verification is being rejected..."
                placeholderTextColor={theme.colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          )}
        </ScrollView>

        {/* Action Buttons */}
        <View style={[styles.modalActions, { borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={handleApprove}
          >
            <Text style={[styles.actionButtonText, { color: theme.colors.background }]}>
              ✓ Approve
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={() => {
              if (showRejectionInput) {
                handleReject();
              } else {
                setShowRejectionInput(true);
              }
            }}
          >
            <Text style={[styles.actionButtonText, { color: theme.colors.background }]}>
              {showRejectionInput ? 'Confirm Reject' : '✗ Reject'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

// ============================================================================
// MAIN ADMIN DASHBOARD COMPONENT
// ============================================================================

export const FreeVerificationAdminDashboard: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<AdminStats>({
    pendingReviews: 0,
    completedToday: 0,
    averageReviewTime: 0,
    verificationSuccessRate: 0,
    monthlySavings: 4310,
    apiUsage: {
      googleMaps: {
        currentUsage: 0,
        limit: 40000,
        percentUsed: 0
      }
    }
  });

  const [verificationRequests, setVerificationRequests] = useState<VerificationRequest[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<VerificationRequest | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'completed'>('all');

  // ============================================================================
  // LOAD DATA
  // ============================================================================

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        loadVerificationRequests(),
        loadAdminStats()
      ]);
    } catch (error) {
      logger.error('Failed to load admin dashboard data', 'FreeVerificationAdminDashboard.loadDashboardData', {}, error as Error);
    }
  };

  const loadVerificationRequests = async () => {
    try {
      const { data: requests, error } = await supabase
        .from('verification_requests')
        .select(`
          *,
          user_profiles:user_id (
            first_name,
            last_name,
            email,
            phone_number
          )
        `)
        .order('submitted_at', { ascending: false })
        .limit(50);

      if (error) {
        logger.error('Failed to load verification requests', 'FreeVerificationAdminDashboard.loadVerificationRequests', { error: error.message });
        return;
      }

      const formattedRequests = requests?.map(request => ({
        ...request,
        user_profile: Array.isArray(request.user_profiles) ? request.user_profiles[0] : request.user_profiles
      })) || [];

      setVerificationRequests(formattedRequests);
    } catch (error) {
      logger.error('Error loading verification requests', 'FreeVerificationAdminDashboard.loadVerificationRequests', {}, error as Error);
    }
  };

  const loadAdminStats = async () => {
    try {
      // Get API usage stats from free verification service
      const apiStats = await freeVerificationService.getAPIUsageStats();
      const phoneStats = await freePhoneVerificationService.getServiceStats();

      // Calculate stats from verification requests
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const pendingCount = verificationRequests.filter(r => r.status === 'pending').length;
      const completedTodayCount = verificationRequests.filter(r => {
        return r.reviewed_at && new Date(r.reviewed_at) >= today;
      }).length;

      const completedRequests = verificationRequests.filter(r => r.processing_time_minutes);
      const avgReviewTime = completedRequests.length > 0
        ? completedRequests.reduce((sum, r) => sum + (r.processing_time_minutes || 0), 0) / completedRequests.length
        : 0;

      const verifiedCount = verificationRequests.filter(r => r.status === 'verified').length;
      const totalProcessed = verificationRequests.filter(r => r.status !== 'pending').length;
      const successRate = totalProcessed > 0 ? (verifiedCount / totalProcessed) * 100 : 0;

      setStats({
        pendingReviews: pendingCount,
        completedToday: completedTodayCount,
        averageReviewTime: Math.round(avgReviewTime),
        verificationSuccessRate: Math.round(successRate),
        monthlySavings: apiStats.monthlySavings + phoneStats.monthlySavings,
        apiUsage: {
          googleMaps: apiStats.googleMaps
        }
      });

    } catch (error) {
      logger.error('Error loading admin stats', 'FreeVerificationAdminDashboard.loadAdminStats', {}, error as Error);
    }
  };

  // ============================================================================
  // VERIFICATION ACTIONS
  // ============================================================================

  const handleApproveVerification = async (requestId: string, notes?: string) => {
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        Alert.alert('Error', 'Admin authentication required');
        return;
      }

      const startTime = Date.now();

      // Update verification request
      const { error: updateError } = await supabase
        .from('verification_requests')
        .update({
          status: 'verified',
          reviewed_at: new Date().toISOString(),
          reviewer_id: currentUser.id,
          notes: notes || 'Approved by admin',
          processing_time_minutes: Math.round((Date.now() - startTime) / 60000)
        })
        .eq('id', requestId);

      if (updateError) {
        Alert.alert('Error', 'Failed to approve verification');
        return;
      }

      // Update user profile with identity verification
      const request = verificationRequests.find(r => r.id === requestId);
      if (request) {
        const { error: profileError } = await supabase
          .from('user_profiles')
          .update({
            identity_verified: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', request.user_id);

        if (profileError) {
          logger.warn('Failed to update user profile after verification approval', 'FreeVerificationAdminDashboard.handleApproveVerification', { error: profileError.message });
        }
      }

      Alert.alert('Success', 'Verification approved successfully');
      await loadDashboardData();

    } catch (error) {
      logger.error('Error approving verification', 'FreeVerificationAdminDashboard.handleApproveVerification', {}, error as Error);
      Alert.alert('Error', 'Failed to approve verification');
    }
  };

  const handleRejectVerification = async (requestId: string, reason: string) => {
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        Alert.alert('Error', 'Admin authentication required');
        return;
      }

      // Update verification request
      const { error: updateError } = await supabase
        .from('verification_requests')
        .update({
          status: 'rejected',
          reviewed_at: new Date().toISOString(),
          reviewer_id: currentUser.id,
          notes: reason
        })
        .eq('id', requestId);

      if (updateError) {
        Alert.alert('Error', 'Failed to reject verification');
        return;
      }

      Alert.alert('Success', 'Verification rejected');
      await loadDashboardData();

    } catch (error) {
      logger.error('Error rejecting verification', 'FreeVerificationAdminDashboard.handleRejectVerification', {}, error as Error);
      Alert.alert('Error', 'Failed to reject verification');
    }
  };

  // ============================================================================
  // RENDER METHODS
  // ============================================================================

  const renderStatsCard = (title: string, value: string | number, subtitle?: string, color?: string) => (
    <View style={[styles.statsCard, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.statsTitle, { color: theme.colors.textSecondary }]}>{title}</Text>
      <Text style={[styles.statsValue, { color: color || theme.colors.text }]}>{value}</Text>
      {subtitle && (
        <Text style={[styles.statsSubtitle, { color: theme.colors.textSecondary }]}>{subtitle}</Text>
      )}
    </View>
  );

  const renderVerificationRequest = ({ item }: { item: VerificationRequest }) => (
    <TouchableOpacity
      style={[styles.requestCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => {
        setSelectedRequest(item);
        setShowReviewModal(true);
      }}
    >
      <View style={styles.requestHeader}>
        <Text style={[styles.requestTitle, { color: theme.colors.text }]}>
          {item.user_profile?.first_name} {item.user_profile?.last_name}
        </Text>
        <View style={[
          styles.statusBadge,
          {
            backgroundColor:
              item.status === 'pending' ? theme.colors.warning :
              item.status === 'verified' ? theme.colors.success :
              item.status === 'rejected' ? theme.colors.error :
              theme.colors.primary
          }
        ]}>
          <Text style={[styles.statusText, { color: theme.colors.background }]}>
            {item.status.toUpperCase()}
          </Text>
        </View>
      </View>

      <Text style={[styles.requestInfo, { color: theme.colors.textSecondary }]}>
        Document: {item.document_type.replace('_', ' ')}
      </Text>
      <Text style={[styles.requestInfo, { color: theme.colors.textSecondary }]}>
        Submitted: {new Date(item.submitted_at).toLocaleDateString()}
      </Text>
      
      {item.status === 'pending' && (
        <View style={styles.urgencyIndicator}>
          <Text style={[styles.urgencyText, { color: theme.colors.warning }]}>
            ⚠️ Requires Review
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const getFilteredRequests = () => {
    switch (filterStatus) {
      case 'pending':
        return verificationRequests.filter(r => r.status === 'pending');
      case 'completed':
        return verificationRequests.filter(r => r.status === 'verified' || r.status === 'rejected');
      default:
        return verificationRequests;
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Free Verification Admin
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.success }]}>
          Monthly Savings: ${stats.monthlySavings.toLocaleString()}
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {renderStatsCard('Pending Reviews', stats.pendingReviews, 'Requiring attention', theme.colors.warning)}
          {renderStatsCard('Completed Today', stats.completedToday, 'Reviews processed')}
          {renderStatsCard('Avg Review Time', `${stats.averageReviewTime}min`, 'Processing time')}
          {renderStatsCard('Success Rate', `${stats.verificationSuccessRate}%`, 'Approval rate', theme.colors.success)}
        </View>

        {/* API Usage */}
        <View style={[styles.apiUsageCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            API Usage Monitoring
          </Text>
          <View style={styles.usageRow}>
            <Text style={[styles.usageLabel, { color: theme.colors.textSecondary }]}>
              Google Maps API
            </Text>
            <Text style={[styles.usageValue, { color: theme.colors.text }]}>
              {stats.apiUsage.googleMaps.currentUsage.toLocaleString()} / {stats.apiUsage.googleMaps.limit.toLocaleString()}
            </Text>
          </View>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${stats.apiUsage.googleMaps.percentUsed}%`,
                  backgroundColor: stats.apiUsage.googleMaps.percentUsed > 90 ? theme.colors.error : theme.colors.success
                }
              ]}
            />
          </View>
          <Text style={[styles.usagePercentage, { color: theme.colors.textSecondary }]}>
            {stats.apiUsage.googleMaps.percentUsed.toFixed(1)}% used this month
          </Text>
        </View>

        {/* Filter Buttons */}
        <View style={styles.filterContainer}>
          {(['all', 'pending', 'completed'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                {
                  backgroundColor: filterStatus === filter ? theme.colors.primary : theme.colors.surface,
                  borderColor: theme.colors.border
                }
              ]}
              onPress={() => setFilterStatus(filter)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  { color: filterStatus === filter ? theme.colors.background : theme.colors.text }
                ]}
              >
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Verification Requests List */}
        <View style={[styles.requestsContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Verification Requests ({getFilteredRequests().length})
          </Text>
          
          <FlatList
            data={getFilteredRequests()}
            renderItem={renderVerificationRequest}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            ListEmptyComponent={
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                No verification requests found
              </Text>
            }
          />
        </View>
      </ScrollView>

      {/* Review Modal */}
      <ReviewModal
        visible={showReviewModal}
        request={selectedRequest}
        onClose={() => {
          setShowReviewModal(false);
          setSelectedRequest(null);
        }}
        onApprove={handleApproveVerification}
        onReject={handleRejectVerification}
      />
    </View>
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  statsCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginRight: '2%',
    marginBottom: 12,
  },
  statsTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  statsSubtitle: {
    fontSize: 11,
  },
  apiUsageCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  usageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  usageLabel: {
    fontSize: 14,
  },
  usageValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  usagePercentage: {
    fontSize: 12,
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  requestsContainer: {
    borderRadius: 12,
    padding: 16,
  },
  requestCard: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  requestTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  requestInfo: {
    fontSize: 14,
    marginBottom: 2,
  },
  urgencyIndicator: {
    marginTop: 8,
  },
  urgencyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 20,
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  infoSection: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  imageContainer: {
    flexDirection: 'row',
  },
  documentImage: {
    width: 150,
    height: 200,
    borderRadius: 8,
    marginRight: 12,
    resizeMode: 'cover',
  },
  notesInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 8,
    marginHorizontal: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FreeVerificationAdminDashboard;
