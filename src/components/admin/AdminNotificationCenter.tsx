import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { Bell, Shield, AlertCircle, MessageSquare, Home, ChevronRight } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { formatDistanceToNow } from 'date-fns';

import { supabase } from "@utils/supabaseUtils";
import { sendPushNotification, registerForPushNotifications, savePushToken, getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead, deleteNotification, getNotificationSettings, updateNotificationSettings } from "@utils/notificationUtils";
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

export interface AdminNotificationCenterProps {
  maxNotifications?: number;
  onlyUnread?: boolean;
  showSuspiciousProfilesOnly?: boolean;
}

export function AdminNotificationCenter({
  maxNotifications = 10,
  onlyUnread = false,
  showSuspiciousProfilesOnly = false,
}: AdminNotificationCenterProps) {
  const router = useRouter();
  const { colors, spacing, borderRadius } = useTheme();
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      
      // Get the current user to ensure they're admin
      const { data: userData } = await supabase.auth.getUser();
      if (!userData || !userData.user) {
        throw new Error('Not authenticated');
      }
      
      // Get notifications
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('created_at', { ascending: false });
      
      // Filter by read status if requested
      if (onlyUnread) {
        query = query.eq('is_read', false);
      }
      
      // Filter by notification type if suspicious profiles only
      if (showSuspiciousProfilesOnly) {
        query = query.eq('type', 'suspicious_profile');
      }
      
      // Limit the number of notifications
      query = query.limit(maxNotifications);
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      setNotifications(data || []);
    } catch (err) {
      console.error('Error loading notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [maxNotifications, onlyUnread, showSuspiciousProfilesOnly]);

  // Initial load
  useEffect(() => {
    loadNotifications();
    
    // Set up real-time subscription for new notifications
    const subscription = supabase
      .channel('admin_notifications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
      }, () => {
        loadNotifications();
      })
      .subscribe();
      
    return () => {
      subscription.unsubscribe();
    };
  }, [loadNotifications]);

  const onRefresh = () => {
    setRefreshing(true);
    loadNotifications();
  };

  const handleNotificationPress = async (notification: any) => {
    try {
      // Mark as read
      if (!notification.is_read) {
        await supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('id', notification.id);
      }
      
      // Navigate based on notification type
      if (notification.type === 'suspicious_profile') {
        router.push('/admin/suspicious-profiles');
      } else if (notification.type === 'message') {
        router.push('/(tabs)/messages');
      } else if (notification.type === 'match') {
        router.push('/matching');
      } else if (notification.type === 'roomUpdate') {
        router.push('/(tabs)/room');
      } else {
        // Handle other notification types
        router.push('/notifications');
      }
    } catch (err) {
      console.error('Error handling notification:', err);
    }
  };

  const renderNotificationIcon = (type: string) => {
    switch (type) {
      case 'suspicious_profile':
        return <Shield size={20} color={colors.error[500]} />;
      case 'message':
        return <MessageSquare size={20} color={colors.primary[500]} />;
      case 'match':
        return <Shield size={20} color={colors.success[500]} />;
      case 'roomUpdate':
        return <Home size={20} color={colors.info[500]} />;
      case 'system':
      default:
        return <Bell size={20} color={colors.warning[500]} />;
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (err) {
      return 'recently';
    }
  };

  // Render a notification item
  const renderItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        { 
          backgroundColor: item.is_read ? colors.white : colors.gray[50],
          borderRadius,
          borderColor: colors.gray[200],
        },
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={[styles.iconContainer, { backgroundColor: colors.gray[100] }]}>
        {renderNotificationIcon(item.type)}
      </View>
      <View style={styles.contentContainer}>
        <Text 
          style={[
            styles.notificationTitle, 
            { 
              color: colors.gray[900],
              fontWeight: item.is_read ? '400' : '600',
            }
          ]}
        >
          {item.title}
        </Text>
        <Text 
          style={[
            styles.notificationBody, 
            { color: colors.gray[700] }
          ]}
          numberOfLines={2}
        >
          {item.body}
        </Text>
        <Text style={[styles.notificationTime, { color: colors.gray[500] }]}>
          {formatTime(item.created_at)}
        </Text>
      </View>
      <ChevronRight size={16} color={colors.gray[400]} />
    </TouchableOpacity>
  );

  if (loading && !refreshing && notifications.length === 0) {
    return (
      <View style={[styles.loadingContainer, { paddingVertical: spacing.md }]}>
        <ActivityIndicator size="small" color={colors.primary[500]} />
        <Text style={[styles.loadingText, { color: colors.gray[500] }]}>
          Loading notifications...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { paddingVertical: spacing.md }]}>
        <AlertCircle size={20} color={colors.error[500]} />
        <Text style={[styles.errorText, { color: colors.error[700] }]}>
          Failed to load notifications
        </Text>
      </View>
    );
  }

  if (notifications.length === 0) {
    return (
      <View style={[styles.emptyContainer, { paddingVertical: spacing.lg }]}>
        <Bell size={24} color={colors.gray[400]} />
        <Text style={[styles.emptyText, { color: colors.gray[500], marginTop: spacing.sm }]}>
          {showSuspiciousProfilesOnly 
            ? 'No suspicious profile alerts' 
            : 'No new notifications'}
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={notifications}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      contentContainerStyle={[styles.listContainer, { padding: spacing.sm }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    />
  );
}

const styles = StyleSheet.create({
  listContainer: {
    flexGrow: 1,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
    marginRight: 8,
  },
  notificationTitle: {
    fontSize: 15,
    marginBottom: 2,
  },
  notificationBody: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginLeft: 8,
    fontSize: 14,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
}); 
export default AdminNotificationCenter;
