import React, { useState, useCallback, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  DollarSign,
  Calendar,
  BedDouble,
  Users,
  ArrowLeft,
  ArrowRight,
  Check,
  Home,
  Info,
  Plus,
  Camera,
  MapPin,
  Eye,
} from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import DateTimePicker from '@react-native-community/datetimepicker';

import { Button } from '@design-system';
import { ImageUploader } from '@components/listing/ImageUploader';
import { LocationPicker } from '@components/listing/LocationPicker';
import { ListingPreview } from '@components/listing/ListingPreview';
import { locationService, LocationData } from '@services/LocationService';
// MIGRATION: Replace legacy roomService with unifiedRoomService
import { unifiedRoomService, type RoomFormData } from '@services/enhanced/UnifiedRoomService';
import { formatDateString, formatDateForDisplay } from '@utils/date';
import { validateListingForm } from '@utils/listingValidation';
import { useColorFix } from '@hooks/useColorFix';
import { colorWithOpacity } from '@design-system';

const ROOM_TYPES = [
  { value: 'private', label: 'Private Room' },
  { value: 'shared', label: 'Shared Room' },
  { value: 'entire', label: 'Entire Place' },
];

const AMENITIES = [
  { value: 'wifi', label: 'WiFi' },
  { value: 'ac', label: 'Air Conditioning' },
  { value: 'kitchen', label: 'Kitchen Access' },
  { value: 'laundry', label: 'Laundry' },
  { value: 'parking', label: 'Parking' },
  { value: 'tv', label: 'TV' },
  { value: 'bathroom', label: 'Private Bathroom' },
  { value: 'furnished', label: 'Furnished' },
];

const ROOMMATE_PREFERENCES = [
  { value: 'students', label: 'Students' },
  { value: 'professionals', label: 'Professionals' },
  { value: 'families', label: 'Families' },
  { value: 'females', label: 'Females Only' },
  { value: 'males', label: 'Males Only' },
  { value: 'non_smokers', label: 'Non-Smokers' },
  { value: 'no_pets', label: 'No Pets' },
  { value: 'quiet', label: 'Quiet Occupants' },
];

interface FormData extends Omit<RoomFormData, 'images'> {
  images: string[];
  imagePaths: string[];
  locationData: LocationData | null;
  locationText: string;
}

interface ListingFormProps {
  onSubmit: (data: RoomFormData) => Promise<void>;
  onSaveDraft?: (data: Partial<FormData>) => Promise<void>;
  initialValues?: Partial<FormData>;
  isEditMode?: boolean;
}

export const ListingForm: React.FC<ListingFormProps> = ({ 
  onSubmit, 
  onSaveDraft,
  initialValues = {}, 
  isEditMode = false 
}) => {
  const router = useRouter();
  const theme = useTheme();
  const styles = createStyles(theme);
  
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    price: 0,
    location: '',
    locationData: null,
    locationText: '',
    room_type: 'private',
    bedrooms: 1,
    bathrooms: 1,
    furnished: false,
    pets_allowed: false,
    images: [],
    imagePaths: [],
    amenities: [],
    preferences: [],
    move_in_date: formatDateString(new Date()),
    ...initialValues,
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [preference, setPreference] = useState('');
  const [amenity, setAmenity] = useState('');

  // If we have initial values, populate the form
  useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0) {
      setFormData(prev => ({
        ...prev,
        ...initialValues,
        // Ensure locationText is set from location if available
        locationText: initialValues.location || prev.locationText,
      }));
    }
  }, [initialValues]);

  // Update form field
  const updateField = <K extends keyof FormData>(field: K, value: FormData[K]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle text input change
  const handleTextChange = (field: keyof FormData) => (text: string) => {
    updateField(field, text as any);
  };

  // Handle numeric input change
  const handleNumberChange = (field: keyof FormData) => (text: string) => {
    const value = text.replace(/[^0-9]/g, '');
    updateField(field, (value ? parseInt(value, 10) : 0) as any);
  };

  // Handle toggle change
  const handleToggleChange = (field: keyof FormData) => (value: boolean) => {
    updateField(field, value as any);
  };

  // Handle array item toggle
  const handleArrayItemToggle = (field: 'amenities' | 'preferences', item: string) => {
    const currentArray = formData[field] as string[];
    if (currentArray.includes(item)) {
      updateField(field, currentArray.filter(i => i !== item) as any);
    } else {
      updateField(field, [...currentArray, item] as any);
    }
  };

  // Handle location change
  const handleLocationChange = (location: LocationData | null) => {
    updateField('locationData', location);
    if (location) {
      // Use name as primary location value for validation
      const locationValue = location.name || location.description || '';
      updateField('location', locationValue);
      updateField('locationText', location.name);
      // Clear form error when location is selected
      setFormError(null);
    } else {
      updateField('location', '');
      updateField('locationText', '');
    }
  };

  // Handle location text change
  const handleLocationTextChange = (text: string) => {
    updateField('locationText', text);
    updateField('location', text); // Update location field for validation
    if (!text) {
      updateField('locationData', null);
      updateField('location', '');
    } else {
      // Clear form error when text is entered
      setFormError(null);
    }
  };

  // Handle date change
  const handleDateChange = (_: any, date?: Date) => {
    setShowDatePicker(false);
    if (date) {
      updateField('move_in_date', formatDateString(date));
    }
  };

  // Validate current step
  const validateStep = (): boolean => {
    setFormError(null);
    
    // Use our validation utility for steps 1-5
    if (step >= 1 && step <= 5) {
      const error = validateListingForm(formData, step as 1 | 2 | 3 | 4 | 5);
      if (error) {
        setFormError(error);
        return false;
      }
    }
    
    // For preview step, validate the entire form
    if (step === 6) {
      const error = validateListingForm(formData, 'all');
      if (error) {
        setFormError(`Please fix the following issue before submitting: ${error}`);
        return false;
      }
    }
    
    return true;
  };

  // Navigate to next step
  const handleNext = () => {
    if (validateStep()) {
      // If we're on the last step, submit the form
      if (step === 6) {
        handleSubmit();
      } else {
        setStep(step + 1);
        
        // Save draft when moving to next step
        if (onSaveDraft) {
          onSaveDraft(formData);
        }
      }
    }
  };

  // Navigate to previous step
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };
  
  // Save current form data as draft
  const handleSaveDraft = async () => {
    if (onSaveDraft) {
      await onSaveDraft(formData);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateStep()) return;

    setLoading(true);

    try {
      // Prepare data for API
      const roomData: RoomFormData = {
        title: formData.title,
        description: formData.description,
        price: formData.price,
        location: formData.location,
        room_type: formData.room_type,
        bedrooms: formData.bedrooms,
        bathrooms: formData.bathrooms,
        furnished: formData.furnished,
        pets_allowed: formData.pets_allowed,
        amenities: formData.amenities,
        preferences: formData.preferences,
        move_in_date: formData.move_in_date,
        images: formData.images,
        // Include additional fields from updated RoomFormData interface
        status: 'available', // Default to available
        locationData: formData.locationData,
        locationText: formData.locationText,
        // Only include location_id if locationData exists and has an id
        ...(formData.locationData?.id ? { location_id: formData.locationData.id } : {})
      };

      await onSubmit(roomData);
    } catch (error) {
      console.error('Form submission error:', error);
      setFormError(error instanceof Error ? error.message : 'Failed to submit the form');
    } finally {
      setLoading(false);
    }
  };

  // Render progress steps
  const renderProgress = () => {
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressStepsWrapper}>
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <View
              key={i}
              style={[
                styles.progressStep,
                i <= step ? styles.progressStepCompleted : {},
              ]}
            >
              <View style={[styles.progressDot, i <= step ? styles.progressDotActive : {}]}>
                <Text style={i <= step ? styles.progressDotTextActive : styles.progressDotText}>
                  {i}
                </Text>
              </View>
            </View>
          ))}
        </View>
        
        {onSaveDraft && (
          <TouchableOpacity 
            style={styles.saveDraftButton} 
            onPress={handleSaveDraft}
          >
            <Text style={styles.saveDraftText}>Save Draft</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Render form step content
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return renderBasicDetailsStep();
      case 2:
        return renderLocationStep();
      case 3:
        return renderRoomDetailsStep();
      case 4:
        return renderAmenitiesStep();
      case 5:
        return renderPhotosStep();
      case 6:
        return renderPreviewStep();
      default:
        return null;
    }
  };
  
  // Step 6: Preview
  const renderPreviewStep = () => {
    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Step 6: Preview & Submit</Text>
        <Text style={styles.stepDescription}>
          Review how your listing will appear to potential roommates
        </Text>
        
        <ListingPreview data={formData} />
        
        <View style={styles.previewNote}>
          <Info size={16} color={theme.colors.warning} />
          <Text style={styles.previewNoteText}>
            This is a preview of how your listing will appear. You can go back to make changes before submitting.
          </Text>
        </View>
      </View>
    );
  };

  // Step 1: Basic Details
  const renderBasicDetailsStep = () => (
    <View>
      <Text style={styles.stepTitle}>Step 1: Basic Details</Text>
      <Text style={styles.stepDescription}>Let's start with the basics of your room listing</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Title</Text>
        <View style={styles.inputContainer}>
          <Home size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.input}
            value={formData.title}
            onChangeText={handleTextChange('title')}
            placeholder="Enter a title for your listing"
            placeholderTextColor={theme.colors.textMuted}
          />
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Price (per month)</Text>
        <View style={styles.inputContainer}>
          <DollarSign size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.input}
            value={formData.price ? formData.price.toString() : ''}
            onChangeText={handleNumberChange('price')}
            placeholder="Enter monthly rent"
            placeholderTextColor={theme.colors.textMuted}
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Description</Text>
        <View style={styles.textAreaContainer}>
          <TextInput
            style={styles.textArea}
            value={formData.description}
            onChangeText={handleTextChange('description')}
            placeholder="Describe your place - include details about the room, the property, and the neighborhood"
            placeholderTextColor={theme.colors.textMuted}
            multiline
            numberOfLines={5}
            textAlignVertical="top"
          />
        </View>
        <Text style={styles.helperText}>
          Min 30 characters. Currently: {formData.description.length}
        </Text>
      </View>
    </View>
  );

  // Step 2: Location
  const renderLocationStep = () => (
    <View>
      <Text style={styles.stepTitle}>Step 2: Location</Text>
      <Text style={styles.stepDescription}>Where is your room located?</Text>

      <LocationPicker
        value={formData.locationData || null}
        onChange={handleLocationChange}
        textValue={formData.locationText}
        onTextChange={handleLocationTextChange}
        error={undefined} // Let the main form handle error display
        label="Property Location"
        placeholder="Search for a neighborhood, city, etc."
      />

      <Text style={styles.helperText}>
        Select an existing location or create a new one if yours is not in the list
      </Text>
    </View>
  );

  // Step 3: Room Details
  const renderRoomDetailsStep = () => (
    <View>
      <Text style={styles.stepTitle}>Step 3: Room Details</Text>
      <Text style={styles.stepDescription}>Tell us more about your room</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Room Type</Text>
        <View style={styles.optionsContainer}>
          {ROOM_TYPES.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                formData.room_type === option.value ? styles.optionButtonActive : {},
              ]}
              onPress={() => updateField('room_type', option.value as any)}
            >
              <Text
                style={[
                  styles.optionText,
                  formData.room_type === option.value ? styles.optionTextActive : {},
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Move-in Date</Text>
        <View style={styles.inputContainer}>
          <Calendar size={20} color={theme.colors.textSecondary} />
          <TouchableOpacity style={styles.inputWithIcon} onPress={() => setShowDatePicker(true)}>
            <Text style={styles.dateText}>
              {formatDateForDisplay(new Date(formData.move_in_date))}
            </Text>
          </TouchableOpacity>
        </View>
        {showDatePicker && (
          <DateTimePicker
            value={new Date(formData.move_in_date)}
            mode="date"
            display="default"
            onChange={handleDateChange}
            minimumDate={new Date()}
          />
        )}
      </View>

      <View style={styles.formRow}>
        <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={styles.label}>Bedrooms</Text>
          <View style={styles.inputContainer}>
            <BedDouble size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.input}
              value={formData.bedrooms.toString()}
              onChangeText={handleNumberChange('bedrooms')}
              placeholder="1"
              placeholderTextColor={theme.colors.textMuted}
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>Bathrooms</Text>
          <View style={styles.inputContainer}>
            <BedDouble size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.input}
              value={formData.bathrooms.toString()}
              onChangeText={handleNumberChange('bathrooms')}
              placeholder="1"
              placeholderTextColor={theme.colors.textMuted}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Furnished</Text>
        <Switch
          value={formData.furnished}
          onValueChange={handleToggleChange('furnished')}
          trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.3) }}
          thumbColor={formData.furnished ? theme.colors.primary : theme.colors.border}
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Pets Allowed</Text>
        <Switch
          value={formData.pets_allowed}
          onValueChange={handleToggleChange('pets_allowed')}
          trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.3) }}
          thumbColor={formData.pets_allowed ? theme.colors.primary : theme.colors.border}
        />
      </View>
    </View>
  );

  // Step 4: Amenities & Preferences
  const renderAmenitiesStep = () => (
    <View>
      <Text style={styles.stepTitle}>Step 4: Amenities & Preferences</Text>
      <Text style={styles.stepDescription}>What amenities and preferences are important?</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Amenities Available</Text>
        <View style={styles.checkboxGroup}>
          {AMENITIES.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.checkboxRow}
              onPress={() => handleArrayItemToggle('amenities', option.value)}
            >
              <View
                style={[
                  styles.checkbox,
                  formData.amenities.includes(option.value) ? styles.checkboxChecked : {},
                ]}
              >
                {formData.amenities.includes(option.value) && <Check size={14} color={theme.colors.background} />}
              </View>
              <Text style={styles.checkboxLabel}>{option.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Roommate Preferences</Text>
        <View style={styles.inputWithButton}>
          <TextInput
            style={styles.inputWithButtonField}
            placeholder="e.g., Non-smoker, Students"
            value={preference}
            onChangeText={setPreference}
          />
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              if (preference.trim() && !formData.preferences.includes(preference.trim())) {
                handleArrayItemToggle('preferences', preference.trim());
                setPreference('');
              }
            }}
          >
            <Plus size={20} color={theme.colors.background} />
          </TouchableOpacity>
        </View>

        <View style={styles.tagsContainer}>
          {formData.preferences.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.tag}
              onPress={() => handleArrayItemToggle('preferences', item)}
            >
              <Text style={styles.tagText}>{item}</Text>
              <Text style={styles.removeTag}>×</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  // Step 5: Photos
  const renderPhotosStep = () => (
    <View>
      <Text style={styles.stepTitle}>Step 5: Photos</Text>
      <Text style={styles.stepDescription}>Add photos to showcase your room</Text>

      <ImageUploader
        images={formData.images}
        onImagesChange={images => updateField('images', images)}
        paths={formData.imagePaths}
        onPathsChange={paths => updateField('imagePaths', paths)}
        maxImages={8}
        title="Room Photos"
        subtitle="Upload up to 8 photos of your space"
      />

      <Text style={styles.helperText}>
        Clear, well-lit photos will help your listing stand out. Include shots of the room, common
        areas, and any special features.
      </Text>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Form Progress */}
      {renderProgress()}

      {/* Form Step Title & Content */}
      <View style={styles.formContainer}>{renderStepContent()}</View>

      {/* Error Message */}
      {formError && (
        <View style={styles.errorContainer}>
          <Info size={16} color={theme.colors.error} />
          <Text style={styles.errorText}>{formError}</Text>
        </View>
      )}

      {/* Navigation Buttons */}
      <View style={styles.navButtons}>
        {step > 1 ? (
          <TouchableOpacity style={styles.backButton} onPress={handleBack} disabled={loading}>
            <ArrowLeft size={20} color={theme.colors.textSecondary} />
            <Text style={styles.backButtonText}>Back</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.spacer} />
        )}

        <TouchableOpacity style={styles.nextButton} onPress={handleNext} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={theme.colors.background} />
          ) : (
            <>
              <Text style={styles.nextButtonText}>
                {step === 6 ? 'Submit Listing' : (step === 5 ? 'Preview Listing' : 'Continue')}
              </Text>
              {step < 6 && <ArrowRight size={20} color={theme.colors.background} />}
            </>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing?.md || 16,
  },
  progressContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.lg || 12,
    padding: theme.spacing?.md || 16,
    marginBottom: theme.spacing?.lg || 24,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  progressStepsWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: theme.spacing?.md || 16,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: theme.spacing?.xs || 4,
  },
  progressStepCompleted: {
    // Enhanced completed step styling
  },
  progressDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.border,
  },
  progressDotActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  progressDotCompleted: {
    backgroundColor: theme.colors.success,
    borderColor: theme.colors.success,
  },
  progressDotText: {
    color: theme.colors.textSecondary,
    fontSize: theme.typography?.fontSize?.sm || 14,
    fontWeight: '600',
  },
  progressDotTextActive: {
    color: theme.colors.textInverse,
    fontWeight: '700',
  },
  saveDraftButton: {
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
    paddingHorizontal: theme.spacing?.md || 12,
    paddingVertical: theme.spacing?.sm || 8,
    borderRadius: theme.borderRadius?.md || 8,
    borderWidth: 1,
    borderColor: colorWithOpacity(theme.colors.primary, 0.2),
  },
  saveDraftText: {
    color: theme.colors.primary,
    fontSize: theme.typography?.fontSize?.sm || 14,
    fontWeight: '600',
  },
  formContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.xl || 16,
    padding: theme.spacing?.lg || 20,
    marginBottom: theme.spacing?.lg || 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 5,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  stepTitle: {
    fontSize: theme.typography?.fontSize?.xl || 22,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing?.sm || 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing?.lg || 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  formGroup: {
    marginBottom: theme.spacing?.lg || 20,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: theme.spacing?.md || 16,
    gap: theme.spacing?.md || 12,
  },
  label: {
    fontSize: theme.typography?.fontSize?.md || 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing?.sm || 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius?.lg || 12,
    paddingHorizontal: theme.spacing?.md || 16,
    height: 52,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  inputFocused: {
    borderColor: theme.colors.primary,
    shadowColor: theme.colors.primary,
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  input: {
    flex: 1,
    marginLeft: theme.spacing?.sm || 12,
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  textAreaContainer: {
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius?.lg || 12,
    paddingHorizontal: theme.spacing?.md || 16,
    paddingVertical: theme.spacing?.md || 12,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  textArea: {
    height: 100,
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    fontWeight: '500',
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.textMuted,
    marginTop: theme.spacing?.xs || 4,
    fontStyle: 'italic',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: theme.spacing?.sm || 8,
    gap: theme.spacing?.sm || 8,
  },
  optionButton: {
    paddingHorizontal: theme.spacing?.md || 16,
    paddingVertical: theme.spacing?.sm || 10,
    borderRadius: theme.borderRadius?.lg || 12,
    backgroundColor: theme.colors.surfaceVariant,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  optionButtonActive: {
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
    borderColor: theme.colors.primary,
    shadowColor: theme.colors.primary,
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  optionText: {
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  optionTextActive: {
    color: theme.colors.primary,
    fontWeight: '700',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.background,
    padding: theme.spacing?.md || 16,
    borderRadius: theme.borderRadius?.lg || 12,
    marginBottom: theme.spacing?.md || 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  switchLabel: {
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
  },
  checkboxGroup: {
    marginTop: theme.spacing?.sm || 8,
    gap: theme.spacing?.sm || 8,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: theme.spacing?.md || 12,
    borderRadius: theme.borderRadius?.lg || 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: theme.borderRadius?.sm || 6,
    borderWidth: 2,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },
  checkboxChecked: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  checkboxLabel: {
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    marginLeft: theme.spacing?.sm || 12,
    fontWeight: '500',
    flex: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colorWithOpacity(theme.colors.error, 0.1),
    borderRadius: theme.borderRadius?.lg || 12,
    padding: theme.spacing?.md || 12,
    marginBottom: theme.spacing?.md || 16,
    borderWidth: 1,
    borderColor: colorWithOpacity(theme.colors.error, 0.3),
  },
  errorText: {
    flex: 1,
    color: theme.colors.error,
    marginLeft: theme.spacing?.sm || 8,
    fontSize: theme.typography?.fontSize?.sm || 14,
    fontWeight: '600',
  },
  navButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing?.xl || 24,
    marginBottom: theme.spacing?.lg || 24,
    gap: theme.spacing?.md || 12,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing?.md || 14,
    paddingHorizontal: theme.spacing?.lg || 20,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius?.lg || 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flex: 1,
    justifyContent: 'center',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  backButtonText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 4,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius?.lg || 12,
    paddingVertical: theme.spacing?.md || 14,
    paddingHorizontal: theme.spacing?.lg || 24,
    flex: 1,
    justifyContent: 'center',
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  nextButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 4,
  },
  spacer: {
    flex: 1,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  dateText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: theme.colors.text,
  },
  inputWithButton: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  inputWithButtonField: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: theme.colors.background,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    padding: theme.spacing?.md || 16,
    borderTopRightRadius: theme.borderRadius?.lg || 12,
    borderBottomRightRadius: theme.borderRadius?.lg || 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
  },
  tag: {
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius?.md || 16,
    paddingHorizontal: theme.spacing?.sm || 12,
    paddingVertical: theme.spacing?.xs || 6,
    margin: theme.spacing?.xs || 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagText: {
    color: theme.colors.text,
    fontSize: 14,
    marginRight: 4,
  },
  removeTag: {
    color: theme.colors.textSecondary,
    fontSize: 18,
    marginLeft: 4,
  },
  stepContainer: {
    marginBottom: theme.spacing?.lg || 24,
  },
  previewNote: {
    flexDirection: 'row',
    backgroundColor: colorWithOpacity(theme.colors.warning, 0.2),
    padding: theme.spacing?.sm || 12,
    borderRadius: theme.borderRadius?.md || 8,
    marginTop: theme.spacing?.md || 16,
    alignItems: 'flex-start',
  },
  previewNoteText: {
    flex: 1,
    marginLeft: theme.spacing?.sm || 8,
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.warning,
    lineHeight: 20,
  },
});

export default ListingForm;
