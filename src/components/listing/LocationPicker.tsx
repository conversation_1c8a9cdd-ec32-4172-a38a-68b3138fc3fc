import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { MapPin, Search, Check } from 'lucide-react-native';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

import { locationService, LocationData } from '@services/LocationService';
import { useColorFix } from '@hooks/useColorFix';
import {  colorWithOpacity, type Theme  } from '@design-system';

interface LocationPickerProps {
  value: LocationData | null;
  onChange: (location: LocationData | null) => void;
  textValue?: string;
  onTextChange?: (text: string) => void;
  error?: string;
  label?: string;
  placeholder?: string;
}

export const LocationPicker: React.FC<LocationPickerProps> = ({
  value,
  onChange,
  textValue = '',
  onTextChange,
  error,
  label = 'Location',
  placeholder = 'Enter location or address',
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [searchQuery, setSearchQuery] = useState<string>(textValue);
  const [searching, setSearching] = useState<boolean>(false);
  const [results, setResults] = useState<LocationData[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);

  // Update internal search query when external text value changes
  useEffect(() => {
    if (textValue !== searchQuery) {
      setSearchQuery(textValue);
    }
  }, [textValue]);

  // Handle search query change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    if (onTextChange) {
      onTextChange(query);
    }

    if (query.length > 2) {
      searchLocations(query);
    } else {
      setResults([]);
      setShowResults(false);
    }
  };

  // Search for locations
  const searchLocations = async (query: string) => {
    setSearching(true);
    try {
      const locations = await locationService.searchLocations(query);
      setResults(locations);
      setShowResults(true);
    } catch (err) {
      console.error('Error searching locations:', err);
      Alert.alert('Search Error', 'Failed to search locations. Please try again.');
    } finally {
      setSearching(false);
    }
  };

  // Handle location selection
  const handleSelectLocation = (location: LocationData) => {
    onChange(location);
    setSearchQuery(formatLocationDisplay(location));
    setShowResults(false);
  };

  // Format location for display
  const formatLocationDisplay = (location: LocationData): string => {
    const parts = [];

    if (location.neighborhood) {
      parts.push(location.neighborhood);
    }

    parts.push(location.city);

    if (location.state) {
      parts.push(location.state);
    }

    return parts.join(', ');
  };

  // Create a new location if it doesn't exist
  const handleCreateLocation = async () => {
    if (searchQuery.trim().length < 3) {
      Alert.alert('Invalid Input', 'Please enter a valid location name');
      return;
    }

    setSearching(true);
    try {
      // Extract city from query - this is a simplified approach
      const [neighborhood, cityPart] = searchQuery.split(',').map(part => part.trim());
      const city = cityPart || neighborhood; // If only one part, assume it's the city

      const newLocation = await locationService.createLocation({
        name: searchQuery.trim(),
        city,
        neighborhood: cityPart ? neighborhood : undefined,
        country: 'Ethiopia', // Default country - could be made more dynamic
      });

      if (newLocation) {
        handleSelectLocation(newLocation);
        Alert.alert('Success', 'New location created successfully');
      } else {
        throw new Error('Failed to create location');
      }
    } catch (err) {
      console.error('Error creating location:', err);
      Alert.alert('Error', 'Failed to create new location. Please try again.');
    } finally {
      setSearching(false);
    }
  };

  // Render result item
  const renderLocationItem = ({ item }: { item: LocationData }) => (
    <TouchableOpacity style={styles.resultItem} onPress={() => handleSelectLocation(item)}>
      <MapPin size={16} color={theme.colors.textSecondary} />
      <View style={styles.locationInfo}>
        <Text style={styles.locationName}>{formatLocationDisplay(item)}</Text>
        {item.description && <Text style={styles.locationDescription}>{item.description}</Text>}
      </View>
      {value?.id === item.id && <Check size={16} color={theme.colors.success} />}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <View style={[styles.inputContainer, error ? styles.inputError : null]}>
        <MapPin size={20} color={theme.colors.textSecondary} />
        <TextInput
          style={styles.input}
          value={searchQuery}
          onChangeText={handleSearchChange}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textMuted}
          onFocus={() => searchQuery.length > 2 && setShowResults(true)}
        />
        {searching && <ActivityIndicator size="small" color={theme.colors.primary} />}
        {!searching && searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => handleSearchChange('')} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {showResults && (
        <View style={styles.resultsContainer}>
          {results.length > 0 ? (
            <ScrollView 
              style={styles.resultsList}
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              maxHeight={200}
            >
              {results.map((item) => (
                <View key={item.id}>
                  {renderLocationItem({ item })}
                </View>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>No locations found</Text>
              <TouchableOpacity
                style={styles.createButton}
                onPress={handleCreateLocation}
                disabled={searching}
              >
                <Text style={styles.createButtonText}>Create New Location</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginVertical: theme.spacing?.lg || 16,
  },
  label: {
    fontSize: theme.typography?.fontSize?.lg || 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing?.md || 12,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.xl || 16,
    paddingHorizontal: theme.spacing?.lg || 18,
    height: 56,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  inputError: {
    borderColor: theme.colors.error,
    shadowColor: theme.colors.error,
    shadowOpacity: 0.2,
  },
  input: {
    flex: 1,
    marginLeft: theme.spacing?.md || 12,
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  clearButton: {
    backgroundColor: colorWithOpacity(theme.colors.textMuted, 0.1),
    borderRadius: theme.borderRadius?.full || 16,
    padding: theme.spacing?.sm || 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearButtonText: {
    color: theme.colors.textMuted,
    fontSize: theme.typography?.fontSize?.sm || 14,
    fontWeight: '600',
  },
  errorText: {
    color: theme.colors.error,
    marginTop: theme.spacing?.sm || 8,
    fontSize: theme.typography?.fontSize?.sm || 14,
    fontWeight: '600',
    marginLeft: theme.spacing?.sm || 8,
  },
  resultsContainer: {
    marginTop: theme.spacing?.sm || 8,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.xl || 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    maxHeight: 240,
    overflow: 'hidden',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  resultsList: {
    padding: theme.spacing?.sm || 8,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing?.md || 14,
    paddingHorizontal: theme.spacing?.lg || 16,
    borderBottomWidth: 1,
    borderBottomColor: colorWithOpacity(theme.colors.border, 0.5),
    backgroundColor: theme.colors.background,
    marginVertical: theme.spacing?.xs || 2,
    borderRadius: theme.borderRadius?.lg || 12,
  },
  locationInfo: {
    flex: 1,
    marginLeft: theme.spacing?.md || 12,
  },
  locationName: {
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing?.tiny || 2,
  },
  locationDescription: {
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  noResultsContainer: {
    padding: theme.spacing?.xl || 20,
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    margin: theme.spacing?.sm || 8,
    borderRadius: theme.borderRadius?.lg || 12,
  },
  noResultsText: {
    fontSize: theme.typography?.fontSize?.md || 16,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing?.md || 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  createButton: {
    paddingVertical: theme.spacing?.md || 12,
    paddingHorizontal: theme.spacing?.lg || 20,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius?.xl || 16,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  createButtonText: {
    fontSize: theme.typography?.fontSize?.md || 15,
    fontWeight: '700',
    color: theme.colors.textInverse,
    textAlign: 'center',
  },
});

export default LocationPicker;
