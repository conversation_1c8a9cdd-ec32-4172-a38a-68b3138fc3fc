import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { CreditCard, Smartphone, Building, X, Lock, Check } from 'lucide-react-native';
import Text from '@components/ui/core/Text';
import { Button } from '@components/ui/core/Button';
import { Spinner } from '@components/ui/core/Spinner';
import { useSubscriptionStore, type PaymentMethod } from '@store/subscriptionStore';
import { getListingPlan } from '@config/listingConfig';
import type { PaymentMethod as SecurePaymentMethod } from '@core/services/payment/SecurePaymentService';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { CardInputForm, type CardData } from '@components/payment/CardInputForm';
import { SavedCardsView } from '@components/payment/SavedCardsView';
import { existingPaymentMethodService, type ExistingPaymentMethod } from '@services/payment/ExistingPaymentMethodService';
import { getCurrentUser } from '@utils/authUtils';

interface PaymentMethodModalProps {
  visible: boolean;
  onClose: () => void;
  onPaymentComplete: (paymentMethod: SecurePaymentMethod) => void;
  planId: string;
  isProcessing?: boolean;
}

export function PaymentMethodModal({
  visible,
  onClose,
  onPaymentComplete,
  planId,
  isProcessing = false
}: PaymentMethodModalProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('credit_card');
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(false);
  const [currentStep, setCurrentStep] = useState<'method_selection' | 'saved_cards' | 'card_input'>('method_selection');
  const [existingCards, setExistingCards] = useState<ExistingPaymentMethod[]>([]);
  const [selectedExistingCard, setSelectedExistingCard] = useState<string | null>(null);
  const { paymentMethods, fetchPaymentMethods, addPaymentMethod } = useSubscriptionStore();

  const plan = getListingPlan(planId);

  useEffect(() => {
    if (visible) {
      loadPaymentMethods();
      setCurrentStep('method_selection'); // Reset to method selection when modal opens
      setSelectedExistingCard(null); // Reset selected card
    }
  }, [visible]);

  const loadPaymentMethods = async () => {
    setIsLoadingPaymentMethods(true);
    try {
      const user = await getCurrentUser();
      if (user?.id) {
        const cards = await existingPaymentMethodService.getPaymentMethods(user.id);
        console.log('Loaded existing payment methods:', JSON.stringify({ count: cards.length }));
        setExistingCards(cards);
      }
    } catch (error) {
      console.error('Error loading payment methods:', error);
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  };

  const handlePayment = async () => {
    if (!plan) {
      Alert.alert('Error', 'Invalid plan selected');
      return;
    }

    // If credit card is selected, check for existing cards first
    if (selectedPaymentMethod === 'credit_card') {
      if (existingCards.length > 0) {
        setCurrentStep('saved_cards');
      } else {
        setCurrentStep('card_input');
      }
      return;
    }

    // For other payment methods, proceed directly
    const paymentMethod: SecurePaymentMethod = {
      type: selectedPaymentMethod as any,
      token: `mock_token_${Date.now()}`, // In real implementation, this would be from payment provider
      metadata: {
        method: selectedPaymentMethod,
        plan_id: planId,
        amount: plan.price,
      }
    };

    onPaymentComplete(paymentMethod);
  };

  const handleCardSubmit = async (cardData: CardData) => {
    try {
      const user = await getCurrentUser();
      if (!user?.id) {
        Alert.alert('Error', 'User not authenticated');
        return;
      }

      // Save card to existing database
      const savedCard = await existingPaymentMethodService.saveCard(user.id, cardData);
      console.log('Card saved successfully:', JSON.stringify({ cardId: savedCard.id }));

      // Process payment with the saved card
      const paymentId = await existingPaymentMethodService.processPaymentWithMethod(
        user.id,
        savedCard.id,
        plan.price,
        {
          plan_id: planId,
          listing_payment: true,
        }
      );

      // Convert to SecurePaymentMethod format for compatibility
      const paymentMethod: SecurePaymentMethod = {
        type: 'credit_card',
        token: savedCard.provider_token,
        metadata: {
          method: 'credit_card',
          plan_id: planId,
          amount: plan.price,
          payment_id: paymentId,
          payment_method_id: savedCard.id,
          cardNumber: savedCard.card_last_four,
          cardBrand: savedCard.card_brand,
        }
      };

      onPaymentComplete(paymentMethod);
    } catch (error) {
      console.error('Card submit failed:', error);
      Alert.alert('Payment Failed', 'Failed to process payment. Please try again.');
    }
  };

  const handleExistingCardPayment = async () => {
    if (!selectedExistingCard) {
      Alert.alert('Error', 'Please select a payment method');
      return;
    }

    try {
      const user = await getCurrentUser();
      if (!user?.id) {
        Alert.alert('Error', 'User not authenticated');
        return;
      }

      // Process payment with existing card
      const paymentId = await existingPaymentMethodService.processPaymentWithMethod(
        user.id,
        selectedExistingCard,
        plan.price,
        {
          plan_id: planId,
          listing_payment: true,
        }
      );

      const selectedCard = existingCards.find(card => card.id === selectedExistingCard);
      
      // Convert to SecurePaymentMethod format for compatibility
      const paymentMethod: SecurePaymentMethod = {
        type: 'credit_card',
        token: selectedCard?.provider_token || '',
        metadata: {
          method: 'credit_card',
          plan_id: planId,
          amount: plan.price,
          payment_id: paymentId,
          payment_method_id: selectedExistingCard,
          cardNumber: selectedCard?.card_last_four,
          cardBrand: selectedCard?.card_brand,
        }
      };

      onPaymentComplete(paymentMethod);
    } catch (error) {
      console.error('Existing card payment failed:', error);
      Alert.alert('Payment Failed', 'Failed to process payment. Please try again.');
    }
  };

  const handleCardCancel = () => {
    if (currentStep === 'card_input') {
      setCurrentStep(existingCards.length > 0 ? 'saved_cards' : 'method_selection');
    } else {
      setCurrentStep('method_selection');
    }
  };

  const handleAddNewCard = () => {
    setCurrentStep('card_input');
  };

  const PaymentMethodOption = ({ 
    id, 
    title, 
    subtitle, 
    icon: Icon, 
    isSelected 
  }: {
    id: string;
    title: string;
    subtitle: string;
    icon: any;
    isSelected: boolean;
  }) => (
    <TouchableOpacity
      style={[styles.paymentOption, isSelected && styles.selectedPaymentOption]}
      onPress={() => setSelectedPaymentMethod(id)}
    >
      <View style={styles.paymentOptionContent}>
        <View style={styles.paymentOptionIcon}>
          <Icon size={24} color={isSelected ? theme.colors.primary : theme.colors.textSecondary} />
        </View>
        <View style={styles.paymentOptionText}>
          <Text style={[styles.paymentOptionTitle, isSelected && styles.selectedText]}>
            {title}
          </Text>
          <Text style={[styles.paymentOptionSubtitle, isSelected && styles.selectedSubtext]}>
            {subtitle}
          </Text>
        </View>
      </View>
      {isSelected && (
        <View style={styles.selectedIndicator}>
          <Check size={16} color={theme.colors.primary} />
        </View>
      )}
    </TouchableOpacity>
  );

  if (!plan) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      {currentStep === 'method_selection' ? (
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Payment Method</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.orderSummary}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Plan</Text>
              <Text style={styles.summaryValue}>{plan.name}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Duration</Text>
              <Text style={styles.summaryValue}>{plan.duration} days</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>${plan.price}</Text>
            </View>
          </View>

          <ScrollView style={styles.paymentMethodsContainer}>
            <Text style={styles.sectionTitle}>Choose Payment Method</Text>
            
            <PaymentMethodOption
              id="credit_card"
              title="Credit Card"
              subtitle="Visa, Mastercard, American Express"
              icon={CreditCard}
              isSelected={selectedPaymentMethod === 'credit_card'}
            />

            <PaymentMethodOption
              id="mobile_money"
              title="Mobile Money"
              subtitle="MTN, Vodafone, AirtelTigo"
              icon={Smartphone}
              isSelected={selectedPaymentMethod === 'mobile_money'}
            />

            <PaymentMethodOption
              id="bank_transfer"
              title="Bank Transfer"
              subtitle="Direct bank transfer"
              icon={Building}
              isSelected={selectedPaymentMethod === 'bank_transfer'}
            />
          </ScrollView>

          <View style={styles.securityNotice}>
            <Lock size={16} color={theme.colors.success} />
            <Text style={styles.securityText}>
              Your payment information is secure and encrypted
            </Text>
          </View>

          <View style={styles.footer}>
            <Button
              onPress={handlePayment}
              style={styles.payButton}
              variant="filled"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <View style={styles.processingContainer}>
                  <ActivityIndicator size="small" color={theme.colors.background} />
                  <Text style={styles.processingText}>Processing...</Text>
                </View>
              ) : (
                selectedPaymentMethod === 'credit_card' 
                  ? 'Continue' 
                  : `Pay $${plan.price}`
              )}
            </Button>
          </View>
        </View>
      ) : currentStep === 'saved_cards' ? (
        <SavedCardsView
          cards={existingCards}
          selectedCardId={selectedExistingCard}
          onCardSelect={setSelectedExistingCard}
          onAddNewCard={handleAddNewCard}
          onBack={handleCardCancel}
          onPayment={handleExistingCardPayment}
          isProcessing={isProcessing}
          amount={plan.price}
        />
      ) : (
        <CardInputForm
          amount={plan.price}
          onSubmit={handleCardSubmit}
          onCancel={handleCardCancel}
          isProcessing={isProcessing}
        />
      )}
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.primary,
    paddingTop: theme.spacing?.xl || 24,
    paddingHorizontal: theme.spacing?.lg || 20,
    paddingBottom: theme.spacing?.lg || 20,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  title: {
    fontSize: theme.typography?.fontSize?.xl || 22,
    fontWeight: '700',
    color: theme.colors.textInverse,
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    backgroundColor: colorWithOpacity(theme.colors.textInverse, 0.15),
    borderRadius: theme.borderRadius?.full || 20,
    padding: theme.spacing?.sm || 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  orderSummary: {
    backgroundColor: theme.colors.surface,
    margin: theme.spacing?.lg || 20,
    padding: theme.spacing?.xl || 24,
    borderRadius: theme.borderRadius?.xl || 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 5,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing?.md || 12,
  },
  summaryLabel: {
    fontSize: theme.typography?.fontSize?.md || 15,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: theme.typography?.fontSize?.md || 15,
    color: theme.colors.text,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1.5,
    borderTopColor: theme.colors.border,
    paddingTop: theme.spacing?.md || 12,
    marginTop: theme.spacing?.md || 12,
    marginBottom: 0,
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.05),
    paddingHorizontal: theme.spacing?.md || 12,
    paddingVertical: theme.spacing?.md || 12,
    borderRadius: theme.borderRadius?.md || 8,
  },
  totalLabel: {
    fontSize: theme.typography?.fontSize?.lg || 18,
    color: theme.colors.text,
    fontWeight: '700',
  },
  totalValue: {
    fontSize: theme.typography?.fontSize?.xl || 22,
    color: theme.colors.primary,
    fontWeight: '800',
  },
  paymentMethodsContainer: {
    flex: 1,
    paddingHorizontal: theme.spacing?.lg || 20,
  },
  sectionTitle: {
    fontSize: theme.typography?.fontSize?.lg || 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing?.lg || 20,
    textAlign: 'center',
  },
  paymentOption: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius?.xl || 16,
    padding: theme.spacing?.xl || 20,
    marginBottom: theme.spacing?.lg || 16,
    borderWidth: 1.5,
    borderColor: theme.colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  selectedPaymentOption: {
    borderColor: theme.colors.primary,
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.08),
    shadowColor: theme.colors.primary,
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    transform: [{ scale: 1.02 }],
  },
  paymentOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentOptionIcon: {
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
    borderRadius: theme.borderRadius?.lg || 12,
    padding: theme.spacing?.md || 12,
    marginRight: theme.spacing?.lg || 16,
  },
  paymentOptionText: {
    flex: 1,
  },
  paymentOptionTitle: {
    fontSize: theme.typography?.fontSize?.lg || 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing?.xs || 4,
  },
  paymentOptionSubtitle: {
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  selectedText: {
    color: theme.colors.primary,
  },
  selectedSubtext: {
    color: theme.colors.primary,
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.success,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: theme.colors.success,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  securityNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colorWithOpacity(theme.colors.success, 0.1),
    paddingHorizontal: theme.spacing?.lg || 16,
    paddingVertical: theme.spacing?.md || 12,
    marginHorizontal: theme.spacing?.lg || 20,
    marginBottom: theme.spacing?.lg || 20,
    borderRadius: theme.borderRadius?.lg || 12,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.success,
    shadowColor: theme.colors.success,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  securityText: {
    fontSize: theme.typography?.fontSize?.sm || 14,
    color: theme.colors.success,
    marginLeft: theme.spacing?.sm || 8,
    flex: 1,
    fontWeight: '600',
  },
  footer: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing?.lg || 20,
    paddingVertical: theme.spacing?.xl || 24,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  payButton: {
    width: '100%',
    paddingVertical: theme.spacing?.lg || 18,
    borderRadius: theme.borderRadius?.xl || 16,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  processingText: {
    color: theme.colors.textInverse,
    marginLeft: theme.spacing?.sm || 8,
    fontSize: theme.typography?.fontSize?.md || 16,
    fontWeight: '700',
  },
}); 