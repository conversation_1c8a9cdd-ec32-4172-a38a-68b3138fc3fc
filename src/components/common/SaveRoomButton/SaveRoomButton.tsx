import React, { useState } from 'react';

import { Heart } from 'lucide-react-native';
import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Modal,
  View,
  Text,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';

import { useSavedRooms } from '@hooks/useSavedRooms';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SaveRoomButtonProps {
  roomId: string;
  roomName?: string;
  roomPrice?: string;
  roomLocation?: string;
  size?: number;
  style?: any;
  showBackground?: boolean;
  enableMemoryBank?: boolean;
}

/**
 * Button component for saving/unsaving rooms
 */
export const SaveRoomButton: React.FC<SaveRoomButtonProps> = ({
  roomId,
  roomName = 'Room',
  roomPrice,
  roomLocation,
  size = 20,
  style,
  showBackground = true,
  enableMemoryBank = true,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { isRoomSaved, saveRoom, unsaveRoom } = useSavedRooms();
  const [processing, setProcessing] = useState(false);
  const [showMemoryModal, setShowMemoryModal] = useState(false);
  const isSaved = isRoomSaved(roomId);

  const handlePress = async () => {
    if (processing) {
      return;
    }

    if (isSaved) {
      setProcessing(true);
      try {
        await unsaveRoom(roomId);
      } finally {
        setProcessing(false);
      }
    } else {
      if (enableMemoryBank) {
        // Show modal when saving
        setShowMemoryModal(true);
      } else {
        // Just save without modal
        saveRoomOnly();
      }
    }
  };

  const saveRoomOnly = async () => {
    setProcessing(true);
    try {
      await saveRoom(roomId);
    } finally {
      setProcessing(false);
      setShowMemoryModal(false);
    }
  };

  const saveAndAddToMemoryBank = async () => {
    setProcessing(true);
    try {
      await saveRoom(roomId);
      // Navigate to the memory bank add entry screen
      router.push({
        pathname: '/memory-bank/add-entry',
        params: {
          title: `Notes about ${roomName}`,
          content: `ROOM DETAILS:
${roomName}
${roomPrice ? `Price: ${roomPrice}` : ''}
${roomLocation ? `Location: ${roomLocation}` : ''}

MY THOUGHTS:`,
          type: 'context',
          tags: 'room,property,saved',
        },
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to save room');
    } finally {
      setProcessing(false);
      setShowMemoryModal(false);
    }
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.container, showBackground && styles.backgroundContainer, style]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        {processing ? (
          <ActivityIndicator size="small" color="#E11D48" />
        ) : (
          <Heart
            size={size}
            color={isSaved ? '#E11D48' : theme.colors.textSecondary}
            fill={isSaved ? '#E11D48' : 'transparent'}
          />
        )}
      </TouchableOpacity>

      {/* Memory Bank Integration Modal */}
      <Modal
        visible={showMemoryModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMemoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Save Room</Text>
              <TouchableOpacity onPress={() => setShowMemoryModal(false)}>
                <MaterialIcons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalText}>
              Would you like to add this room to your Memory Bank as well? This helps you keep track
              of your thoughts and decisions.
            </Text>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={saveRoomOnly}
                disabled={processing}
              >
                <Text style={styles.modalButtonText}>Just Save</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.primaryButton]}
                onPress={saveAndAddToMemoryBank}
                disabled={processing}
              >
                <MaterialIcons name="psychology" size={18} color={theme.colors.background} />
                <Text style={styles.primaryButtonText}>Save & Add to Memory Bank</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  backgroundContainer: {
    backgroundColor: theme.colors.background,
    borderRadius: 20,
    width: 40,
    height: 40,
    shadowColor: theme.colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 20,
    width: '85%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalText: {
    fontSize: 16,
    color: '#475569',
    marginBottom: 20,
    lineHeight: 24,
  },
  modalButtons: {
    flexDirection: 'column',
    gap: 12,
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  primaryButton: {
    backgroundColor: '#6366F1',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.background,
    marginLeft: 8,
  },
});

export default SaveRoomButton;
