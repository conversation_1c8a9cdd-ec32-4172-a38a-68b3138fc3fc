import React, { useState } from 'react';
import { TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { Heart, Bookmark } from 'lucide-react-native';
import { useSavedItems, type SavedItemType } from '@hooks/useSavedItems';

interface SaveButtonProps {
  itemId: string;
  itemType: SavedItemType;
  size?: number;
  variant?: 'heart' | 'bookmark';
  showToast?: boolean;
  onSaveChange?: (isSaved: boolean) => void;
  style?: any;
}

export function SaveButton({
  itemId,
  itemType,
  size = 24,
  variant = 'heart',
  showToast = true,
  onSaveChange,
  style,
}: SaveButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    saveRoom,
    unsaveRoom,
    saveRoommate,
    unsaveRoommate,
    saveServiceProvider,
    unsaveServiceProvider,
    isRoomSaved,
    isRoommateSaved,
    isServiceProviderSaved,
  } = useSavedItems();

  // Check if item is saved based on type
  const isSaved = (() => {
    switch (itemType) {
      case 'room':
        return isRoomSaved(itemId);
      case 'roommate':
        return isRoommateSaved(itemId);
      case 'service_provider':
        return isServiceProviderSaved(itemId);
      default:
        return false;
    }
  })();

  // Handle save/unsave action
  const handlePress = async () => {
    if (isLoading) return;

    setIsLoading(true);
    let success = false;
    let actionText = '';

    try {
      if (isSaved) {
        // Unsave the item
        switch (itemType) {
          case 'room':
            success = await unsaveRoom(itemId);
            actionText = 'Room removed from saved';
            break;
          case 'roommate':
            success = await unsaveRoommate(itemId);
            actionText = 'Roommate removed from saved';
            break;
          case 'service_provider':
            success = await unsaveServiceProvider(itemId);
            actionText = 'Service provider removed from saved';
            break;
        }
      } else {
        // Save the item
        switch (itemType) {
          case 'room':
            success = await saveRoom(itemId);
            actionText = 'Room saved successfully';
            break;
          case 'roommate':
            success = await saveRoommate(itemId);
            actionText = 'Roommate saved successfully';
            break;
          case 'service_provider':
            success = await saveServiceProvider(itemId);
            actionText = 'Service provider saved successfully';
            break;
        }
      }

      if (success) {
        if (showToast) {
          Alert.alert('Success', actionText);
        }
        onSaveChange?.(!isSaved);
      } else {
        if (showToast) {
          Alert.alert('Error', 'Failed to update saved status');
        }
      }
    } catch (error) {
      console.error('Error saving/unsaving item:', error);
      if (showToast) {
        Alert.alert('Error', 'Something went wrong');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const Icon = variant === 'heart' ? Heart : Bookmark;
  const fillColor = isSaved ? '#E11D48' : 'transparent';
  const strokeColor = isSaved ? '#E11D48' : '#64748B';

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={isLoading}
      style={[
        {
          backgroundColor: '#FFFFFF',
          borderRadius: size / 2,
          padding: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 2,
          elevation: 2,
          alignItems: 'center',
          justifyContent: 'center',
        },
        style,
      ]}
    >
      {isLoading ? (
        <ActivityIndicator size="small" color={strokeColor} />
      ) : (
        <Icon
          size={size}
          color={strokeColor}
          fill={fillColor}
          strokeWidth={isSaved ? 0 : 1.5}
        />
      )}
    </TouchableOpacity>
  );
}

export default SaveButton; 