import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface Props {
  children: ReactNode;
  fallbackComponent?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * Error boundary specifically designed to catch and handle 
 * native bridge errors in Expo/React Native
 */
export class ExpoBridgeErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render shows the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error
    console.error('BRIDGE ERROR CAUGHT:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    
    // Log additional information about the error
    if (error.message.includes('undefined') || error.message.includes('null')) {
      console.warn('This appears to be a null/undefined error, which may be related to native module initialization');
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback component is provided, use it
      if (this.props.fallbackComponent) {
        return this.props.fallbackComponent;
      }
      
      // Otherwise, show the default error UI
      return (
        <View style={styles.container}>
          <Text style={styles.errorTitle}>Native Bridge Error</Text>
          <Text style={styles.errorMessage}>
            {this.state.error?.message || 'An unexpected error occurred with a native component'}
          </Text>
          <Text style={styles.recoveryMessage}>
            The app will continue to function, but some features may be limited.
          </Text>
        </View>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

// Styles for the default error UI
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#ffefef',
    borderRadius: 8,
    margin: 8,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#d00',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 8,
  },
  recoveryMessage: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
});

export default ExpoBridgeErrorBoundary;
