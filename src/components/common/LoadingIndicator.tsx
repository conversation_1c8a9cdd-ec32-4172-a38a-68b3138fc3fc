/**
 * Loading Indicator Component
 * 
 * A reusable loading indicator to be used throughout the application.
 */

import React from 'react';
import { StyleSheet, View, ActivityIndicator } from 'react-native';
// If react-native-paper is not installed, install it with: npm install react-native-paper
import { Text } from 'react-native';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface LoadingIndicatorProps {
  message?: string;
  size?: 'small' | 'large';
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message = 'Loading...',
  size = 'large'
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color="#0066CC" /> 
      {message && <Text style={styles.message}>{message}</Text>}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  message: {
    marginTop: 12,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
});

export default LoadingIndicator;
