import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Flag, X, AlertTriangle, Check } from 'lucide-react-native';

import { fraudDetectionService, type FraudFlag } from '@services/fraudDetectionService';
import { logger } from '@utils/logger';
import { supabase } from "@utils/supabaseUtils";
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface ReportSuspiciousActivityProps {
  targetUserId: string;
  buttonStyle?: 'default' | 'compact' | 'text';
}

export function ReportSuspiciousActivity({
  targetUserId,
  buttonStyle = 'default',
}: ReportSuspiciousActivityProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [reportReason, setReportReason] = useState('');
  const [reportType, setReportType] = useState<string | null>(null);

  const reportTypes = [
    { id: 'fake_profile', label: 'Fake Profile', description: 'Profile seems suspicious or fake' },
    { id: 'scam', label: 'Scam Attempt', description: 'User is attempting to scam others' },
    { id: 'inappropriate', label: 'Inappropriate Content', description: 'Posts or messages contain inappropriate content' },
    { id: 'impersonation', label: 'Impersonation', description: 'User is pretending to be someone else' },
    { id: 'suspicious_behavior', label: 'Suspicious Behavior', description: 'Other concerning behavior' },
  ];

  const handleReport = async () => {
    if (!reportType) {
      Alert.alert('Error', 'Please select a report type');
      return;
    }

    if (reportReason.trim().length < 10) {
      Alert.alert('Error', 'Please provide more details about the suspicious activity');
      return;
    }
    
    setLoading(true);
    
    try {
      // Get current user using Supabase auth
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) {
        throw new Error('You must be logged in to report suspicious activity');
      }
      
      const reporterId = userData.user.id;
      
      // Create fraud flag based on the report type and reason
      const fraudFlag: FraudFlag = {
        category: 'user_report',
        severity: 'medium', // Explicitly typed as 'medium' to match FraudFlag type
        description: `Reported for ${reportTypes.find(t => t.id === reportType)?.label}: ${reportReason.substring(0, 100)}${reportReason.length > 100 ? '...' : ''}`,
      };
      
      // Use FraudDetectionService to report the suspicious user and create/update record
      // This will handle all the database operations for suspicious_profiles table
      await fraudDetectionService.reportSuspiciousUser(
        targetUserId,  // The user being reported
        40,           // Initial fraud score 
        [fraudFlag]   // The flag describing the suspicious activity
      );
      
      // Also create a direct entry in the user_reports table for admin review
      // We need to do this directly as the FraudDetectionService doesn't handle user_reports
      const { error } = await supabase.from('user_reports').insert({
        reporter_id: reporterId,
        reported_user_id: targetUserId,
        report_type: reportType,
        details: reportReason,
        status: 'pending_review',
      });
      
      if (error) {
        throw error;
      }
      
      // Log the report
      logger.info(
        'User reported suspicious activity',
        'ReportSuspiciousActivity',
        { reporterId, targetUserId, reportType }
      );
      
      setSuccess(true);
      setTimeout(() => {
        setModalVisible(false);
        setSuccess(false);
        setReportType(null);
        setReportReason('');
      }, 2000);
      
    } catch (error) {
      logger.error(
        'Failed to submit suspicious activity report',
        'ReportSuspiciousActivity',
        { targetUserId, error: error instanceof Error ? error.message : String(error) }
      );
      Alert.alert('Error', 'Failed to submit report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  let buttonComponent;
  
  switch(buttonStyle) {
    case 'compact':
      buttonComponent = (
        <TouchableOpacity 
          style={styles.compactButton} 
          onPress={() => setModalVisible(true)}
        >
          <Flag size={16} color={theme.colors.error} />
          <Text style={styles.compactButtonText}>Report</Text>
        </TouchableOpacity>
      );
      break;
    case 'text':
      buttonComponent = (
        <TouchableOpacity 
          style={styles.textButton} 
          onPress={() => setModalVisible(true)}
        >
          <Text style={styles.textButtonText}>Report suspicious activity</Text>
        </TouchableOpacity>
      );
      break;
    default:
      buttonComponent = (
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => setModalVisible(true)}
        >
          <Flag size={18} color={theme.colors.background} />
          <Text style={styles.buttonText}>Report Suspicious Activity</Text>
        </TouchableOpacity>
      );
  }

  return (
    <>
      {buttonComponent}
      
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          if (!loading) setModalVisible(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {!success ? (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Report Suspicious Activity</Text>
                  <TouchableOpacity 
                    style={styles.closeButton} 
                    onPress={() => !loading && setModalVisible(false)}
                    disabled={loading}
                  >
                    <X size={24} color="#6b7280" />
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.scrollView}>
                  <Text style={styles.sectionTitle}>What type of suspicious activity?</Text>
                  
                  {reportTypes.map((type) => (
                    <TouchableOpacity
                      key={type.id}
                      style={[
                        styles.reportTypeItem,
                        reportType === type.id && styles.reportTypeSelected
                      ]}
                      onPress={() => setReportType(type.id)}
                    >
                      <View style={styles.reportTypeContent}>
                        <Text style={styles.reportTypeLabel}>{type.label}</Text>
                        <Text style={styles.reportTypeDescription}>{type.description}</Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                  
                  <Text style={styles.sectionTitle}>Provide details</Text>
                  <Text style={styles.sectionDescription}>
                    Please provide specific details about the suspicious activity you've observed.
                    This helps our team investigate the issue properly.
                  </Text>
                  
                  <TextInput
                    style={styles.textInput}
                    placeholder="Describe the suspicious activity..."
                    placeholderTextColor={theme.colors.textSecondary}
                    multiline={true}
                    numberOfLines={5}
                    value={reportReason}
                    onChangeText={setReportReason}
                    editable={!loading}
                  />
                  
                  <View style={styles.noteContainer}>
                    <AlertTriangle size={16} color={theme.colors.warning} />
                    <Text style={styles.noteText}>
                      Your report will be handled confidentially. False reports may result in account restrictions.
                    </Text>
                  </View>
                </ScrollView>
                
                <View style={styles.actionsContainer}>
                  <TouchableOpacity
                    style={[styles.cancelButton, loading && styles.disabledButton]}
                    onPress={() => setModalVisible(false)}
                    disabled={loading}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={[styles.submitButton, loading && styles.disabledButton]} 
                    onPress={handleReport}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator size="small" color={theme.colors.background} />
                    ) : (
                      <Text style={styles.submitButtonText}>Submit Report</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <View style={styles.successContainer}>
                <View style={styles.successIconContainer}>
                  <Check size={40} color={theme.colors.success} />
                </View>
                <Text style={styles.successTitle}>Report Submitted</Text>
                <Text style={styles.successText}>
                  Thank you for helping keep our community safe. Our team will review your report and take appropriate action.
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.error,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  buttonText: {
    color: theme.colors.background,
    fontWeight: '600',
    marginLeft: 8,
    fontSize: 16,
  },
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fee2e2',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  compactButtonText: {
    color: theme.colors.error,
    fontWeight: '600',
    marginLeft: 4,
    fontSize: 14,
  },
  textButton: {
    padding: 4,
  },
  textButtonText: {
    color: theme.colors.error,
    fontWeight: '500',
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
    marginTop: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
  },
  reportTypeItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    marginBottom: 8,
  },
  reportTypeSelected: {
    borderColor: theme.colors.error,
    backgroundColor: '#fee2e2',
  },
  reportTypeContent: {
    flex: 1,
  },
  reportTypeLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  reportTypeDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    color: '#1f2937',
    backgroundColor: '#f9fafb',
    minHeight: 120,
    textAlignVertical: 'top',
  },
  noteContainer: {
    flexDirection: 'row',
    backgroundColor: '#fef3c7',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 16,
  },
  noteText: {
    fontSize: 14,
    color: '#92400e',
    flex: 1,
    marginLeft: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#6b7280',
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 1,
    paddingVertical: 12,
    marginLeft: 8,
    backgroundColor: theme.colors.error,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  successContainer: {
    padding: 24,
    alignItems: 'center',
  },
  successIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#d1fae5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  successText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
export default ReportSuspiciousActivity;
