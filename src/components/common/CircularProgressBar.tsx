import React from 'react';

import type { TextStyle, ViewStyle } from 'react-native';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface CircularProgressBarProps {
  size?: number;
  strokeWidth?: number;
  progress?: number;
  color?: string;
  backgroundColor?: string;
  showText?: boolean;
  text?: string;
  style?: ViewStyle;
}

/**
 * A reusable circular progress bar component
 * Displays a percentage as a circular progress indicator with text in the center
 */
export default function CircularProgressBar({
  size = 100,
  strokeWidth = 8,
  progress = 0,
  color,
  backgroundColor,
  showText = true,
  text,
  style,
}: CircularProgressBarProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const colors = theme.colors;
  
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <View style={[{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }, style]}>
      <Svg width={size} height={size}>
        {/* Background circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor || theme.colors.border}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* Progress circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color || theme.colors.primary}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      {showText && (
        <Text style={{
          position: 'absolute',
          fontSize: size / 6,
          fontWeight: 'bold',
          color: theme.colors.text,
        }}>
          {text || `${Math.round(progress)}%`}
        </Text>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontWeight: '700',
    fontSize: 24,
    textAlign: 'center',
  },
});
