import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { useMemoryBank } from '@hooks/useMemoryBank';
import { MemoryEntry } from '@services/memoryBankService';
import { useSavedRooms } from '@hooks/useSavedRooms';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SavedRoomMemoryIntegrationProps {
  roomId: string;
  savedId: string;
  roomName: string;
  notes?: string;
  roomPrice?: string;
  roomLocation?: string;
}

export const SavedRoomMemoryIntegration: React.FC<SavedRoomMemoryIntegrationProps> = ({
  roomId,
  savedId,
  roomName,
  notes,
  roomPrice,
  roomLocation,
}) => {
  const router = useRouter();
  const { searchEntries, addEntry } = useMemoryBank();
  const { updateNotes } = useSavedRooms();
  const [relatedMemories, setRelatedMemories] = useState<MemoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Find memories related to this room
  useEffect(() => {
    const fetchRelatedMemories = async () => {
      setIsLoading(true);
      try {
        // Search for memories that contain this room's ID in metadata
        const entries = await searchEntries(roomId);
        const filteredEntries = entries.filter(
          entry => entry.metadata && entry.metadata.roomId === roomId
        );
        setRelatedMemories(filteredEntries);
      } catch (error) {
        console.error('Error fetching related memories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (roomId) {
      fetchRelatedMemories();
    }
  }, [roomId, searchEntries]);

  // Navigate to memory bank
  const handleViewInMemoryBank = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    router.push('/memory-bank');
  };

  // Navigate to add entry page
  const handleAddMemory = () => {
    router.push({
      pathname: '/memory-bank/add-entry',
      params: {
        title: `Notes about ${roomName}`,
        content: notes ? `SAVED NOTES:\n${notes}\n\nADDITIONAL THOUGHTS:` : '',
        type: 'context',
        tags: 'room,property,saved',
      },
    });
  };

  // Navigate to detailed room memory view
  const handleViewRoomMemories = () => {
    router.push({
      pathname: '/memory-bank/room-details',
      params: {
        roomId,
        roomName,
        roomPrice: roomPrice || '',
        roomLocation: roomLocation || '',
      },
    });
  };

  // Sync saved notes to memory bank
  const handleSyncToMemoryBank = async () => {
    if (!notes) {
      Alert.alert('No Notes', 'There are no notes to sync to Memory Bank');
      return;
    }

    setIsLoading(true);
    try {
      // Create a new memory entry with the saved notes
      await addEntry({
        type: 'context',
        title: `Saved Notes: ${roomName}`,
        content: `SAVED ROOM NOTES:\n${notes}`,
        tags: ['room', 'property', 'saved'],
        metadata: {
          roomId,
          savedId,
          synced: true,
        },
        userId: '', // This will be set by the service
      });

      Alert.alert('Success', 'Notes have been synced to Memory Bank');
    } catch (error) {
      Alert.alert('Error', 'Failed to sync notes to Memory Bank');
      console.error('Error syncing to memory bank:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#6366F1" />
        <Text style={styles.loadingText}>Loading memory bank data...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Memory Bank Integration</Text>
      </View>

      {relatedMemories.length > 0 ? (
        <View style={styles.relatedContainer}>
          <Text style={styles.relatedTitle}>
            {relatedMemories.length} {relatedMemories.length === 1 ? 'entry' : 'entries'} in Memory
            Bank
          </Text>

          <TouchableOpacity style={styles.button} onPress={handleViewRoomMemories}>
            <MaterialIcons name="psychology" size={18} color="#6366F1" />
            <Text style={styles.buttonText}>View Room Memories</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={handleViewInMemoryBank}
          >
            <MaterialIcons name="folder" size={18} color="#6366F1" />
            <Text style={styles.buttonText}>View All Memories</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No entries in Memory Bank for this room</Text>
          <TouchableOpacity style={styles.button} onPress={handleAddMemory}>
            <MaterialIcons name="add" size={18} color="#6366F1" />
            <Text style={styles.buttonText}>Add to Memory Bank</Text>
          </TouchableOpacity>
        </View>
      )}

      {notes && (
        <TouchableOpacity
          style={[styles.button, styles.syncButton]}
          onPress={handleSyncToMemoryBank}
        >
          <MaterialIcons name="sync" size={18} color="#6366F1" />
          <Text style={styles.buttonText}>Sync Notes to Memory Bank</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginVertical: 12,
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  relatedContainer: {
    marginVertical: 8,
  },
  relatedTitle: {
    fontSize: 14,
    color: '#334155',
    marginBottom: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EEF2FF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
  },
  secondaryButton: {
    backgroundColor: '#F1F5F9',
  },
  buttonText: {
    color: '#6366F1',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  emptyContainer: {
    marginVertical: 8,
  },
  emptyText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  syncButton: {
    marginTop: 8,
    backgroundColor: '#F0F9FF',
  },
});

export default SavedRoomMemoryIntegration;
