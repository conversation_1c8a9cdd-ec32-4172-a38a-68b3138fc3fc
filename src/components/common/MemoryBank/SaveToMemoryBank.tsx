import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useMemoryBank } from '@hooks/useMemoryBank';
import { MaterialIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SaveToMemoryBankProps {
  roomId: string;
  roomName: string;
  roomDescription?: string;
  roomPrice?: string;
  roomLocation?: string;
  onSaved?: () => void;
}

export const SaveToMemoryBank: React.FC<SaveToMemoryBankProps> = ({
  roomId,
  roomName,
  roomDescription = '',
  roomPrice = '',
  roomLocation = '',
  onSaved,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [notes, setNotes] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [selectedType, setSelectedType] = useState<'decision' | 'context'>('context');
  const { addEntry } = useMemoryBank();
  const router = useRouter();

  const theme = useTheme();
  const styles = createStyles(theme);

  const toggleModal = () => {
    setIsModalVisible(!isModalVisible);
    if (!isModalVisible) {
      // Reset form when opening
      setNotes('');
      setSelectedType('context');
    }
  };

  const handleSave = async () => {
    if (!notes.trim()) {
      Alert.alert('Error', 'Please enter some notes about this room');
      return;
    }

    setIsSaving(true);
    try {
      // Prepare metadata about the room
      const metadata = {
        roomId,
        roomPrice,
        roomLocation,
      };

      // Generate a title based on the room name and type
      const title =
        selectedType === 'decision' ? `Decision about ${roomName}` : `Notes about ${roomName}`;

      // Format content to include room details and user notes
      const content = `
ROOM DETAILS:
${roomName}
${roomPrice ? `Price: ${roomPrice}` : ''}
${roomLocation ? `Location: ${roomLocation}` : ''}

MY NOTES:
${notes}

${roomDescription ? `\nROOM DESCRIPTION:\n${roomDescription.substring(0, 500)}${roomDescription.length > 500 ? '...' : ''}` : ''}
      `.trim();

      // Save to memory bank
      await addEntry({
        type: selectedType,
        title,
        content,
        tags: ['room', 'property', roomLocation ? 'location' : undefined].filter(
          Boolean
        ) as string[],
        metadata,
        userId: '', // This will be set by the service
      });

      Alert.alert('Saved to Memory Bank', 'Your notes have been saved to your Memory Bank', [
        {
          text: 'View in Memory Bank',
          onPress: () => {
            toggleModal();
            router.push('/memory-bank');
          },
        },
        {
          text: 'Close',
          onPress: toggleModal,
          style: 'cancel',
        },
      ]);

      if (onSaved) {
        onSaved();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save to Memory Bank');
      console.error('Error saving to memory bank:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <View>
      <TouchableOpacity style={styles.button} onPress={toggleModal}>
        <MaterialIcons name="psychology" size={20} color="#6366F1" />
        <Text style={styles.buttonText}>Save to Memory Bank</Text>
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={toggleModal}
      >
        <SafeAreaView style={styles.modalContainer}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Save to Memory Bank</Text>
                <TouchableOpacity onPress={toggleModal} style={styles.closeButton}>
                  <MaterialIcons name="close" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.scrollView}>
                <View style={styles.propertyInfoContainer}>
                  <Text style={styles.propertyName}>{roomName}</Text>
                  {roomPrice && <Text style={styles.propertyDetail}>Price: {roomPrice}</Text>}
                  {roomLocation && (
                    <Text style={styles.propertyDetail}>Location: {roomLocation}</Text>
                  )}
                </View>

                <Text style={styles.sectionTitle}>Type of Memory</Text>
                <View style={styles.typeSelector}>
                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      selectedType === 'context' && styles.selectedTypeButton,
                    ]}
                    onPress={() => setSelectedType('context')}
                  >
                    <MaterialIcons
                      name="info"
                      size={20}
                      color={selectedType === 'context' ? theme.colors.background : '#30B0C7'}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        selectedType === 'context' && styles.selectedTypeButtonText,
                      ]}
                    >
                      Notes & Context
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.typeButton,
                      selectedType === 'decision' && styles.selectedTypeButton,
                      styles.decisionButton,
                    ]}
                    onPress={() => setSelectedType('decision')}
                  >
                    <MaterialIcons
                      name="lightbulb-outline"
                      size={20}
                      color={selectedType === 'decision' ? theme.colors.background : '#FF9500'}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        selectedType === 'decision' && styles.selectedTypeButtonText,
                      ]}
                    >
                      Decision
                    </Text>
                  </TouchableOpacity>
                </View>

                <Text style={styles.sectionTitle}>Your Notes</Text>
                <TextInput
                  style={styles.notesInput}
                  multiline
                  placeholder={
                    selectedType === 'decision'
                      ? 'Record your decision about this property...'
                      : 'Add your thoughts, observations, or questions about this property...'
                  }
                  placeholderTextColor={theme.colors.textSecondary}
                  value={notes}
                  onChangeText={setNotes}
                  autoFocus
                  maxLength={500}
                />
                <Text style={styles.characterCount}>{notes.length}/500</Text>
              </ScrollView>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={toggleModal}
                  disabled={isSaving}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={handleSave}
                  disabled={isSaving || !notes.trim()}
                >
                  {isSaving ? (
                    <ActivityIndicator size="small" color={theme.colors.background} />
                  ) : (
                    <>
                      <MaterialIcons name="save" size={18} color={theme.colors.background} />
                      <Text style={styles.saveButtonText}>Save to Memory Bank</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.overlay || colorWithOpacity(theme.colors.text, 0.5),
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 30,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    maxHeight: '70%',
  },
  propertyInfoContainer: {
    backgroundColor: theme.colors.surface || colorWithOpacity(theme.colors.text, 0.05),
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  propertyName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  propertyDetail: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginTop: 8,
    marginBottom: 12,
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface || colorWithOpacity(theme.colors.text, 0.05),
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 8,
    marginRight: 12,
    flex: 1,
  },
  decisionButton: {
    backgroundColor: colorWithOpacity('#FF9500', 0.1),
  },
  selectedTypeButton: {
    backgroundColor: theme.colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
    fontWeight: '500',
  },
  selectedTypeButtonText: {
    color: theme.colors.background,
  },
  notesInput: {
    backgroundColor: theme.colors.surface || colorWithOpacity(theme.colors.text, 0.05),
    borderRadius: 12,
    padding: 16,
    height: 150,
    fontSize: 16,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: theme.colors.border,
    color: theme.colors.text,
  },
  characterCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surface || colorWithOpacity(theme.colors.text, 0.1),
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 12,
  },
  cancelButtonText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 2,
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default SaveToMemoryBank;
