import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useMemoryBank } from '@hooks/useMemoryBank';
import { MemoryEntry } from '@services/memoryBankService';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

// Memory types with icons and colors
const MEMORY_TYPES = [
  { id: 'decision', label: 'Decision', icon: 'lightbulb', color: '#FF9500' },
  { id: 'context', label: 'Context', icon: 'info', color: '#30B0C7' },
  { id: 'progress', label: 'Progress', icon: 'check-circle', color: '#34C759' },
  { id: 'pattern', label: 'Pattern', icon: 'category', color: '#AF52DE' },
];

export default function AddEntryScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);

  const router = useRouter();
  const params = useLocalSearchParams();

  // Check if we're editing an existing entry
  const isEditing = !!params.id;

  const [title, setTitle] = useState((params.title as string) || '');
  const [content, setContent] = useState((params.content as string) || '');
  const [type, setType] = useState((params.type as MemoryEntry['type']) || 'decision');
  const [tagsInput, setTagsInput] = useState((params.tags as string) || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { addEntry, updateEntry } = useMemoryBank();

  // Parse tags from comma-separated input
  const parseTags = (input: string): string[] => {
    if (!input.trim()) return [];

    return input
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  };

  // Validate form
  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title');
      return false;
    }

    if (!content.trim()) {
      Alert.alert('Error', 'Please enter content');
      return false;
    }

    return true;
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const tags = parseTags(tagsInput);

      const entryData = {
        title,
        content,
        type,
        tags,
      };

      if (isEditing && params.id) {
        // Update existing entry
        const updatedEntry = await updateEntry(params.id as string, entryData);

        if (!updatedEntry) {
          throw new Error('Failed to update memory');
        }
      } else {
        // Add new entry
        await addEntry(entryData);
      }

      Alert.alert('Success', `Memory ${isEditing ? 'updated' : 'added'} successfully`, [
        {
          text: 'OK',
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} memory`);
      console.error('Error saving memory:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView style={styles.scrollView}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{isEditing ? 'Edit Memory' : 'Add New Memory'}</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.label}>Memory Type</Text>
            <View style={styles.typeSelector}>
              {MEMORY_TYPES.map(memoryType => (
                <TouchableOpacity
                  key={memoryType.id}
                  style={[
                    styles.typeButton,
                    type === memoryType.id && {
                      backgroundColor: `${memoryType.color}20`,
                      borderColor: memoryType.color,
                    },
                  ]}
                  onPress={() => setType(memoryType.id as MemoryEntry['type'])}
                >
                  <MaterialIcons name={memoryType.icon as any} size={20} color={memoryType.color} />
                  <Text
                    style={[
                      styles.typeButtonText,
                      type === memoryType.id && { color: memoryType.color },
                    ]}
                  >
                    {memoryType.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.label}>Title</Text>
            <TextInput
              style={styles.titleInput}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter a title for your memory"
              placeholderTextColor="#8E8E93"
              maxLength={100}
            />
            <Text style={styles.charCount}>{title.length}/100</Text>

            <Text style={styles.label}>Content</Text>
            <TextInput
              style={styles.contentInput}
              value={content}
              onChangeText={setContent}
              placeholder="Enter the details of your memory..."
              placeholderTextColor="#8E8E93"
              multiline
              textAlignVertical="top"
              maxLength={2000}
            />
            <Text style={styles.charCount}>{content.length}/2000</Text>

            <Text style={styles.label}>Tags (comma-separated)</Text>
            <TextInput
              style={styles.tagsInput}
              value={tagsInput}
              onChangeText={setTagsInput}
              placeholder="e.g. important, follow-up, idea"
              placeholderTextColor="#8E8E93"
            />
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => router.back()}
            disabled={isSubmitting}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={theme.colors.background} />
            ) : (
              <Text style={styles.submitButtonText}>
                {isEditing ? 'Update Memory' : 'Save Memory'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e4e8',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 8,
  },
  placeholder: {
    width: 24,
  },
  formContainer: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d6',
    marginRight: 8,
    marginBottom: 8,
  },
  typeButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#3C3C43',
  },
  titleInput: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#d1d1d6',
  },
  contentInput: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#d1d1d6',
    height: 200,
  },
  tagsInput: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#d1d1d6',
  },
  charCount: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'right',
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e1e4e8',
    backgroundColor: theme.colors.background,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f1f3f5',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#495057',
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#0066cc',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
});
