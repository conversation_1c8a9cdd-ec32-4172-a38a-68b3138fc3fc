/**
 * Location Permission Banner
 * 
 * Shows a banner when location permission is needed or denied,
 * with actions to enable location access
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MapPin, Settings, X } from 'lucide-react-native';

import { useLocationPermission } from '@hooks/useLocationPermission';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface LocationPermissionBannerProps {
  visible?: boolean;
  onDismiss?: () => void;
  showOnGranted?: boolean;
  customMessage?: string;
}

export function LocationPermissionBanner({
  visible = true,
  onDismiss,
  showOnGranted = false,
  customMessage,
}: LocationPermissionBannerProps) {
  const { colors } = useTheme();
  const {
    permissionStatus,
    hasPermission,
    canRequestPermission,
    needsSettings,
    statusMessage,
    requestPermission,
    isLoading,
  } = useLocationPermission();

  // Don't show if not visible or still loading initial status
  if (!visible || !permissionStatus) {
    return null;
  }

  // Don't show if already granted and showOnGranted is false
  if (hasPermission && !showOnGranted) {
    return null;
  }

  const getBannerStyle = () => {
    if (hasPermission) {
      return {
        backgroundColor: colors.green[50],
        borderColor: colors.green[200],
      };
    }
    
    if (needsSettings) {
      return {
        backgroundColor: colors.red[50],
        borderColor: colors.red[200],
      };
    }
    
    return {
      backgroundColor: colors.orange[50],
      borderColor: colors.orange[200],
    };
  };

  const getIconColor = () => {
    if (hasPermission) return colors.green[600];
    if (needsSettings) return colors.red[600];
    return colors.orange[600];
  };

  const getTextColor = () => {
    if (hasPermission) return colors.green[800];
    if (needsSettings) return colors.red[800];
    return colors.orange[800];
  };

  const handleAction = async () => {
    if (hasPermission) {
      // Already granted - maybe dismiss banner
      onDismiss?.();
      return;
    }

    if (needsSettings) {
      // Need to go to settings
      await requestPermission(false);
      return;
    }

    if (canRequestPermission) {
      // Can request permission
      await requestPermission(true, customMessage);
      return;
    }
  };

  const getActionText = () => {
    if (hasPermission) return 'Dismiss';
    if (needsSettings) return 'Open Settings';
    if (canRequestPermission) return 'Enable Location';
    return null;
  };

  const actionText = getActionText();

  return (
    <View style={[styles.banner, getBannerStyle()]}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <MapPin size={20} color={getIconColor()} />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.message, { color: getTextColor() }]}>
            {customMessage || statusMessage}
          </Text>
        </View>

        <View style={styles.actions}>
          {actionText && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: getIconColor() }]}
              onPress={handleAction}
              disabled={isLoading}
            >
              {needsSettings && <Settings size={14} color={colors.white} />}
              <Text style={[styles.actionText, { color: colors.white }]}>
                {actionText}
              </Text>
            </TouchableOpacity>
          )}
          
          {onDismiss && (
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={onDismiss}
            >
              <X size={16} color={colors.gray[500]} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  banner: {
    margin: 16,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 12,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  dismissButton: {
    padding: 4,
  },
});

export default LocationPermissionBanner; 