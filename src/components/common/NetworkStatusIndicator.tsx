import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Wifi, WifiOff, Upload } from 'lucide-react-native';
import { offlineFirstProfileService } from '@/services/OfflineFirstProfileService';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

export default function NetworkStatusIndicator() {
  const theme = useTheme();
  const styles = createStyles(theme);

  const [networkStatus, setNetworkStatus] = useState(null);
  const [syncStatus, setSyncStatus] = useState({ pending: 0, inProgress: false });

  useEffect(() => {
    const interval = setInterval(() => {
      setNetworkStatus(offlineFirstProfileService.getNetworkStatus());
      setSyncStatus(offlineFirstProfileService.getSyncQueueStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!networkStatus) return null;

  return (
    <View style={styles.container}>
      {networkStatus.isConnected ? (
        <View style={styles.onlineIndicator}>
          <Wifi size={16} color={SUCCESS} />
          <Text style={styles.onlineText}>Online</Text>
          {syncStatus.pending > 0 && (
            <View style={styles.syncIndicator}>
              <Upload size={12} color={PRIMARY} />
              <Text style={styles.syncText}>{syncStatus.pending}</Text>
            </View>
          )}
        </View>
      ) : (
        <View style={styles.offlineIndicator}>
          <WifiOff size={16} color={ERROR} />
          <Text style={styles.offlineText}>Offline</Text>
          {syncStatus.pending > 0 && (
            <Text style={styles.pendingText}>{syncStatus.pending} changes pending</Text>
          )}
        </View>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  onlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  onlineText: {
    color: SUCCESS,
    fontSize: 14,
    fontWeight: '600',
  },
  offlineText: {
    color: ERROR,
    fontSize: 14,
    fontWeight: '600',
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  syncText: {
    color: PRIMARY,
    fontSize: 12,
    fontWeight: '600',
  },
  pendingText: {
    color: WARNING,
    fontSize: 12,
  },
});
