import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { MapPin, Star, DollarSign, MessageCircle, Heart } from 'lucide-react-native';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

/**
 * Generic listing card that can be used for both rooms and housemates
 */
export interface ListingCardProps {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl?: string;
  price?: string;
  location?: string;
  rating?: number;
  badges?: Array<{
    text: string;
    color: string;
  }>;
  isFeatured?: boolean;
  type: 'room' | 'housemate';
  ownerId?: string;
  onMessagePress?: (id: string, ownerId: string, name: string) => void;
  onLikePress?: (id: string) => void;
  onPress?: (id: string) => void;
}

const ListingCard: React.FC<ListingCardProps> = ({
  id,
  title,
  subtitle,
  description,
  imageUrl,
  price,
  location,
  rating,
  badges = [],
  isFeatured = false,
  type,
  ownerId,
  onMessagePress,
  onLikePress,
  onPress
}) => {
  // Default image if none is provided
  const defaultImage = type === 'room'
    ? 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80'
    : 'https://via.placeholder.com/100';
  
  const displayImage = imageUrl || defaultImage;
  
  const handlePress = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    if (onPress) {
      onPress(id);
    } else {
      // Default navigation behavior
      // Use a simple string path to avoid type issues
      const path = type === 'room' 
        ? `/room/${id}` 
        : `/profile/${id}`;
      
      // @ts-ignore - Ignoring router type issues
      router.push(path);
    }
  };
  
  const handleMessagePress = () => {
    if (onMessagePress && ownerId) {
      onMessagePress(id, ownerId, title);
    }
  };
  
  const handleLikePress = () => {
    if (onLikePress) {
      onLikePress(id);
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, isFeatured && styles.featuredContainer]} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      {/* Image Section */}
      {type === 'room' && (
        <View style={styles.imageContainer}>
          <Image source={{ uri: displayImage }} style={styles.image} />
          {isFeatured && (
            <View style={styles.featuredBadge}>
              <Text style={styles.featuredText}>Featured</Text>
            </View>
          )}
          {price && (
            <View style={styles.priceBadge}>
              <DollarSign size={14} color={theme.colors.background} />
              <Text style={styles.priceText}>{price}/month</Text>
            </View>
          )}
        </View>
      )}

      {/* Content Section */}
      <View style={styles.contentContainer}>
        {/* For housemate type, show avatar and details side by side */}
        {type === 'housemate' && (
          <View style={styles.housemateHeader}>
            <Image 
              source={{ uri: displayImage }} 
              style={styles.avatar}
            />
            <View style={styles.housemateDetails}>
              <Text style={styles.title} numberOfLines={1}>{title}</Text>
              {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
            </View>
          </View>
        )}

        {/* For room type, show title in full width */}
        {type === 'room' && (
          <Text style={styles.title} numberOfLines={1}>{title}</Text>
        )}
        
        {/* Location */}
        {location && (
          <View style={styles.locationContainer}>
            <MapPin size={14} color="#6B7280" />
            <Text style={styles.locationText} numberOfLines={1}>{location}</Text>
          </View>
        )}

        {/* Badges */}
        {badges.length > 0 && (
          <View style={styles.badgeContainer}>
            {badges.map((badge, index) => (
              <View 
                key={index} 
                style={[styles.badge, { backgroundColor: badge.color || '#F3F4F6' }]}
              >
                <Text style={styles.badgeText}>{badge.text}</Text>
              </View>
            ))}
            
            {/* Rating badge */}
            {rating && (
              <View style={styles.ratingContainer}>
                <Star size={14} fill={theme.colors.warning} color={theme.colors.warning} />
                <Text style={styles.ratingText}>{rating}</Text>
              </View>
            )}
          </View>
        )}

        {/* Description */}
        {description && (
          <Text numberOfLines={2} style={styles.description}>{description}</Text>
        )}

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          {onMessagePress && (
            <TouchableOpacity
              style={styles.messageButton}
              onPress={handleMessagePress}
            >
              <MessageCircle size={16} color={theme.colors.background} />
              <Text style={styles.messageButtonText}>Message</Text>
            </TouchableOpacity>
          )}
          
          {onLikePress && (
            <TouchableOpacity
              style={styles.likeButton}
              onPress={handleLikePress}
            >
              <Heart size={16} color="#4F46E5" />
              <Text style={styles.likeButtonText}>Like</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    width: Dimensions.get('window').width * 0.9,
    borderRadius: 12,
    backgroundColor: theme.colors.background,
    marginBottom: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  featuredContainer: {
    borderWidth: 1,
    borderColor: theme.colors.warning,
  },
  imageContainer: {
    position: 'relative',
    height: 180,
    width: '100%',
  },
  image: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
  },
  featuredBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: theme.colors.warning,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  featuredText: {
    color: theme.colors.background,
    fontWeight: '600',
    fontSize: 12,
  },
  priceBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: theme.colors.success,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  priceText: {
    color: theme.colors.background,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  contentContainer: {
    padding: 16,
  },
  housemateHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  housemateDetails: {
    marginLeft: 12,
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  badge: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  badgeText: {
    fontSize: 12,
    color: '#4B5563',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#4B5563',
    fontWeight: '600',
    marginLeft: 4,
  },
  description: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 12,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366F1',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  messageButtonText: {
    color: theme.colors.background,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.background,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6366F1',
    flex: 1,
  },
  likeButtonText: {
    color: '#6366F1',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default React.memo(ListingCard);
