import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, TextInput } from 'react-native';
import { X, Edit2, Check, BookOpen } from 'lucide-react-native';
import { useRouter } from 'expo-router';

import { useSavedRooms } from '@hooks/useSavedRooms';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface SavedRoomNotesProps {
  savedId: string;
  notes?: string;
  roomId?: string;
  onUpdate?: () => void;
}

export const SavedRoomNotes: React.FC<SavedRoomNotesProps> = ({
  savedId,
  notes: initialNotes = '',
  roomId,
  onUpdate,
}) => {
  const router = useRouter();
  const theme = useTheme();
  const styles = createStyles(theme);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [notesText, setNotesText] = useState(initialNotes);
  const [isSaving, setIsSaving] = useState(false);
  const { updateNotes } = useSavedRooms();

  const toggleModal = () => {
    setIsModalVisible(!isModalVisible);
    if (!isModalVisible) {
      // Reset the text to the current notes when opening the modal
      setNotesText(initialNotes);
    }
  };

  const handleSave = async () => {
    if (!savedId) return;

    setIsSaving(true);
    try {
      await updateNotes(savedId, notesText);
      if (onUpdate) onUpdate();
      toggleModal();
    } catch (error) {
      console.error('Error saving notes:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleOpenMemoryBank = () => {
    router.push('/memory-bank');
  };

  // Create a truncated preview of the notes
  const getNotePreview = () => {
    if (!initialNotes) return '';

    if (initialNotes.length <= 50) {
      return initialNotes;
    }

    return initialNotes.substring(0, 50) + '...';
  };

  return (
    <View>
      {initialNotes ? (
        <TouchableOpacity style={styles.notesContainer} onPress={toggleModal}>
          <View style={styles.noteTextContainer}>
            <Text style={styles.notesLabel}>Notes:</Text>
            <Text style={styles.notesText}>{getNotePreview()}</Text>
          </View>
          <View style={styles.actionButtons}>
            {roomId && (
              <TouchableOpacity style={styles.memoryBankButton} onPress={handleOpenMemoryBank}>
                <BookOpen size={16} color="#6366F1" />
              </TouchableOpacity>
            )}
            <Edit2 size={16} color={theme.colors.textSecondary} />
          </View>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity style={styles.addNotesButton} onPress={toggleModal}>
          <Text style={styles.addNotesText}>Add Notes</Text>
        </TouchableOpacity>
      )}

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={toggleModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Room Notes</Text>
              <TouchableOpacity onPress={toggleModal}>
                <X size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <Text style={styles.inputLabel}>Add your personal notes about this room:</Text>
            <TextInput
              style={styles.textInput}
              multiline
              value={notesText}
              onChangeText={setNotesText}
              placeholder="E.g., Good lighting, close to public transport, etc."
              placeholderTextColor="#94A3B8"
              autoFocus
              maxLength={500}
            />

            <Text style={styles.charCount}>{notesText.length}/500</Text>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={toggleModal}
                disabled={isSaving}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <Text style={styles.saveButtonText}>Saving...</Text>
                ) : (
                  <>
                    <Check size={16} color={theme.colors.background} />
                    <Text style={styles.saveButtonText}>Save Notes</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  notesContainer: {
    flexDirection: 'row',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    alignItems: 'center',
  },
  noteTextContainer: {
    flex: 1,
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  notesText: {
    fontSize: 14,
    color: '#475569',
  },
  addNotesButton: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    alignItems: 'center',
  },
  addNotesText: {
    fontSize: 14,
    color: '#6366F1',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: colorWithOpacity(theme.colors.text, 0.5),
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
  },
  inputLabel: {
    fontSize: 16,
    color: '#475569',
    marginBottom: 12,
  },
  textInput: {
    height: 150,
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.text,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    color: '#94A3B8',
    textAlign: 'right',
    marginTop: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#F1F5F9',
    marginRight: 12,
    flexGrow: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  saveButton: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#6366F1',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexGrow: 2,
  },
  saveButtonText: {
    fontSize: 16,
    color: theme.colors.background,
    fontWeight: '500',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memoryBankButton: {
    marginRight: 12,
  },
});

export default SavedRoomNotes;
