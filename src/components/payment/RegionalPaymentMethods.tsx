import React, { useState, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, TouchableOpacity, FlatList, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  internationalPaymentService,
  RegionalPaymentMethod,
} from '@services/internationalPaymentService';
import { logger } from '@services/loggerService';

interface RegionalPaymentMethodsProps {
  regionCode: string;
  currency?: string;
  selectedMethodId?: string;
  onMethodSelect: (method: RegionalPaymentMethod) => void;
  showFees?: boolean;
  showProcessingTime?: boolean;
  style?: any;
}

const PAYMENT_TYPE_ICONS: Record<string, string> = {
  card: 'card',
  bank_transfer: 'business',
  mobile_money: 'phone-portrait',
  digital_wallet: 'wallet',
};

const PAYMENT_TYPE_LABELS: Record<string, string> = {
  card: 'Credit/Debit Card',
  bank_transfer: 'Bank Transfer',
  mobile_money: 'Mobile Money',
  digital_wallet: 'Digital Wallet',
};

const PROVIDER_COLORS: Record<string, string> = {
  stripe: '#635BFF',
  paypal: '#0070BA',
  chapa: '#FF6B35',
  mpesa: '#00A651',
  telebirr: '#FF6B00',
};

export const RegionalPaymentMethods: React.FC<RegionalPaymentMethodsProps> = ({
  regionCode,
  currency,
  selectedMethodId,
  onMethodSelect,
  showFees = true,
  showProcessingTime = true,
  style,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [paymentMethods, setPaymentMethods] = useState<RegionalPaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPaymentMethods();
  }, [regionCode, currency]);

  const loadPaymentMethods = async () => {
    setLoading(true);
    setError(null);

    try {
      const methods = await internationalPaymentService.getRegionalPaymentMethods({
        region_code: regionCode,
        currency,
      });

      setPaymentMethods(methods);
    } catch (err) {
      const errorMessage = 'Failed to load payment methods';
      setError(errorMessage);
      logger.error(errorMessage, 'RegionalPaymentMethods', { regionCode, currency }, err as Error);
    } finally {
      setLoading(false);
    }
  };

  const formatProcessingTime = (minutes: number): string => {
    if (minutes === 0) return 'Instant';
    if (minutes < 60) return `${minutes} min`;
    if (minutes < 1440) return `${Math.round(minutes / 60)} hr`;
    return `${Math.round(minutes / 1440)} day${Math.round(minutes / 1440) > 1 ? 's' : ''}`;
  };

  const formatFee = (percentage: number, fixed: number, currency: string = 'USD'): string => {
    const parts: string[] = [];

    if (percentage > 0) {
      parts.push(`${(percentage * 100).toFixed(2)}%`);
    }

    if (fixed > 0) {
      const currencySymbol = getCurrencySymbol(currency);
      parts.push(`${currencySymbol}${fixed.toFixed(2)}`);
    }

    if (parts.length === 0) return 'Free';
    return parts.join(' + ');
  };

  const getCurrencySymbol = (currencyCode: string): string => {
    const symbols: Record<string, string> = {
      USD: '$',
      EUR: '€',
      GBP: '£',
      CAD: 'C$',
      AUD: 'A$',
      ETB: 'Br',
      JPY: '¥',
      CHF: 'Fr',
      CNY: '¥',
      INR: '₹',
    };
    return symbols[currencyCode] || currencyCode;
  };

  const handleMethodPress = (method: RegionalPaymentMethod) => {
    if (method.requires_verification) {
      Alert.alert(
        'Verification Required',
        `This payment method requires additional verification. You'll be redirected to complete the verification process.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue', onPress: () => onMethodSelect(method) },
        ]
      );
    } else {
      onMethodSelect(method);
    }
  };

  const renderPaymentMethod = ({ item }: { item: RegionalPaymentMethod }) => {
    const isSelected = item.id === selectedMethodId;
    const providerColor = PROVIDER_COLORS[item.provider_name] || theme.colors.textSecondary;

    return (
      <TouchableOpacity
        style={[
          {
            backgroundColor: theme.colors.background,
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            borderWidth: 2,
            borderColor: isSelected ? '#007AFF' : '#f0f0f0',
            shadowColor: theme.colors.text,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          },
          isSelected && {
            backgroundColor: '#f8f9ff',
            borderColor: '#007AFF',
          },
        ]}
        onPress={() => handleMethodPress(item)}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: `${providerColor}15`,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}
          >
            <Ionicons
              name={PAYMENT_TYPE_ICONS[item.payment_type] as any}
              size={20}
              color={providerColor}
            />
          </View>

          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                color: isSelected ? '#007AFF' : theme.colors.text,
              }}
            >
              {item.method_name}
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: theme.colors.textSecondary,
                marginTop: 2,
              }}
            >
              {PAYMENT_TYPE_LABELS[item.payment_type]} • {item.provider_name}
            </Text>
          </View>

          {item.requires_verification && (
            <View
              style={{
                backgroundColor: theme.colors.background3CD,
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
                marginRight: 8,
              }}
            >
              <Text style={{ fontSize: 10, color: '#856404', fontWeight: '500' }}>
                VERIFICATION
              </Text>
            </View>
          )}

          {isSelected && <Ionicons name="checkmark-circle" size={24} color="#007AFF" />}
        </View>

        <View
          style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}
        >
          {showFees && (
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 12, color: theme.colors.textSecondary, marginBottom: 2 }}>Processing Fee</Text>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '500',
                  color:
                    item.processing_fee_percentage === 0 && item.processing_fee_fixed === 0
                      ? '#28A745'
                      : theme.colors.textSecondary,
                }}
              >
                {formatFee(item.processing_fee_percentage, item.processing_fee_fixed, currency)}
              </Text>
            </View>
          )}

          {showProcessingTime && (
            <View style={{ flex: 1, alignItems: 'flex-end' }}>
              <Text style={{ fontSize: 12, color: theme.colors.textSecondary, marginBottom: 2 }}>Processing Time</Text>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '500',
                  color: item.processing_time_minutes === 0 ? '#28A745' : theme.colors.textSecondary,
                }}
              >
                {formatProcessingTime(item.processing_time_minutes)}
              </Text>
            </View>
          )}
        </View>

        {item.min_amount > 0 ||
          (item.max_amount && (
            <View
              style={{
                marginTop: 8,
                paddingTop: 8,
                borderTopWidth: 1,
                borderTopColor: '#f0f0f0',
              }}
            >
              <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                Amount limits: {getCurrencySymbol(currency || 'USD')}
                {item.min_amount.toFixed(2)}
                {item.max_amount &&
                  ` - ${getCurrencySymbol(currency || 'USD')}${item.max_amount.toFixed(2)}`}
              </Text>
            </View>
          ))}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View
        style={[
          {
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 40,
          },
          style,
        ]}
      >
        <ActivityIndicator size="large" color="#007AFF" />
        <Text
          style={{
            fontSize: 16,
            color: theme.colors.textSecondary,
            marginTop: 12,
          }}
        >
          Loading payment methods...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View
        style={[
          {
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 40,
          },
          style,
        ]}
      >
        <Ionicons name="alert-circle" size={48} color="#FF3B30" />
        <Text
          style={{
            fontSize: 16,
            color: '#FF3B30',
            marginTop: 12,
            textAlign: 'center',
          }}
        >
          {error}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: '#007AFF',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8,
            marginTop: 16,
          }}
          onPress={loadPaymentMethods}
        >
          <Text style={{ color: theme.colors.background, fontWeight: '500' }}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (paymentMethods.length === 0) {
    return (
      <View
        style={[
          {
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 40,
          },
          style,
        ]}
      >
        <Ionicons name="card" size={48} color={theme.colors.textSecondary} />
        <Text
          style={{
            fontSize: 16,
            color: theme.colors.textSecondary,
            marginTop: 12,
            textAlign: 'center',
          }}
        >
          No payment methods available for this region
        </Text>
        {currency && (
          <Text
            style={{
              fontSize: 14,
              color: '#999',
              marginTop: 4,
              textAlign: 'center',
            }}
          >
            Try selecting a different currency
          </Text>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <Ionicons name="location" size={20} color="#007AFF" />
        <Text
          style={{
            fontSize: 16,
            fontWeight: '600',
            marginLeft: 8,
            color: '#007AFF',
          }}
        >
          Available Payment Methods
        </Text>
        {currency && (
          <Text
            style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              marginLeft: 8,
            }}
          >
            ({currency})
          </Text>
        )}
      </View>

      <FlatList
        data={paymentMethods}
        renderItem={renderPaymentMethod}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />

      <View
        style={{
          backgroundColor: '#f8f9ff',
          padding: 12,
          borderRadius: 8,
          marginTop: 16,
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons name="shield-checkmark" size={16} color="#007AFF" />
          <Text
            style={{
              fontSize: 12,
              color: '#007AFF',
              marginLeft: 6,
              flex: 1,
            }}
          >
            All payments are secured with industry-standard encryption
          </Text>
        </View>
      </View>
    </View>
  );
};

export default RegionalPaymentMethods;
