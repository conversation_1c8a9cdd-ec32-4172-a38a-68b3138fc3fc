import React, { useState, useEffect } from 'react';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { View, Text, TouchableOpacity, Modal, FlatList, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { internationalPaymentService } from '@services/internationalPaymentService';
import { logger } from '@services/loggerService';

interface Currency {
  code: string;
  name: string;
  symbol: string;
  flag: string;
}

interface CurrencySelectorProps {
  selectedCurrency: string;
  onCurrencyChange: (currency: string) => void;
  supportedCurrencies?: string[];
  showExchangeRate?: boolean;
  baseCurrency?: string;
  disabled?: boolean;
  style?: any;
}

const CURRENCIES: Currency[] = [
  { code: 'USD', name: 'US Dollar', symbol: '$', flag: '🇺🇸' },
  { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺' },
  { code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', flag: '🇨🇦' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', flag: '🇦🇺' },
  { code: 'ETB', name: 'Ethiopian Birr', symbol: 'Br', flag: '🇪🇹' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'Fr', flag: '🇨🇭' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳' },
];

export const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  selectedCurrency,
  onCurrencyChange,
  supportedCurrencies,
  showExchangeRate = false,
  baseCurrency = 'USD',
  disabled = false,
  style,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [modalVisible, setModalVisible] = useState(false);
  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
  const [loadingRates, setLoadingRates] = useState(false);

  const availableCurrencies = supportedCurrencies
    ? CURRENCIES.filter(currency => supportedCurrencies.includes(currency.code))
    : CURRENCIES;

  const selectedCurrencyData = CURRENCIES.find(c => c.code === selectedCurrency);

  useEffect(() => {
    if (showExchangeRate && baseCurrency !== selectedCurrency) {
      loadExchangeRates();
    }
  }, [showExchangeRate, baseCurrency, selectedCurrency]);

  const loadExchangeRates = async () => {
    if (!showExchangeRate) return;

    setLoadingRates(true);
    try {
      const rates: Record<string, number> = {};

      for (const currency of availableCurrencies) {
        if (currency.code !== baseCurrency) {
          const rate = await internationalPaymentService.getExchangeRate(
            baseCurrency,
            currency.code
          );
          if (rate) {
            rates[currency.code] = rate;
          }
        }
      }

      setExchangeRates(rates);
    } catch (error) {
      logger.error('Failed to load exchange rates', 'CurrencySelector', {}, error as Error);
    } finally {
      setLoadingRates(false);
    }
  };

  const handleCurrencySelect = (currency: Currency) => {
    onCurrencyChange(currency.code);
    setModalVisible(false);
  };

  const formatExchangeRate = (rate: number): string => {
    if (rate < 0.01) {
      return rate.toFixed(6);
    } else if (rate < 1) {
      return rate.toFixed(4);
    } else {
      return rate.toFixed(2);
    }
  };

  const renderCurrencyItem = ({ item }: { item: Currency }) => {
    const exchangeRate = exchangeRates[item.code];
    const isSelected = item.code === selectedCurrency;

    return (
      <TouchableOpacity
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 16,
            paddingHorizontal: 20,
            borderBottomWidth: 1,
            borderBottomColor: '#f0f0f0',
          },
          isSelected && { backgroundColor: '#f8f9ff' },
        ]}
        onPress={() => handleCurrencySelect(item)}
      >
        <Text style={{ fontSize: 24, marginRight: 12 }}>{item.flag}</Text>
        <View style={{ flex: 1 }}>
          <Text
            style={{
              fontSize: 16,
              fontWeight: isSelected ? '600' : '400',
              color: isSelected ? '#007AFF' : theme.colors.text,
            }}
          >
            {item.code}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              marginTop: 2,
            }}
          >
            {item.name}
          </Text>
          {showExchangeRate && exchangeRate && item.code !== baseCurrency && (
            <Text
              style={{
                fontSize: 12,
                color: '#888',
                marginTop: 2,
              }}
            >
              1 {baseCurrency} = {formatExchangeRate(exchangeRate)} {item.code}
            </Text>
          )}
        </View>
        <Text
          style={{
            fontSize: 18,
            color: theme.colors.textSecondary,
            marginRight: 8,
          }}
        >
          {item.symbol}
        </Text>
        {isSelected && <Ionicons name="checkmark" size={20} color="#007AFF" />}
      </TouchableOpacity>
    );
  };

  return (
    <View style={style}>
      <TouchableOpacity
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderWidth: 1,
            borderColor: '#ddd',
            borderRadius: 8,
            backgroundColor: disabled ? theme.colors.surface : theme.colors.background,
          },
          disabled && { opacity: 0.6 },
        ]}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
      >
        {selectedCurrencyData && (
          <>
            <Text style={{ fontSize: 20, marginRight: 8 }}>{selectedCurrencyData.flag}</Text>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '500',
                flex: 1,
              }}
            >
              {selectedCurrencyData.code} - {selectedCurrencyData.name}
            </Text>
            <Text
              style={{
                fontSize: 16,
                color: theme.colors.textSecondary,
                marginRight: 8,
              }}
            >
              {selectedCurrencyData.symbol}
            </Text>
          </>
        )}
        {!disabled && <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />}
      </TouchableOpacity>

      {showExchangeRate && selectedCurrency !== baseCurrency && (
        <View
          style={{
            marginTop: 8,
            paddingHorizontal: 16,
          }}
        >
          {loadingRates ? (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <ActivityIndicator size="small" color="#007AFF" />
              <Text
                style={{
                  fontSize: 12,
                  color: theme.colors.textSecondary,
                  marginLeft: 8,
                }}
              >
                Loading exchange rate...
              </Text>
            </View>
          ) : exchangeRates[selectedCurrency] ? (
            <Text
              style={{
                fontSize: 12,
                color: theme.colors.textSecondary,
              }}
            >
              1 {baseCurrency} = {formatExchangeRate(exchangeRates[selectedCurrency])}{' '}
              {selectedCurrency}
            </Text>
          ) : null}
        </View>
      )}

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f0f0f0',
            }}
          >
            <Text
              style={{
                fontSize: 18,
                fontWeight: '600',
              }}
            >
              Select Currency
            </Text>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={{
                padding: 8,
                borderRadius: 20,
                backgroundColor: '#f0f0f0',
              }}
            >
              <Ionicons name="close" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {showExchangeRate && (
            <View
              style={{
                paddingHorizontal: 20,
                paddingVertical: 12,
                backgroundColor: '#f8f9ff',
                borderBottomWidth: 1,
                borderBottomColor: '#f0f0f0',
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="information-circle" size={16} color="#007AFF" />
                <Text
                  style={{
                    fontSize: 12,
                    color: '#007AFF',
                    marginLeft: 6,
                    flex: 1,
                  }}
                >
                  Exchange rates are updated in real-time
                </Text>
                {loadingRates && <ActivityIndicator size="small" color="#007AFF" />}
              </View>
            </View>
          )}

          <FlatList
            data={availableCurrencies}
            renderItem={renderCurrencyItem}
            keyExtractor={item => item.code}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  );
};

export default CurrencySelector;
