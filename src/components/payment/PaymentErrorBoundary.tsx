import React, { Component, <PERSON>rrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { initializePaymentBridge } from '@utils/paymentBridge';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  errorMessage: string;
  retryCount: number;
}

/**
 * A specialized error boundary for payment-related components
 * Handles NOBRIDGE and other payment-related errors
 */
export class PaymentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorMessage: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Extract a user-friendly error message
    let errorMessage = 'An error occurred with the payment system';

    // Check for different types of errors and provide appropriate messages
    if (error.message?.includes('undefined')) {
      errorMessage = 'Payment system not available';
    } else if (error.message?.includes('null')) {
      errorMessage = 'Payment system resources not loaded';
    } else if (error.message?.includes('convert') && error.message?.includes('object')) {
      errorMessage = 'Payment system configuration issue';
    } else if (error.message?.includes('NOBRIDGE')) {
      errorMessage = 'Payment system communication error';
    } else if (error.message?.includes('prototype')) {
      errorMessage = 'Payment module initialization error';
    }

    console.error('Payment system error captured by boundary:', error.message);

    return {
      hasError: true,
      errorMessage,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Payment component error caught by boundary:', error.name, error.message);
    console.error('Component stack where error occurred:', errorInfo.componentStack);

    // Add more detailed logging for specific errors
    if (error.message?.includes("Cannot read property 'prototype'")) {
      console.error('### Payment Module Prototype Error Details ###', {
        name: error.name,
        message: error.message,
        stackPreview: error.stack?.split('\n').slice(0, 5).join('\n'),
        componentStack: errorInfo.componentStack,
      });

      // Immediately attempt recovery for this specific error
      if (this.state.retryCount < 1) {
        setTimeout(() => {
          this.handleRetry();
        }, 500);
      }
    }
    // Log generic info for other errors
    else if (error.message?.includes('undefined') || error.message?.includes('null')) {
      console.warn('### Undefined/Null Payment Error Details ###', {
        name: error.name,
        message: error.message,
        stackPreview: error.stack?.split('\n').slice(0, 5).join('\n'),
      });
    } else {
      console.log('Non-TypeError payment error details:', {
        name: error.name,
        message: error.message,
      });
    }
  }

  handleRetry = async (): Promise<void> => {
    try {
      // Initialize payment bridge
      await initializePaymentBridge();

      // Reset error state
      this.setState(prevState => ({
        hasError: false,
        retryCount: prevState.retryCount + 1,
      }));

      // Call custom retry handler if provided
      if (this.props.onRetry) {
        this.props.onRetry();
      }
    } catch (error) {
      console.error('Error during retry:', error);

      // Even if retry fails, we'll attempt to recover the UI
      if (this.state.retryCount < 2) {
        this.setState(prevState => ({
          hasError: false,
          retryCount: prevState.retryCount + 1,
        }));
      } else {
        // After multiple failures, update error message
        this.setState({
          errorMessage: 'Unable to connect to payment system. Please try again later.',
        });
      }
    }
  };

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Payment System Error</Text>
          <Text style={styles.errorMessage}>{this.state.errorMessage}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={this.handleRetry}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Wrap children in error-catching View
    return <View style={styles.container}>{this.props.children}</View>;
  }
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: theme.colors.background7f7,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#d32f2f',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: '#555',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#6366F1',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  retryText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
});


export default PaymentErrorBoundary;
