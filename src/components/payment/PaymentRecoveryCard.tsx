import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {  colorWithOpacity, type Theme  } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
// Type definitions for payment recovery (moved from old paymentRecoveryService)
interface PaymentRecoveryAttempt {
  id: string;
  payment_id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'successful' | 'failed' | 'cancelled';
  attempt_number: number;
  next_attempt_date?: string;
  reason: string;
  created_at: string;
  updated_at: string;
}

interface PaymentGracePeriod {
  id: string;
  payment_id: string;
  user_id: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'expired' | 'completed';
  reason: string;
  created_at: string;
}

interface PaymentRecoveryCardProps {
  recoveryAttempt?: PaymentRecoveryAttempt;
  gracePeriod?: PaymentGracePeriod;
  onRetryPayment?: (attemptId: string) => void;
  onCancelRecovery?: (attemptId: string) => void;
  onUpdatePaymentMethod?: () => void;
  onViewDetails?: () => void;
  style?: any;
}

const STATUS_COLORS = {
  pending: '#FFC107',
  processing: '#007AFF',
  success: '#28A745',
  failed: '#DC3545',
  cancelled: '#6C757D',
  expired: '#DC3545',
  active: '#007AFF',
  resolved: '#28A745',
};

const STATUS_ICONS = {
  pending: 'time',
  processing: 'sync',
  success: 'checkmark-circle',
  failed: 'close-circle',
  cancelled: 'ban',
  expired: 'alert-circle',
  active: 'hourglass',
  resolved: 'checkmark-circle',
};

const RECOVERY_METHOD_LABELS = {
  retry_same: 'Retry Same Method',
  fallback_method: 'Fallback Method',
  manual_intervention: 'Manual Intervention',
  grace_period: 'Grace Period',
};

const GRACE_PERIOD_TYPE_LABELS = {
  subscription_renewal: 'Subscription Renewal',
  service_payment: 'Service Payment',
  penalty_fee: 'Penalty Fee',
  late_payment: 'Late Payment',
};

export const PaymentRecoveryCard: React.FC<PaymentRecoveryCardProps> = ({
  recoveryAttempt,
  gracePeriod,
  onRetryPayment,
  onCancelRecovery,
  onUpdatePaymentMethod,
  onViewDetails,
  style,
}) => {
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number, currency: string): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  };

  const getDaysRemaining = (endDate: string): number => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getProgressPercentage = (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    const totalTime = end.getTime() - start.getTime();
    const elapsedTime = now.getTime() - start.getTime();

    return Math.min(100, Math.max(0, (elapsedTime / totalTime) * 100));
  };

  const handleRetryPress = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

    if (!recoveryAttempt || !onRetryPayment) return;

    Alert.alert(
      'Retry Payment',
      `Retry payment of ${formatCurrency(recoveryAttempt.retry_amount, recoveryAttempt.retry_currency)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Retry',
          onPress: () => onRetryPayment(recoveryAttempt.id),
        },
      ]
    );
  };

  const handleCancelPress = () => {
    if (!recoveryAttempt || !onCancelRecovery) return;

    Alert.alert(
      'Cancel Recovery',
      'Are you sure you want to cancel this payment recovery attempt?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => onCancelRecovery(recoveryAttempt.id),
        },
      ]
    );
  };

  // Determine which data to display (recovery attempt or grace period)
  const isRecoveryAttempt = !!recoveryAttempt;
  const isGracePeriod = !!gracePeriod;

  if (!isRecoveryAttempt && !isGracePeriod) {
    return null;
  }

  const status = isRecoveryAttempt ? recoveryAttempt!.status : gracePeriod!.status;
  const statusColor = STATUS_COLORS[status as keyof typeof STATUS_COLORS];
  const statusIcon = STATUS_ICONS[status as keyof typeof STATUS_ICONS];

  return (
    <View
      style={[
        {
          backgroundColor: theme.colors.background,
          borderRadius: 12,
          padding: 16,
          marginBottom: 12,
          borderLeftWidth: 4,
          borderLeftColor: statusColor,
          shadowColor: theme.colors.text,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        },
        style,
      ]}
    >
      {/* Header */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: `${statusColor}15`,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          }}
        >
          <Ionicons name={statusIcon as any} size={20} color={statusColor} />
        </View>

        <View style={{ flex: 1 }}>
          <Text
            style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
            }}
          >
            {isRecoveryAttempt ? 'Payment Recovery' : 'Grace Period'}
          </Text>
          <Text
            style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              marginTop: 2,
            }}
          >
            {isRecoveryAttempt
              ? RECOVERY_METHOD_LABELS[recoveryAttempt!.recovery_method]
              : GRACE_PERIOD_TYPE_LABELS[gracePeriod!.grace_period_type]}
          </Text>
        </View>

        <View
          style={{
            backgroundColor: `${statusColor}15`,
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 12,
          }}
        >
          <Text
            style={{
              fontSize: 12,
              fontWeight: '600',
              color: statusColor,
              textTransform: 'uppercase',
            }}
          >
            {status.replace('_', ' ')}
          </Text>
        </View>
      </View>

      {/* Amount and Details */}
      <View style={{ marginBottom: 12 }}>
        <Text style={{ fontSize: 14, color: theme.colors.textSecondary, marginBottom: 4 }}>Amount:</Text>
        <Text style={{ fontSize: 18, fontWeight: '600', color: theme.colors.text }}>
          {isRecoveryAttempt
            ? formatCurrency(recoveryAttempt!.retry_amount, recoveryAttempt!.retry_currency)
            : formatCurrency(gracePeriod!.amount_due, gracePeriod!.currency)}
        </Text>
      </View>

      {/* Recovery Attempt Specific Info */}
      {isRecoveryAttempt && (
        <View style={{ marginBottom: 12 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>Attempt:</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: theme.colors.text }}>
              {recoveryAttempt!.attempt_number} of {recoveryAttempt!.max_retry_attempts}
            </Text>
          </View>

          {recoveryAttempt!.next_retry_at && (
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}
            >
              <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>Next Retry:</Text>
              <Text style={{ fontSize: 14, fontWeight: '500', color: theme.colors.text }}>
                {formatDate(recoveryAttempt!.next_retry_at)}
              </Text>
            </View>
          )}

          {recoveryAttempt!.failure_reason && (
            <View style={{ marginTop: 8 }}>
              <Text style={{ fontSize: 12, color: theme.colors.textSecondary, marginBottom: 4 }}>Failure Reason:</Text>
              <Text style={{ fontSize: 12, color: '#DC3545' }}>
                {recoveryAttempt!.failure_reason}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Grace Period Specific Info */}
      {isGracePeriod && (
        <View style={{ marginBottom: 12 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>Days Remaining:</Text>
            <Text
              style={{
                fontSize: 14,
                fontWeight: '600',
                color: getDaysRemaining(gracePeriod!.grace_end_date) <= 1 ? '#DC3545' : theme.colors.text,
              }}
            >
              {Math.max(0, getDaysRemaining(gracePeriod!.grace_end_date))} days
            </Text>
          </View>

          {/* Progress Bar */}
          <View
            style={{
              height: 6,
              backgroundColor: '#f0f0f0',
              borderRadius: 3,
              marginBottom: 8,
            }}
          >
            <View
              style={{
                height: '100%',
                backgroundColor: statusColor,
                borderRadius: 3,
                width: `${getProgressPercentage(gracePeriod!.grace_start_date, gracePeriod!.grace_end_date)}%`,
              }}
            />
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
              Started: {formatDate(gracePeriod!.grace_start_date)}
            </Text>
            <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
              Ends: {formatDate(gracePeriod!.grace_end_date)}
            </Text>
          </View>

          {gracePeriod!.late_fee_applied && (
            <View
              style={{
                backgroundColor: 'theme.colors.background3CD',
                padding: 8,
                borderRadius: 6,
                marginTop: 8,
              }}
            >
              <Text style={{ fontSize: 12, color: '#856404' }}>
                Late fee applied:{' '}
                {formatCurrency(gracePeriod!.late_fee_amount, gracePeriod!.currency)}
              </Text>
            </View>
          )}

          {gracePeriod!.service_suspended && (
            <View
              style={{
                backgroundColor: '#F8D7DA',
                padding: 8,
                borderRadius: 6,
                marginTop: 8,
              }}
            >
              <Text style={{ fontSize: 12, color: '#721C24' }}>
                Service suspended since {formatDate(gracePeriod!.suspension_date!)}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Actions */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 8 }}>
        {isRecoveryAttempt && recoveryAttempt!.status === 'pending' && (
          <>
            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: '#007AFF',
                paddingVertical: 10,
                paddingHorizontal: 16,
                borderRadius: 8,
                marginRight: 8,
              }}
              onPress={handleRetryPress}
            >
              <Text
                style={{
                  color: theme.colors.background,
                  fontSize: 14,
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              >
                Retry Now
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: theme.colors.surface,
                paddingVertical: 10,
                paddingHorizontal: 16,
                borderRadius: 8,
                borderWidth: 1,
                borderColor: '#dee2e6',
                marginLeft: 8,
              }}
              onPress={handleCancelPress}
            >
              <Text
                style={{
                  color: '#495057',
                  fontSize: 14,
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              >
                Cancel
              </Text>
            </TouchableOpacity>
          </>
        )}

        {isGracePeriod && gracePeriod!.status === 'active' && (
          <>
            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: '#28A745',
                paddingVertical: 10,
                paddingHorizontal: 16,
                borderRadius: 8,
                marginRight: 8,
              }}
              onPress={onRetryPayment ? () => onRetryPayment('') : undefined}
            >
              <Text
                style={{
                  color: theme.colors.background,
                  fontSize: 14,
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              >
                Pay Now
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: theme.colors.surface,
                paddingVertical: 10,
                paddingHorizontal: 16,
                borderRadius: 8,
                borderWidth: 1,
                borderColor: '#dee2e6',
                marginLeft: 8,
              }}
              onPress={onUpdatePaymentMethod}
            >
              <Text
                style={{
                  color: '#495057',
                  fontSize: 14,
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              >
                Update Method
              </Text>
            </TouchableOpacity>
          </>
        )}

        {(recoveryAttempt?.status === 'success' || gracePeriod?.status === 'resolved') && (
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: theme.colors.surface,
              paddingVertical: 10,
              paddingHorizontal: 16,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#dee2e6',
            }}
            onPress={onViewDetails}
          >
            <Text
              style={{
                color: '#495057',
                fontSize: 14,
                fontWeight: '500',
                textAlign: 'center',
              }}
            >
              View Details
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default PaymentRecoveryCard;
