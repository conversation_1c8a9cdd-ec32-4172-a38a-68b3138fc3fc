import { getSupabaseClient } from '@services/supabaseService';
import { decode } from 'base64-arraybuffer';
import * as FileSystem from 'expo-file-system';

interface UploadOptions {
  uri: string;
  bucket: string;
  path: string;
  contentType: string;
}

interface UploadResult {
  success: boolean;
  publicUrl?: string;
  error?: string;
}

/**
 * Simple upload utility for Supabase storage
 * Used by threshold detector and other basic upload needs
 */
export async function uploadToSupabase(options: UploadOptions): Promise<UploadResult> {
  try {
    const { uri, bucket, path, contentType } = options;
    const supabaseClient = getSupabaseClient();

    // Check authentication
    const { data: authData, error: authError } = await supabaseClient.auth.getUser();
    if (authError || !authData.user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    let uploadData: any;

    // Handle different URI types
    if (uri.startsWith('data:')) {
      // Data URI - extract base64 part
      const base64Part = uri.split(',')[1];
      if (!base64Part) {
        return {
          success: false,
          error: 'Invalid data URI format'
        };
      }
      
      // For text data, use the base64 string directly
      if (contentType.startsWith('text/')) {
        uploadData = base64Part;
      } else {
        // For binary data, convert to ArrayBuffer
        uploadData = decode(base64Part);
      }
    } else {
      // File URI - read as base64 then convert
      const fileData = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      
      if (!fileData) {
        return {
          success: false,
          error: 'Could not read file data'
        };
      }

      // For text data, use base64 directly; for binary, convert to ArrayBuffer
      if (contentType.startsWith('text/')) {
        uploadData = fileData;
      } else {
        uploadData = decode(fileData);
      }
    }

    // Upload to Supabase
    const { data, error } = await supabaseClient.storage
      .from(bucket)
      .upload(path, uploadData, {
        contentType,
        upsert: true,
      });

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    // ✅ ENHANCED: Debug upload response
    console.log(`🔍 [uploadToSupabase] Upload response:`, {
      uploadSuccess: !!data,
      uploadPath: data?.path,
      uploadId: data?.id,
      bucket,
      path
    });

    // Get public URL
    const { data: publicUrlData } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(path);

    // ✅ ENHANCED: Debug public URL generation
    console.log(`🔍 [uploadToSupabase] Public URL generation:`, {
      publicUrlData,
      publicUrl: publicUrlData?.publicUrl,
      hasPublicUrl: !!publicUrlData?.publicUrl,
      bucket,
      path
    });

    // ✅ ENHANCED: Validate URL before returning
    if (!publicUrlData?.publicUrl) {
      console.error(`❌ [uploadToSupabase] Public URL generation failed:`, {
        publicUrlData,
        bucket,
        path,
        supabaseUrl: supabaseClient.supabaseUrl
      });
      return {
        success: false,
        error: 'Upload succeeded but public URL generation failed'
      };
    }

    console.log(`✅ [uploadToSupabase] Upload and URL generation successful:`, publicUrlData.publicUrl);

    return {
      success: true,
      publicUrl: publicUrlData.publicUrl
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
} 