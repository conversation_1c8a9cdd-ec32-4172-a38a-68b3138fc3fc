
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ChevronLeft, MessageSquare, Heart } from 'lucide-react-native';
import { unifiedProfileService } from '@services/unified-profile';
import { matchingService } from '@services/matchingService';
interface UserProfile {
  id: string;
  username: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  bio?: string;
  avatar_url?: string;
  occupation?: string;
  compatibility_score?: number;
  [key: string]: any; // Allow for additional fields
}
import { ProfileMemoryTab } from '@components/profile/ProfileMemoryTab';
import { useAuth } from '@hooks/useAuth';
import { startChatWithMatch } from '@utils/chatUtils';
import { MatchCelebrationModal } from '@components/matching/MatchCelebrationModal';

export default function ProfileViewScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const params = useLocalSearchParams();
  const { authState } = useAuth();
  const user = authState?.user;
  
  // Handle both direct id and query parameter formats
  const profileId =
    typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'info' | 'memory'>('info');
  const [matchStatus, setMatchStatus] = useState<'none' | 'liked' | 'matched' | 'loading'>('none');
  const [showMatchModal, setShowMatchModal] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);

  // Check if this is the current user's profile
  const isCurrentUser = user?.id === profileId;
  
  // Load match status if this is not the current user
  useEffect(() => {
    if (!isCurrentUser && user?.id && profileId) {
      loadMatchStatus();
    }
  }, [user?.id, profileId, isCurrentUser]);
  
  // Function to load match status
  const loadMatchStatus = async () => {
    if (!user?.id || !profileId) return;
    
    try {
      setMatchStatus('loading');
      const status = await matchingService.getMatchStatus(user.id, profileId);
      setMatchStatus(status || 'none');
    } catch (err) {
      console.error('Error loading match status:', err);
      setMatchStatus('none');
    }
  };

  useEffect(() => {
    let isMounted = true;

    async function loadProfileData() {
      try {
        setLoading(true);

        if (!profileId) {
          if (isMounted) {
            setError('Profile ID is missing');
            setLoading(false);
          }
          return;
        }

        // Fetch profile data
        try {
          const profileData = await unifiedProfileService.getUserProfile(profileId);
          if (!profileData) {
            if (isMounted) {
              setError('Profile not found');
              setLoading(false);
            }
            return;
          }

          if (isMounted) {
            setProfile(profileData);
            setLoading(false);
          }
        } catch (profileError) {
          console.error('Error fetching profile data:', profileError);
          if (isMounted) {
            setError('Failed to load profile data');
            setLoading(false);
          }
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        if (isMounted) {
          setError('An unexpected error occurred');
          setLoading(false);
        }
      }
    }

    loadProfileData();

    return () => {
      isMounted = false;
    };
  }, [profileId]);

  if (loading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ChevronLeft size={24} color="#1E293B" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366F1" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </View>
    );
  }

  if (error || !profile) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ChevronLeft size={24} color="#1E293B" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Error</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Profile not found'}</Text>
          <TouchableOpacity style={styles.returnButton} onPress={() => router.back()}>
            <Text style={styles.returnButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Handle message button press
  const handleMessagePress = async () => {
    if (!user?.id || !profileId || !profile || sendingMessage) return;
    
    try {
      setSendingMessage(true);
      const name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.username || 'User';
      
      const success = await startChatWithMatch(
        user.id,
        profileId,
        name
      );
      
      if (success) {
        router.push('/chat');
      } else {
        Alert.alert('Error', 'Failed to start chat. Please try again.');
      }
    } catch (err) {
      console.error('Error starting chat:', err);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setSendingMessage(false);
    }
  };
  
  // Handle like button press
  const handleLikePress = async () => {
    if (!user?.id || !profileId) return;
    
    try {
      if (matchStatus === 'none') {
        // Like the profile
        const result = await matchingService.likeProfile(user.id, profileId);
        
        if (result?.matchCreated) {
          // If a match was created, show the match celebration modal
          setMatchStatus('matched');
          setShowMatchModal(true);
        } else {
          // Otherwise, just update the like status
          setMatchStatus('liked');
        }
      } else if (matchStatus === 'liked') {
        // Unlike the profile
        await matchingService.unlikeProfile(user.id, profileId);
        setMatchStatus('none');
      }
    } catch (err) {
      console.error('Error updating like status:', err);
      Alert.alert('Error', 'Failed to update like status. Please try again.');
    }
  };
  
  // Close match modal
  const handleCloseMatchModal = () => {
    setShowMatchModal(false);
  };
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Match Celebration Modal */}
      {showMatchModal && profile && (
        <MatchCelebrationModal
          visible={showMatchModal}
          onClose={handleCloseMatchModal}
          matchedUser={{
            id: profileId || '',
            name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.username || 'User',
            firstName: profile.first_name,
            avatar: profile.avatar_url,
            compatibility: profile.compatibility_score,
          }}
          currentUser={{
            id: user?.id || '',
            avatar: user?.user_metadata?.avatar_url || undefined,
          }}
          onStartMessaging={handleMessagePress}
          onViewProfile={() => {}}
        />
      )}
    
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ChevronLeft size={24} color="#1E293B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>

      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          {profile.avatar_url ? (
            <Image source={{ uri: profile.avatar_url }} style={styles.avatar} />
          ) : (
            <View style={styles.placeholderAvatar}>
              <Text style={styles.placeholderText}>
                {profile.first_name?.charAt(0) || profile.username?.charAt(0) || '?'}
              </Text>
            </View>
          )}
        </View>

        <Text style={styles.name}>
          {profile.first_name || profile.username || 'Anonymous User'}
        </Text>

        <Text style={styles.occupation}>{profile.occupation || 'No occupation listed'}</Text>

        <Text style={styles.location}>{profile.location || 'No location specified'}</Text>
      </View>

      {/* Tab navigation */}
      <View style={styles.tabBar}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'info' && styles.activeTab]}
          onPress={() => setActiveTab('info')}
        >
          <Text style={[styles.tabText, activeTab === 'info' && styles.activeTabText]}>Info</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'memory' && styles.activeTab]}
          onPress={() => setActiveTab('memory')}
        >
          <Text style={[styles.tabText, activeTab === 'memory' && styles.activeTabText]}>Memory Bank</Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'info' ? (
        <ScrollView style={styles.content}>
          <Text style={styles.sectionTitle}>About</Text>
          <View style={styles.infoBox}>
            <Text style={styles.aboutText}>{profile.bio || 'No bio provided.'}</Text>
          </View>

          {/* Action buttons for interacting with the profile */}
          {!isCurrentUser && (
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.messageButton, sendingMessage && styles.disabledButton]}
                onPress={handleMessagePress}
                disabled={sendingMessage}
              >
                <MessageSquare size={18} color="#FFFFFF" style={{marginRight: 8}} />
                <Text style={styles.messageButtonText}>
                  {sendingMessage ? 'Opening Chat...' : 'Message'}
                </Text>
              </TouchableOpacity>
              
              {matchStatus !== 'matched' && (
                <TouchableOpacity 
                  style={[styles.likeButton, matchStatus === 'liked' && styles.likedButton]}
                  onPress={handleLikePress}
                >
                  <Heart 
                    size={18} 
                    color={matchStatus === 'liked' ? "#EC4899" : "#6366F1"} 
                    fill={matchStatus === 'liked' ? "#EC4899" : "transparent"}
                    style={{marginRight: 8}} 
                  />
                  <Text style={[styles.likeButtonText, matchStatus === 'liked' && styles.likedButtonText]}>
                    {matchStatus === 'liked' ? 'Liked' : 'Like Profile'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      ) : (
        <View style={styles.memoryTabContainer}>
          <ProfileMemoryTab profileId={profileId} isCurrentUser={isCurrentUser} />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginLeft: 12,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748B',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  returnButton: {
    backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  returnButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  profileHeader: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  avatarContainer: {
    marginBottom: 12,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  placeholderAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#E2E8F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#94A3B8',
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  occupation: {
    fontSize: 16,
    color: '#64748B',
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    color: '#94A3B8',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#6366F1',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
  },
  activeTabText: {
    color: '#6366F1',
    fontWeight: '600',
  },
  memoryTabContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginTop: 12,
    marginBottom: 8,
  },
  infoBox: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  aboutText: {
    fontSize: 16,
    color: '#334155',
    lineHeight: 22,
  },
  actionButtons: {
    marginTop: 16,
    marginBottom: 32,
    gap: 12,
  },
  messageButton: {
    backgroundColor: '#6366F1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  messageButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#A5B4FC',
  },
  likeButton: {
    backgroundColor: '#EEF2FF',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#C7D2FE',
  },
  likeButtonText: {
    color: '#6366F1',
    fontSize: 16,
    fontWeight: '600',
  },
  likedButton: {
    backgroundColor: '#FDF2F8',
    borderColor: '#FBCFE8',
  },
  likedButtonText: {
    color: '#EC4899',
  },
});
