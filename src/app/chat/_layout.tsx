/**
 * Chat Layout
 *
 * This layout wraps all chat screens with the MessagingProvider to provide
 * centralized state management for chat functionality.
 */

import { Stack } from 'expo-router';
import { useTheme } from '@design-system';
import { MessagingProvider } from '@context/MessagingContext';

export default function ChatLayout() {
  const { colors } = useTheme();

  return (
    <MessagingProvider>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: colors.background,
          },
          animation: 'slide_from_right',
        }}
      />
    </MessagingProvider>
  );
}
