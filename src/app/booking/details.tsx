import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  FileText, 
  Users, 
  ArrowLeft,
  CheckCircle,
  XCircle,
  CalendarClock,
  ChevronDown,
  PhoneCall,
  Mail,
  CreditCard,
  MessageCircle,
  Star
} from 'lucide-react-native';
import { format } from 'date-fns';
import { useTheme } from '@design-system';
import { useBookings } from '@hooks/useBookings';
import { BookingStatus } from '@services/bookingService';
import { useToast } from '@core/errors';
import { Button } from '@design-system';
import * as Linking from 'expo-linking';

const BookingDetailsScreen = () => {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { colors } = useTheme();
  const toast = useToast();
  const { 
    currentBooking, 
    isLoading, 
    error, 
    fetchBookingDetails,
    cancelBooking
  } = useBookings();
  
  const [showDetails, setShowDetails] = useState(true);

  useEffect(() => {
    if (id) {
      fetchBookingDetails(id as string);
    }
  }, [id, fetchBookingDetails]);

  const handleCancel = () => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking?',
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes, Cancel', 
          style: 'destructive',
          onPress: async () => {
            if (currentBooking?.id) {
              const success = await cancelBooking(currentBooking.id);
              if (success) {
                toast.success('Booking cancelled successfully');
              } else {
                toast.error('Failed to cancel booking');
              }
            }
          }
        }
      ]
    );
  };

  const handleReschedule = () => {
    if (currentBooking?.id) {
      router.push(`/booking/reschedule?id=${currentBooking.id}`);
    }
  };

  const handleAddToCalendar = () => {
    // Simplified implementation without actual calendar integration for now
    toast.success('Calendar feature will be available soon');
  };

  const handleShare = () => {
    // In a real app, would use Share API
    toast.success('Sharing booking details...');
  };

  const handleContactProvider = () => {
    if (currentBooking?.service?.provider) {
      router.push(`/messages?providerId=${currentBooking.service.provider.id}`);
    }
  };

  const handleCallProvider = () => {
    if (currentBooking?.service?.provider?.contact_phone) {
      Linking.openURL(`tel:${currentBooking.service.provider.contact_phone}`);
    } else {
      toast.error('No phone number available');
    }
  };

  const handleEmailProvider = () => {
    if (currentBooking?.service?.provider?.contact_email) {
      Linking.openURL(`mailto:${currentBooking.service.provider.contact_email}`);
    } else {
      toast.error('No email available');
    }
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case BookingStatus.CONFIRMED: return colors.success;
      case BookingStatus.PENDING: return colors.warning;
      case BookingStatus.CANCELLED: return colors.error;
      case BookingStatus.COMPLETED: return colors.success;
      case BookingStatus.RESCHEDULED: return colors.info;
      default: return colors.textLight;
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            title: 'Booking Details',
            headerShown: true,
            headerShadowVisible: false,
            headerStyle: { backgroundColor: colors.background },
            headerTintColor: colors.text,
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={colors.text} />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textLight }]}>
            Loading booking details...
          </Text>
        </View>
      </View>
    );
  }

  if (error || !currentBooking) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            title: 'Booking Details',
            headerShown: true,
            headerShadowVisible: false,
            headerStyle: { backgroundColor: colors.background },
            headerTintColor: colors.text,
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                <ArrowLeft size={24} color={colors.text} />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error || 'Booking not found'}
          </Text>
          <Button
            title="Go Back"
            onPress={() => router.back()}
          />
        </View>
      </View>
    );
  }

  const bookingDate = new Date(currentBooking.booking_date);
  const serviceName = currentBooking.service?.name || 'Service';
  const providerName = currentBooking.service?.provider?.business_name || 'Provider';
  const isPending = currentBooking.status === BookingStatus.PENDING || currentBooking.status === BookingStatus.CONFIRMED;
  const isCancelled = currentBooking.status === BookingStatus.CANCELLED;
  const isCompleted = currentBooking.status === BookingStatus.COMPLETED;

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Booking Details',
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: colors.background },
          headerTintColor: colors.text,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <View 
            style={[
              styles.statusBadge, 
              { backgroundColor: `${getStatusColor(currentBooking.status)}20` }
            ]}
          >
            <Text 
              style={[
                styles.statusText, 
                { color: getStatusColor(currentBooking.status) }
              ]}
            >
              {currentBooking.status}
            </Text>
          </View>
          
          <Text style={[styles.serviceName, { color: colors.text }]}>
            {serviceName}
          </Text>
          
          <Text style={[styles.providerName, { color: colors.textLight }]}>
            {providerName}
          </Text>
          
          <View style={styles.infoRow}>
            <Calendar size={18} color={colors.primary} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {format(bookingDate, 'EEEE, MMMM d, yyyy')}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Clock size={18} color={colors.primary} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {format(bookingDate, 'h:mm a')}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <MapPin size={18} color={colors.primary} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {currentBooking.address}
            </Text>
          </View>
          
          {currentBooking.roommate_shared && (
            <View style={styles.infoRow}>
              <Users size={18} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Shared with {currentBooking.shared_with?.length || 0} roommate(s)
              </Text>
            </View>
          )}
        </View>
        
        <TouchableOpacity 
          style={[
            styles.collapsibleHeader, 
            { 
              backgroundColor: colors.surface,
              borderBottomLeftRadius: showDetails ? 0 : 12,
              borderBottomRightRadius: showDetails ? 0 : 12,
              marginBottom: showDetails ? 0 : 16
            }
          ]}
          onPress={() => setShowDetails(!showDetails)}
        >
          <Text style={[styles.collapsibleTitle, { color: colors.text }]}>
            Booking Details
          </Text>
          <ChevronDown 
            size={20} 
            color={colors.text} 
            style={[
              styles.collapsibleIcon, 
              { transform: [{ rotate: showDetails ? '180deg' : '0deg' }] }
            ]} 
          />
        </TouchableOpacity>
        
        {showDetails && (
          <View style={[styles.detailsSection, { backgroundColor: colors.surface }]}>
            {currentBooking.special_instructions && (
              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: colors.text }]}>
                  Special Instructions:
                </Text>
                <Text style={[styles.detailText, { color: colors.textLight }]}>
                  {currentBooking.special_instructions}
                </Text>
              </View>
            )}
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.text }]}>
                Service Price:
              </Text>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                ${currentBooking.price.toFixed(2)}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.text }]}>
                Payment Status:
              </Text>
              <Text 
                style={[
                  styles.detailText, 
                  { 
                    color: currentBooking.payment_status === 'paid' 
                      ? colors.success 
                      : colors.warning 
                  }
                ]}
              >
                {currentBooking.payment_status}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.text }]}>
                Booking ID:
              </Text>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                {currentBooking.id}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.text }]}>
                Booked On:
              </Text>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                {format(new Date(currentBooking.created_at || Date.now()), 'MMM d, yyyy')}
              </Text>
            </View>
          </View>
        )}
        
        <View style={[styles.actionSection, { backgroundColor: colors.surface }]}>
          <Text style={[styles.actionTitle, { color: colors.text }]}>
            Actions
          </Text>
          
          <View style={styles.actionButtons}>
            {isPending && (
              <>
                <Button
                  title="Cancel Booking"
                  onPress={handleCancel}
                  variant="destructive"
                  icon={<XCircle size={18} color={colors.white} />}
                  style={styles.actionButton}
                />
                
                <Button
                  title="Reschedule"
                  onPress={handleReschedule}
                  variant="outlined"
                  icon={<CalendarClock size={18} color={colors.primary} />}
                  style={styles.actionButton}
                />
              </>
            )}
            
            {isCompleted && !currentBooking.is_reviewed && (
              <Button
                title="Write Review"
                onPress={() => router.push({
                  pathname: '/booking/write-review',
                  params: { bookingId: currentBooking.id }
                })}
                variant="primary"
                icon={<Star size={18} color={colors.white} />}
                style={styles.actionButton}
              />
            )}
            
            <Button
              title="Add to Calendar"
              onPress={handleAddToCalendar}
              variant={isPending ? "outline" : "primary"}
              icon={<Calendar size={18} color={isPending ? colors.primary : colors.white} />}
              style={styles.actionButton}
            />
          </View>
        </View>
        
        <View style={[styles.contactSection, { backgroundColor: colors.surface }]}>
          <Text style={[styles.contactTitle, { color: colors.text }]}>
            Contact Provider
          </Text>
          
          <View style={styles.contactButtons}>
            <TouchableOpacity 
              style={[styles.contactButton, { backgroundColor: colors.primary }]}
              onPress={handleMessageProvider}
            >
              <MessageCircle size={20} color={colors.white} />
              <Text style={styles.contactButtonText}>Message</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.contactButton, { backgroundColor: colors.success }]}
              onPress={handleCallProvider}
            >
              <PhoneCall size={20} color={colors.white} />
              <Text style={styles.contactButtonText}>Call</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.contactButton, { backgroundColor: colors.info }]}
              onPress={handleEmailProvider}
            >
              <Mail size={20} color={colors.white} />
              <Text style={styles.contactButtonText}>Email</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {currentBooking.roommate_shared && (
          <View style={[styles.sharedSection, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sharedTitle, { color: colors.text }]}>
              Shared with Roommates
            </Text>
            
            <View style={styles.sharedDetails}>
              {/* In a real app, would fetch and display actual roommate details */}
              <View style={[styles.sharedItem, { borderBottomColor: colors.border }]}>
                <View style={styles.sharedItemInfo}>
                  <Text style={[styles.sharedItemName, { color: colors.text }]}>
                    You
                  </Text>
                  <Text style={[styles.sharedItemAmount, { color: colors.success }]}>
                    ${(currentBooking.price / (currentBooking.shared_with?.length || 0 + 1)).toFixed(2)}
                  </Text>
                </View>
                <Text style={[styles.sharedItemStatus, { color: colors.success }]}>
                  Paid
                </Text>
              </View>
              
              {currentBooking.shared_with?.map((roommateId, index) => (
                <View 
                  key={roommateId} 
                  style={[
                    styles.sharedItem, 
                    index < (currentBooking.shared_with?.length || 0) - 1 && 
                      { borderBottomColor: colors.border }
                  ]}
                >
                  <View style={styles.sharedItemInfo}>
                    <Text style={[styles.sharedItemName, { color: colors.text }]}>
                      {`Roommate ${index + 1}`}
                    </Text>
                    <Text style={[styles.sharedItemAmount, { color: colors.success }]}>
                      ${(currentBooking.price / (currentBooking.shared_with?.length || 0 + 1)).toFixed(2)}
                    </Text>
                  </View>
                  <Text style={[styles.sharedItemStatus, { color: colors.warning }]}>
                    Pending
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default BookingDetailsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  header: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginBottom: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  serviceName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  providerName: {
    fontSize: 16,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    marginLeft: 10,
  },
  collapsibleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    marginBottom: 0,
  },
  collapsibleTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  collapsibleIcon: {
  },
  detailsSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginHorizontal: 16,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    marginBottom: 16,
  },
  detailItem: {
    marginTop: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 16,
  },
  actionSection: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
    marginBottom: 16,
    minWidth: '40%',
  },
  contactSection: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  contactButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 8,
  },
  contactButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 6,
  },
  sharedSection: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  sharedTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  sharedDetails: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  sharedItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  sharedItemInfo: {
    flex: 1,
  },
  sharedItemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  sharedItemAmount: {
    fontSize: 14,
  },
  sharedItemStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
});