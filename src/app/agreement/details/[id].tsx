import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Alert, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { supabase } from "@utils/supabaseUtils";
import { ArrowLeft, AlertTriangle, FileText, CheckSquare, FilePlus } from 'lucide-react-native';
import { useAuth } from '@context/AuthContext';
import AgreementDetails from '@components/agreement/AgreementDetails';
import AgreementCompliance from '@components/agreement/AgreementCompliance';
import AgreementAmendment from '@components/agreement/AgreementAmendment';
import { AgreementService } from '@services/agreementService';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { useColorFix } from '@hooks/useColorFix';

const Tab = createMaterialTopTabNavigator();

export default function AgreementDetailsScreen() {
  const { fix } = useColorFix();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { authState } = useAuth();
  const user = authState?.user;
  const agreementService = new AgreementService();
  
  const [agreement, setAgreement] = useState<any>(null);
  const [sections, setSections] = useState<any[]>([]);
  const [participants, setParticipants] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreator, setIsCreator] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    if (!id) {
      setError('No agreement ID provided');
      setIsLoading(false);
      return;
    }

    fetchAgreementDetails();
  }, [id, user?.id, refreshTrigger]);

  const fetchAgreementDetails = async () => {
    try {
      setIsLoading(true);
      
      // Fetch agreement details
      const { data: agreementData, error: agreementError } = await supabase
        .from('roommate_agreements')
        .select('*')
        .eq('id', id)
        .single();

      if (agreementError) throw agreementError;
      
      // Fetch agreement sections
      const { data: sectionsData, error: sectionsError } = await supabase
        .from('agreement_sections')
        .select('*')
        .eq('agreement_id', id)
        .order('order_index', { ascending: true });

      if (sectionsError) throw sectionsError;
      
      // Fetch agreement participants
      const { data: participantsData, error: participantsError } = await supabase
        .from('agreement_participants')
        .select(`
          agreement_id,
          user_id,
          role,
          status,
          signed_at,
          signature_data
        `)
        .eq('agreement_id', id);

      if (participantsError) throw participantsError;

      // Fetch user profiles for each participant
      const participantsWithProfiles = [];
      for (const participant of participantsData || []) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name, email, avatar_url')
          .eq('id', participant.user_id)
          .single();

        participantsWithProfiles.push({
          ...participant,
          profiles: profile ? {
            id: profile.id,
            first_name: profile.first_name,
            last_name: profile.last_name,
            email: profile.email,
            avatar_url: profile.avatar_url
          } : null,
          // Also add user_profiles for compatibility with AgreementDetails component
          user_profiles: profile ? {
            id: profile.id,
            first_name: profile.first_name,
            last_name: profile.last_name,
            email: profile.email,
            avatar_url: profile.avatar_url
          } : null
        });
      }

      setAgreement(agreementData);
      setSections(sectionsData || []);
      setParticipants(participantsWithProfiles);
      setIsCreator(agreementData.created_by === user?.id);
    } catch (err) {
      console.error('Error fetching agreement details:', err);
      setError('Failed to load agreement details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePdf = async () => {
    try {
      const pdfData = await agreementService.generateAgreementPDF(id as string);
      
      if (!pdfData) {
        throw new Error('Failed to generate PDF');
      }
      
      // Navigate to PDF preview screen
      router.push({
        pathname: '/agreement/pdf-preview',
        params: { agreementId: id },
      });
    } catch (err) {
      console.error('Error generating PDF:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      Alert.alert('Error', `Failed to generate PDF: ${errorMessage}`);
    }
  };

  const handleEditSection = (sectionId: string) => {
    router.push({
      pathname: '/agreement/customize',
      params: { agreementId: id, editSectionId: sectionId }
    });
  };
  
  const handleRaiseDispute = (sectionId: string, title: string) => {
    router.push({
      pathname: '/agreement/disputes/create',
      params: { 
        agreementId: id, 
        sectionId, 
        title
      }
    });
  };
  
  const handleAmendmentCreated = () => {
    // Refresh the agreement data
    setRefreshTrigger(prev => prev + 1);
    // Show success message
    Alert.alert(
      'Amendment Created',
      'Your amendment has been created and participants have been notified.',
      [{ text: 'OK' }]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer} edges={['top']}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading agreement details...</Text>
      </SafeAreaView>
    );
  }

  if (error || !agreement) {
    return (
      <SafeAreaView style={styles.errorContainer} edges={['top']}>
        <AlertTriangle size={48} color={"#EF4444"} />
        <Text style={styles.errorText}>{error || 'Agreement not found'}</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.push('/agreement/dashboard')} 
          style={styles.errorButton}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen options={{ headerShown: false }} />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()} 
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#1E293B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Agreement Details</Text>
        <View style={{ width: 40 }} />
      </View>

      <View style={styles.content}>
        <Tab.Navigator
          screenOptions={{
            tabBarActiveTintColor: '#6366F1',
            tabBarInactiveTintColor: '#64748B',
            tabBarIndicatorStyle: { backgroundColor: '#6366F1' },
            tabBarLabelStyle: { fontSize: 14, fontWeight: '500', textTransform: 'none' },
            tabBarStyle: { backgroundColor: '#FFFFFF', elevation: 0, shadowOpacity: 0, borderBottomWidth: 1, borderBottomColor: '#E2E8F0' },
          }}
        >
          <Tab.Screen 
            name="Details" 
            options={{ 
              tabBarIcon: ({ color }) => <FileText size={18} color={color} />,
            }}
          >
            {() => (
              <AgreementDetails
                agreement={agreement}
                sections={sections}
                participants={participants}
                isCreator={isCreator}
                onGeneratePdf={handleGeneratePdf}
                onEditSection={handleEditSection}
                onRefresh={() => setRefreshTrigger(prev => prev + 1)}
              />
            )}
          </Tab.Screen>
          
          <Tab.Screen 
            name="Compliance" 
            options={{ 
              tabBarIcon: ({ color }) => <CheckSquare size={18} color={color} />,
            }}
          >
            {() => (
              <AgreementCompliance
                agreementId={id as string}
                onRaiseDispute={handleRaiseDispute}
              />
            )}
          </Tab.Screen>
          
          {isCreator && (
            <Tab.Screen 
              name="Amend" 
              options={{ 
                tabBarIcon: ({ color }) => <FilePlus size={18} color={color} />,
              }}
            >
              {() => (
                <AgreementAmendment
                  agreementId={id as string}
                  currentVersion={agreement.current_version}
                  onAmendmentCreated={handleAmendmentCreated}
                />
              )}
            </Tab.Screen>
          )}
        </Tab.Navigator>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    marginBottom: 20,
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
  },
  errorButton: {
    minWidth: 120,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
  },
  content: {
    flex: 1,
  },
});