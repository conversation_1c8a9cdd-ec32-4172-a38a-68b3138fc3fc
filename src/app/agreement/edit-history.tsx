import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { ArrowLeft, Clock, Users, FileText } from 'lucide-react-native';
import EditHistoryViewer from '@components/agreement/EditHistoryViewer';
import { useAuth } from '@context/AuthContext';
import { supabase } from "@utils/supabaseUtils";

export default function EditHistoryScreen() {
  const { id: agreementId, sectionId } = useLocalSearchParams<{ id: string, sectionId?: string }>();
  const { state, actions } = useAuth();
  const [title, setTitle] = useState('');
  const [sectionTitle, setSectionTitle] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!agreementId) {
      router.back();
      return;
    }

    fetchAgreementDetails();
  }, [agreementId, sectionId]);

  const fetchAgreementDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch agreement details
      const { data: agreement, error: agreementError } = await supabase
        .from('roommate_agreements')
        .select('title')
        .eq('id', agreementId)
        .single();

      if (agreementError) throw agreementError;
      
      setTitle(agreement?.title || 'Agreement');

      // If section ID provided, fetch section title
      if (sectionId) {
        const { data: section, error: sectionError } = await supabase
          .from('agreement_sections')
          .select('section_title')
          .eq('id', sectionId)
          .single();

        if (sectionError) throw sectionError;
        
        setSectionTitle(section?.section_title || 'Section');
      }
    } catch (err) {
      console.error('Error fetching details:', err);
      setError('Failed to load agreement details');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleViewAgreement = () => {
              router.push(`/agreement/details/${agreementId}`);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <Stack.Screen
        options={{
          headerTitle: sectionId ? 'Section Edit History' : 'Agreement Edit History',
          headerLeft: () => (
            <TouchableOpacity onPress={handleGoBack} style={styles.headerButton}>
              <ArrowLeft size={24} color="#1E293B" />
            </TouchableOpacity>
          ),
        }}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366F1" />
          <Text style={styles.loadingText}>Loading history...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button title="Go Back" onPress={handleGoBack} />
        </View>
      ) : (
        <View style={styles.content}>
          <View style={styles.headerContainer}>
            <View style={styles.titleContainer}>
              <Clock size={20} color="#6366F1" style={styles.titleIcon} />
              <Text style={styles.mainTitle}>
                {sectionId ? 'Edit History for Section' : 'Edit History'}
              </Text>
            </View>
            
            <Text style={styles.agreementTitle}>{title}</Text>
            
            {sectionId && (
              <Text style={styles.sectionTitle}>Section: {sectionTitle}</Text>
            )}
            
            <View style={styles.infoBox}>
              <FileText size={16} color="#64748B" />
              <Text style={styles.infoText}>
                This shows all edits made to the {sectionId ? 'section' : 'agreement'}, 
                including who made them and when.
              </Text>
            </View>
          </View>

          <EditHistoryViewer
            agreementId={agreementId as string}
            sectionId={sectionId}
          />

          <View style={styles.footer}>
            <Button
              title="View Agreement"
              onPress={handleViewAgreement}
              variant="secondary"
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748B',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 16,
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  headerContainer: {
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleIcon: {
    marginRight: 8,
  },
  mainTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1E293B',
  },
  agreementTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#334155',
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 12,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#F1F5F9',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#334155',
  },
  footer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  }
});