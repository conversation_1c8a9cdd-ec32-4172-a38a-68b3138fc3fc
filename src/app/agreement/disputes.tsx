import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Modal } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Calendar, AlertTriangle, MessageCircle, ChevronRight, Plus } from 'lucide-react-native';
import { useDisputes } from '@hooks/useDisputes';
import { Dispute, DisputeStatus } from '@utils/agreement';
import { format } from 'date-fns';
import { colors } from '@constants/colors';
import { Input } from '@components/ui';
import { Button } from '@design-system';

export default function DisputesScreen() {
  const { agreementId } = useLocalSearchParams();
  const router = useRouter();
  const { isLoading, disputes, getDisputesByAgreement, createDispute } = useDisputes();
  
  const [isCreatingDispute, setIsCreatingDispute] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (agreementId) {
      getDisputesByAgreement(String(agreementId));
    }
  }, [agreementId, getDisputesByAgreement]);

  const getStatusColor = (status: DisputeStatus) => {
    switch (status) {
      case 'open':
        return colors.warning;
      case 'in_progress':
        return colors.info;
      case 'resolved':
        return colors.success;
      case 'closed':
        return colors.dark;
      case 'escalated':
        return colors.danger;
      default:
        return colors.gray;
    }
  };

  const getStatusLabel = (status: DisputeStatus) => {
    switch (status) {
      case 'open':
        return 'Open';
      case 'in_progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      case 'escalated':
        return 'Escalated';
      default:
        return 'Unknown';
    }
  };

  const handleCreateDispute = async () => {
    if (!title.trim() || !description.trim() || !agreementId) return;
    
    setIsSubmitting(true);
    try {
      const newDispute = await createDispute(
        String(agreementId),
        title,
        description
      );
      
      if (newDispute) {
        setIsCreatingDispute(false);
        setTitle('');
        setDescription('');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const navigateToDisputeDetails = (disputeId: string) => {
    router.push(`/agreement/dispute-details?disputeId=${disputeId}`);
  };

  const renderDisputeItem = ({ item }: { item: Dispute }) => (
    <TouchableOpacity
      style={styles.disputeItem}
      onPress={() => navigateToDisputeDetails(item.id)}
    >
      <View style={styles.disputeHeader}>
        <View style={styles.titleContainer}>
          <AlertTriangle size={16} color={getStatusColor(item.status)} />
          <Text style={styles.disputeTitle}>{item.title}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusLabel(item.status)}</Text>
        </View>
      </View>
      
      <Text style={styles.disputeDescription} numberOfLines={2}>
        {item.description}
      </Text>
      
      <View style={styles.disputeFooter}>
        <View style={styles.disputeInfo}>
          <Text style={styles.infoText}>Raised by: {item.raised_by_name || 'Unknown'}</Text>
          <View style={styles.dateContainer}>
            <Calendar size={12} color={colors.gray} />
            <Text style={styles.dateText}>
              {format(new Date(item.created_at), 'MMM d, yyyy')}
            </Text>
          </View>
        </View>
        <ChevronRight size={16} color={colors.gray} />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MessageCircle size={48} color={colors.primary} />
      <Text style={styles.emptyTitle}>No Disputes Yet</Text>
      <Text style={styles.emptyDescription}>
        Disputes help resolve disagreements about terms in your agreement.
      </Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => setIsCreatingDispute(true)}
      >
        <Text style={styles.createButtonText}>Create Dispute</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Disputes</Text>
        {disputes.length > 0 && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setIsCreatingDispute(true)}
          >
            <Plus size={20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={disputes}
          renderItem={renderDisputeItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}

      <Modal
        visible={isCreatingDispute}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsCreatingDispute(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create Dispute</Text>
              <TouchableOpacity onPress={() => setIsCreatingDispute(false)}>
                <Text style={styles.modalClose}>Close</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.formContainer}>
              <Text style={styles.label}>Title</Text>
              <Input
                placeholder="Enter dispute title"
                value={title}
                onChangeText={setTitle}
              />

              <Text style={styles.label}>Description</Text>
              <Input
                placeholder="Describe the issue in detail"
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                style={styles.textarea}
              />

              <TouchableOpacity
                style={[
                  styles.submitButton,
                  (!title.trim() || !description.trim() || isSubmitting) &&
                    styles.disabledButton,
                ]}
                onPress={handleCreateDispute}
                disabled={!title.trim() || !description.trim() || isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.submitButtonText}>Create Dispute</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
    paddingBottom: 24,
  },
  disputeItem: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  disputeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  disputeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  disputeDescription: {
    fontSize: 14,
    color: colors.darkGray,
    marginBottom: 12,
  },
  disputeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  disputeInfo: {
    flex: 1,
  },
  infoText: {
    fontSize: 13,
    color: colors.gray,
    marginBottom: 4,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: colors.gray,
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: colors.darkGray,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  formContainer: {
    padding: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
    marginTop: 16,
  },
  textarea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  disabledButton: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  modalClose: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.primary,
  },
  addButton: {
    padding: 8,
  },
}); 