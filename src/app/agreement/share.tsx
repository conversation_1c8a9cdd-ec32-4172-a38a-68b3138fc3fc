import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { supabase } from "@utils/supabaseUtils";
import { ArrowLeft, Copy, CheckCircle, Users, Mail, Share2, FileSignature } from 'lucide-react-native';
import { TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import { useAuth } from '@context/AuthContext';
import * as Clipboard from 'expo-clipboard';
import * as Sharing from 'expo-sharing';
import { useAgreements } from '@hooks/useAgreements';
import { useColorFix } from '@hooks/useColorFix';
import { showToast } from '@utils/toast';

export default function ShareAgreementScreen() {
  const { fix } = useColorFix();
  const { agreementId } = useLocalSearchParams<{ agreementId: string }>();
  const { authState } = useAuth();
  const user = authState?.user;
  const { generatePDF } = useAgreements();
  
  const [agreement, setAgreement] = useState<any>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [participants, setParticipants] = useState<any[]>([]);
  const [shareLink, setShareLink] = useState('');
  const [copied, setCopied] = useState(false);
  const [generatingPdf, setGeneratingPdf] = useState(false);

  useEffect(() => {
    if (!agreementId) return;
    
    fetchAgreementDetails();
    generateShareLink();
  }, [agreementId]);

  const fetchAgreementDetails = async () => {
    try {
      setIsLoading(true);
      
      // Fetch agreement details
      const { data: agreementData, error: agreementError } = await supabase
        .from('roommate_agreements')
        .select('*')
        .eq('id', agreementId)
        .single();

      if (agreementError) throw agreementError;
      
      // Fetch participants
      const { data: participantsData, error: participantsError } = await supabase
        .from('agreement_participants')
        .select(`
          *,
          user_profiles (
            id,
            first_name,
            last_name,
            email,
            avatar_url
          )
        `)
        .eq('agreement_id', agreementId);

      if (participantsError) throw participantsError;

      setAgreement(agreementData);
      setParticipants(participantsData);
    } catch (err) {
      console.error('Error fetching agreement details:', err);
      Alert.alert('Error', 'Failed to load agreement details.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateShareLink = () => {
    // In a real app, you might use a deep linking solution
    const baseUrl = 'https://roomie-match.com/agreement';
    setShareLink(`${baseUrl}/${agreementId}`);
  };

  const handleCopyLink = async () => {
    try {
      await Clipboard.setStringAsync(shareLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    } catch (err) {
      console.error('Error copying to clipboard:', err);
    }
  };

  const handleShareLink = async () => {
    try {
      if (!(await Sharing.isAvailableAsync())) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return;
      }

      await Sharing.shareAsync(shareLink);
    } catch (err) {
      console.error('Error sharing:', err);
      Alert.alert('Error', 'Failed to share the agreement.');
    }
  };

  const handleSendInvite = async () => {
    if (!inviteEmail.trim() || !validateEmail(inviteEmail)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    try {
      setIsSending(true);
      
      // Check if user exists with this email
      const { data: userData, error: userError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', inviteEmail.trim())
        .single();

      if (userError && userError.code !== 'PGRST116') {
        throw userError;
      }

      let userId = userData?.id;

      if (!userId) {
        // In a real app, you would likely send an email invitation
        // For now, we'll just show an alert
        Alert.alert(
          'User Not Found',
          'No user found with this email. In a real app, we would send an invitation email to join the platform.'
        );
        return;
      }

      // Check if user is already a participant
      const isParticipant = participants.some(p => p.user_profiles?.id === userId);
      
      if (isParticipant) {
        Alert.alert('Already Invited', 'This user is already a participant in the agreement.');
        return;
      }

      // Add participant
      const { error: participantError } = await supabase
        .from('agreement_participants')
        .insert({
          agreement_id: agreementId,
          user_id: userId,
          role: 'roommate',
          status: 'invited'
        });

      if (participantError) throw participantError;

      // Refresh participants list
      fetchAgreementDetails();
      
      // Clear input
      setInviteEmail('');
      
      Alert.alert('Success', 'Invitation sent successfully!');
    } catch (err) {
      console.error('Error sending invite:', err);
      Alert.alert('Error', 'Failed to send invitation. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const handleDownloadPdf = async () => {
    try {
      setGeneratingPdf(true);
      showToast('Generating PDF preview...', 'info');
      
      const pdfData = await generatePDF(agreementId as string);
      
      if (!pdfData) {
        throw new Error('Failed to generate PDF');
      }
      
      showToast('PDF generated successfully!', 'success');
      
      // Navigate to the PDF preview screen
      router.push({
        pathname: '/agreement/pdf-preview',
        params: { agreementId: agreementId },
      });
      
    } catch (err) {
      console.error('Error generating PDF:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      showToast(`PDF generation failed: ${errorMessage}`, 'error');
      Alert.alert('Error', `Failed to generate PDF: ${errorMessage}`);
    } finally {
      setGeneratingPdf(false);
    }
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const getParticipantStatusLabel = (status: string) => {
    switch (status) {
      case 'invited': return 'Invited';
      case 'reviewing': return 'Reviewing';
      case 'approved': return 'Approved';
      case 'signed': return 'Signed';
      case 'declined': return 'Declined';
      default: return status;
    }
  };

  const getParticipantStatusColor = (status: string) => {
    switch (status) {
      case 'invited': return '#F59E0B';
      case 'reviewing': return '#3B82F6';
      case 'approved': return '#10B981';
      case 'signed': return '#10B981';
      case 'declined': return '#EF4444';
      default: return '#64748B';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer} edges={['top']}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading agreement details...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen options={{ headerShown: false }} />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()} 
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#1E293B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Share Agreement</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Agreement Link</Text>
          <Text style={styles.sectionDescription}>
            Share this link with your roommates to invite them to view and sign the agreement.
          </Text>
          
          <View style={styles.linkContainer}>
            <Text style={styles.link} numberOfLines={1}>{shareLink}</Text>
            <TouchableOpacity style={styles.copyButton} onPress={handleCopyLink}>
              {copied ? (
                <CheckCircle size={20} color={"#10B981"} />
              ) : (
                <Copy size={20} color="#6366F1" />
              )}
            </TouchableOpacity>
          </View>
          
          <Button 
            title="Share Link" 
            onPress={handleShareLink}
            icon={<Share2 size={18} color="#FFFFFF" />}
            style={styles.shareButton}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Invite by Email</Text>
          <Text style={styles.sectionDescription}>
            Invite a roommate directly by email to view and sign the agreement.
          </Text>
          
          <View style={styles.emailInputContainer}>
            <TextInput 
              style={styles.emailInput}
              placeholder="Enter email address"
              value={inviteEmail}
              onChangeText={setInviteEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <Button
              title="Send"
              onPress={handleSendInvite}
              style={styles.sendButton}
              loading={isSending}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Participants</Text>
          <Text style={styles.sectionDescription}>
            People who can view and sign this agreement.
          </Text>
          
          <View style={styles.participantsList}>
            {participants.map((participant) => (
              <View key={participant.user_id} style={styles.participantItem}>
                <View style={styles.participantAvatar}>
                  <Users size={24} color="#6366F1" />
                </View>
                <View style={styles.participantInfo}>
                  <Text style={styles.participantName}>
                    {participant.user_profiles ? 
                      `${participant.user_profiles.first_name} ${participant.user_profiles.last_name}`.trim() : 
                      'Unknown User'}
                    {participant.user_id === user?.id && ' (You)'}
                  </Text>
                  <Text style={styles.participantEmail}>
                    {participant.user_profiles?.email || 'No email'}
                  </Text>
                </View>
                <View 
                  style={[
                    styles.statusBadge, 
                    { backgroundColor: getParticipantStatusColor(participant.status) + '20' }
                  ]}
                >
                  <Text 
                    style={[
                      styles.statusText, 
                      { color: getParticipantStatusColor(participant.status) }
                    ]}
                  >
                    {getParticipantStatusLabel(participant.status)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Signature Management</Text>
          <Text style={styles.sectionDescription}>
            Track approval status and collect digital signatures from all participants.
          </Text>
          
          <Button 
            title="Manage Signatures" 
            onPress={() => router.push({
              pathname: '/agreement/signature-flow',
              params: { agreementId }
            })}
            icon={<FileSignature size={18} color="#FFFFFF" />}
            style={styles.signatureButton}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Download Agreement</Text>
          <Text style={styles.sectionDescription}>
            Download a PDF copy of the agreement for your records.
          </Text>
          
          <Button 
            title="Download PDF" 
            onPress={handleDownloadPdf}
            variant="outlined"
            icon={<Mail size={18} color="#6366F1" />}
            style={styles.downloadButton}
            loading={generatingPdf}
          />
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>What happens next?</Text>
          <Text style={styles.infoText}>
            • Participants will receive a notification to review the agreement{'\n'}
            • Once all participants sign, the agreement becomes active{'\n'}
            • You'll be notified when participants sign or decline{'\n'}
            • You can track the status of all signatures here
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 16,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  link: {
    flex: 1,
    fontSize: 14,
    color: '#334155',
    marginRight: 8,
  },
  copyButton: {
    padding: 4,
  },
  shareButton: {
    backgroundColor: '#6366F1',
  },
  emailInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  emailInput: {
    flex: 1,
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#334155',
    marginRight: 8,
  },
  sendButton: {
    minWidth: 80,
  },
  participantsList: {
    marginTop: 8,
  },
  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  participantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
    marginBottom: 2,
  },
  participantEmail: {
    fontSize: 14,
    color: '#64748B',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  signatureButton: {
    backgroundColor: '#059669',
    marginBottom: 8,
  },
  downloadButton: {
    borderColor: '#6366F1',
  },
  infoBox: {
    backgroundColor: '#EEF2FF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4F46E5',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#334155',
    lineHeight: 20,
  },
});