import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import {
  ArrowLeft,
  BadgeCheck,
  Shield,
  CircleCheck,
  Clock,
  UserCheck,
  Share2,
  Award,
  Sparkles,
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useAuth } from '@context/AuthContext';
import { verificationService } from '@services';
import { backgroundCheckService } from '@services';
import { profileCompletionService } from '@services/profileCompletionService';

export default function TrustScoreScreen() {
  const insets = useSafeAreaInsets();
  const { state, actions } = useAuth();

  const [loading, setLoading] = useState(true);
  const [trustData, setTrustData] = useState({
    idVerification: {
      isVerified: false,
      score: 0,
      maxScore: 40,
      pendingRequest: false,
    },
    backgroundCheck: {
      isVerified: false,
      score: 0,
      maxScore: 40,
      pendingRequest: false,
    },
    profileCompletion: {
      percentage: 0,
      score: 0,
      maxScore: 20,
    },
    totalScore: 0,
  });

  useEffect(() => {
    fetchTrustData();
  }, []);

  const fetchTrustData = async () => {
    try {
      setLoading(true);

      // Fetch verification status
      const verificationResponse = await verificationService.getVerificationStatus();
      const verificationData = verificationResponse?.data || { is_verified: false, pending_request: null };

      // Fetch background check status
      const backgroundCheckResponse = await backgroundCheckService.getBackgroundCheckStatus();
      const backgroundCheckData = backgroundCheckResponse?.data || { has_background_check: false, latest_check: null };

      // Fetch profile completion
      let profileCompletionValue = 0;
      if (authState.user) {
        const profileCompletionResponse = await profileCompletionService.calculateProfileCompletion(authState.user.id);
        // Extract the number value from the ApiResponse
        profileCompletionValue = profileCompletionResponse?.data || 0;
      }

      // Calculate scores
      const idVerificationScore = verificationData.is_verified ? 40 : 0;
      const backgroundCheckScore = backgroundCheckData.has_background_check ? 40 : 0;
      const profileCompletionScore = Math.round((profileCompletionValue / 100) * 20);
      const totalScore = idVerificationScore + backgroundCheckScore + profileCompletionScore;

      setTrustData({
        idVerification: {
          isVerified: verificationData.is_verified || false,
          score: idVerificationScore,
          maxScore: 40,
          pendingRequest: !!verificationData.pending_request,
        },
        backgroundCheck: {
          isVerified: backgroundCheckData.has_background_check || false,
          score: backgroundCheckScore,
          maxScore: 40,
          pendingRequest: !!(
            backgroundCheckData.latest_check &&
            (backgroundCheckData.latest_check.status === 'pending' ||
              backgroundCheckData.latest_check.status === 'in_progress')
          ),
        },
        profileCompletion: {
          percentage: profileCompletionValue,
          score: profileCompletionScore,
          maxScore: 20,
        },
        totalScore,
      });
    } catch (error) {
      console.error('Error fetching trust data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTrustLevel = (score: number): { level: string; color: string } => {
    if (score >= 80) return { level: 'Excellent', color: '#10b981' };
    if (score >= 60) return { level: 'Good', color: '#6366f1' };
    if (score >= 40) return { level: 'Fair', color: '#f59e0b' };
    return { level: 'Basic', color: '#64748b' };
  };

  const renderScoreGauge = (score: number, maxScore: number, color: string) => {
    const percentage = (score / 100) * 100;

    return (
      <View style={styles.scoreGaugeContainer}>
        <View style={styles.scoreGauge}>
          <View
            style={[styles.scoreGaugeFill, { width: `${percentage}%`, backgroundColor: color }]}
          />
        </View>
        <Text style={styles.scoreText}>{score}/100</Text>
      </View>
    );
  };

  const renderTrustLevel = () => {
    const { level, color } = getTrustLevel(trustData.totalScore);

    return (
      <View style={styles.trustLevelCard}>
        <View style={styles.trustLevelHeader}>
          <Award size={28} color={color} />
          <View style={styles.trustLevelTextContainer}>
            <Text style={styles.trustLevelLabel}>Trust Level</Text>
            <Text style={[styles.trustLevelValue, { color }]}>{level}</Text>
          </View>
        </View>

        {renderScoreGauge(trustData.totalScore, 100, color)}

        <Text style={styles.trustLevelDescription}>{getTrustLevelDescription(level)}</Text>
      </View>
    );
  };

  const getTrustLevelDescription = (level: string): string => {
    switch (level) {
      case 'Excellent':
        return 'You have achieved the highest level of trust. Roommates will have full confidence in your profile.';
      case 'Good':
        return 'You have built a solid level of trust. Most roommates will feel comfortable connecting with you.';
      case 'Fair':
        return 'You have a moderate trust level. Continue verifying your profile to build more trust.';
      default:
        return 'You are just getting started. Complete verifications to build trust with potential roommates.';
    }
  };

  const renderTrustFactors = () => {
    return (
      <View style={styles.trustFactorsCard}>
        <Text style={styles.trustFactorsTitle}>Trust Factors</Text>

        <View style={styles.trustFactor}>
          <View style={styles.trustFactorHeader}>
            <View style={styles.trustFactorIcon}>
              <BadgeCheck
                size={20}
                color={trustData.idVerification.isVerified ? '#10b981' : '#64748b'}
              />
            </View>
            <View style={styles.trustFactorInfo}>
              <Text style={styles.trustFactorName}>ID Verification</Text>
              <Text style={styles.trustFactorStatus}>
                {trustData.idVerification.isVerified
                  ? 'Verified'
                  : trustData.idVerification.pendingRequest
                    ? 'Pending'
                    : 'Not verified'}
              </Text>
            </View>
            <Text style={styles.trustFactorScore}>
              {trustData.idVerification.score}/{trustData.idVerification.maxScore}
            </Text>
          </View>
          <View style={styles.trustFactorProgress}>
            <View
              style={[
                styles.trustFactorProgressFill,
                {
                  width: `${(trustData.idVerification.score / trustData.idVerification.maxScore) * 100}%`,
                  backgroundColor: trustData.idVerification.isVerified ? '#10b981' : '#64748b',
                },
              ]}
            />
          </View>
          {!trustData.idVerification.isVerified && !trustData.idVerification.pendingRequest && (
            <TouchableOpacity
              style={styles.trustFactorAction}
              onPress={() => router.push('/verification')}
            >
              <Text style={styles.trustFactorActionText}>Verify Now</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.trustFactor}>
          <View style={styles.trustFactorHeader}>
            <View style={styles.trustFactorIcon}>
              <Shield
                size={20}
                color={trustData.backgroundCheck.isVerified ? '#10b981' : '#64748b'}
              />
            </View>
            <View style={styles.trustFactorInfo}>
              <Text style={styles.trustFactorName}>Background Check</Text>
              <Text style={styles.trustFactorStatus}>
                {trustData.backgroundCheck.isVerified
                  ? 'Verified'
                  : trustData.backgroundCheck.pendingRequest
                    ? 'Pending'
                    : 'Not verified'}
              </Text>
            </View>
            <Text style={styles.trustFactorScore}>
              {trustData.backgroundCheck.score}/{trustData.backgroundCheck.maxScore}
            </Text>
          </View>
          <View style={styles.trustFactorProgress}>
            <View
              style={[
                styles.trustFactorProgressFill,
                {
                  width: `${(trustData.backgroundCheck.score / trustData.backgroundCheck.maxScore) * 100}%`,
                  backgroundColor: trustData.backgroundCheck.isVerified ? '#10b981' : '#64748b',
                },
              ]}
            />
          </View>
          {!trustData.backgroundCheck.isVerified && !trustData.backgroundCheck.pendingRequest && (
            <TouchableOpacity
              style={styles.trustFactorAction}
              onPress={() => router.push('/verification/background-check')}
            >
              <Text style={styles.trustFactorActionText}>Get Background Check</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.trustFactor}>
          <View style={styles.trustFactorHeader}>
            <View style={styles.trustFactorIcon}>
              <UserCheck
                size={20}
                color={trustData.profileCompletion.percentage >= 80 ? '#10b981' : '#f59e0b'}
              />
            </View>
            <View style={styles.trustFactorInfo}>
              <Text style={styles.trustFactorName}>Profile Completion</Text>
              <Text style={styles.trustFactorStatus}>
                {trustData.profileCompletion.percentage}% Complete
              </Text>
            </View>
            <Text style={styles.trustFactorScore}>
              {trustData.profileCompletion.score}/{trustData.profileCompletion.maxScore}
            </Text>
          </View>
          <View style={styles.trustFactorProgress}>
            <View
              style={[
                styles.trustFactorProgressFill,
                {
                  width: `${(trustData.profileCompletion.score / trustData.profileCompletion.maxScore) * 100}%`,
                  backgroundColor:
                    trustData.profileCompletion.percentage >= 80 ? '#10b981' : '#f59e0b',
                },
              ]}
            />
          </View>
          {trustData.profileCompletion.percentage < 100 && (
            <TouchableOpacity
              style={styles.trustFactorAction}
              onPress={() => router.push('/profile/edit')}
            >
              <Text style={styles.trustFactorActionText}>Complete Profile</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderTrustBenefits = () => {
    return (
      <View style={styles.benefitsCard}>
        <Text style={styles.benefitsTitle}>Trust Score Benefits</Text>

        <View style={styles.benefitItem}>
          <Sparkles size={20} color="#6366f1" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>3x More Responses</Text>
            <Text style={styles.benefitDescription}>
              Verified users receive up to 3x more responses from potential roommates
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <CircleCheck size={20} color="#6366f1" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Priority in Search Results</Text>
            <Text style={styles.benefitDescription}>
              Higher trust scores place your profile higher in search results
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <Shield size={20} color="#6366f1" />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Enhanced Trust</Text>
            <Text style={styles.benefitDescription}>
              Build credibility with potential roommates through verified components
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#1e293b" />
        </TouchableOpacity>
        <Text style={styles.title}>Trust Score</Text>
        <View style={styles.placeholder} />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366F1" />
          <Text style={styles.loadingText}>Loading trust information...</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {renderTrustLevel()}
          {renderTrustFactors()}
          {renderTrustBenefits()}

          <TouchableOpacity style={styles.shareButton} onPress={() => router.push('/verification')}>
            <Share2 size={20} color="#ffffff" />
            <Text style={styles.shareButtonText}>Back to Verification Center</Text>
          </TouchableOpacity>

          <View style={{ height: 40 }} />
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#ffffff',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  scrollContent: {
    flex: 1,
    padding: 16,
  },
  trustLevelCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  trustLevelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  trustLevelTextContainer: {
    marginLeft: 12,
  },
  trustLevelLabel: {
    fontSize: 14,
    color: '#64748b',
  },
  trustLevelValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  scoreGaugeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreGauge: {
    flex: 1,
    height: 12,
    backgroundColor: '#e2e8f0',
    borderRadius: 6,
    overflow: 'hidden',
    marginRight: 12,
  },
  scoreGaugeFill: {
    height: 12,
    borderRadius: 6,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    width: 60,
    textAlign: 'right',
  },
  trustLevelDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  trustFactorsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  trustFactorsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  trustFactor: {
    marginBottom: 20,
  },
  trustFactorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  trustFactorIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
  },
  trustFactorInfo: {
    flex: 1,
    marginLeft: 12,
  },
  trustFactorName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
  },
  trustFactorStatus: {
    fontSize: 14,
    color: '#64748b',
  },
  trustFactorScore: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  trustFactorProgress: {
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  trustFactorProgressFill: {
    height: 6,
    borderRadius: 3,
  },
  trustFactorAction: {
    alignSelf: 'flex-start',
    backgroundColor: '#eef2ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 8,
  },
  trustFactorActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366f1',
  },
  benefitsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  benefitContent: {
    flex: 1,
    marginLeft: 12,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  shareButton: {
    backgroundColor: '#6366f1',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  shareButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 8,
  },
});
