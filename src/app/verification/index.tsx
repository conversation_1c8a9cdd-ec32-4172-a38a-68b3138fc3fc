import React, { useState, useEffect } from 'react';

import * as ImagePicker from 'expo-image-picker';
import { router, useRouter, Stack } from 'expo-router';
import {
  ChevronLeft,
  Shield,
  Upload,
  Camera,
  Check,
  Clock,
  AlertCircle,
  ChevronRight,
  Share2,
  ArrowLeft,
  BadgeCheck,
  UserCheck,
  Lock,
  CreditCard,
} from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SafeAreaView } from 'react-native-safe-area-context';

import { colors } from '@constants/colors';
import { useAuth } from '@context/AuthContext';
import { backgroundCheckService, verificationService } from '@services';
import { referenceCheckService } from '@services/referenceCheckService';
import type { VerificationRequest } from '../../types/auth';
import { dbHealthCheck } from '@utils/databaseUtils';
import { VerificationBadge } from '@components/verification/VerificationBadge';
import { useColorFix } from '@hooks/useColorFix';

export default function VerificationScreen() {
  const { fix } = useColorFix();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { state, actions } = useAuth();

  // Define consistent color palette
  const primaryLight = '#93C5FD';
  const primaryColor = '#1E40AF';
  const successLight = '#ECFDF5'; 
  const successColor = '#10B981';
  const warningLight = '#FCD34D';
  const warningColor = '#92400E';
  const neutralLight = '#F8FAFC';
  const neutralColor = '#64748B';

  const [loading, setLoading] = useState(true);
  const [documentImage, setDocumentImage] = useState<string | null>(null);
  const [selfieImage, setSelfieImage] = useState<string | null>(null);
  const [documentType, setDocumentType] = useState<string>('passport');
  const [verificationStatus, setVerificationStatus] = useState<{
    is_verified: boolean;
    pending_request: VerificationRequest | null;
  } | null>(null);
  const [backgroundCheckStatus, setBackgroundCheckStatus] = useState<{
    has_background_check: boolean;
    latest_check: any | null;
  } | null>(null);
  const [verificationBadges, setVerificationBadges] = useState<{
    identity_verified: boolean;
    background_check_verified: boolean;
    references_verified: boolean;
    trust_score: number;
    verification_level: string;
  } | null>(null);

  useEffect(() => {
    const initScreen = async () => {
      try {
        // Check if verification tables exist
        const verificationTableExists =
          await dbHealthCheck.checkTableExists('verification_requests');
        const backgroundCheckTableExists =
          await dbHealthCheck.checkTableExists('background_checks');

        if (verificationTableExists) {
          fetchVerificationStatus();
        } else {
          setVerificationStatus({
            is_verified: false,
            pending_request: null,
          });
          setLoading(false);
        }

        if (backgroundCheckTableExists) {
          fetchBackgroundCheckStatus();
        } else {
          setBackgroundCheckStatus({
            has_background_check: false,
            latest_check: null,
          });
        }

        // Fetch verification badges (includes reference checks)
        fetchVerificationBadges();
      } catch (error) {
        console.error('Error initializing verification screen:', error);
        setLoading(false);
      }
    };

    initScreen();
  }, []);

  const fetchVerificationStatus = async () => {
    try {
      setLoading(true);
      const { data, error } = await verificationService.getVerificationStatus();
      if (error) {
        Alert.alert('Error', error);
        return;
      }
      setVerificationStatus(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch verification status');
      console.error('Error fetching verification status:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBackgroundCheckStatus = async () => {
    try {
      const response = await backgroundCheckService.getBackgroundCheckStatus();

      // Properly extract data and error from the API response
      const { data, error } = response;

      if (error) {
        console.error('Error fetching background check status:', error);
        return;
      }

      // Extract the actual data from the API response data property
      const backgroundCheckData = {
        has_background_check: data?.has_background_check || false,
        latest_check: data?.latest_check || null,
      };

      setBackgroundCheckStatus(backgroundCheckData);
    } catch (error) {
      console.error('Error fetching background check status:', error);
    }
  };

  const fetchVerificationBadges = async () => {
    try {
      if (!state.user?.id) return;

      const badges = await verificationService.getVerificationBadges(state.user.id);
      setVerificationBadges(badges);
    } catch (error) {
      console.error('Error fetching verification badges:', error);
    }
  };

  const pickDocument = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'], // ✅ Modern API - no deprecated enum
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      exif: false,
    });

    if (!result.canceled) {
      setDocumentImage(result.assets[0].uri);
    }
  };

  const takeSelfie = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission denied', 'We need camera permission to take a selfie');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setSelfieImage(result.assets[0].uri);
    }
  };

  const handleSubmit = async () => {
    if (!documentImage || !selfieImage) {
      Alert.alert('Missing information', 'Please provide both document and selfie images');
      return;
    }

    try {
      setLoading(true);

      // Upload document image
      const documentUrl = await verificationService.uploadDocument(documentImage, 'image/jpeg');

      // Upload selfie image
      const selfieUrl = await verificationService.uploadDocument(selfieImage, 'image/jpeg');

      // Submit verification request
      const { error } = await verificationService.submitVerificationRequest(
        documentType,
        documentUrl,
        selfieUrl
      );

      if (error) {
        Alert.alert('Error', error);
        return;
      }

      Alert.alert(
        'Verification Submitted',
        'Your verification request has been submitted and is pending review.',
        [{ text: 'OK', onPress: () => fetchVerificationStatus() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit verification request');
      console.error('Submit verification error:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderVerificationStatus = () => {
    if (!verificationStatus) {
      return null;
    }

    if (verificationStatus.is_verified) {
      return (
        <View style={styles.verificationCard}>
          <View style={styles.verificationCardHeader}>
            <BadgeCheck size={24} color="#10b981" />
            <Text style={styles.verificationCardTitle}>Identity Verified</Text>
          </View>
          <Text style={styles.verificationCardDescription}>
            Your identity has been verified. You now have access to all features and higher trust
            from potential roommates.
          </Text>
          <View style={styles.verificationBenefits}>
            <View style={styles.benefitItem}>
              <UserCheck size={16} color="#10b981" />
              <Text style={styles.benefitText}>Trusted by roommates</Text>
            </View>
            <View style={styles.benefitItem}>
              <Lock size={16} color="#10b981" />
              <Text style={styles.benefitText}>Enhanced security</Text>
            </View>
            <View style={styles.benefitItem}>
              <CreditCard size={16} color="#10b981" />
              <Text style={styles.benefitText}>Unlock premium features</Text>
            </View>
          </View>
        </View>
      );
    }

    if (verificationStatus.pending_request) {
      const status = verificationStatus.pending_request.status;

      return (
        <View style={styles.verificationCard}>
          <View style={styles.verificationCardHeader}>
            <Clock size={24} color="#f59e0b" />
            <Text style={styles.verificationCardTitle}>
              Verification {status === 'pending' ? 'Pending' : 'In Review'}
            </Text>
          </View>
          <Text style={styles.verificationCardDescription}>
            Your verification request has been submitted and is{' '}
            {status === 'pending' ? 'pending review' : 'being reviewed'}. We'll notify you once it's
            complete.
          </Text>
          <Text style={styles.submittedDate}>
            Submitted:{' '}
            {new Date(verificationStatus.pending_request.submitted_at).toLocaleDateString()}
          </Text>
          <View style={styles.inProgressSteps}>
            <View style={[styles.step, styles.stepCompleted]}>
              <View style={styles.stepIndicator}>
                <Check size={16} color={successColor} />
              </View>
              <Text style={styles.stepText}>Documents Submitted</Text>
            </View>
            <View
              style={[
                styles.step,
                status === 'in_review' ? styles.stepCompleted : styles.stepPending,
              ]}
            >
              <View style={styles.stepIndicator}>
                {status === 'in_review' ? (
                  <Check size={16} color={successColor} />
                ) : (
                  <Text style={styles.stepNumber}>2</Text>
                )}
              </View>
              <Text style={styles.stepText}>Under Review</Text>
            </View>
            <View style={[styles.step, styles.stepPending]}>
              <View style={styles.stepIndicator}>
                <Text style={styles.stepNumber}>3</Text>
              </View>
              <Text style={styles.stepText}>Verification Complete</Text>
            </View>
          </View>
        </View>
      );
    }

    return null;
  };

  const renderBackgroundCheckSection = () => {
    let statusIcon = <Shield size={20} color="#6B7280" />;
    let statusText = 'Not verified';
    let statusColor = '#6B7280';

    if (backgroundCheckStatus) {
      if (backgroundCheckStatus.has_background_check) {
        statusIcon = <Check size={20} color={fix("#10B981", "#10B981")} />;
        statusText = 'Verified';
        statusColor = '#10B981';
      } else if (backgroundCheckStatus.latest_check) {
        if (
          backgroundCheckStatus.latest_check.status === 'pending' ||
          backgroundCheckStatus.latest_check.status === 'in_progress'
        ) {
          statusIcon = <Clock size={20} color={fix("#F59E0B", "#F59E0B")} />;
          statusText = 'In progress';
          statusColor = '#F59E0B';
        } else if (backgroundCheckStatus.latest_check.status === 'failed') {
          statusIcon = <AlertCircle size={20} color={"#EF4444"} />;
          statusText = 'Failed';
          statusColor = '#EF4444';
        }
      }
    }

    return (
      <View style={styles.verificationCard}>
        <View style={styles.verificationCardHeader}>
                            <Shield size={24} color={primaryColor} />
          <Text style={styles.verificationCardTitle}>Background Check</Text>
        </View>
        <Text style={styles.verificationCardDescription}>
          Complete a background check to build trust with potential roommates. This verification
          includes criminal history, address verification, and more.
        </Text>

        <View style={styles.backgroundCheckStatus}>
          <View style={styles.statusIconContainer}>{statusIcon}</View>
          <Text style={[styles.statusText, { color: statusColor }]}>{statusText}</Text>
        </View>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/verification/background-check')}
        >
          <Text style={styles.actionButtonText}>
            {backgroundCheckStatus?.has_background_check
              ? 'View Details'
              : backgroundCheckStatus?.latest_check?.status === 'pending' ||
                  backgroundCheckStatus?.latest_check?.status === 'in_progress'
                ? 'Check Status'
                : 'Get Background Check'}
          </Text>
                        <ChevronRight size={20} color={primaryColor} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderIdVerificationForm = () => {
    if (verificationStatus?.is_verified || verificationStatus?.pending_request) {
      return null;
    }

    return (
      <View style={styles.verificationCard}>
        <View style={styles.verificationCardHeader}>
                        <BadgeCheck size={24} color={primaryColor} />
          <Text style={styles.verificationCardTitle}>Identity Verification</Text>
        </View>
        <Text style={styles.verificationCardDescription}>
          Verify your identity to build trust with potential roommates. This helps ensure safety and
          security for everyone on the platform.
        </Text>

        <View style={styles.verificationOptions}>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => router.push('/verification/id-verification')}
          >
            <Text style={styles.optionButtonText}>Advanced Verification</Text>
            <Text style={styles.optionButtonSubtext}>Using Persona (Recommended)</Text>
          </TouchableOpacity>

          <Text style={styles.orDivider}>OR</Text>

          <View style={styles.documentUploadSection}>
            <Text style={styles.uploadSectionTitle}>Manual Verification</Text>

            <View style={styles.uploadContainer}>
              <TouchableOpacity
                style={[styles.uploadButton, documentImage ? styles.uploadButtonWithImage : {}]}
                onPress={pickDocument}
              >
                {documentImage ? (
                  <Image source={{ uri: documentImage }} style={styles.documentPreview} />
                ) : (
                  <>
                    <Upload size={24} color={primaryColor} />
                    <Text style={styles.uploadButtonText}>Upload ID</Text>
                  </>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.uploadButton, selfieImage ? styles.uploadButtonWithImage : {}]}
                onPress={takeSelfie}
              >
                {selfieImage ? (
                  <Image source={{ uri: selfieImage }} style={styles.selfiePreview} />
                ) : (
                  <>
                    <Camera size={24} color={primaryColor} />
                    <Text style={styles.uploadButtonText}>Take Selfie</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.documentTypeSelector}>
              <Text style={styles.documentTypeLabel}>Document Type:</Text>
              <View style={styles.documentTypeOptions}>
                {['passport', 'drivers_license', 'id_card'].map(type => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.documentTypeOption,
                      documentType === type && styles.documentTypeOptionSelected,
                    ]}
                    onPress={() => setDocumentType(type)}
                  >
                    <Text
                      style={[
                        styles.documentTypeText,
                        documentType === type && styles.documentTypeTextSelected,
                      ]}
                    >
                      {type === 'passport'
                        ? 'Passport'
                        : type === 'drivers_license'
                          ? "Driver's License"
                          : 'ID Card'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.submitButton,
                (!documentImage || !selfieImage) && styles.submitButtonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={!documentImage || !selfieImage || loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color={primaryColor} />
              ) : (
                <Text style={styles.submitButtonText}>Submit for Verification</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const renderTrustScoreSection = () => {
    // Calculate trust score based on verifications
    const idVerificationScore = verificationStatus?.is_verified ? 40 : 0;
    const backgroundCheckScore = backgroundCheckStatus?.has_background_check ? 40 : 0;
    const profileScore = 20; // Assume profile is complete for now
    const totalScore = idVerificationScore + backgroundCheckScore + profileScore;

    return (
      <TouchableOpacity
        style={styles.trustScoreCard}
        onPress={() => router.push('/verification/trust-score')}
      >
        <View style={styles.trustScoreHeader}>
          <Text style={styles.trustScoreTitle}>Trust Score</Text>
          <View style={styles.trustScoreCircle}>
            <Text style={styles.trustScoreValue}>{totalScore}%</Text>
          </View>
        </View>

        <View style={styles.trustScoreBreakdown}>
          <View style={styles.trustScoreItem}>
            <View style={styles.trustScoreItemHeader}>
              <Text style={styles.trustScoreItemTitle}>ID Verification</Text>
              <Text style={styles.trustScoreItemValue}>{idVerificationScore}%</Text>
            </View>
            <View style={styles.trustScoreBar}>
              <View
                style={[
                  styles.trustScoreBarFill,
                  { width: `${(idVerificationScore / 40) * 100}%`, backgroundColor: '#6366F1' },
                ]}
              />
            </View>
          </View>

          <View style={styles.trustScoreItem}>
            <View style={styles.trustScoreItemHeader}>
              <Text style={styles.trustScoreItemTitle}>Background Check</Text>
              <Text style={styles.trustScoreItemValue}>{backgroundCheckScore}%</Text>
            </View>
            <View style={styles.trustScoreBar}>
              <View
                style={[
                  styles.trustScoreBarFill,
                  { width: `${(backgroundCheckScore / 40) * 100}%`, backgroundColor: '#10B981' },
                ]}
              />
            </View>
          </View>

          <View style={styles.trustScoreItem}>
            <View style={styles.trustScoreItemHeader}>
              <Text style={styles.trustScoreItemTitle}>Profile Completion</Text>
              <Text style={styles.trustScoreItemValue}>{profileScore}%</Text>
            </View>
            <View style={styles.trustScoreBar}>
              <View
                style={[
                  styles.trustScoreBarFill,
                  { width: `${(profileScore / 20) * 100}%`, backgroundColor: '#F59E0B' },
                ]}
              />
            </View>
          </View>
        </View>

        <Text style={styles.trustScoreDescription}>
          A higher trust score helps you stand out to potential roommates and increases your chances
          of finding a match.
        </Text>

        <View style={styles.viewDetailsContainer}>
          <Text style={styles.viewDetailsText}>View detailed breakdown</Text>
                            <ChevronRight size={16} color={primaryColor} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <Stack.Screen options={{ title: 'Identity Verification' }} />
              <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <ArrowLeft size={24} color={primaryColor} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Verification</Text>
        </View>

      {/* Link to Badge Demo */}
      <TouchableOpacity
        style={styles.demoBadgeButton}
        onPress={() => router.push('/verification/badge-demo')}
      >
        <BadgeCheck size={20} color={colors.primary[500]} />
        <Text style={styles.demoBadgeButtonText}>View Enhanced Verification Badges</Text>
        <ChevronRight size={16} color={colors.primary[500]} />
      </TouchableOpacity>

      {loading && !verificationStatus ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={styles.loadingText}>Loading verification status...</Text>
        </View>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollContent}>
          {renderTrustScoreSection()}
          
          {/* Enhanced Verification Badge */}
          {verificationBadges && (
            <View style={styles.verificationCard}>
              <Text style={styles.verificationCardTitle}>Your Verification Status</Text>
              <View style={{ marginTop: 12, alignItems: 'center' }}>
                <VerificationBadge
                  trustScore={verificationBadges.trust_score}
                  verificationLevel={verificationBadges.verification_level}
                  identityVerified={verificationBadges.identity_verified}
                  backgroundCheckVerified={verificationBadges.background_check_verified}
                  referencesVerified={verificationBadges.references_verified}
                  size="large"
                  showDetails={true}
                  onPress={() => router.push('/verification/trust-score')}
                />
              </View>
              {!verificationBadges.references_verified && (
                <TouchableOpacity 
                  style={[styles.actionButton, { marginTop: 16 }]}
                  onPress={() => router.push('/verification/reference-check')}
                >
                  <Text style={styles.actionButtonText}>Add References</Text>
                  <ChevronRight size={16} color={primaryColor} />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          {renderVerificationStatus()}
          {!verificationStatus?.is_verified &&
            !verificationStatus?.pending_request &&
            renderIdVerificationForm()}
          {renderBackgroundCheckSection()}

          <View style={styles.infoCard}>
            <View style={styles.infoCardHeader}>
              <Share2 size={20} color={primaryColor} />
              <Text style={styles.infoCardTitle}>Why Verify?</Text>
            </View>
            <Text style={styles.infoCardText}>
              Verified users are 3x more likely to receive responses and find compatible roommates.
              Verification helps build trust and safety in our community.
            </Text>
          </View>

          <View style={{ height: 80 }} />

          <TouchableOpacity
            style={styles.returnToProfileButton}
            onPress={() => router.push('/(tabs)/profile')}
          >
            <Text style={styles.returnToProfileText}>Return to Profile</Text>
          </TouchableOpacity>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#ffffff',
  },
  backButton: {
    padding: 8,
    backgroundColor: '#93C5FD',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#7DD3FC',
    minWidth: 44,
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  scrollContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  verificationCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  verificationCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  verificationCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  verificationCardDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
    marginBottom: 16,
  },
  verificationBenefits: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitText: {
    fontSize: 14,
    color: '#334155',
    marginLeft: 8,
  },
  inProgressSteps: {
    marginTop: 16,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#93C5FD',
    borderWidth: 1,
    borderColor: '#7DD3FC',
  },
  stepCompleted: {
    opacity: 1,
  },
  stepPending: {
    opacity: 0.6,
  },
  stepNumber: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748b',
  },
  stepText: {
    fontSize: 14,
    color: '#334155',
  },
  submittedDate: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
  },
  backgroundCheckStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
  },
  statusIconContainer: {
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#93C5FD',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#7DD3FC',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    marginRight: 8,
  },
  verificationOptions: {
    marginTop: 16,
  },
  optionButton: {
    backgroundColor: '#93C5FD',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#7DD3FC',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  optionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 4,
  },
  optionButtonSubtext: {
    fontSize: 14,
    color: '#64748B',
  },
  orDivider: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
    color: '#94A3B8',
    marginVertical: 16,
  },
  documentUploadSection: {
    marginTop: 8,
  },
  uploadSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#334155',
    marginBottom: 12,
  },
  uploadContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  uploadButton: {
    width: '48%',
    height: 120,
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadButtonWithImage: {
    borderStyle: 'solid',
    borderColor: '#7DD3FC',
    backgroundColor: '#93C5FD',
  },
  uploadButtonText: {
    marginTop: 8,
    fontSize: 14,
    color: '#64748B',
  },
  documentPreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  selfiePreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  documentTypeSelector: {
    marginBottom: 16,
  },
  documentTypeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#334155',
    marginBottom: 8,
  },
  documentTypeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  documentTypeOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#F8FAFC',
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  documentTypeOptionSelected: {
    backgroundColor: '#93C5FD',
    borderColor: '#7DD3FC',
    borderWidth: 1,
  },
  documentTypeText: {
    fontSize: 14,
    color: '#64748B',
  },
  documentTypeTextSelected: {
    color: '#1E40AF',
    fontWeight: '600',
  },
  submitButton: {
    backgroundColor: '#93C5FD',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#7DD3FC',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  submitButtonDisabled: {
    backgroundColor: '#94A3B8',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
  },
  infoCard: {
    backgroundColor: '#F1F5F9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  infoCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#334155',
    marginLeft: 8,
  },
  infoCardText: {
    fontSize: 14,
    color: '#64748B',
    lineHeight: 20,
  },
  trustScoreCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  trustScoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  trustScoreTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  trustScoreCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#93C5FD',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#7DD3FC',
  },
  trustScoreValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1E40AF',
  },
  trustScoreBreakdown: {
    marginBottom: 16,
  },
  trustScoreItem: {
    marginBottom: 12,
  },
  trustScoreItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  trustScoreItemTitle: {
    fontSize: 14,
    color: '#334155',
  },
  trustScoreItemValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e293b',
  },
  trustScoreBar: {
    height: 8,
    backgroundColor: '#E2E8F0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  trustScoreBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  trustScoreDescription: {
    fontSize: 14,
    color: '#64748B',
    lineHeight: 20,
  },
  viewDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E40AF',
    marginRight: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
  },
  demoBadgeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#93C5FD',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#7DD3FC',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  demoBadgeButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: colors.primary[700],
    marginLeft: 12,
  },
  returnToProfileButton: {
    marginVertical: 20,
    padding: 16,
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  returnToProfileText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#334155',
  },
});
