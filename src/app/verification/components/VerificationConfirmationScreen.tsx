import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  ScrollView,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { 
  CheckCircle2, 
  Share, 
  ChevronRight, 
  Shield, 
  BadgeCheck, 
  AlertCircle, 
  AlertTriangle,
  ArrowLeft
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  Easing,
} from 'react-native-reanimated';

import { colors } from '@constants/colors';
import { useAuth } from '@context/AuthContext';
import { verificationService } from '@services';
import { unifiedProfileService } from '@services/unified-profile';

export default function VerificationConfirmationScreen() {
  const router = useRouter();
  const { state, actions } = useAuth();
  const params = useLocalSearchParams<{ status: string; type: string }>();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [verificationData, setVerificationData] = useState<any>(null);
  const [trustScore, setTrustScore] = useState<number>(0);
  
  // Animation values
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);
  const checkmarkScale = useSharedValue(0);
  const badgeOpacity = useSharedValue(0);
  
  // Get verification status from URL params
  const verificationStatus = params.status || 'unknown';
  const verificationType = params.type || 'id';
  
  useEffect(() => {
    fetchVerificationData();
    startAnimations();
  }, []);
  
  const startAnimations = () => {
    scale.value = withTiming(1, {
      duration: 600,
      easing: Easing.bezier(0.16, 1, 0.3, 1),
    });
    
    opacity.value = withTiming(1, { duration: 500 });
    
    if (verificationStatus === 'success') {
      checkmarkScale.value = withDelay(
        600,
        withSequence(
          withTiming(1.2, { duration: 300 }),
          withTiming(1, { duration: 200 })
        )
      );
      
      badgeOpacity.value = withDelay(900, withTiming(1, { duration: 500 }));
    }
  };
  
  const fetchVerificationData = async () => {
    try {
      setLoading(true);
      
      // Fetch verification status from the server
      const response = await verificationService.getVerificationStatus();
      
      if (response.data) {
        setVerificationData(response.data);
        
        // Update trust score
        if (response.data.is_verified) {
          // Calculate trust score based on verification status
          // In a real app, this would be more sophisticated
          let score = 60; // Base score for verification
          
          // Fetch user profile to get other verification factors
          const userProfile = await unifiedProfileService.getCurrentUserProfile();
          
          if (userProfile?.email_verified) score += 10;
          if (userProfile?.phone_verified) score += 10;
          if (response.data.verification_level === 'advanced') score += 20;
          
          setTrustScore(Math.min(score, 100));
        }
      } else if (response.error) {
        setError(response.error);
      }
    } catch (err) {
      console.error('Error fetching verification data:', err);
      setError('Failed to load verification data');
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    // Navigate to the appropriate screen based on verification type
    if (verificationType === 'id') {
      if (verificationStatus === 'success') {
        // If ID verification was successful, offer background check as next step
        router.push('/verification/background-check');
      } else {
        // If failed or cancelled, go back to verification main screen
        router.push('/verification');
      }
    } else if (verificationType === 'background') {
      // After background check, return to profile
      router.push('/(tabs)/profile');
    } else {
      router.push('/(tabs)/profile');
    }
  };

  const handleGoBack = () => {
    router.push('/verification');
  };
  
  const handleShare = () => {
    // In a real app, this would share the verification status
    alert('Sharing verification status is not implemented in this demo.');
  };
  
  // Animated styles
  const cardStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));
  
  const checkmarkStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkmarkScale.value }],
  }));
  
  const badgeStyle = useAnimatedStyle(() => ({
    opacity: badgeOpacity.value,
  }));
  
  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={styles.loadingText}>Loading verification status...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.errorContainer}>
          <AlertTriangle size={50} color={colors.error[500]} />
          <Text style={styles.errorTitle}>Verification Error</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.button} onPress={handleGoBack}>
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  // Success state
  if (verificationStatus === 'success') {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View style={[styles.card, cardStyle]}>
            <Animated.View style={[styles.checkmarkContainer, checkmarkStyle]}>
              <CheckCircle2 size={80} color={colors.success[500]} />
            </Animated.View>
            
            <Text style={styles.title}>Verification Successful!</Text>
            <Text style={styles.subtitle}>
              {verificationType === 'id' 
                ? 'Your identity has been verified' 
                : 'Your background check has been completed'}
            </Text>
            
            <Animated.View style={[styles.trustBadge, badgeStyle]}>
              <View style={styles.badgeHeader}>
                <Shield size={20} color={colors.primary[500]} />
                <Text style={styles.badgeTitle}>Trust Score</Text>
              </View>
              
              <View style={styles.scoreContainer}>
                <View style={styles.scoreCircle}>
                  <Text style={styles.scoreValue}>{trustScore}</Text>
                </View>
                <Text style={styles.scoreLabel}>
                  {trustScore >= 80 ? 'Excellent' : 
                   trustScore >= 60 ? 'Good' : 
                   trustScore >= 40 ? 'Fair' : 'Needs Improvement'}
                </Text>
              </View>
              
              <View style={styles.badgeDetails}>
                <View style={styles.badgeItem}>
                  <BadgeCheck size={16} color={colors.success[500]} />
                  <Text style={styles.badgeText}>
                    {verificationType === 'id' ? 'Identity Verified' : 'Background Check Passed'}
                  </Text>
                </View>
                
                {verificationType === 'id' && (
                  <TouchableOpacity 
                    style={styles.nextStepButton}
                    onPress={() => router.push('/verification/background-check')}
                  >
                    <Text style={styles.nextStepText}>Complete Background Check</Text>
                    <ChevronRight size={16} color={colors.primary[500]} />
                  </TouchableOpacity>
                )}
                
                {verificationType === 'background' && (
                  <View style={styles.badgeItem}>
                    <BadgeCheck size={16} color={colors.success[500]} />
                    <Text style={styles.badgeText}>Background Verified</Text>
                  </View>
                )}
              </View>
            </Animated.View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.button} onPress={handleContinue}>
                <Text style={styles.buttonText}>
                  {verificationType === 'id' && verificationStatus === 'success'
                    ? 'Continue to Background Check'
                    : 'Return to Profile'}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
                <Share size={20} color={colors.primary[500]} />
                <Text style={styles.shareButtonText}>Share Verification</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    );
  }
  
  // Failed or cancelled state
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      
      <View style={styles.content}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <ArrowLeft size={24} color="#000" />
        </TouchableOpacity>
        
        <Animated.View style={[styles.card, cardStyle]}>
          <View style={styles.iconContainer}>
            {verificationStatus === 'cancelled' ? (
              <AlertCircle size={60} color={colors.warning[500]} />
            ) : (
              <AlertTriangle size={60} color={colors.error[500]} />
            )}
          </View>
          
          <Text style={styles.title}>
            {verificationStatus === 'cancelled' 
              ? 'Verification Cancelled' 
              : 'Verification Failed'}
          </Text>
          
          <Text style={styles.subtitle}>
            {verificationStatus === 'cancelled'
              ? 'You cancelled the verification process. You can try again whenever you\'re ready.'
              : 'We couldn\'t verify your identity. Please check your information and try again.'}
          </Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.button} 
              onPress={() => router.push('/verification')}
            >
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.secondaryButton} 
              onPress={() => router.push('/(tabs)/profile')}
            >
              <Text style={styles.secondaryButtonText}>Return to Profile</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 16,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.error[700],
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  checkmarkContainer: {
    marginBottom: 24,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 32,
  },
  trustBadge: {
    width: '100%',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  badgeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  badgeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 8,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.primary[500],
    marginRight: 16,
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary[700],
  },
  scoreLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
  },
  badgeDetails: {
    width: '100%',
  },
  badgeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  badgeText: {
    fontSize: 14,
    color: '#334155',
    marginLeft: 8,
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButton: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#cbd5e1',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748b',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  shareButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.primary[600],
    marginLeft: 8,
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
  },
  nextStepButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 8,
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  nextStepText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary[600],
  },
});
