import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';
import { zeroVerificationService } from '@services/zeroVerificationService';

interface VerificationMethod {
  id: string;
  title: string;
  description: string;
  icon: string;
  time: string;
  difficulty: 'Easy' | 'Medium' | 'Advanced';
  premium: boolean;
  disabled?: boolean;
  disabledReason?: string;
  free?: boolean;
}

interface ReferenceContact {
  id: string;
  name: string;
  email: string;
  relationship: string;
  phone?: string;
  verified: boolean;
}

export default function BackgroundCheckScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [references, setReferences] = useState<ReferenceContact[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [completedChecks, setCompletedChecks] = useState<string[]>([]);
  const [showReferenceForm, setShowReferenceForm] = useState(false);
  const [tempReference, setTempReference] = useState({
    name: '',
    email: '',
    relationship: '',
    phone: '',
  });

  const verificationMethods: VerificationMethod[] = [
    {
      id: 'sex_offender_check',
      title: 'Sex Offender Registry Check',
      description: 'Free government database search (NSOPW)',
      icon: 'shield',
      time: '2 minutes',
      difficulty: 'Easy',
      premium: false,
      free: true,
    },
    {
      id: 'reference_verification',
      title: 'Reference Verification',
      description: 'Verify character through personal references',
      icon: 'users',
      time: '10 minutes',
      difficulty: 'Easy',
      premium: false,
      free: true,
    },
    {
      id: 'social_media_check',
      title: 'Social Media Verification',
      description: 'Manual review of public social profiles',
      icon: 'globe',
      time: '15 minutes',
      difficulty: 'Medium',
      premium: false,
      free: true,
    },
    {
      id: 'public_records_search',
      title: 'Public Records Search',
      description: 'Free court records and public database search',
      icon: 'file-text',
      time: '20 minutes',
      difficulty: 'Medium',
      premium: false,
      free: true,
    },
    {
      id: 'criminal_background',
      title: 'Comprehensive Criminal Check',
      description: 'Premium criminal background service coming soon',
      icon: 'alert-triangle',
      time: '24 hours',
      difficulty: 'Easy',
      premium: true,
      disabled: true,
      disabledReason: 'Premium criminal background check service coming soon - saves $35 per check',
    },
    {
      id: 'employment_verification',
      title: 'Employment Verification',
      description: 'Premium employment history service coming soon',
      icon: 'briefcase',
      time: '48 hours',
      difficulty: 'Easy',
      premium: true,
      disabled: true,
      disabledReason: 'Premium employment verification service coming soon - saves $25 per check',
    },
    {
      id: 'credit_check',
      title: 'Credit History Check',
      description: 'Premium credit report service coming soon',
      icon: 'credit-card',
      time: '24 hours',
      difficulty: 'Easy',
      premium: true,
      disabled: true,
      disabledReason: 'Premium credit history service coming soon - saves $30 per check',
    },
  ];

  const relationshipTypes = [
    'Former Landlord',
    'Previous Roommate',
    'Employer/Supervisor',
    'Colleague',
    'Friend',
    'Family Member',
    'Teacher/Professor',
    'Other',
  ];

  useEffect(() => {
    // Load any existing verification data
    loadExistingVerifications();
  }, []);

  const loadExistingVerifications = async () => {
    try {
      // Check for existing verifications
      const existingChecks = await zeroVerificationService.getBackgroundCheckStatus(user?.id || '');
      if (existingChecks.success) {
        setCompletedChecks(existingChecks.completedChecks || []);
      }
    } catch (error) {
      logger.error('Failed to load existing verifications', error as Error);
    }
  };

  const handleMethodSelect = (methodId: string) => {
    const method = verificationMethods.find(m => m.id === methodId);
    
    // Check if method is disabled (premium coming soon)
    if (method?.disabled) {
      Alert.alert(
        'Coming Soon',
        method.disabledReason || 'This premium feature is coming soon',
        [{ text: 'OK' }]
      );
      return;
    }
    
    setSelectedMethod(methodId);
    setCurrentStep(1);
  };

  const handleSexOffenderCheck = async () => {
    setIsProcessing(true);
    try {
      // Simulate sex offender registry check using free government API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const result = await zeroVerificationService.checkSexOffenderRegistry(
        user?.email || '',
        user?.profile?.full_name || ''
      );
      
      if (result.success) {
        setCompletedChecks([...completedChecks, 'sex_offender_check']);
        setCurrentStep(2);
        logger.info('Sex offender registry check completed', { result });
      } else {
        Alert.alert('Check Failed', 'Unable to complete sex offender registry check');
      }
    } catch (error) {
      logger.error('Sex offender check failed', error as Error);
      Alert.alert('Check Failed', 'Please try again');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReferenceVerification = async () => {
    if (references.length < 2) {
      Alert.alert('Insufficient References', 'Please add at least 2 references');
      return;
    }

    setIsProcessing(true);
    try {
      // Send reference verification emails
      const result = await zeroVerificationService.sendReferenceVerifications(
        user?.id || '',
        references
      );
      
      if (result.success) {
        setCompletedChecks([...completedChecks, 'reference_verification']);
        setCurrentStep(2);
        Alert.alert(
          'References Sent',
          'Verification emails have been sent to your references. You\'ll be notified when they respond.'
        );
        logger.info('Reference verification initiated', { referenceCount: references.length });
      } else {
        Alert.alert('Failed to Send', 'Unable to send reference verifications');
      }
    } catch (error) {
      logger.error('Reference verification failed', error as Error);
      Alert.alert('Verification Failed', 'Please try again');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSocialMediaCheck = async () => {
    setIsProcessing(true);
    try {
      // Simulate social media verification process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result = await zeroVerificationService.performSocialMediaCheck(user?.id || '');
      
      if (result.success) {
        setCompletedChecks([...completedChecks, 'social_media_check']);
        setCurrentStep(2);
        logger.info('Social media check completed', { result });
      } else {
        Alert.alert('Check Failed', 'Unable to complete social media verification');
      }
    } catch (error) {
      logger.error('Social media check failed', error as Error);
      Alert.alert('Check Failed', 'Please try again');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePublicRecordsSearch = async () => {
    setIsProcessing(true);
    try {
      // Simulate public records search using free APIs
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      const result = await zeroVerificationService.searchPublicRecords(
        user?.profile?.full_name || '',
        user?.profile?.address || ''
      );
      
      if (result.success) {
        setCompletedChecks([...completedChecks, 'public_records_search']);
        setCurrentStep(2);
        logger.info('Public records search completed', { result });
      } else {
        Alert.alert('Search Failed', 'Unable to complete public records search');
      }
    } catch (error) {
      logger.error('Public records search failed', error as Error);
      Alert.alert('Search Failed', 'Please try again');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAddReference = () => {
    if (!tempReference.name || !tempReference.email || !tempReference.relationship) {
      Alert.alert('Missing Information', 'Please fill in all required fields');
      return;
    }

    const newReference: ReferenceContact = {
      id: Date.now().toString(),
      name: tempReference.name,
      email: tempReference.email,
      relationship: tempReference.relationship,
      phone: tempReference.phone,
      verified: false,
    };

    setReferences([...references, newReference]);
    setTempReference({ name: '', email: '', relationship: '', phone: '' });
    setShowReferenceForm(false);
  };

  const handleRemoveReference = (id: string) => {
    setReferences(references.filter(ref => ref.id !== id));
  };

  const handleCompleteVerification = () => {
    if (completedChecks.length === 0) {
      Alert.alert('No Checks Completed', 'Please complete at least one verification method');
      return;
    }
    setCurrentStep(3);
  };

  const renderMethodSelection = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Choose Background Check Method
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Select from our free verification methods. Premium services coming soon!
      </Text>

      <View style={styles.methodsContainer}>
        {verificationMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.methodCard,
              { 
                backgroundColor: theme.colors.surface,
                borderColor: method.disabled ? theme.colors.border : 
                           method.free ? '#10b981' : theme.colors.border,
                opacity: method.disabled ? 0.6 : 1,
              },
              completedChecks.includes(method.id) && styles.completedMethod,
            ]}
            onPress={() => handleMethodSelect(method.id)}
            disabled={method.disabled}
          >
            <View style={styles.methodHeader}>
              <View style={styles.methodIconContainer}>
                <Feather 
                  name={method.icon as any} 
                  size={24} 
                  color={method.disabled ? theme.colors.textSecondary : 
                         method.free ? '#10b981' : theme.colors.primary} 
                />
                {method.free && (
                  <View style={styles.freeLabel}>
                    <Text style={styles.freeLabelText}>FREE</Text>
                  </View>
                )}
                {method.premium && (
                  <View style={styles.premiumLabel}>
                    <Text style={styles.premiumLabelText}>PREMIUM</Text>
                  </View>
                )}
              </View>
              <View style={styles.methodInfo}>
                <Text style={[styles.methodTitle, { color: theme.colors.text }]}>
                  {method.title}
                </Text>
                <Text style={[styles.methodDescription, { color: theme.colors.textSecondary }]}>
                  {method.description}
                </Text>
              </View>
              {completedChecks.includes(method.id) && (
                <Feather name="check-circle" size={20} color="#10b981" />
              )}
            </View>
            <View style={styles.methodMeta}>
              <Text style={[styles.methodTime, { color: theme.colors.textSecondary }]}>
                <Feather name="clock" size={12} color={theme.colors.textSecondary} /> {method.time}
              </Text>
              <Text style={[styles.methodDifficulty, { color: theme.colors.textSecondary }]}>
                {method.difficulty}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity
        style={[styles.continueButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => setCurrentStep(2)}
      >
        <Text style={styles.continueButtonText}>Review Completed Checks</Text>
      </TouchableOpacity>
    </View>
  );

  const renderVerificationProcess = () => {
    const method = verificationMethods.find(m => m.id === selectedMethod);
    if (!method) return null;

    return (
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
          {method.title}
        </Text>
        <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
          {method.description}
        </Text>

        {selectedMethod === 'sex_offender_check' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name="info" size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                This check searches the National Sex Offender Public Website (NSOPW) database.
                This is a free government service.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSexOffenderCheck}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.actionButtonText}>Run Sex Offender Check</Text>
              )}
            </TouchableOpacity>
          </View>
        )}

        {selectedMethod === 'reference_verification' && (
          <View style={styles.processContainer}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Add References (Minimum 2)
            </Text>
            
            {references.map((reference) => (
              <View key={reference.id} style={[styles.referenceCard, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.referenceInfo}>
                  <Text style={[styles.referenceName, { color: theme.colors.text }]}>
                    {reference.name}
                  </Text>
                  <Text style={[styles.referenceDetails, { color: theme.colors.textSecondary }]}>
                    {reference.relationship} • {reference.email}
                  </Text>
                </View>
                <TouchableOpacity onPress={() => handleRemoveReference(reference.id)}>
                  <Feather name="x" size={20} color={theme.colors.error} />
                </TouchableOpacity>
              </View>
            ))}

            <TouchableOpacity
              style={[styles.addButton, { borderColor: theme.colors.border }]}
              onPress={() => setShowReferenceForm(true)}
            >
              <Feather name="plus" size={20} color={theme.colors.primary} />
              <Text style={[styles.addButtonText, { color: theme.colors.primary }]}>
                Add Reference
              </Text>
            </TouchableOpacity>

            {references.length >= 2 && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleReferenceVerification}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text style={styles.actionButtonText}>Send Reference Verifications</Text>
                )}
              </TouchableOpacity>
            )}
          </View>
        )}

        {selectedMethod === 'social_media_check' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name="info" size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                We'll manually review your public social media profiles to verify your identity
                and check for any concerning content.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSocialMediaCheck}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.actionButtonText}>Start Social Media Check</Text>
              )}
            </TouchableOpacity>
          </View>
        )}

        {selectedMethod === 'public_records_search' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name="info" size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                We'll search free public records databases including court records,
                property records, and other publicly available information.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handlePublicRecordsSearch}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.actionButtonText}>Search Public Records</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderSummary = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Background Check Summary
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Review your completed background checks
      </Text>

      <View style={styles.summaryContainer}>
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
            Completed Checks
          </Text>
          
          {completedChecks.map((checkId) => {
            const method = verificationMethods.find(m => m.id === checkId);
            return method ? (
              <View key={checkId} style={styles.completedItem}>
                <Feather name="check-circle" size={16} color="#10b981" />
                <Text style={[styles.completedText, { color: theme.colors.text }]}>
                  {method.title}
                </Text>
              </View>
            ) : null;
          })}

          {completedChecks.length === 0 && (
            <Text style={[styles.noChecksText, { color: theme.colors.textSecondary }]}>
              No checks completed yet
            </Text>
          )}
        </View>

        <View style={[styles.savingsCard, { backgroundColor: '#f0f9ff' }]}>
          <Text style={[styles.savingsTitle, { color: '#1e40af' }]}>
            💰 Money Saved with Free Verification
          </Text>
          <Text style={[styles.savingsAmount, { color: '#1e40af' }]}>
            ${completedChecks.length * 25} saved
          </Text>
          <Text style={[styles.savingsDescription, { color: '#3b82f6' }]}>
            vs. traditional background check services
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleCompleteVerification}
      >
        <Text style={styles.actionButtonText}>Complete Verification</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSuccess = () => (
    <View style={styles.stepContent}>
      <View style={styles.successContainer}>
        <View style={styles.successIcon}>
          <Feather name="check-circle" size={60} color="#10b981" />
        </View>
        <Text style={[styles.successTitle, { color: theme.colors.text }]}>
          Background Check Complete!
        </Text>
        <Text style={[styles.successDescription, { color: theme.colors.textSecondary }]}>
          Your background verification has been completed using our free verification system.
          Your profile is now more trustworthy to potential roommates.
        </Text>

        <View style={[styles.successStats, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
              {completedChecks.length}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Checks Completed
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#10b981' }]}>
              $0
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Cost
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#3b82f6' }]}>
              ${completedChecks.length * 25}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Saved
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.back()}
        >
          <Text style={styles.actionButtonText}>Return to Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderReferenceForm = () => (
    <Modal
      visible={showReferenceForm}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowReferenceForm(false)}>
            <Text style={[styles.modalCancel, { color: theme.colors.textSecondary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Add Reference</Text>
          <TouchableOpacity onPress={handleAddReference}>
            <Text style={[styles.modalSave, { color: theme.colors.primary }]}>Add</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Full Name *</Text>
            <TextInput
              style={[styles.formInput, { 
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text 
              }]}
              value={tempReference.name}
              onChangeText={(text) => setTempReference({...tempReference, name: text})}
              placeholder="Enter reference's full name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Email Address *</Text>
            <TextInput
              style={[styles.formInput, { 
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text 
              }]}
              value={tempReference.email}
              onChangeText={(text) => setTempReference({...tempReference, email: text})}
              placeholder="Enter email address"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Relationship *</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.relationshipTags}>
              {relationshipTypes.map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.relationshipTag,
                    { 
                      backgroundColor: tempReference.relationship === type ? 
                        theme.colors.primary : theme.colors.surface,
                      borderColor: theme.colors.border 
                    }
                  ]}
                  onPress={() => setTempReference({...tempReference, relationship: type})}
                >
                  <Text style={[
                    styles.relationshipTagText,
                    { 
                      color: tempReference.relationship === type ? 
                        'white' : theme.colors.text 
                    }
                  ]}>
                    {type}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Phone Number (Optional)</Text>
            <TextInput
              style={[styles.formInput, { 
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text 
              }]}
              value={tempReference.phone}
              onChangeText={(text) => setTempReference({...tempReference, phone: text})}
              placeholder="Enter phone number"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="phone-pad"
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderMethodSelection();
      case 1:
        return renderVerificationProcess();
      case 2:
        return renderSummary();
      case 3:
        return renderSuccess();
      default:
        return renderMethodSelection();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Background Check</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          {[0, 1, 2, 3].map((step) => (
            <View
              key={step}
              style={[
                styles.progressStep,
                {
                  backgroundColor: step <= currentStep ? theme.colors.primary : theme.colors.border,
                },
              ]}
            />
          ))}
        </View>
        <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
          Step {currentStep + 1} of 4
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {renderReferenceForm()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerRight: {
    width: 32,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  progressBar: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  progressStep: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepContent: {
    padding: 20,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
  methodsContainer: {
    gap: 16,
    marginBottom: 24,
  },
  methodCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  completedMethod: {
    borderColor: '#10b981',
    backgroundColor: '#f0fdf4',
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  methodIconContainer: {
    position: 'relative',
    marginRight: 12,
  },
  freeLabel: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#10b981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  freeLabelText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  premiumLabel: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#f59e0b',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  premiumLabelText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  methodInfo: {
    flex: 1,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  methodMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  methodTime: {
    fontSize: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodDifficulty: {
    fontSize: 12,
    fontWeight: '500',
  },
  continueButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  processContainer: {
    gap: 20,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    alignItems: 'flex-start',
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  actionButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  referenceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  referenceInfo: {
    flex: 1,
  },
  referenceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  referenceDetails: {
    fontSize: 14,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    marginBottom: 20,
    gap: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  summaryContainer: {
    gap: 20,
    marginBottom: 24,
  },
  summaryCard: {
    padding: 20,
    borderRadius: 12,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  completedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  completedText: {
    fontSize: 16,
  },
  noChecksText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  savingsCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  savingsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  savingsAmount: {
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 4,
  },
  savingsDescription: {
    fontSize: 14,
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  successIcon: {
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  successDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  successStats: {
    flexDirection: 'row',
    padding: 20,
    borderRadius: 12,
    marginBottom: 32,
    gap: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  modalCancel: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSave: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  formInput: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    fontSize: 16,
  },
  relationshipTags: {
    marginTop: 8,
  },
  relationshipTag: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  relationshipTagText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
