import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import { WebView } from 'react-native-webview';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ScrollView,
  Animated,
  StatusBar,
  Modal,
  Linking,
  Platform,
} from 'react-native';
import {
  ArrowLeft,
  Shield,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  CreditCard,
  Passport,
  IdCard,
  ExternalLink,
  Info,
  Sparkles,
  DollarSign,
  Clock,
  Upload,
  Camera,
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useAuth } from '@context/AuthContext';
import { verificationService } from '@services';
import { logger } from '@services/loggerService';
import LivenessDetection from '@app/verification/components/LivenessDetection';
import { zeroVerificationService } from '@services/zeroVerificationService';

export default function IDVerificationScreen() {
  const router = useRouter();
  const { state, actions } = useAuth();
  const insets = useSafeAreaInsets();
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [verificationUrl, setVerificationUrl] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [maxRetries] = useState(3); // Match with verificationService.maxRetries
  const [progress, setProgress] = useState(0);
  const [stepComplete, setStepComplete] = useState<{ id: number; complete: boolean }[]>([
    { id: 1, complete: false },
    { id: 2, complete: false },
  ]);
  // Add document type state
  const [documentType, setDocumentType] = useState<'passport' | 'drivers_license' | 'id_card'>('passport');
  const [showDocumentSelection, setShowDocumentSelection] = useState(true);
  // Add liveness detection state
  const [showLivenessDetection, setShowLivenessDetection] = useState(false);
  const [selfieUri, setSelfieUri] = useState<string | null>(null);
  const [showComingSoon, setShowComingSoon] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // WebView reference
  const webViewRef = useRef<WebView | null>(null);

  // Initialize verification when screen loads
  useEffect(() => {
    // Don't start verification automatically, just fade in
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Update progress animation when progress changes
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  // Start the document verification process
  const startDocumentVerification = async () => {
    try {
      setLoading(true);
      setError(null);
      setProgress(10);

      if (!state.user) {
        setError('You must be logged in to verify your identity');
        setLoading(false);
        setProgress(0);
        return;
      }

      // Initialize verification service
      const initialized = await verificationService.initialize();

      if (!initialized) {
        setError('Verification service is currently unavailable. Please try again later.');
        setLoading(false);
        setProgress(0);
        return;
      }

      setProgress(30);

      // First proceed to liveness detection
      setShowDocumentSelection(false);
      setShowLivenessDetection(true);
      setLoading(false);
    } catch (error) {
      logger.error('Error starting verification', 'IDVerificationScreen', {}, error as Error);
      setError('An error occurred while setting up verification. Please try again.');
      setLoading(false);
      setProgress(0);
    }
  };

  // Continue to Persona verification after liveness check
  const continueToPersaonVerification = async (selfieImageUri: string) => {
    try {
      setLoading(true);
      setShowLivenessDetection(false);
      setSelfieUri(selfieImageUri);
      setProgress(40);

      // Now start verification session with Persona API
      const session = await verificationService.startVerification(
        state.user!.id,
        state.user!.email || '',
        documentType
      );

      if (!session) {
        setError('Failed to start verification process. Please try again.');
        setLoading(false);
        setProgress(0);
        return;
      }

      setSessionId(session.id);
      setVerificationUrl(session.verificationUrl || null);
      setRetryCount(session.retryCount || 0);
      setLoading(false);
      setProgress(50);

      // Update first step as complete
      const newStepComplete = [...stepComplete];
      newStepComplete[0].complete = true;
      setStepComplete(newStepComplete);
    } catch (error) {
      logger.error(
        'Error starting Persona verification',
        'IDVerificationScreen',
        {},
        error as Error
      );
      setError('An error occurred while setting up verification. Please try again.');
      setLoading(false);
      setProgress(0);
    }
  };

  // Cancel liveness detection
  const cancelLivenessDetection = () => {
    setShowLivenessDetection(false);
    setShowDocumentSelection(true);
  };

  // Retry verification if failed
  const retryVerification = async () => {
    // Show document selection again when retrying
    setShowDocumentSelection(true);
    setSelfieUri(null);

    try {
      if (!state.user) {
        setError('You must be logged in to verify your identity');
        return;
      }

      setLoading(true);
      setError(null);
      setProgress(20);

      // Use the retry method from the service
      const session = await verificationService.retryVerification(
        state.user.id,
        state.user.email || '',
        documentType
      );

      if (!session) {
        setError(
          `You've reached the maximum number of verification attempts. Please contact support for assistance.`
        );
        setLoading(false);
        setProgress(0);
        return;
      }

      setSessionId(session.id);
      setVerificationUrl(session.verificationUrl || null);
      setRetryCount(session.retryCount || 0);
      setLoading(false);
      setProgress(50);
      setShowDocumentSelection(false);
    } catch (error) {
      logger.error('Error retrying verification', 'IDVerificationScreen', {}, error as Error);
      setError('An error occurred while setting up verification. Please try again.');
      setLoading(false);
      setProgress(0);
    }
  };

  // Handle WebView navigation state changes
  const handleNavigationStateChange = async (navState: any) => {
    // Track document upload step completion
    if (navState.url.includes('document-upload-success')) {
      const newStepComplete = [...stepComplete];
      newStepComplete[0].complete = true;
      setStepComplete(newStepComplete);
      setProgress(75);
    }

    // Track selfie verification step completion
    if (navState.url.includes('selfie-success')) {
      const newStepComplete = [...stepComplete];
      newStepComplete[1].complete = true;
      setStepComplete(newStepComplete);
      setProgress(90);
    }

    // Check if verification is completed based on URL
    if (navState.url.includes('verification-complete') && sessionId) {
      setVerifying(true);
      setProgress(95);

      // Check verification status
      try {
        const result = await verificationService.checkVerificationStatus(sessionId);

        if (result && result.isVerified) {
          const newStepComplete = stepComplete.map(step => ({ ...step, complete: true }));
          setStepComplete(newStepComplete);
          setCompleted(true);
          setVerifying(false);
          setProgress(100);
        } else if (result && result.failureReason) {
          setError(`Verification failed: ${result.failureReason}`);
          setVerifying(false);
          setProgress(0);
        }
      } catch (error) {
        logger.error(
          'Error checking verification status',
          'IDVerificationScreen',
          {},
          error as Error
        );
        setError('Failed to confirm verification status. Please contact support.');
        setVerifying(false);
        setProgress(0);
      }
    }

    // Check if we've been redirected to our success or cancel URLs
    if (navState.url.includes('verification-success')) {
      // The verification was successful
      router.replace('/verification');
    } else if (navState.url.includes('verification-canceled')) {
      // The user canceled the verification
      router.back();
    }
  };

  // Handle manual check of verification status
  const checkVerificationStatus = async () => {
    if (!sessionId) return;

    setVerifying(true);
    setProgress(95);

    try {
      const result = await verificationService.checkVerificationStatus(sessionId);

      if (result && result.isVerified) {
        const newStepComplete = stepComplete.map(step => ({ ...step, complete: true }));
        setStepComplete(newStepComplete);
        setCompleted(true);
        setProgress(100);
      } else if (result && result.failureReason) {
        setError(`Verification failed: ${result.failureReason}`);
      } else {
        Alert.alert(
          'Verification In Progress',
          'Your verification is still being processed. This usually takes 1-2 minutes. You can wait or check back later.'
        );
      }
    } catch (error) {
      logger.error(
        'Error checking verification status',
        'IDVerificationScreen',
        {},
        error as Error
      );
      setError('Failed to check verification status');
    } finally {
      setVerifying(false);
    }
  };

  const handleRetry = () => {
    setError(null);
    retryVerification();
  };

  const handleContinue = () => {
    router.push('/profile');
  };

  const handleStartVerification = () => {
    Alert.alert(
      '�� Zero-Cost Identity Verification',
      'Our FREE identity verification system is being implemented!\n\n✅ Manual document review\n✅ Secure upload to Supabase\n✅ 24-hour processing\n✅ Saves $7 per verification\n\nThis will replace expensive Persona API verification.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Continue with FREE System', 
          onPress: () => setShowComingSoon(true)
        }
      ]
    );
  };

  const handleComingSoonContinue = () => {
    setShowComingSoon(false);
    Alert.alert(
      '🚧 Implementation in Progress',
      'Free identity verification is being developed. You\'ll be notified when it\'s ready!\n\nMeanwhile, your profile remains active with existing verification methods.',
      [{ text: 'OK', onPress: () => router.back() }]
    );
  };

  // Render document type selection UI
  const renderDocumentTypeSelection = () => {
    const documentTypes = [
      {
        type: 'passport' as const,
        title: 'Passport',
        description: 'International travel document',
        icon: Passport,
        recommended: true,
      },
      {
        type: 'drivers_license' as const,
        title: 'Driver\'s License',
        description: 'State-issued driving permit',
        icon: CreditCard,
        recommended: false,
      },
      {
        type: 'id_card' as const,
        title: 'National ID Card',
        description: 'Government-issued identification',
        icon: IdCard,
        recommended: false,
      },
    ];

    return (
      <View style={styles.documentSelectionContainer}>
        <View style={styles.headerSection}>
          <View style={styles.iconContainer}>
            <Sparkles size={32} color="#10b981" />
          </View>
          <Text style={styles.title}>FREE Identity Verification</Text>
          <Text style={styles.subtitle}>
            Zero-cost document verification with manual review
        </Text>

          <View style={styles.savingsHighlight}>
            <DollarSign size={16} color="#10b981" />
            <Text style={styles.savingsText}>Saves $7 vs Persona API</Text>
          </View>
        </View>

        <View style={styles.documentsContainer}>
          <Text style={styles.sectionTitle}>Select Document Type</Text>
          
          {documentTypes.map((doc) => {
            const IconComponent = doc.icon;
            const isSelected = documentType === doc.type;
            
            return (
          <TouchableOpacity
                key={doc.type}
            style={[
                  styles.documentOption,
                  isSelected && styles.documentOptionSelected,
            ]}
                onPress={() => setDocumentType(doc.type)}
              >
                <View style={styles.documentOptionContent}>
                  <View style={styles.documentOptionLeft}>
                    <View style={[
                      styles.documentIcon,
                      isSelected && styles.documentIconSelected
                    ]}>
                      <IconComponent 
                        size={24} 
                        color={isSelected ? '#10b981' : '#64748b'} 
                      />
                    </View>
                    <View style={styles.documentInfo}>
                      <View style={styles.documentTitleContainer}>
                        <Text style={[
                          styles.documentTitle,
                          isSelected && styles.documentTitleSelected
                        ]}>
                          {doc.title}
            </Text>
                        {doc.recommended && (
                          <View style={styles.recommendedBadge}>
                            <Text style={styles.recommendedText}>Recommended</Text>
                          </View>
                        )}
                      </View>
                      <Text style={styles.documentDescription}>
                        {doc.description}
                      </Text>
                    </View>
                  </View>
                  <View style={[
                    styles.radioButton,
                    isSelected && styles.radioButtonSelected
                  ]}>
                    {isSelected && <View style={styles.radioButtonInner} />}
                  </View>
                </View>
          </TouchableOpacity>
            );
          })}
        </View>

        <View style={styles.processInfo}>
          <Text style={styles.processTitle}>How it works:</Text>
          <View style={styles.processSteps}>
            <View style={styles.processStep}>
              <Upload size={16} color="#10b981" />
              <Text style={styles.processStepText}>Upload document securely</Text>
            </View>
            <View style={styles.processStep}>
              <Shield size={16} color="#10b981" />
              <Text style={styles.processStepText}>Manual admin review</Text>
            </View>
            <View style={styles.processStep}>
              <Clock size={16} color="#10b981" />
              <Text style={styles.processStepText}>24-hour processing</Text>
            </View>
          </View>
        </View>

          <TouchableOpacity
          style={styles.continueButton}
          onPress={handleStartVerification}
            >
          <Text style={styles.continueButtonText}>Start FREE Verification</Text>
          <Sparkles size={16} color="white" />
        </TouchableOpacity>
      </View>
    );
  };

  // Render progress bar
  const renderProgressBar = () => {
    const width = progressAnim.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    });

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <Animated.View style={[styles.progressFill, { width }]} />
        </View>
        <Text style={styles.progressText}>{Math.round(progress)}%</Text>
      </View>
    );
  };

  // Render verification steps with status
  const renderSteps = () => {
    return (
      <View style={styles.stepsContainer}>
        <View style={styles.stepContainer}>
          <View
            style={[styles.stepCircle, stepComplete[0].complete ? styles.stepCompleteCircle : null]}
          >
            {stepComplete[0].complete ? (
              <CheckCircle size={18} color="#fff" />
            ) : (
              <Text style={styles.stepNumber}>1</Text>
            )}
          </View>
          <Text
            style={[styles.stepText, stepComplete[0].complete ? styles.stepCompleteText : null]}
          >
            Take a photo of your government ID
          </Text>
        </View>

        <View style={styles.stepDivider} />

        <View style={styles.stepContainer}>
          <View
            style={[styles.stepCircle, stepComplete[1].complete ? styles.stepCompleteCircle : null]}
          >
            {stepComplete[1].complete ? (
              <CheckCircle size={18} color="#fff" />
            ) : (
              <Text style={styles.stepNumber}>2</Text>
            )}
          </View>
          <Text
            style={[styles.stepText, stepComplete[1].complete ? styles.stepCompleteText : null]}
          >
            Take a selfie for facial verification
          </Text>
        </View>
      </View>
    );
  };

  const renderComingSoonModal = () => (
    <Modal
      visible={showComingSoon}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <View style={styles.modalIconContainer}>
              <Shield size={48} color="#10b981" />
            </View>
            <Text style={styles.modalTitle}>Zero-Cost Verification</Text>
            <Text style={styles.modalSubtitle}>Implementation in Progress</Text>
          </View>

          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <CheckCircle size={20} color="#10b981" />
              <Text style={styles.featureText}>Manual document review by admin</Text>
            </View>
            <View style={styles.featureItem}>
              <CheckCircle size={20} color="#10b981" />
              <Text style={styles.featureText}>Secure storage in Supabase</Text>
            </View>
            <View style={styles.featureItem}>
              <CheckCircle size={20} color="#10b981" />
              <Text style={styles.featureText}>24-hour processing time</Text>
            </View>
            <View style={styles.featureItem}>
              <CheckCircle size={20} color="#10b981" />
              <Text style={styles.featureText}>Completely FREE (saves $7)</Text>
            </View>
          </View>

          <View style={styles.comparisonCard}>
            <Text style={styles.comparisonTitle}>Cost Comparison</Text>
            <View style={styles.comparisonRow}>
              <Text style={styles.comparisonLabel}>Persona API:</Text>
              <Text style={styles.comparisonPrice}>$7 per check</Text>
            </View>
            <View style={styles.comparisonRow}>
              <Text style={styles.comparisonLabel}>Our System:</Text>
              <Text style={styles.comparisonFree}>FREE</Text>
            </View>
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleComingSoonContinue}
            >
              <Text style={styles.modalButtonText}>Got it!</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#1e293b" />
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>ID Verification</Text>
        </View>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          <View style={styles.headingContainer}>
            <Shield size={28} color="#4f46e5" />
            <Text style={styles.heading}>
              {completed
                ? 'Verification Successful'
                : error
                  ? 'Verification Error'
                  : 'Verify Your Identity'}
            </Text>
          </View>

          {renderProgressBar()}

          {error && (
            <View style={styles.errorContainer}>
              <AlertTriangle size={24} color="#ef4444" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {completed ? (
            <View style={styles.completedContainer}>
              <CheckCircle size={64} color="#10b981" />
              <Text style={styles.completedText}>
                Your identity has been successfully verified!
              </Text>
              <TouchableOpacity style={styles.completeButton} onPress={handleContinue}>
                <Text style={styles.completeButtonText}>Continue</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              {renderSteps()}

              {showDocumentSelection ? (
                renderDocumentTypeSelection()
              ) : verificationUrl ? (
                <View style={styles.webviewContainer}>
                  <WebView
                    ref={webViewRef}
                    source={{ uri: verificationUrl }}
                    style={styles.webview}
                    onNavigationStateChange={handleNavigationStateChange}
                    incognito={true}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    startInLoadingState={true}
                    renderLoading={() => (
                      <View style={styles.webviewLoading}>
                        <ActivityIndicator size="large" color="#6366F1" />
                        <Text style={styles.webviewLoadingText}>Loading verification...</Text>
                      </View>
                    )}
                  />
                </View>
              ) : (
                <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
                  <RefreshCw size={18} color="#fff" style={styles.buttonIcon} />
                  <Text style={styles.retryButtonText}>Start Verification</Text>
                </TouchableOpacity>
              )}
            </>
          )}

          {verifying && (
            <View style={styles.verifyingContainer}>
              <ActivityIndicator size="large" color="#6366F1" />
              <Text style={styles.verifyingText}>Checking verification status...</Text>
            </View>
          )}

          {!completed && !verifying && !loading && sessionId && (
            <TouchableOpacity style={styles.checkButton} onPress={checkVerificationStatus}>
              <Text style={styles.checkButtonText}>Check Verification Status</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </ScrollView>

      {/* Liveness Detection Modal */}
      <Modal
        visible={showLivenessDetection}
        animationType="slide"
        onRequestClose={cancelLivenessDetection}
        statusBarTranslucent={true}
      >
        <LivenessDetection
          onComplete={continueToPersaonVerification}
          onCancel={cancelLivenessDetection}
        />
      </Modal>

      {/* Footer with privacy information */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          By continuing, you agree to our Terms of Service and Privacy Policy
        </Text>
        <TouchableOpacity
          style={styles.privacyLink}
          onPress={() => Linking.openURL('https://roomiematch.com/privacy')}
        >
          <Text style={styles.linkText}>Learn more</Text>
          <ExternalLink size={16} color="#6366F1" />
        </TouchableOpacity>
      </View>

      {renderComingSoonModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  backButton: {
    padding: 8,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginRight: 24, // compensate for back button
  },
  content: {
    padding: 16,
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  heading: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginLeft: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  // Progress bar
  progressContainer: {
    marginBottom: 24,
    width: '100%',
  },
  progressBackground: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: 4,
    backgroundColor: '#6366F1',
    borderRadius: 2,
  },
  progressText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    color: '#4B5563',
  },
  // Steps
  stepsContainer: {
    marginBottom: 24,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepCompleteCircle: {
    backgroundColor: '#10b981',
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748b',
  },
  stepText: {
    fontSize: 16,
    color: '#64748b',
    flex: 1,
  },
  stepCompleteText: {
    color: '#10b981',
    fontWeight: '500',
  },
  stepDivider: {
    height: 24,
    width: 1,
    backgroundColor: '#e2e8f0',
    marginLeft: 15,
    marginBottom: 12,
  },
  // Document Type Selection
  documentSelectionContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  savingsHighlight: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  savingsText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10b981',
  },
  documentsContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 16,
  },
  documentOption: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#e2e8f0',
  },
  documentOptionSelected: {
    borderColor: '#10b981',
    backgroundColor: 'rgba(16, 185, 129, 0.02)',
  },
  documentOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  documentOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentIconSelected: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  documentInfo: {
    flex: 1,
  },
  documentTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginRight: 8,
  },
  documentTitleSelected: {
    color: '#10b981',
  },
  recommendedBadge: {
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#92400e',
  },
  documentDescription: {
    fontSize: 14,
    color: '#64748b',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#10b981',
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10b981',
  },
  processInfo: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  processTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 12,
  },
  processSteps: {
    gap: 8,
  },
  processStep: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  processStepText: {
    fontSize: 14,
    color: '#64748b',
  },
  continueButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  // Error
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorText: {
    marginLeft: 12,
    fontSize: 14,
    color: '#ef4444',
    flex: 1,
  },
  // Webview
  webviewContainer: {
    flex: 1,
    height: 500,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  webview: {
    flex: 1,
  },
  webviewLoading: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  webviewLoadingText: {
    marginTop: 12,
    fontSize: 14,
    color: '#4B5563',
  },
  // Completed
  completedContainer: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    marginBottom: 24,
  },
  completedText: {
    fontSize: 16,
    color: '#047857',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  completeButton: {
    backgroundColor: '#10b981',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 10,
    width: '100%',
    alignItems: 'center',
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  // Verify and Retry Buttons
  checkButton: {
    backgroundColor: 'transparent',
    borderColor: '#6366F1',
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  checkButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366F1',
  },
  retryButton: {
    backgroundColor: '#6366F1',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
  buttonIcon: {
    marginRight: 8,
  },
  // Verifying state
  verifyingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginVertical: 16,
  },
  verifyingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    color: '#4B5563',
  },
  // Liveness detection
  livenessContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  footer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  footerText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 8,
  },
  privacyLink: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366F1',
    marginRight: 4,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  modalIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  featuresList: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#1a202c',
    flex: 1,
  },
  comparisonCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  comparisonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a202c',
    marginBottom: 12,
  },
  comparisonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  comparisonLabel: {
    fontSize: 14,
    color: '#64748b',
  },
  comparisonPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ef4444',
    textDecorationLine: 'line-through',
  },
  comparisonFree: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10b981',
  },
  modalActions: {
    marginTop: 'auto',
  },
  modalButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
