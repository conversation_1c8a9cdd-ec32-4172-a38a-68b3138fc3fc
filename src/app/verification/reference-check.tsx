import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { ChevronLeft, Plus, Check, Clock, AlertCircle, Mail, Phone } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useAuth } from '@context/AuthContext';
import { referenceCheckService } from '@services/referenceCheckService';
import { ReferenceCheck } from '@components/verification/ReferenceCheck';
import { useColorFix } from '@hooks/useColorFix';

interface ReferenceRequest {
  id: string;
  reference_type: 'landlord' | 'employer' | 'roommate' | 'personal';
  reference_name: string;
  reference_email: string;
  reference_phone?: string;
  status: 'pending' | 'sent' | 'completed' | 'expired' | 'failed';
  created_at: string;
  completed_at?: string;
}

interface ReferenceSession {
  id: string;
  required_references: number;
  completed_references: number;
  status: 'in_progress' | 'completed' | 'failed' | 'expired';
  overall_score?: number;
  verification_level: 'basic' | 'standard' | 'premium';
}

export default function ReferenceCheckScreen() {
  const { fix } = useColorFix();
  const insets = useSafeAreaInsets();
  const { state } = useAuth();
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState<ReferenceSession | null>(null);
  const [requests, setRequests] = useState<ReferenceRequest[]>([]);
  const [showAddReference, setShowAddReference] = useState(false);

  useEffect(() => {
    loadReferenceData();
  }, []);

  const loadReferenceData = async () => {
    try {
      setLoading(true);
      if (!state.user?.id) return;

      // Get reference check status
      const statusResponse = await referenceCheckService.getReferenceCheckStatus(state.user.id);

      if (statusResponse.data) {
        setSession(statusResponse.data.session);
        setRequests(statusResponse.data.requests || []);
      } else {
        // Start a new reference check session if none exists
        const startResponse = await referenceCheckService.startReferenceCheck(state.user.id);
        if (startResponse.data) {
          setSession(startResponse.data);
        }
      }
    } catch (error) {
      console.error('Error loading reference data:', error);
      Alert.alert('Error', 'Failed to load reference check data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddReference = async (referenceData: {
    reference_type: 'landlord' | 'employer' | 'roommate' | 'personal';
    reference_name: string;
    reference_email: string;
    reference_phone?: string;
    relationship_description?: string;
  }) => {
    try {
      if (!state.user?.id) return;

      const response = await referenceCheckService.addReferenceRequest(
        state.user.id,
        referenceData
      );

      if (response.data) {
        setRequests(prev => [...prev, response.data]);
        setShowAddReference(false);
        Alert.alert(
          'Reference Added',
          'Your reference has been added and an invitation will be sent shortly.'
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to add reference');
      }
    } catch (error) {
      console.error('Error adding reference:', error);
      Alert.alert('Error', 'Failed to add reference');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check size={20} color={fix("#10B981", "#10B981")} />;
      case 'sent':
      case 'pending':
        return <Clock size={20} color={fix("#F59E0B", "#F59E0B")} />;
      case 'failed':
      case 'expired':
        return <AlertCircle size={20} color={"#EF4444"} />;
      default:
        return <Clock size={20} color="#6B7280" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10B981';
      case 'sent':
      case 'pending':
        return '#F59E0B';
      case 'failed':
      case 'expired':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getProgressPercentage = () => {
    if (!session) return 0;
    return (session.completed_references / session.required_references) * 100;
  };

  if (loading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color="#1e293b" />
          </TouchableOpacity>
          <Text style={styles.title}>Reference Checks</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366F1" />
          <Text style={styles.loadingText}>Loading reference data...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color="#1e293b" />
        </TouchableOpacity>
        <Text style={styles.title}>Reference Checks</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Progress Card */}
        {session && (
          <View style={styles.progressCard}>
            <Text style={styles.progressTitle}>Verification Progress</Text>
            <View style={styles.progressInfo}>
              <Text style={styles.progressText}>
                {session.completed_references} of {session.required_references} references completed
              </Text>
              <Text style={styles.progressPercentage}>{Math.round(getProgressPercentage())}%</Text>
            </View>
            <View style={styles.progressBar}>
              <View style={[styles.progressBarFill, { width: `${getProgressPercentage()}%` }]} />
            </View>
            {session.overall_score && (
              <Text style={styles.overallScore}>
                Overall Score: {session.overall_score.toFixed(1)}/5.0
              </Text>
            )}
          </View>
        )}

        {/* Reference Requests */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your References</Text>
            <TouchableOpacity style={styles.addButton} onPress={() => setShowAddReference(true)}>
              <Plus size={20} color="#6366F1" />
              <Text style={styles.addButtonText}>Add Reference</Text>
            </TouchableOpacity>
          </View>

          {requests.length === 0 ? (
            <View style={styles.emptyState}>
              <Mail size={48} color="#D1D5DB" />
              <Text style={styles.emptyStateTitle}>No References Added</Text>
              <Text style={styles.emptyStateDescription}>
                Add references from landlords, employers, or previous roommates to build trust.
              </Text>
            </View>
          ) : (
            requests.map(request => (
              <View key={request.id} style={styles.referenceCard}>
                <View style={styles.referenceHeader}>
                  <View style={styles.referenceInfo}>
                    <Text style={styles.referenceName}>{request.reference_name}</Text>
                    <Text style={styles.referenceType}>
                      {request.reference_type.charAt(0).toUpperCase() +
                        request.reference_type.slice(1)}
                    </Text>
                  </View>
                  <View style={styles.statusContainer}>
                    {getStatusIcon(request.status)}
                    <Text style={[styles.statusText, { color: getStatusColor(request.status) }]}>
                      {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                    </Text>
                  </View>
                </View>

                <View style={styles.referenceContacts}>
                  <View style={styles.contactItem}>
                    <Mail size={16} color="#6B7280" />
                    <Text style={styles.contactText}>{request.reference_email}</Text>
                  </View>
                  {request.reference_phone && (
                    <View style={styles.contactItem}>
                      <Phone size={16} color="#6B7280" />
                      <Text style={styles.contactText}>{request.reference_phone}</Text>
                    </View>
                  )}
                </View>

                <Text style={styles.requestDate}>
                  Added: {new Date(request.created_at).toLocaleDateString()}
                </Text>
                {request.completed_at && (
                  <Text style={styles.completedDate}>
                    Completed: {new Date(request.completed_at).toLocaleDateString()}
                  </Text>
                )}
              </View>
            ))
          )}
        </View>

        {/* Info Section */}
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>Why Add References?</Text>
          <Text style={styles.infoText}>
            References help potential roommates trust you more. We'll send a quick survey to your
            references asking about your reliability, cleanliness, and communication style.
          </Text>
          <View style={styles.infoPoints}>
            <Text style={styles.infoPoint}>• Builds trust with potential roommates</Text>
            <Text style={styles.infoPoint}>• Increases your response rate</Text>
            <Text style={styles.infoPoint}>• Quick 2-minute survey for references</Text>
          </View>
        </View>
      </ScrollView>

      {/* Add Reference Modal */}
      {showAddReference && (
        <ReferenceCheck
          onSubmit={handleAddReference}
          onCancel={() => setShowAddReference(false)}
          mode="modal"
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#ffffff',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  progressCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#64748b',
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E2E8F0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 12,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#6366F1',
  },
  overallScore: {
    fontSize: 14,
    fontWeight: '500',
    color: '#059669',
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6366F1',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    backgroundColor: '#ffffff',
    borderRadius: 12,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    paddingHorizontal: 24,
    lineHeight: 20,
  },
  referenceCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  referenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  referenceInfo: {
    flex: 1,
  },
  referenceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  referenceType: {
    fontSize: 14,
    color: '#6366F1',
    textTransform: 'capitalize',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  referenceContacts: {
    marginBottom: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  contactText: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 8,
  },
  requestDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  completedDate: {
    fontSize: 12,
    color: '#059669',
    marginTop: 2,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
    marginBottom: 12,
  },
  infoPoints: {
    marginTop: 8,
  },
  infoPoint: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
});
