import React, { useEffect } from 'react';
import { Text, View, StyleSheet, ActivityIndicator } from 'react-native';
import { Link, Stack, useRouter } from 'expo-router';
import { useAuthAdapter } from '@context/AuthContextAdapter';

export default function NotFoundScreen() {
  const router = useRouter();
  const { authState, authLoaded } = useAuthAdapter();

  useEffect(() => {
    // Auto-redirect after 3 seconds if auth is loaded
    if (authLoaded) {
      const timer = setTimeout(() => {
        if (authState.isAuthenticated && authState.authStatus === 'authenticated') {
          console.log('🔄 [NotFound] Redirecting authenticated user to main app');
          router.replace('/(tabs)');
        } else {
          console.log('🔄 [NotFound] Redirecting unauthenticated user to login');
          router.replace('/(auth)/login');
        }
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [authLoaded, authState.isAuthenticated, authState.authStatus, router]);

  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <View style={styles.container}>
        <Text style={styles.title}>This screen doesn't exist.</Text>
        
        {!authLoaded ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#4F46E5" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : (
          <>
            <Text style={styles.subtitle}>
              We'll redirect you to the right place in a moment...
            </Text>
            
            <View style={styles.linksContainer}>
              {authState.isAuthenticated ? (
                <Link href="/(tabs)" style={styles.link}>
                  <Text style={styles.linkText}>Go to Home</Text>
                </Link>
              ) : (
                <Link href="/(auth)/login" style={styles.link}>
                  <Text style={styles.linkText}>Go to Login</Text>
                </Link>
              )}
            </View>
          </>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#6B7280',
  },
  linksContainer: {
    marginTop: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
    backgroundColor: '#4F46E5',
    borderRadius: 8,
  },
  linkText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },
});
