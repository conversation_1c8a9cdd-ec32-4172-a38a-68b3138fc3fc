import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  TextInput,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Shield, Mail } from 'lucide-react-native';
import { colors } from '@constants/colors';
import * as Haptics from 'expo-haptics';

import EnhancedEmailVerificationHandler from '@components/auth/EnhancedEmailVerificationHandler';
import EnhancedEmailVerificationConfirmation from '@components/auth/EnhancedEmailVerificationConfirmation';
import { Button } from '@design-system';
import { verificationService as emailVerificationService } from '@services';
import { validateEmail } from '@utils/validation';

export default function VerifyEmailScreen() {
  const router = useRouter();
  const { token, type, email } = useLocalSearchParams<{ token: string; type: string; email: string }>();
  
  // State for resend functionality
  const [resendEmail, setResendEmail] = useState('');
  const [resendStatus, setResendStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [resendError, setResendError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(0);
  
  // Determine if this is a valid verification link
  const isValidVerificationLink = token && type === 'email_confirmation' && email;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      <ScrollView
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.push('/(auth)/login')}
          accessibilityLabel="Back to login"
        >
          <ArrowLeft size={24} color={colors.gray[800]} />
        </TouchableOpacity>

        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Shield size={32} color={colors.white} />
          </View>
          <Text style={styles.title}>Email Verification</Text>
          <Text style={styles.subtitle}>
            {isValidVerificationLink 
              ? 'We\'re verifying your email address to secure your account.'
              : 'Please check your email for a verification link or request a new one.'}
          </Text>
        </View>

        {/* Enhanced Email Verification Confirmation */}
        {isValidVerificationLink ? (
          <EnhancedEmailVerificationConfirmation 
            token={token}
            email={email}
            onDismiss={() => router.replace('/(auth)/login')}
            onVerificationComplete={(success) => {
              // Provide haptic feedback based on verification result
              if (success) {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              } else {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              }
            }}
            redirectPath="/(tabs)"
          />
        ) : (
          <View style={styles.infoContainer}>
            <View style={styles.iconWrapper}>
              <Mail size={32} color={colors.primary[500]} />
            </View>
            <Text style={styles.infoTitle}>Verification Required</Text>
            <Text style={styles.infoText}>
              It looks like you're missing a valid verification link. Please check your email for the verification link or enter your email below to request a new one.
            </Text>
            
            <View style={styles.resendForm}>
              <TextInput
                style={styles.emailInput}
                placeholder="Enter your email address"
                value={resendEmail}
                onChangeText={setResendEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
              
              <Button
                onPress={async () => {
                  if (!resendEmail || !validateEmail(resendEmail)) {
                    setResendError('Please enter a valid email address');
                    return;
                  }
                  
                  setResendStatus('loading');
                  setResendError(null);
                  
                  try {
                    const { success, error } = await emailVerificationService.resendVerificationEmail(resendEmail);
                    
                    if (success) {
                      setResendStatus('success');
                      setCountdown(60);
                      // Start countdown timer
                      const timer = setInterval(() => {
                        setCountdown((prev) => {
                          if (prev <= 1) {
                            clearInterval(timer);
                            return 0;
                          }
                          return prev - 1;
                        });
                      }, 1000);
                      
                      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                    } else {
                      setResendStatus('error');
                      setResendError(error || 'Failed to resend verification email');
                      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
                    }
                  } catch (err) {
                    setResendStatus('error');
                    setResendError('An unexpected error occurred');
                    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
                  }
                }}
                disabled={resendStatus === 'loading' || countdown > 0}
                isLoading={resendStatus === 'loading'}
                style={styles.resendButton}
              >
                {countdown > 0 ? `Resend in ${countdown}s` : 'Send Verification Email'}
              </Button>
              
              {resendError && (
                <Text style={styles.errorText}>{resendError}</Text>
              )}
              
              {resendStatus === 'success' && (
                <Text style={styles.successText}>
                  Verification email sent! Please check your inbox and spam folder.
                </Text>
              )}
            </View>
            
            <TouchableOpacity 
              style={styles.linkButton}
              onPress={() => router.push('/(auth)/login')}
            >
              <Text style={styles.linkText}>Return to Login</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flexGrow: 1,
    padding: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: colors.primary[500],
    shadowColor: colors.primary[500],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  infoContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    alignItems: 'center',
  },
  iconWrapper: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
    textAlign: 'center',
  },
  infoText: {
    fontSize: 16,
    color: colors.gray[700],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  resendForm: {
    width: '100%',
    marginTop: 16,
    marginBottom: 16,
  },
  emailInput: {
    width: '100%',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
    color: colors.gray[800],
  },
  resendButton: {
    marginTop: 8,
    width: '100%',
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444', // Red color
    marginTop: 8,
    textAlign: 'center',
  },
  successText: {
    fontSize: 14,
    color: '#10B981', // Green color
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: colors.primary[50],
  },
  linkText: {
    fontSize: 16,
    color: colors.primary[600],
    fontWeight: '600',
  },
});
