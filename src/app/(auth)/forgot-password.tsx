import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  Keyboard,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Mail, ArrowLeft, Shield, Check, AlertTriangle } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Input } from '@components/ui';
import { Button } from '@design-system';
import { colors } from '@constants/colors';
import { logger } from '@services/loggerService';
import { validateEmail } from '@utils/validation';
import { passwordResetService } from '@services/PasswordResetService';
import EnhancedPasswordResetNotification from '@components/auth/EnhancedPasswordResetNotification';
import { ASYNC_STORAGE_KEYS } from '@utils/constants';

export default function ForgotPasswordScreen() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  // Validate email and provide feedback
  const validateEmailInput = (email: string): boolean => {
    if (!email.trim()) {
      setError('Please enter your email address');
      return false;
    } 
    
    if (!validateEmail(email.trim())) {
      setError('Please enter a valid email address');
      return false;
    }
    
    return true;
  };

  // Handle password reset request
  const handleResetPassword = async () => {
    // Reset error state
    setError(null);

    // Validate email
    if (!validateEmailInput(email)) {
      return;
    }

    // Dismiss keyboard
    Keyboard.dismiss();

    // Provide haptic feedback before starting the request
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    setLoading(true);

    try {
      logger.info('Password reset requested', 'ForgotPasswordScreen', { email });
      
      // Store the email for which we're resetting the password
      await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.PENDING_PASSWORD_RESET_EMAIL, email.trim());
      
      // Use the PasswordResetService to request a password reset
      const { success: resetSuccess, error: resetError } = await passwordResetService.requestPasswordReset(email.trim());

      if (!resetSuccess) {
        logger.error('Password reset failed', 'ForgotPasswordScreen', { error: resetError, email });
        setError(resetError || 'Failed to send reset instructions');
        
        // Remove the pending email from storage since it failed
        await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.PENDING_PASSWORD_RESET_EMAIL);
        
        // Provide error haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      } else {
        logger.info('Password reset email sent', 'ForgotPasswordScreen', { email });
        
        // Provide success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        setSuccess(true);
        setEmailSent(true);
      }
    } catch (err) {
      logger.error('Password reset error', 'ForgotPasswordScreen', { err, email });
      setError(err instanceof Error ? err.message : 'Failed to send reset instructions');
      
      // Remove the pending email from storage since it failed
      await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.PENDING_PASSWORD_RESET_EMAIL);
      
      // Provide error haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      <ScrollView
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.push('/(auth)/login')}
          accessibilityLabel="Back to login"
        >
          <ArrowLeft size={24} color={colors.gray[800]} />
        </TouchableOpacity>

        <View style={styles.header}>
          <View style={[styles.iconContainer, error ? styles.iconError : (success ? styles.iconSuccess : styles.iconDefault)]}>
            {loading ? (
              <ActivityIndicator size="large" color={colors.white} />
            ) : error ? (
              <AlertTriangle size={32} color={colors.white} />
            ) : success ? (
              <Check size={32} color={colors.white} />
            ) : (
              <Mail size={32} color={colors.white} />
            )}
          </View>
          <Text style={styles.title}>
            {success ? 'Check Your Email' : 'Forgot Password'}
          </Text>
          <Text style={styles.subtitle}>
            {success
              ? 'We have sent password reset instructions to your email.'
              : 'Enter your email address and we will send you instructions to reset your password.'}
          </Text>
        </View>

        <View style={styles.formContainer}>
          {success ? (
            <EnhancedPasswordResetNotification 
              email={email}
              onResend={handleResetPassword}
              onDismiss={() => {
                setSuccess(false);
                setEmailSent(false);
              }}
            />
          ) : (
            <View style={styles.form}>
              <Input
                label="Email"
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  // Clear error when user types
                  if (error) setError(null);
                }}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                leftIcon={Mail}
                error={error || undefined}
              />

              <Button 
                onPress={handleResetPassword} 
                isLoading={loading} 
                style={styles.button}
                disabled={!email.trim()}
              >
                Send Reset Instructions
              </Button>

              <TouchableOpacity
                style={styles.linkButton}
                onPress={() => router.push('/(auth)/login')}
              >
                <Text style={styles.linkText}>Back to Login</Text>
              </TouchableOpacity>
              
              <View style={styles.securityNote}>
                <Text style={styles.securityText}>
                  For security reasons, we'll send a link to your email address that will expire in 24 hours.
                </Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flexGrow: 1,
    padding: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  iconDefault: {
    backgroundColor: colors.primary[500],
    shadowColor: colors.primary[500],
  },
  iconSuccess: {
    backgroundColor: colors.success[500],
    shadowColor: colors.success[500],
  },
  iconError: {
    backgroundColor: colors.error[500],
    shadowColor: colors.error[500],
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  form: {
    gap: 20,
  },
  button: {
    marginTop: 24,
  },
  errorText: {
    color: colors.error[500],
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  linkText: {
    fontSize: 14,
    color: colors.primary[500],
    fontWeight: '500',
  },
  securityNote: {
    marginTop: 24,
    padding: 12,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.gray[300],
  },
  securityText: {
    fontSize: 13,
    color: colors.gray[600],
    lineHeight: 18,
  },
});