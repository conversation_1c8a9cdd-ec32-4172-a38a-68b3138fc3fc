import { useState, useEffect } from 'react';

// Image picker functionality now handled by ProfileImageUploader component
import { useRouter } from 'expo-router';
import { Calendar, MapPin, Briefcase, User, Shield, ChevronLeft } from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
} from 'react-native';

import { Input } from '@components/ui';
import { Button } from '@design-system';
import { APP_CONFIG } from '@utils/constants';
import { getSupabaseClient } from "@services/supabaseService";
import ProfileImageUploader from '@components/profile/ProfileImageUploader';
import { useAuth } from '@context/AuthContext';
// Validation functions implemented directly in this file
const validateDate = (date: string): boolean => {
  // Basic YYYY-MM-DD format validation
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(date)) return false;
  
  // Check if it's a valid date
  const parsedDate = new Date(date);
  return !isNaN(parsedDate.getTime());
};

const validatePhone = (phone: string): boolean => {
  // Basic phone number validation (at least 10 digits)
  const digits = phone.replace(/\D/g, '');
  return digits.length >= 10;
};

interface OnboardingData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  occupation: string;
  bio: string;
  phoneNumber: string;
  avatarUrl: string;
  location: string;
}

export default function OnboardingScreen() {
  const router = useRouter();
  const { authState, isAuthenticated } = useAuth();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [authReady, setAuthReady] = useState(false);
  const [formData, setFormData] = useState<OnboardingData>({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    occupation: '',
    bio: '',
    phoneNumber: '',
    avatarUrl: '',
    location: '',
  });

  // Wait for authentication to stabilize after registration
  useEffect(() => {
    const checkAuthState = async () => {
      // Give auth state time to settle after registration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      try {
        // Double-check authentication with fresh session
        const { data: { user } } = await getSupabaseClient().auth.getUser();
        if (user) {
          console.log('✅ Authentication confirmed for onboarding:', user.id);
          setAuthReady(true);
        } else {
          console.log('❌ No authenticated user found, redirecting to login');
          Alert.alert(
            'Authentication Required',
            'Please log in to complete your profile setup.',
            [{ text: 'OK', onPress: () => router.replace('/(auth)/login') }]
          );
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setError('Authentication verification failed. Please try logging in again.');
      }
    };

    checkAuthState();
  }, []);

  // Image upload is now handled by ProfileImageUploader component

  const validateStep = (currentStep: number): boolean => {
    setError(null);

    switch (currentStep) {
      case 1:
        if (!formData.firstName || !formData.lastName) {
          setError('Please enter your full name');
          return false;
        }
        if (!formData.avatarUrl) {
          setError('Please upload a profile photo');
          return false;
        }
        break;

      case 2:
        if (!formData.dateOfBirth || !validateDate(formData.dateOfBirth)) {
          setError('Please enter a valid date of birth (YYYY-MM-DD)');
          return false;
        }
        if (!formData.phoneNumber || !validatePhone(formData.phoneNumber)) {
          setError('Please enter a valid phone number');
          return false;
        }
        break;

      case 3:
        if (!formData.location) {
          setError('Please enter your location');
          return false;
        }
        if (!formData.bio || formData.bio.length < 50) {
          setError('Please write a bio (minimum 50 characters)');
          return false;
        }
        break;
    }

    return true;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      if (step < 3) {
        setStep(step + 1);
      } else {
        handleSubmit();
      }
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = async () => {
    setError(null);
    setLoading(true);

    try {
      // Get user data
      const { data: userData } = await getSupabaseClient().auth.getUser();
      const user = userData?.user;

      if (!user) {
        throw new Error('User not authenticated');
      }

      console.log('Submitting profile data:', {
        firstName: formData.firstName,
        lastName: formData.lastName,
        dateOfBirth: formData.dateOfBirth,
        occupation: formData.occupation,
        bio: formData.bio,
        phoneNumber: formData.phoneNumber,
        avatarUrl: formData.avatarUrl,
      });

      // Update user profile in Supabase
      const { error } = await getSupabaseClient()
        .from('user_profiles')
        .upsert(
          {
            id: user.id,
            first_name: formData.firstName,
            last_name: formData.lastName,
            date_of_birth: formData.dateOfBirth,
            occupation: formData.occupation,
            bio: formData.bio,
            phone_number: formData.phoneNumber,
            avatar_url: formData.avatarUrl,
            preferences: {
              location: formData.location,
            },
            profile_completion: 100, // Mark profile as complete
            updated_at: new Date().toISOString(),
          },
          { onConflict: 'id' }
        );

      if (error) {
        console.error('Profile update error:', error);
        throw error;
      }

      console.log('Profile updated successfully');
      router.replace('/(tabs)');
    } catch (err) {
      console.error('Profile submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while authentication settles
  if (!authReady) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Shield size={48} color="#6366F1" />
        <Text style={[styles.title, { marginTop: 16 }]}>Setting up your profile...</Text>
        <Text style={[styles.subtitle, { textAlign: 'center', marginTop: 8 }]}>
          Please wait while we prepare your zero-cost verification dashboard
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.content}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.header}>
        <Shield size={32} color="#6366F1" />
        <Text style={styles.title}>Complete Your Profile</Text>
        <Text style={styles.subtitle}>Step {step} of 3</Text>
        <View style={styles.progressBar}>
          <View style={[styles.progress, { width: `${(step / 3) * 100}%` }]} />
        </View>
      </View>

      {step === 1 && (
        <View style={styles.step}>
          <ProfileImageUploader
            currentImageUrl={formData.avatarUrl}
            onUploadSuccess={(imageUrl) => {
              console.log('✅ Profile image uploaded successfully:', imageUrl);
              console.log('📱 Setting avatar URL in form data:', imageUrl);
              setFormData(prev => {
                const updated = { ...prev, avatarUrl: imageUrl };
                console.log('📋 Updated form data:', updated);
                return updated;
              });
              setError(null); // Clear any previous errors
            }}
            onUploadError={(error) => {
              console.error('❌ Profile image upload failed:', error);
              setError(`Failed to upload image: ${error}`);
            }}
            size={120}
            disabled={loading}
          />
          
          {/* Debug info */}
          {__DEV__ && formData.avatarUrl && (
            <Text style={{ fontSize: 10, color: '#666', textAlign: 'center', marginTop: 8 }}>
              Debug: {formData.avatarUrl}
            </Text>
          )}

          <Input
            label="First Name"
            value={formData.firstName}
            onChangeText={text => setFormData(prev => ({ ...prev, firstName: text }))}
            placeholder="Enter your first name"
          />

          <Input
            label="Last Name"
            value={formData.lastName}
            onChangeText={text => setFormData(prev => ({ ...prev, lastName: text }))}
            placeholder="Enter your last name"
          />
        </View>
      )}

      {step === 2 && (
        <View style={styles.step}>
          <Input
            label="Date of Birth"
            value={formData.dateOfBirth}
            onChangeText={text => setFormData(prev => ({ ...prev, dateOfBirth: text }))}
            placeholder="YYYY-MM-DD"
          />

          <Input
            label="Phone Number"
            value={formData.phoneNumber}
            onChangeText={text => setFormData(prev => ({ ...prev, phoneNumber: text }))}
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
          />

          <Input
            label="Occupation"
            value={formData.occupation}
            onChangeText={text => setFormData(prev => ({ ...prev, occupation: text }))}
            placeholder="What do you do?"
          />
        </View>
      )}

      {step === 3 && (
        <View style={styles.step}>
          <Input
            label="Location"
            value={formData.location}
            onChangeText={text => setFormData(prev => ({ ...prev, location: text }))}
            placeholder="Where are you located?"
          />

          <Input
            label="Bio"
            value={formData.bio}
            onChangeText={text => setFormData(prev => ({ ...prev, bio: text }))}
            placeholder="Tell us about yourself..."
            multiline
            numberOfLines={4}
            maxLength={APP_CONFIG.VALIDATION.MAX_BIO_LENGTH}
          />
        </View>
      )}

      {error && <Text style={styles.errorText}>{error}</Text>}

      <View style={styles.buttons}>
        {step > 1 && (
          <Button 
            variant="outlined" 
            color="primary" 
            onPress={handleBack} 
            style={styles.backButton}
            leftIcon={<ChevronLeft size={16} color="#1890FF" />}
          >
            Back
          </Button>
        )}

        <Button
          variant="filled"
          color="primary"
          onPress={handleNext}
          rightIcon={step < 3 ? <ChevronLeft size={16} color="#FFFFFF" style={{ transform: [{ rotate: '180deg' }] }} /> : undefined}
          style={styles.nextButton}
          isLoading={loading}
        >
          {step < 3 ? 'Next' : 'Submit'}
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  content: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    marginBottom: 16,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E2E8F0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progress: {
    height: '100%',
    backgroundColor: '#6366F1',
    borderRadius: 2,
  },
  step: {
    gap: 16,
  },
  // Image upload styles now handled by ProfileImageUploader component
  bioInput: {
    height: 120,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    marginTop: 16,
  },
  buttons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
});
