import { useState, useRef, useEffect, useMemo } from 'react';

import { useRouter } from 'expo-router';
import {
  Shield,
  Mail,
  Lock,
  User,
  Briefcase,
  Chrome as Home,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
} from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Alert,
  SafeAreaView,
  TextInput,
  Keyboard,
} from 'react-native';
import { Stack } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { FormInput as Input } from '@components/ui';
import { Button } from '@design-system';
import { useTheme } from '@design-system';
import type { UserRole } from '../../types/auth';
import { supabase } from "@utils/supabaseUtils";
import { validateEmail, validatePassword } from '@utils/validation';
import { unifiedAuthService } from '@services/auth/UnifiedAuthService';
import { logger } from '@utils/logger';
import { platformKeyboard, platformSpacing, platformBorder } from '@utils/crossPlatformStyles';
import { useAuthAdapter } from '@context/AuthContextAdapter';
import { useAuthCompat } from '@hooks/useAuthCompat';

// Role configuration with colors
const ROLES = [
  {
    id: 'roommate_seeker' as UserRole,
    title: 'Looking for Roommates',
    description: 'Find compatible roommates to share living space',
    icon: User,
    color: '#10B981', // Green
    bgColor: '#F0FDF4'
  },
  {
    id: 'property_owner' as UserRole,
    title: 'Property Owner',
    description: 'List your property and find reliable tenants',
    icon: Home,
    color: '#8B5CF6', // Purple
    bgColor: '#FAF5FF'
  },
  {
    id: 'service_provider' as UserRole,
    title: 'Service Provider',
    description: 'Offer cleaning, maintenance, or other services',
    icon: Briefcase,
    color: '#3B82F6', // Blue
    bgColor: '#EFF6FF'
  }
];

const { width } = Dimensions.get('window');

export default function RegisterScreen() {
  const router = useRouter();
  const theme = useTheme();
  const { authState, authLoaded } = useAuthAdapter();
  const insets = useSafeAreaInsets();
  
  // Debug: Log the theme to ensure it's loaded correctly
  console.log('🎨 Theme loaded:', theme ? 'SUCCESS' : 'FAILED');
  console.log('🎨 Theme colors:', theme?.colors ? 'AVAILABLE' : 'MISSING');
  
  // Fallback theme to prevent errors
  const safeTheme = theme || {
    colors: {
      primary: '#6366F1',
      background: '#FFFFFF',
      surface: '#f8fafc',
      text: '#1e293b',
      textSecondary: '#475569',
      border: '#e2e8f0',
      error: '#ef4444',
      success: '#10b981'
    },
    spacing: { md: 16, lg: 24 },
    borderRadius: { md: 8 }
  };
  
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    passwordConfirm: '',
    selectedRole: null as UserRole | null,
  });

  // Add keyboard state management for iOS
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  
  // Add back missing state variables
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const inputRefs = useRef<{ [key: string]: TextInput }>({});

  // Additional fields for service provider registration
  const [businessName, setBusinessName] = useState('');
  const [businessDescription, setBusinessDescription] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [businessAddress, setBusinessAddress] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  // Service categories for provider registration
  const serviceCategories = [
    'Cleaning', 'Maintenance', 'Moving', 'Plumbing', 
    'Electrical', 'Furniture Assembly', 'Landscaping', 'Other'
  ];

  // Calculate total steps based on selected role
  const getTotalSteps = () => {
    if (formData.selectedRole === 'service_provider') {
      return 5; // 0: email/username, 1: password, 2: role, 3: business info, 4: contact/services
    }
    return 3; // 0: email/username, 1: password, 2: role
  };

  // Get maximum step index (0-based)
  const getMaxStep = () => getTotalSteps() - 1;

  // Add keyboard event listeners for iOS
  useEffect(() => {
    if (Platform.OS === 'ios') {
      const keyboardWillShowListener = Keyboard.addListener('keyboardWillShow', (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        setIsKeyboardVisible(true);
      });

      const keyboardWillHideListener = Keyboard.addListener('keyboardWillHide', () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      });

      return () => {
        keyboardWillShowListener?.remove();
        keyboardWillHideListener?.remove();
      };
    }
  }, []);

  // Scroll to input when focused (iOS specific)
  const scrollToInput = (inputKey: string) => {
    if (Platform.OS === 'ios' && inputRefs.current[inputKey]) {
      setTimeout(() => {
        inputRefs.current[inputKey]?.measureInWindow((x, y, width, height) => {
          const screenHeight = Dimensions.get('window').height;
          const keyboardTop = screenHeight - keyboardHeight;
          const inputBottom = y + height + 20; // Add some padding
          
          if (inputBottom > keyboardTop && scrollViewRef.current) {
            const scrollOffset = inputBottom - keyboardTop;
            scrollViewRef.current.scrollTo({ 
              x: currentStep * Dimensions.get('window').width, 
              y: scrollOffset, 
              animated: true 
            });
          }
        });
      }, 100);
    }
  };

  // Effect to scroll to the correct step when step changes
  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: currentStep * width, animated: true });
    }
  }, [currentStep]);

  // Memoized validation state to prevent infinite re-renders
  const isStepValid = useMemo(() => {
    if (currentStep === 0) {
      // First step: Email and username validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return formData.email.trim().length > 0 && 
             formData.username.trim().length > 0 && 
             emailRegex.test(formData.email.trim());
    }
    if (currentStep === 1) {
      // Second step: Password validation
      if (!formData.password || !formData.passwordConfirm) return false;
      if (formData.password !== formData.passwordConfirm) return false;
      
      // Basic password validation (minimum length check)
      return formData.password.length >= 8;
    }
    if (currentStep === 2) {
      // Third step: Role selection
      return !!formData.selectedRole;
    }
    if (currentStep === 3 && formData.selectedRole === 'service_provider') {
      // Fourth step: Business information (service providers only)
      return businessName.trim().length > 0 && 
             businessDescription.trim().length >= 50;
    }
    if (currentStep === 4 && formData.selectedRole === 'service_provider') {
      // Fifth step: Contact and services (service providers only)
      return contactPhone.trim().length > 0 && 
             businessAddress.trim().length > 0 && 
             selectedCategories.length > 0;
    }
    return false;
  }, [currentStep, formData.email, formData.username, formData.password, formData.passwordConfirm, formData.selectedRole, businessName, businessDescription, contactPhone, businessAddress, selectedCategories]);

  // Validate current step
  const validateStep = () => {
    console.log('🔍 Validating step', { 
      currentStep, 
      selectedRole: formData.selectedRole,
      businessName: businessName?.length || 0,
      businessDescription: businessDescription?.length || 0,
      isServiceProvider: formData.selectedRole === 'service_provider'
    });

    if (currentStep === 0) {
      // First step: Email and username
      if (!formData.email.trim()) {
        setError('Please enter your email');
        return false;
      }

      if (!formData.username.trim()) {
        setError('Please enter a username');
        return false;
      }

      // Basic email validation (more robust than the imported function)
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        setError('Please enter a valid email address');
        return false;
      }

      console.log('Step 0 validation passed');
      return true;
    }
    if (currentStep === 1) {
      // Second step: Password
      if (!formData.password) {
        setError('Please enter a password');
        return false;
      }

      if (!formData.passwordConfirm) {
        setError('Please confirm your password');
        return false;
      }

      if (formData.password !== formData.passwordConfirm) {
        setError('Passwords do not match');
        return false;
      }

      const passwordValidation = validatePassword(formData.password);
      if (passwordValidation && typeof passwordValidation === 'object' && 'error' in passwordValidation) {
        setError(passwordValidation.error as string);
        return false;
      } else if (typeof passwordValidation === 'string') {
        setError(passwordValidation);
        return false;
      }

      console.log('Step 1 validation passed');
      return true;
    }
    if (currentStep === 2) {
      // Third step: Role selection
      if (!formData.selectedRole) {
        setError('Please select your role');
        return false;
      }

      console.log('Step 2 validation passed');
      return true;
    }
    if (currentStep === 3 && formData.selectedRole === 'service_provider') {
      // Fourth step: Business information
      if (!businessName.trim()) {
        setError('Please enter your business name');
        return false;
      }

      if (!businessDescription.trim()) {
        setError('Please enter a business description');
        return false;
      }

      if (businessDescription.trim().length < 50) {
        setError('Business description must be at least 50 characters');
        return false;
      }

      console.log('Step 3 validation passed');
      return true;
    }
    if (currentStep === 4 && formData.selectedRole === 'service_provider') {
      // Fifth step: Contact and services
      if (!contactPhone.trim()) {
        setError('Please enter your contact phone number');
        return false;
      }

      if (!businessAddress.trim()) {
        setError('Please enter your business address');
        return false;
      }

      if (selectedCategories.length === 0) {
        setError('Please select at least one service category');
        return false;
      }

      console.log('Step 4 validation passed');
      return true;
    }

    return false;
  };

  // Handle next step
  const handleNext = () => {
    setError(null);
    
    // Prevent action if already loading
    if (loading) {
      console.log('⏳ Already processing, ignoring handleNext call');
      return;
    }
    
    // Enhanced debugging
    console.log('🔍 DEBUG: handleNext called', {
      currentStep,
      selectedRole: formData.selectedRole,
      totalSteps: getTotalSteps(),
      maxStep: getMaxStep(),
      businessName: businessName?.substring(0, 20) + '...',
      businessDescription: businessDescription?.length,
      isServiceProvider: formData.selectedRole === 'service_provider'
    });
    
    if (validateStep()) {
      const maxStep = getMaxStep();
      console.log('✅ Step validation passed', { currentStep, maxStep, willContinue: currentStep < maxStep });
      
      if (currentStep < maxStep) {
        console.log(`🔄 Moving from step ${currentStep} to ${currentStep + 1}`);
        setCurrentStep(currentStep + 1);
        // Directly scroll to the next step
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({ x: (currentStep + 1) * width, animated: true });
        }
      } else {
        // At final step, proceed with registration
        console.log(`🚀 At final step ${currentStep}, proceeding with registration`);
        handleRegister();
      }
    } else {
      console.log('❌ Step validation failed', { currentStep, selectedRole: formData.selectedRole });
    }
  };

  // Handle back
  const handleBack = () => {
    setError(null);
    if (currentStep > 0) {
      console.log(`Moving from step ${currentStep} to ${currentStep - 1}`);
      setCurrentStep(currentStep - 1);
      // Directly scroll to the previous step
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ x: (currentStep - 1) * width, animated: true });
      }
    }
  };

  const handleRegister = async () => {
    // Prevent duplicate registration attempts
    if (loading) {
      console.log('⏳ Registration already in progress, ignoring duplicate attempt');
      return;
    }

    console.log('🚀 Starting registration process', {
      currentStep,
      selectedRole: formData.selectedRole,
      totalSteps: getTotalSteps(),
      maxStep: getMaxStep(),
      hasAllBasicInfo: !!(formData.email && formData.password && formData.selectedRole && formData.username),
      isServiceProvider: formData.selectedRole === 'service_provider'
    });

    if (!formData.email || !formData.password || !formData.selectedRole || !formData.username) {
      console.log('❌ Missing basic registration fields');
      setError('Please fill in all fields and select a role');
      return;
    }

    // Additional validation for service providers
    if (formData.selectedRole === 'service_provider') {
      const hasServiceProviderInfo = !!(businessName && businessDescription && contactPhone && businessAddress && selectedCategories.length > 0);
      console.log('🔍 Service provider validation', {
        businessName: !!businessName,
        businessDescription: !!businessDescription,
        contactPhone: !!contactPhone,
        businessAddress: !!businessAddress,
        selectedCategories: selectedCategories.length,
        hasAllInfo: hasServiceProviderInfo
      });
      
      if (!hasServiceProviderInfo) {
        console.log('❌ Missing service provider information');
        setError('Please complete all service provider information');
        return;
      }
    }

    // Validate role is a valid role type
    const validRoles = ['roommate_seeker', 'property_owner', 'service_provider', 'admin'];
    if (!validRoles.includes(formData.selectedRole)) {
      setError('Invalid role selected');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Creating new user account', 'RegisterScreen.handleRegister', {
        role: formData.selectedRole,
        username: formData.username,
        isServiceProvider: formData.selectedRole === 'service_provider'
      });

      // Prepare signup data
      const signUpData: any = {
        email: formData.email,
        password: formData.password,
        username: formData.username,
        firstName: formData.username,
        role: formData.selectedRole
      };

      // Add service provider specific data
      if (formData.selectedRole === 'service_provider') {
        signUpData.serviceProviderData = {
          businessName,
          businessDescription,
          contactPhone,
          businessAddress,
          serviceCategories: selectedCategories
        };
      }

      // Use unified auth service for atomic user and profile creation
      const result = await unifiedAuthService.signUp(signUpData);

      if (!result.success) {
        logger.error('Registration failed', 'RegisterScreen.handleRegister', { error: result.error });
        throw new Error(result.error || 'Registration failed');
      }

      logger.info('User registration completed successfully with zero-cost verification', 'RegisterScreen.handleRegister', { 
        userId: result.user?.id,
        profileCompletion: result.profile?.profile_completion || 35,
        costSavings: '$57+ saved per verification cycle'
      });

      // Show success message with cost savings information
      Alert.alert(
        'Registration Complete - 100% FREE! 🎉',
        `Welcome to WeRoomies! You're saving $57+ per month with our zero-cost verification system.\n\n• Identity verification: $7 saved\n• Background check: $35 saved\n• Reference verification: $15 saved\n\nYour verification journey starts now!`,
        [
          {
            text: 'Start Verification',
            onPress: async () => {
              // Successfully created account and profile
              if (formData.selectedRole === 'service_provider') {
                // For service providers, wait longer for auth state propagation
                console.log('🔄 Waiting for auth state to propagate before provider navigation...');
                
                // Wait for auth state to fully propagate (increased delay)
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Verify auth state before navigation
                console.log('🔍 Checking auth state before navigation...', {
                  isAuthenticated: authState?.isAuthenticated,
                  authStatus: authState?.authStatus,
                  user: authState?.user ? 'User exists' : 'No user',
                  userId: authState?.user?.id
                });
                
                // If still not authenticated, wait a bit more
                if (!authState?.isAuthenticated) {
                  console.log('⏳ Auth state not ready, waiting additional time...');
                  await new Promise(resolve => setTimeout(resolve, 3000));
                }
                
                // Go directly to provider onboarding to complete setup
                console.log('🚀 Navigating directly to provider onboarding');
                router.replace('/provider/onboarding');
              } else {
                // Send regular users to the standard onboarding with shorter delay
                console.log('🔄 Waiting for auth state to propagate before onboarding navigation...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                console.log('🚀 Navigating to onboarding screen');
                router.replace('/(auth)/onboarding');
              }
            }
          }
        ]
      );
    } catch (err) {
      logger.error('Registration error', 'RegisterScreen.handleRegister', {
        error: err instanceof Error ? err.message : String(err)
      });
      
      if (err instanceof Error) {
        // Extract the most useful part of the error message
        const errorMessage = err.message;

        // Handle specific error cases with zero-cost verification context
        if (errorMessage.includes('User already registered') || errorMessage.includes('already exists')) {
          console.log('⚠️ User already exists, offering signin option');
          // Show an alert with options instead of just setting error text
          Alert.alert(
            'Account Already Exists',
            'An account with this email already exists. Would you like to sign in instead?',
            [
              {
                text: 'Try Different Email',
                style: 'cancel',
                onPress: () => {
                  console.log('User chose to try different email');
                  setError('Please use a different email address');
                  setCurrentStep(0); // Go back to first step to change email
                }
              },
              {
                text: 'Sign In Instead',
                onPress: () => {
                  console.log('User chose to sign in instead');
                  router.replace({
                    pathname: '/(auth)/login',
                    params: { 
                      email: formData.email, // Pre-fill the email
                      from: 'register' 
                    },
                  });
                }
              }
            ]
          );
          return;
        }
        if (errorMessage.includes('duplicate key') || errorMessage.includes('already exists')) {
          setError('An account with this email or username already exists');
        } else if (errorMessage.includes('weak password')) {
          setError('Please use a stronger password for better security');
        } else if (errorMessage.includes('not a valid UUID')) {
          setError('Authentication system error - please try again');
        } else if (errorMessage.includes('violates foreign key constraint')) {
          setError('Database connection error - please try again in a moment');
        } else if (errorMessage.includes('violates check constraint')) {
          setError('Invalid role selected - please choose a valid role');
        } else if (errorMessage.includes('permission denied')) {
          setError('Permission denied - please contact support');
        } else if (errorMessage.includes('generated column')) {
          setError('Profile creation error - this has been fixed, please try again');
        } else if (errorMessage.includes('display_name')) {
          setError('Profile setup error - retrying with corrected data...');
          // Auto-retry once for display_name errors
          setTimeout(() => {
            handleRegister();
          }, 1000);
          return;
        } else {
          // Provide user-friendly message while preserving technical details in logs
          console.log('❌ Generic registration error', { originalError: errorMessage });
          setError('Registration failed. Our zero-cost verification system is experiencing high demand. Please try again in a moment.');
          logger.error('Detailed registration error', 'RegisterScreen.handleRegister', { 
            originalError: errorMessage,
            context: 'zero_cost_verification_system'
          });
        }
      } else {
        console.log('❌ Unknown registration error', { error: err });
        setError('Failed to create account. Please try again later.');
      }
    } finally {
      setLoading(false);
      console.log('✅ Registration process completed (success or failure)');
    }
  };

  const styles = useMemo(() => createStyles(theme, insets), [theme, insets]);

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidView}
      behavior={Platform.OS === 'ios' ? 'position' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? -50 : 0}
      enabled={Platform.OS === 'ios'}
    >
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: 'Create Account',
            headerStyle: {
              backgroundColor: '#F8FAFC',
            },
            headerTitleStyle: {
              color: '#1E293B',
              fontWeight: '600',
            },
            headerTintColor: '#64748B',
          }}
        />
        
        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          {Array.from({ length: getTotalSteps() }, (_, index) => (
            <View key={index} style={styles.progressStep}>
              <View
                style={[
                  styles.progressDot,
                  currentStep >= index && styles.progressDotActive
                ]}
              >
                <Text style={[
                  styles.progressDotText,
                  currentStep >= index && styles.progressDotTextActive
                ]}>
                  {index + 1}
                </Text>
              </View>
              {index < getTotalSteps() - 1 && (
                <View style={[
                  styles.progressLine,
                  currentStep > index && styles.progressLineActive
                ]} />
              )}
            </View>
          ))}
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={[
            styles.content,
            // Reduced padding bottom to prevent content being pushed too high
            Platform.OS === 'ios' && isKeyboardVisible && {
              paddingBottom: 40
            }
          ]}
          keyboardShouldPersistTaps="handled"
          horizontal
          pagingEnabled
          scrollEnabled={false}
          showsHorizontalScrollIndicator={false}
          bounces={Platform.OS === 'ios'}
          scrollEventThrottle={16}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
            useNativeDriver: false,
          })}
        >
          {/* Step 1: Account Info */}
          <View style={[styles.page, { width }]}>
            <View style={styles.header}>
              <Shield size={40} color="#6366F1" />
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>First, let's set up your basic account</Text>
            </View>

            <View style={styles.form}>
              <Input
                ref={(ref: TextInput) => { inputRefs.current['email'] = ref; }}
                placeholder="Enter your email"
                value={formData.email}
                onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                onFocus={() => scrollToInput('email')}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="off"
                textContentType="none"
                autoCorrect={false}
                leftIcon={Mail}
                error={error && currentStep === 0 && !validateEmail(formData.email) ? 'Please enter a valid email' : undefined}
              />
              
              <Input
                ref={(ref: TextInput) => { inputRefs.current['username'] = ref; }}
                placeholder="Choose a username"
                value={formData.username}
                onChangeText={(text) => setFormData(prev => ({ ...prev, username: text }))}
                onFocus={() => scrollToInput('username')}
                autoCapitalize="none"
                autoComplete="off"
                textContentType="none"
                autoCorrect={false}
                leftIcon={User}
                error={error && currentStep === 0 && formData.username.length < 3 ? 'Username must be at least 3 characters' : undefined}
              />
            </View>
          </View>

          {/* Step 2: Password */}
          <View style={[styles.page, { width }]}>
            <View style={styles.header}>
              <Lock size={40} color="#6366F1" />
              <Text style={styles.title}>Secure Your Account</Text>
              <Text style={styles.subtitle}>Create a strong password to protect your account</Text>
            </View>

            <View style={styles.form}>
              <Input
                label="Password"
                value={formData.password}
                onChangeText={(text) => setFormData({ ...formData, password: text })}
                placeholder="Create a password"
                secureTextEntry
                autoComplete="new-password"
                ref={(ref) => { if (ref) inputRefs.current['password'] = ref; }}
                onFocus={() => scrollToInput('password')}
              />

              <Input
                label="Confirm Password"
                value={formData.passwordConfirm}
                onChangeText={(text) => setFormData({ ...formData, passwordConfirm: text })}
                placeholder="Confirm your password"
                secureTextEntry
                autoComplete="new-password"
                ref={(ref) => { if (ref) inputRefs.current['passwordConfirm'] = ref; }}
                onFocus={() => scrollToInput('passwordConfirm')}
              />

              <View style={styles.passwordRequirements}>
                <Text style={styles.passwordHint}>Your password should:</Text>
                <Text style={styles.passwordHint}>• Be at least 8 characters long</Text>
                <Text style={styles.passwordHint}>• Include at least one uppercase letter</Text>
                <Text style={styles.passwordHint}>• Include at least one number</Text>
                <Text style={styles.passwordHint}>• Include at least one special character</Text>
              </View>
            </View>
          </View>

          {/* Step 3: Role Selection */}
          <View style={[styles.page, { width }]}>
            <View style={styles.header}>
              <Briefcase size={40} color="#6366F1" />
              <Text style={styles.title}>Your Role</Text>
              <Text style={styles.subtitle}>Tell us how you plan to use RoomieMatch</Text>
            </View>

            <View style={styles.roleForm}>
              {ROLES.map(role => {
                const Icon = role.icon;
                const isSelected = formData.selectedRole === role.id;
                return (
                  <TouchableOpacity
                    key={role.id}
                    style={[
                      styles.roleCard, 
                      isSelected && styles.selectedRole,
                      { backgroundColor: isSelected ? role.color : role.bgColor }
                    ]}
                    onPress={() => setFormData({ ...formData, selectedRole: role.id })}
                  >
                    <View style={styles.roleHeader}>
                      <View
                        style={[
                          styles.roleIconContainer,
                          { backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : 'rgba(255,255,255,0.8)' }
                        ]}
                      >
                        <Icon size={20} color={isSelected ? '#FFFFFF' : role.color} />
                      </View>
                      <Text
                        style={[
                          styles.roleTitle,
                          { color: isSelected ? '#FFFFFF' : role.color }
                        ]}
                      >
                        {role.title}
                      </Text>
                    </View>
                    <Text style={[
                      styles.roleDescription,
                      { color: isSelected ? 'rgba(255,255,255,0.9)' : '#64748B' }
                    ]}>
                      {role.description}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          {/* Step 3: Business Information (Service Providers Only) */}
          {formData.selectedRole === 'service_provider' && (
            <View style={[styles.page, { width }]}>
              <View style={styles.header}>
                <Briefcase size={40} color="#6366F1" />
                <Text style={styles.title}>Business Information</Text>
                <Text style={styles.subtitle}>Tell us about your business</Text>
              </View>

              <View style={styles.form}>
                <Input
                  label="Business Name"
                  value={businessName}
                  onChangeText={setBusinessName}
                  placeholder="Enter your business name"
                  leftIcon={Briefcase}
                />

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Business Description</Text>
                  <TextInput
                    style={styles.textArea}
                    value={businessDescription}
                    onChangeText={setBusinessDescription}
                    placeholder="Describe your business and services..."
                    multiline
                    numberOfLines={6}
                    textAlignVertical="top"
                    maxLength={500}
                  />
                  <Text style={[
                    styles.characterCount,
                    businessDescription.length < 50 && styles.characterCountWarning
                  ]}>
                    {businessDescription.length < 50 ? `${businessDescription.length}/50 chars min` : `${businessDescription.length}/500`}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Step 4: Contact & Services (Service Providers Only) */}
          {formData.selectedRole === 'service_provider' && (
            <View style={[styles.page, { width }]}>
              <View style={styles.header}>
                <User size={40} color="#6366F1" />
                <Text style={styles.title}>Contact & Services</Text>
                <Text style={styles.subtitle}>How can customers reach you?</Text>
              </View>

              <View style={styles.form}>
                <Input
                  label="Contact Phone"
                  value={contactPhone}
                  onChangeText={setContactPhone}
                  placeholder="Enter your business phone number"
                  keyboardType="phone-pad"
                  leftIcon={User}
                />

                <Input
                  label="Business Address"
                  value={businessAddress}
                  onChangeText={setBusinessAddress}
                  placeholder="Enter your business address"
                  leftIcon={User}
                />

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Service Categories</Text>
                  <Text style={styles.inputHint}>Select all services you provide</Text>
                  <View style={styles.categoriesContainer}>
                    {serviceCategories.map(category => (
                      <TouchableOpacity
                        key={category}
                        style={[
                          styles.categoryChip,
                          selectedCategories.includes(category) && styles.categoryChipSelected
                        ]}
                        onPress={() => {
                          if (selectedCategories.includes(category)) {
                            setSelectedCategories(selectedCategories.filter(c => c !== category));
                          } else {
                            setSelectedCategories([...selectedCategories, category]);
                          }
                        }}
                      >
                        <Text style={[
                          styles.categoryChipText,
                          selectedCategories.includes(category) && styles.categoryChipTextSelected
                        ]}>
                          {category}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Error Message */}
        {error && <Text style={styles.errorText}>{error}</Text>}

        {/* Google Sign Up Option */}
        {currentStep === 0 && (
          <View style={styles.socialSignUpContainer}>
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>Or sign up with</Text>
              <View style={styles.divider} />
            </View>
            
            <TouchableOpacity 
              style={styles.googleButton}
              onPress={() => {
                // TODO: Implement Google Sign Up
                Alert.alert('Coming Soon', 'Google Sign Up will be available soon!');
              }}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Sign up with Google"
              accessibilityHint="Create account using your Google account"
            >
              <View style={styles.googleIcon}>
                <Text style={styles.googleIconText}>G</Text>
              </View>
              <Text style={styles.googleButtonText}>Continue with Google</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Bottom Navigation Container */}
        <View style={styles.buttonContainer}>
          {/* Navigation Buttons */}
          <View style={styles.buttonRow}>
            {currentStep > 0 && (
              <TouchableOpacity style={styles.backButton} onPress={handleBack} disabled={loading}>
                <ArrowLeft size={20} color="#64748B" />
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
            )}

            <Button
              onPress={handleNext}
              isLoading={loading}
              disabled={loading || !isStepValid}
              style={styles.continueButton}
              rightIcon={currentStep < getMaxStep() ? <ArrowRight size={16} color={safeTheme.colors.textInverse} /> : undefined}
            >
              {currentStep === getMaxStep() ? 'Create Account' : 'Continue'}
            </Button>
          </View>

          {/* Login Link - Separate from buttons */}
          <TouchableOpacity style={styles.linkButton} onPress={() => router.push('/(auth)/login')}>
            <Text style={styles.linkText}>
              Already have an account? <Text style={styles.link}>Log In</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

const createStyles = (theme: any, insets: { top: number, bottom: number, left: number, right: number }) => StyleSheet.create({
  keyboardAvoidView: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
  },
  page: {
    width: width,
    paddingHorizontal: 24,
    paddingBottom: 80,
    paddingTop: 20,
    minHeight: Dimensions.get('window').height - 200,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12, // Reduced vertical padding
    paddingHorizontal: 40,
    position: 'absolute',
    top: insets.top + 10, // Position below status bar with a small margin
    left: 0,
    right: 0,
    alignSelf: 'center',
    width: '100%',
    zIndex: 10,
    backgroundColor: '#F8FAFC', // Add background to avoid content showing through
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 4, 
  },
  progressDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E2E8F0',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  progressDotActive: {
    backgroundColor: '#3B82F6', // More vibrant blue
    shadowColor: '#3B82F6',
    shadowOpacity: 0.3,
    elevation: 6,
  },
  progressLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#E2E8F0',
    marginHorizontal: 4,
  },
  progressLineActive: {
    backgroundColor: '#3B82F6', // More vibrant blue
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
    marginTop: 60,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1E293B',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    gap: 16,
  },
  roleForm: {
    gap: 12,
  },
  roleCard: {
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: 12,
    marginBottom: 8,
  },
  selectedRole: {
    borderColor: 'rgba(255,255,255,0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  roleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  roleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  roleTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  roleDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  passwordRequirements: {
    marginTop: 8,
    backgroundColor: '#F1F5F9',
    padding: 16,
    borderRadius: 12,
  },
  passwordHint: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    textAlign: 'center',
    marginHorizontal: 24,
    marginTop: 8,
  },
  buttonContainer: {
    backgroundColor: '#F8FAFC',
    borderTopWidth: Platform.select({
      ios: StyleSheet.hairlineWidth,
      android: 1,
      default: 1
    }),
    borderTopColor: '#E2E8F0',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: Platform.select({
      ios: 34, // Account for iOS safe area
      android: 20,
      default: 20
    }),
    paddingTop: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: Platform.select({
      ios: 16,
      android: 12,
      default: 12
    }),
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  backButtonText: {
    marginLeft: 8,
    color: '#64748B',
    fontWeight: '500',
  },
  continueButton: {
    flex: 1,
    marginLeft: 12,
  },
  linkButton: {
    alignItems: 'center',
    paddingVertical: Platform.select({
      ios: 14,
      android: 12,
      default: 12
    }),
    paddingHorizontal: 20,
    marginHorizontal: 24,
  },
  linkText: {
    fontSize: 14,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 20,
  },
  link: {
    color: '#3B82F6',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  socialSignUpContainer: {
    padding: 24,
    alignItems: 'center',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: '#E2E8F0',
  },
  dividerText: {
    marginHorizontal: 12,
    color: '#64748B',
    fontWeight: '600',
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 2,
    borderColor: '#E2E8F0',
    borderRadius: 12,
  },
  googleIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  googleIconText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6366F1',
  },
  googleButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  inputContainer: {
    width: '100%',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  textArea: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 8,
    backgroundColor: '#F8FAFC',
    fontSize: 16,
    lineHeight: 22,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'right',
    marginTop: 4,
  },
  characterCountWarning: {
    color: '#EF4444',
  },
  inputHint: {
    fontSize: 12,
    color: '#64748B',
    marginBottom: 8,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  categoryChipSelected: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
    color: '#FFFFFF',
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E293B',
  },
  categoryChipTextSelected: {
    color: '#FFFFFF',
  },
  progressDotText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
  },
  progressDotTextActive: {
    color: '#FFFFFF',
  },
});
