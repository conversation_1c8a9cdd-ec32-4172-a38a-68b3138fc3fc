import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@design-system';
import { Button } from '@design-system';

// SQL migration to execute
const MIGRATION_SQL = `
DO $$
BEGIN
  -- Create the check_column_exists function if it doesn't exist
  CREATE OR REPLACE FUNCTION check_column_exists(p_table_name text, p_column_name text) 
  RETURNS boolean AS $$
  DECLARE
    column_exists boolean;
  BEGIN
    SELECT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public'
      AND table_name = p_table_name
      AND column_name = p_column_name
    ) INTO column_exists;
    
    RETURN column_exists;
  END;
  $$ LANGUAGE plpgsql SECURITY DEFINER;

  -- Add video_thumbnail_url and video_compression_stats fields to the user_profiles table if they don't exist
  IF NOT check_column_exists('user_profiles', 'video_thumbnail_url') THEN
    ALTER TABLE user_profiles ADD COLUMN video_thumbnail_url TEXT;
    RAISE NOTICE 'Added video_thumbnail_url column to user_profiles table';
  ELSE
    RAISE NOTICE 'Column video_thumbnail_url already exists in user_profiles table';
  END IF;
  
  IF NOT check_column_exists('user_profiles', 'video_compression_stats') THEN
    ALTER TABLE user_profiles ADD COLUMN video_compression_stats JSONB;
    RAISE NOTICE 'Added video_compression_stats column to user_profiles table';
  ELSE
    RAISE NOTICE 'Column video_compression_stats already exists in user_profiles table';
  END IF;

  -- Update RLS policies if needed (only if table exists)
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname='public' AND tablename='user_profiles') THEN
    -- Check if RLS policies exist before updating them
    IF EXISTS (SELECT FROM pg_policies WHERE tablename='user_profiles' AND policyname='Users can view their own profile') THEN
      ALTER POLICY "Users can view their own profile" ON user_profiles 
      USING (auth.uid() = id);
      RAISE NOTICE 'Updated view policy for user_profiles table';
    END IF;

    IF EXISTS (SELECT FROM pg_policies WHERE tablename='user_profiles' AND policyname='Users can update their own profile') THEN
      ALTER POLICY "Users can update their own profile" ON user_profiles 
      USING (auth.uid() = id)
      WITH CHECK (auth.uid() = id);
      RAISE NOTICE 'Updated update policy for user_profiles table';
    END IF;
  END IF;

  -- Add column comments
  COMMENT ON COLUMN user_profiles.video_thumbnail_url IS 'URL to the user''s video introduction thumbnail image';
  COMMENT ON COLUMN user_profiles.video_compression_stats IS 'Statistics about video compression (size before/after, ratio)';
END$$;
`;

export default function VideoThumbnailMigrationScreen() {
  const theme = useTheme();
  const colors = theme.colors;
  const [status, setStatus] = React.useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = React.useState('');
  const router = useRouter();

  const runMigration = React.useCallback(async () => {
    try {
      setStatus('loading');
      setMessage('Running migration...');

      // Check if the table exists first
      const { data: tableData, error: tableError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'user_profiles');

      if (tableError) {
        throw tableError;
      }

      if (!Array.isArray(tableData) || tableData.length === 0) {
        setStatus('error');
        setMessage('Migration failed: user_profiles table does not exist');
        return;
      }

      // Execute the SQL directly using Supabase's RPC function
      const { error } = await supabase.rpc('exec_sql', { sql: MIGRATION_SQL });

      if (error) {
        throw error;
      }

      logger.info('Migration successful', 'VideoThumbnailMigrationScreen', {
        migration: 'video_thumbnail_fields',
        table: 'user_profiles',
        columns: ['video_thumbnail_url', 'video_compression_stats'],
      });

      setStatus('success');
      setMessage(
        'Migration completed successfully. Added video thumbnail and compression stats columns to user_profiles table.'
      );
    } catch (error) {
      logger.error('Migration error', 'VideoThumbnailMigrationScreen', error);
      setStatus('error');
      setMessage(`Migration failed: ${(error as Error).message}`);
    }
  }, []);

  React.useEffect(() => {
    runMigration();
  }, [runMigration]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen options={{ title: 'Database Migration' }} />

      <View style={styles.content}>
        {status === 'loading' && (
          <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
        )}

        <View
          style={[
            styles.statusCard,
            {
              backgroundColor:
                status === 'success'
                  ? colors.success + '20'
                  : status === 'error'
                    ? colors.error + '20'
                    : colors.surface,
              borderColor:
                status === 'success'
                  ? colors.success
                  : status === 'error'
                    ? colors.error
                    : colors.border,
            },
          ]}
        >
          <Text
            style={[
              styles.statusText,
              {
                color:
                  status === 'success'
                    ? colors.success
                    : status === 'error'
                      ? colors.error
                      : colors.text,
              },
            ]}
          >
            {message}
          </Text>
        </View>

        {status === 'success' || status === 'error' ? (
          <Button
                          onPress={() => router.push('/(tabs)/profile/unified-settings')}
            style={{ marginTop: 20 }}
            variant="primary"
          >
            Return to Settings
          </Button>
        ) : null}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loader: {
    marginBottom: 20,
  },
  statusCard: {
    width: '100%',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
