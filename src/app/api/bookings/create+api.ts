import { logger } from '@utils/logger';
import { BookingService, BookingStatus, PaymentStatus } from '@services/standardized/BookingService';

interface BookingCreationData {
  service_id: string;
  user_id: string;
  booking_date: string;
  end_date: string;
  address: string;
  special_instructions?: string;
  price: number;
  payment_status?: PaymentStatus;
  roommate_shared?: boolean;
  shared_with?: string[];
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Enhanced validation for booking creation
 */
function validateBookingData(data: BookingCreationData): ValidationResult {
  const errors: string[] = [];

  // Required field validation
  if (!data.service_id || data.service_id.trim() === '') {
    errors.push('Service ID is required');
  }

  if (!data.user_id || data.user_id.trim() === '') {
    errors.push('User ID is required');
  }

  if (!data.booking_date || data.booking_date.trim() === '') {
    errors.push('Booking date is required');
  } else {
    const bookingDate = new Date(data.booking_date);
    if (isNaN(bookingDate.getTime())) {
      errors.push('Invalid booking date format');
    } else if (bookingDate < new Date()) {
      errors.push('Booking date cannot be in the past');
    }
  }

  if (!data.end_date || data.end_date.trim() === '') {
    errors.push('End date is required');
  } else {
    const endDate = new Date(data.end_date);
    if (isNaN(endDate.getTime())) {
      errors.push('Invalid end date format');
    } else if (data.booking_date && endDate <= new Date(data.booking_date)) {
      errors.push('End date must be after booking date');
    }
  }

  if (!data.address || data.address.trim() === '') {
    errors.push('Service address is required');
  } else if (data.address.length < 5) {
    errors.push('Address must be at least 5 characters');
  } else if (data.address.length > 200) {
    errors.push('Address cannot exceed 200 characters');
  }

  // Price validation
  if (data.price === undefined || data.price === null) {
    errors.push('Service price is required');
  } else if (data.price < 0) {
    errors.push('Price cannot be negative');
  } else if (data.price > 50000) {
    errors.push('Price cannot exceed $50,000');
  }

  // Special instructions validation (optional)
  if (data.special_instructions && data.special_instructions.length > 1000) {
    errors.push('Special instructions cannot exceed 1000 characters');
  }

  // Shared booking validation
  if (data.roommate_shared) {
    if (!data.shared_with || data.shared_with.length === 0) {
      errors.push('At least one roommate must be selected for shared bookings');
    } else if (data.shared_with.length > 10) {
      errors.push('Cannot share booking with more than 10 roommates');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate service exists and is available
 */
async function validateService(serviceId: string): Promise<{ valid: boolean; service?: any; error?: string }> {
  try {
    const bookingService = new BookingService();
    
    // We need to implement a method to check service availability
    // For now, we'll assume the service exists if we can fetch it
    // In a real implementation, we'd check the services table
    
    // This is a placeholder - you would implement service validation here
    // const service = await bookingService.getServiceById(serviceId);
    
    return { valid: true };
  } catch (error) {
    logger.error('Error validating service', 'BookingCreateAPI', { serviceId }, error as Error);
    return { valid: false, error: 'Failed to validate service' };
  }
}

/**
 * Check for booking conflicts with existing appointments
 */
async function checkBookingConflicts(
  serviceId: string, 
  bookingDate: string, 
  endDate: string
): Promise<{ hasConflict: boolean; conflictDetails?: string }> {
  try {
    const bookingService = new BookingService();
    
    // Check if the time slot is available
    const availabilityResult = await bookingService.checkAvailableTimeSlots(
      serviceId,
      new Date(bookingDate).toISOString().split('T')[0],
      'system' // We'll need to handle authentication properly
    );

    // For now, we'll assume no conflicts
    // In a real implementation, you'd check against existing bookings
    return { hasConflict: false };
  } catch (error) {
    logger.error('Error checking booking conflicts', 'BookingCreateAPI', { serviceId, bookingDate }, error as Error);
    return { hasConflict: false }; // Allow booking if we can't check conflicts
  }
}

export async function POST(request: Request) {
  try {
    logger.info('Booking creation request received', 'BookingCreateAPI');

    // Parse request body
    let bookingData: BookingCreationData;
    try {
      bookingData = await request.json();
    } catch (error) {
      logger.error('Invalid JSON in booking creation request', 'BookingCreateAPI', {}, error as Error);
      return Response.json(
        { error: 'Invalid JSON data' },
        { status: 400 }
      );
    }

    // Validate booking data
    const validation = validateBookingData(bookingData);
    if (!validation.isValid) {
      logger.warn('Booking creation validation failed', 'BookingCreateAPI', {
        userId: bookingData.user_id,
        serviceId: bookingData.service_id,
        errors: validation.errors
      });
      return Response.json(
        { 
          error: 'Validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Validate service exists and is available
    const serviceValidation = await validateService(bookingData.service_id);
    if (!serviceValidation.valid) {
      logger.warn('Invalid service in booking creation', 'BookingCreateAPI', {
        userId: bookingData.user_id,
        serviceId: bookingData.service_id,
        error: serviceValidation.error
      });
      return Response.json(
        { error: serviceValidation.error || 'Service not available' },
        { status: 400 }
      );
    }

    // Check for booking conflicts
    const conflictCheck = await checkBookingConflicts(
      bookingData.service_id,
      bookingData.booking_date,
      bookingData.end_date
    );

    if (conflictCheck.hasConflict) {
      logger.warn('Booking conflict detected', 'BookingCreateAPI', {
        userId: bookingData.user_id,
        serviceId: bookingData.service_id,
        bookingDate: bookingData.booking_date,
        conflictDetails: conflictCheck.conflictDetails
      });
      return Response.json(
        { 
          error: 'Booking conflict detected',
          details: conflictCheck.conflictDetails
        },
        { status: 409 }
      );
    }

    // Prepare booking data for creation
    const newBookingData = {
      service_id: bookingData.service_id,
      user_id: bookingData.user_id,
      booking_date: bookingData.booking_date,
      end_date: bookingData.end_date,
      status: BookingStatus.PENDING,
      address: bookingData.address.trim(),
      special_instructions: bookingData.special_instructions?.trim() || '',
      price: bookingData.price,
      payment_status: bookingData.payment_status || PaymentStatus.PENDING,
      roommate_shared: bookingData.roommate_shared || false,
      shared_with: bookingData.shared_with || [],
      is_reviewed: false
    };

    // Create booking using BookingService
    const bookingService = new BookingService();
    const response = await bookingService.createBooking(newBookingData, bookingData.user_id);

    if (response.error) {
      logger.error('Failed to create booking', 'BookingCreateAPI', {
        userId: bookingData.user_id,
        serviceId: bookingData.service_id,
        error: response.error
      });
      return Response.json(
        { error: 'Failed to create booking' },
        { status: 500 }
      );
    }

    logger.info('Booking created successfully', 'BookingCreateAPI', {
      bookingId: response.data?.id,
      userId: bookingData.user_id,
      serviceId: bookingData.service_id,
      bookingDate: bookingData.booking_date,
      price: bookingData.price,
      sharedBooking: bookingData.roommate_shared
    });

    // Return success response
    return Response.json(
      {
        success: true,
        message: 'Booking created successfully',
        data: {
          id: response.data?.id,
          service_id: response.data?.service_id,
          booking_date: response.data?.booking_date,
          end_date: response.data?.end_date,
          status: response.data?.status,
          price: response.data?.price,
          payment_status: response.data?.payment_status,
          created_at: response.data?.created_at
        }
      },
      { status: 201 }
    );

  } catch (error) {
    logger.error('Unexpected error in booking creation', 'BookingCreateAPI', {}, error as Error);
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 