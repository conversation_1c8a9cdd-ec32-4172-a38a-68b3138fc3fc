import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import {
  Star as StarRaw,
  Shield as ShieldRaw,
  MapPin as MapPinRaw,
  Phone as PhoneRaw,
  Mail as MailRaw,
  Globe as GlobeRaw,
  Calendar as CalendarRaw,
  Clock as ClockRaw,
  ArrowLeft as ArrowLeftRaw,
  Share2 as Share2Raw,
  MessageCircle as MessageCircleRaw,
  Heart as HeartRaw,
  Bookmark as BookmarkRaw,
  BarChart2 as BarChart2Raw,
} from 'lucide-react-native';
import { Icons } from '@components/common/Icon';
import { useTheme, colorWithOpacity, Theme } from '@design-system';
import { useServiceProviders } from '@hooks/useServiceProviders';
import {
  getFavoriteProviders,
  checkIfFavorite,
  saveFavoriteProvider,
  removeFavoriteProvider,
} from '@services/favoritesService';
// import ProviderGallery from '@components/services/ProviderGallery';
// import ProviderReviews from '@components/services/ProviderReviews';
// import ProviderAvailability from '@components/services/ProviderAvailability';
// import ProviderFAQ from '@components/services/ProviderFAQ';
// import { FAQItem } from '@components/services/ProviderFAQ';
import {
  Service,
  getServiceProviderById,
  getServicesByProviderId,
  getProviderReviews,
} from '@services/index';
import { useToast } from '@core/errors';

// Create validated versions of all icons
const Star = Icons.Star;
const Shield = Icons.Shield;
const MapPin = Icons.MapPin;
const Phone = Icons.Phone;
const Mail = Icons.Mail;
const Globe = Icons.Globe;
const Calendar = Icons.Calendar;
const Clock = Icons.Clock;
const ArrowLeft = Icons.ArrowLeft;
const Share2 = Icons.Share2;
const MessageCircle = Icons.MessageCircle;
const Heart = Icons.Heart;
const Bookmark = Icons.Bookmark;
const BarChart2 = Icons.BarChart2;

// Sample FAQs (in a real app, would come from the API)
const SAMPLE_FAQS: any[] = [
  {
    question: 'What is your cancellation policy?',
    answer:
      'You can cancel up to 24 hours before your scheduled appointment without any charges. Cancellations made less than 24 hours in advance may be subject to a fee of 50% of the service cost.',
  },
  {
    question: 'Do you provide all necessary equipment and supplies?',
    answer:
      "Yes, we bring all professional equipment and supplies needed for the service. If you have specific products you'd prefer us to use, please let us know in advance.",
  },
  {
    question: 'How far in advance should I book?',
    answer:
      'We recommend booking at least 3-5 days in advance to ensure availability, especially during peak times. For urgent requests, you can check our last-minute availability.',
  },
  {
    question: 'Are you insured and licensed?',
    answer:
      'Yes, all our service providers are fully insured, licensed, and have undergone background checks for your peace of mind.',
  },
  {
    question: 'Do you offer discounts for recurring services?',
    answer:
      'Yes, we offer various subscription packages that provide discounts for recurring services. Check our pricing section or contact us for details.',
  },
];

export default function ServiceProviderDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  // Fallback to hardcoded ID for testing if no ID in route
  const providerId = id || 'bd9abd0e-ceec-4fdc-b9c7-6d4b2f8b9246';
  const router = useRouter();
  const theme = useTheme();
  const styles = createStyles(theme);
  const { showToast } = useToast();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [checkingBookmark, setCheckingBookmark] = useState(true);

  const {
    selectedProvider,
    selectedProviderServices,
    isProviderLoading,
    error: serviceProviderError,
    fetchProviderDetails,
  } = useServiceProviders();

  // Use our utility functions for color handling, simplified because the theme is now validated
  const primaryColorWithOpacity = colorWithOpacity(theme.colors.primary, 0.15);

  useEffect(() => {
    if (providerId) {
      fetchProviderDetails(providerId as string);
    }
  }, [providerId, fetchProviderDetails]);

  // Check if provider is bookmarked
  useEffect(() => {
    const checkBookmarked = async () => {
      if (providerId) {
        try {
          const isFavorite = await checkIfFavorite(providerId as string);
          setIsBookmarked(isFavorite);
        } catch (error) {
          console.error('Error checking bookmark status:', error);
        } finally {
          setCheckingBookmark(false);
        }
      }
    };

    checkBookmarked();
  }, [providerId]);

  const handleCall = () => {
    console.log('📞 Call button pressed', { 
      hasProvider: !!selectedProvider, 
      contactPhone: selectedProvider?.contact_phone 
    });
    
    if (selectedProvider?.contact_phone) {
      Linking.openURL(`tel:${selectedProvider.contact_phone}`)
        .catch(error => {
          console.error('Error opening phone app:', error);
          if (__DEV__) {
            showToast('info', `Would call: ${selectedProvider.contact_phone} (Simulator limitation)`);
          } else {
            showToast('error', 'Could not open phone app');
          }
        });
    } else {
      showToast('error', 'No phone number available');
    }
  };

  const handleEmail = () => {
    console.log('📧 Email button pressed', { 
      hasProvider: !!selectedProvider, 
      contactEmail: selectedProvider?.contact_email 
    });
    
    if (selectedProvider?.contact_email) {
      Linking.openURL(`mailto:${selectedProvider.contact_email}`)
        .catch(error => {
          console.error('Error opening email app:', error);
          if (__DEV__) {
            showToast('info', `Would email: ${selectedProvider.contact_email} (Simulator limitation)`);
          } else {
            showToast('error', 'Could not open email app');
          }
        });
    } else {
      showToast('error', 'No email address available');
    }
  };

  const handleWebsite = () => {
    console.log('🌐 Website button pressed', { 
      hasProvider: !!selectedProvider, 
      website: selectedProvider?.website 
    });
    
    if (selectedProvider?.website) {
      let url = selectedProvider.website;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`;
      }
      
      Linking.openURL(url)
        .catch(error => {
          console.error('Error opening website:', error);
          showToast('error', 'Could not open website');
        });
    } else {
      showToast('error', 'No website available');
    }
  };

  const handleBookService = (service: Service) => {
    console.log('📅 Book Service button pressed', { 
      id: service.id,
      serviceName: service.name,
      providerId: selectedProvider?.id,
      providerName: selectedProvider?.business_name
    });
    
    try {
      router.push({
        pathname: '/service-booking',
        params: {
          id: service.id,
          providerId: selectedProvider?.id,
        },
      });
    } catch (error) {
      console.error('Error navigating to booking screen:', error);
      showToast('error', 'Could not open booking screen');
    }
  };

  const handleToggleBookmark = async () => {
    if (!providerId || checkingBookmark) return;

    try {
      if (isBookmarked) {
        await removeFavoriteProvider(providerId as string);
        setIsBookmarked(false);
        showToast('success', 'Removed from saved providers');
      } else {
        await saveFavoriteProvider(providerId as string);
        setIsBookmarked(true);
        showToast('success', 'Added to saved providers');
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      showToast('error', 'Could not update saved providers');
    }
  };

  const handleShare = () => {
    // In a real app, would use Share API
          showToast('info', 'Sharing provider details...');
  };

  const handleContactProvider = () => {
    console.log('💬 Message button pressed', { 
      hasProvider: !!selectedProvider,
      providerId: selectedProvider?.id,
      providerName: selectedProvider?.business_name
    });
    
    if (selectedProvider) {
      try {
        router.push({
          pathname: '/(tabs)/messages',
          params: { providerId: selectedProvider.id },
        });
      } catch (error) {
        console.error('Error navigating to messages:', error);
        showToast('error', 'Could not open messages');
      }
    } else {
      showToast('error', 'Provider information not available');
    }
  };

  if (isProviderLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (serviceProviderError || !selectedProvider) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {serviceProviderError || 'Provider not found'}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.back()}
        >
          <Text style={{ color: theme.colors.white, fontWeight: '600' }}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen
        options={{
          title: selectedProvider.business_name,
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background },
          headerTintColor: theme.colors.text,
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleToggleBookmark}
                disabled={checkingBookmark}
              >
                <Bookmark
                  size={24}
                  color={theme.colors.text}
                  fill={isBookmarked ? theme.colors.text : 'transparent'}
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
                <Share2 size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        {/* Provider Header */}
        <View style={styles.header}>
          <Image
            source={{
              uri:
                selectedProvider.profile_image ||
                'https://via.placeholder.com/300x200?text=No+Image',
            }}
            style={styles.coverImage}
          />

          <View style={[styles.providerInfoCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.providerHeader}>
              <Text style={[styles.businessName, { color: theme.colors.text }]}>
                {selectedProvider.business_name}
              </Text>
              {selectedProvider.is_verified && (
                <View style={styles.verifiedBadge}>
                  <Shield size={12} color={theme.colors.white} />
                  <Text style={styles.verifiedText}>Verified</Text>
                </View>
              )}
            </View>

            <View style={styles.ratingContainer}>
              <Star size={18} color={theme.colors.primary} fill={theme.colors.primary} />
              <Text style={[styles.rating, { color: theme.colors.text }]}>
                {selectedProvider.rating_average
                  ? selectedProvider.rating_average.toFixed(1)
                  : 'New'}
                {selectedProvider.review_count > 0 && (
                  <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
                    ({selectedProvider.review_count} reviews)
                  </Text>
                )}
              </Text>
            </View>

            <View style={styles.locationContainer}>
              <MapPin size={18} color={theme.colors.secondary} />
              <Text style={[styles.locationText, { color: theme.colors.textSecondary }]}>
                {selectedProvider.business_address}
              </Text>
            </View>

            <View style={styles.categories}>
              {(selectedProvider.resolved_category_names || selectedProvider.service_categories).map((category, index) => (
                <View
                  key={index}
                  style={[styles.categoryBadge, { backgroundColor: primaryColorWithOpacity }]}
                >
                  <Text style={[styles.categoryText, { color: theme.colors.primary }]}>{category}</Text>
                </View>
              ))}
            </View>

            <View style={styles.contactButtons}>
              <TouchableOpacity
                style={[styles.contactButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleCall}
              >
                <Phone size={20} color={theme.colors.white} />
                <Text style={styles.contactButtonText}>Call</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.contactButton, { backgroundColor: theme.colors.success }]}
                onPress={handleEmail}
              >
                <Mail size={20} color={theme.colors.white} />
                <Text style={styles.contactButtonText}>Email</Text>
              </TouchableOpacity>

              {selectedProvider.website && (
                <TouchableOpacity
                  style={[styles.contactButton, { backgroundColor: theme.colors.info }]}
                  onPress={handleWebsite}
                >
                  <Globe size={20} color={theme.colors.white} />
                  <Text style={styles.contactButtonText}>Website</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {/* Enhanced Gallery Section */}
        {selectedProvider.gallery_images && selectedProvider.gallery_images.length > 0 && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Portfolio Gallery</Text>
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>Gallery temporarily disabled</Text>
          </View>
        )}

        {/* About Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>About</Text>
          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
            {selectedProvider.description}
          </Text>
        </View>

        {/* Services Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Services Offered</Text>

          {selectedProviderServices.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              No services listed yet.
            </Text>
          ) : (
            selectedProviderServices.map((service, index) => (
              <View
                key={service.id}
                style={[
                  styles.serviceCard,
                  { borderBottomColor: theme.colors.border },
                  index === selectedProviderServices.length - 1 && styles.lastServiceCard,
                ]}
              >
                <View style={styles.serviceInfo}>
                  <Text style={[styles.serviceName, { color: theme.colors.text }]}>{service.name}</Text>
                  <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>
                    {service.description}
                  </Text>

                  <View style={styles.serviceDetails}>
                    {service.duration && (
                      <View style={styles.serviceDetail}>
                        <Clock size={16} color={theme.colors.textSecondary} />
                        <Text style={[styles.serviceDetailText, { color: theme.colors.textSecondary }]}>
                          {service.duration} min
                        </Text>
                      </View>
                    )}

                    {service.price !== null && (
                      <View style={styles.serviceDetail}>
                        <Text style={[styles.servicePrice, { color: theme.colors.text }]}>
                          ${service.price.toFixed(2)}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>

                <TouchableOpacity
                  style={[styles.bookButton, { backgroundColor: theme.colors.primary }]}
                  onPress={() => handleBookService(service)}
                >
                  <Text style={styles.bookButtonText}>Book</Text>
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>

        {/* Availability Calendar Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Availability</Text>
          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>Availability calendar temporarily disabled</Text>
        </View>

        {/* Reviews Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeaderRow}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Reviews & Ratings</Text>
            <TouchableOpacity
              style={[styles.analyticsButton, { backgroundColor: primaryColorWithOpacity }]}
              onPress={() =>
                router.push({
                  pathname: '/provider/analytics',
                  params: { id: selectedProvider.id },
                })
              }
            >
              <View style={styles.analyticsButtonContent}>
                <BarChart2 size={16} color={theme.colors.primary} />
                <Text style={[styles.analyticsButtonText, { color: theme.colors.primary }]}>
                  Analytics
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>Reviews temporarily disabled</Text>
        </View>

        {/* FAQ Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>FAQ</Text>
          <Text style={[styles.description, { color: theme.colors.textSecondary }]}>FAQ temporarily disabled</Text>
        </View>
      </ScrollView>

      {/* Fixed Book/Message Buttons at bottom */}
      <View
        style={[
          styles.bottomButtons,
          { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border },
        ]}
      >
        <TouchableOpacity
          style={[styles.messageButton, { borderColor: theme.colors.primary }]}
          onPress={handleContactProvider}
        >
          <MessageCircle size={20} color={theme.colors.primary} />
          <Text style={[styles.messageButtonText, { color: theme.colors.primary }]}>Message</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.bookNowButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => {
            if (selectedProviderServices.length > 0) {
              handleBookService(selectedProviderServices[0]);
            } else {
              showToast('error', 'No services available for booking');
            }
          }}
        >
          <Calendar size={20} color={theme.colors.white} />
          <Text style={styles.bookNowButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 4,
  },
  header: {
    marginBottom: 16,
  },
  coverImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  providerInfoCard: {
    marginTop: -40,
    marginHorizontal: 16,
    borderRadius: 16,
    padding: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  providerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  businessName: {
    fontSize: 24,
    fontWeight: '700',
    flex: 1,
    marginRight: 8,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rating: {
    marginLeft: 4,
    fontSize: 16,
    fontWeight: '600',
  },
  reviewCount: {
    fontSize: 14,
    fontWeight: '400',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  locationText: {
    marginLeft: 4,
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  categories: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  categoryBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  contactButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  contactButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
    marginLeft: 6,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 22,
  },
  serviceCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  lastServiceCard: {
    borderBottomWidth: 0,
    paddingBottom: 0,
  },
  serviceInfo: {
    flex: 1,
    marginRight: 12,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  serviceDetailText: {
    fontSize: 14,
    marginLeft: 4,
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: '700',
  },
  bookButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  bookButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
  },
  bottomButtons: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 12,
    flex: 1,
  },
  messageButtonText: {
    fontWeight: '600',
    marginLeft: 8,
  },
  bookNowButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 2,
  },
  bookNowButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
    marginLeft: 8,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 14,
    paddingVertical: 16,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analyticsButton: {
    padding: 8,
    borderRadius: 8,
  },
  analyticsButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  analyticsButtonText: {
    fontWeight: '600',
    marginLeft: 4,
  },
});
