import React from 'react';
import { View } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { OnboardingScreen } from '@components/onboarding/OnboardingScreen';

export default function Onboarding() {
  const handleOnboardingComplete = async () => {
    try {
      // Mark onboarding as completed
      await AsyncStorage.setItem('onboardingCompleted', 'true');
      console.log('🟢 [Onboarding] Onboarding completed, navigating to auth welcome');
      
      // Navigate to auth welcome screen (shows both sign in and sign up options)
      router.replace('/(auth)');
    } catch (error) {
      console.error('🔴 [Onboarding] Error saving onboarding completion:', error);
      // Still navigate even if storage fails
      router.replace('/(auth)');
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <OnboardingScreen onComplete={handleOnboardingComplete} />
    </View>
  );
} 