import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { 
  ArrowLeft, 
  MessageCircle, 
  User, 
  Heart, 
  Star, 
  CheckCircle, 
  AlertCircle,
  MapPin,
  Home,
  Calendar,
  Award,
  FileText,
  Clock,
  ArrowRight
} from 'lucide-react-native';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { matchingService } from '@services/matchingService';
import type { LocationCompatibility } from '@services/LocationService';
import { profileCompletionService } from '@services/profileCompletionService';
import { colors } from '@constants/colors';
import { startChatWithMatch } from '@utils/chatUtils';
// Import the flow service and components
import { matchingToAgreementFlow } from '@services/flows/MatchingToAgreementFlowService';
import type { FlowProgress, FlowStage } from '@types/matching-flow';

interface CompatibilityDetail {
  score: number;
  positiveFactors: string[];
  negativeFactors: string[];
  locationCompatibility?: LocationCompatibility;
}

interface FlowState {
  stage: FlowStage;
  progress: FlowProgress | null;
  isLoading: boolean;
  error: string | null;
}

export default function MatchDetailScreen() {
  const { matchId } = useLocalSearchParams<{ matchId: string }>();
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(true);
  const [matchProfile, setMatchProfile] = useState<any>(null);
  const [compatibility, setCompatibility] = useState<CompatibilityDetail | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Flow state management
  const [flowState, setFlowState] = useState<FlowState>({
    stage: 'discovery',
    progress: null,
    isLoading: false,
    error: null
  });
  const [isStartingFlow, setIsStartingFlow] = useState(false);

  useEffect(() => {
    if (!user?.id || !matchId) return;

    const loadMatchDetails = async () => {
      try {
        setLoading(true);
        
        // Load match profile
        const { data: profile } = await profileCompletionService.getUserProfile(matchId);
        setMatchProfile(profile);
        
        // Load detailed compatibility
        const compatibilityData = await matchingService.getDetailedCompatibility(
          user.id,
          matchId
        );
        
        setCompatibility(compatibilityData);

        // Load flow progress if it exists
        await loadFlowProgress();
      } catch (error) {
        console.error('Error loading match details:', error);
        setError('Failed to load match details. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    loadMatchDetails();
  }, [matchId, user?.id]);

  const loadFlowProgress = async () => {
    if (!user?.id || !matchId) return;

    try {
      const progress = await matchingToAgreementFlow.getFlowProgress(user.id, matchId);
      setFlowState(prev => ({
        ...prev,
        progress,
        stage: progress?.current_stage || 'discovery'
      }));
    } catch (error) {
      console.error('Error loading flow progress:', error);
    }
  };

  const handleStartMatchingFlow = async () => {
    if (!user?.id || !matchId || !matchProfile) return;
    
    try {
      setIsStartingFlow(true);
      setFlowState(prev => ({ ...prev, isLoading: true, error: null }));

      // Step 1: Handle the match action (like)
      const matchResult = await matchingToAgreementFlow.handleMatchAction(
        user.id,
        matchId,
        'like'
      );

      if (!matchResult.success) {
        throw new Error(matchResult.error || 'Failed to process match');
      }

      // Step 2: Update flow state
      await loadFlowProgress();

      // Step 3: Navigate based on result
      if (matchResult.is_mutual_match && matchResult.chat_room_id) {
        // Mutual match - navigate to chat with agreement capability
        const name = `${matchProfile.first_name || ''} ${matchProfile.last_name || ''}`.trim();
        
        router.push({
          pathname: '/chat',
          params: {
            roomId: matchResult.chat_room_id,
            recipientId: matchId,
            recipientName: name,
            fromMatch: 'true',
            context: 'matching_flow'
          }
        });
      } else {
        // Not a mutual match yet - show waiting state
        setFlowState(prev => ({
          ...prev,
          stage: 'matching',
          isLoading: false
        }));
      }

    } catch (error) {
      console.error('Error starting matching flow:', error);
      setFlowState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to start matching flow'
      }));
    } finally {
      setIsStartingFlow(false);
    }
  };

  const handleViewProfile = () => {
    router.push(`/profile/${matchId}`);
  };

  const getFlowStatusInfo = () => {
    const { stage, progress } = flowState;
    
    switch (stage) {
      case 'discovery':
      case 'matching':
        return {
          title: 'Ready to Connect',
          description: 'Start a conversation to begin the roommate matching process',
          icon: MessageCircle,
          color: colors.primary[600],
          actionText: 'Start Conversation',
          canProceed: true
        };
      case 'mutual_interest':
      case 'chat_initiated':
        return {
          title: 'Conversation Started',
          description: 'Continue chatting and propose an agreement when ready',
          icon: CheckCircle,
          color: colors.success[600],
          actionText: 'Continue Chat',
          canProceed: true
        };
      case 'agreement_proposed':
      case 'agreement_customizing':
      case 'agreement_reviewing':
        return {
          title: 'Agreement in Progress',
          description: 'Review and customize your roommate agreement',
          icon: FileText,
          color: colors.warning[600],
          actionText: 'View Agreement',
          canProceed: true
        };
      case 'agreement_approving':
      case 'signature_collection':
        return {
          title: 'Awaiting Signatures',
          description: 'Agreement is ready for digital signatures',
          icon: Clock,
          color: colors.blue[600],
          actionText: 'Sign Agreement',
          canProceed: true
        };
      case 'agreement_active':
        return {
          title: 'Agreement Active',
          description: 'Your roommate agreement is fully executed',
          icon: CheckCircle,
          color: colors.success[600],
          actionText: 'View Agreement',
          canProceed: false
        };
      default:
        return {
          title: 'Ready to Connect',
          description: 'Start a conversation to begin the roommate matching process',
          icon: MessageCircle,
          color: colors.primary[600],
          actionText: 'Start Conversation',
          canProceed: true
        };
    }
  };

  const handleFlowAction = () => {
    const { stage, progress } = flowState;
    
    switch (stage) {
      case 'discovery':
      case 'matching':
        handleStartMatchingFlow();
        break;
      case 'mutual_interest':
      case 'chat_initiated':
        // Navigate to existing chat
        if (progress?.current_stage && matchProfile) {
          const name = `${matchProfile.first_name || ''} ${matchProfile.last_name || ''}`.trim();
          router.push({
            pathname: '/chat',
            params: {
              recipientId: matchId,
              recipientName: name,
              context: 'matching_flow'
            }
          });
        }
        break;
      case 'agreement_proposed':
      case 'agreement_customizing':
      case 'agreement_reviewing':
      case 'agreement_approving':
      case 'signature_collection':
        // Navigate to agreement flow
        router.push({
          pathname: '/agreement',
          params: { otherUserId: matchId }
        });
        break;
      case 'agreement_active':
        // Navigate to agreement details
        if (progress?.current_stage) {
          router.push('/agreement/details');
        }
        break;
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{
            headerShown: false
          }}
        />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color={colors.gray[800]} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Match Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={styles.loadingText}>Loading match details...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (error || !matchProfile || !compatibility) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen 
          options={{
            headerShown: false
          }}
        />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color={colors.gray[800]} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Match Details</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error[500]} />
          <Text style={styles.errorText}>{error || 'Failed to load match details'}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              setLoading(true);
              setError(null);
              // This will trigger the useEffect again
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const flowStatusInfo = getFlowStatusInfo();
  const StatusIcon = flowStatusInfo.icon;
  
  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          headerShown: false
        }}
      />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={colors.gray[800]} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Match Details</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        {/* Profile header section */}
        <View style={styles.profileHeader}>
          <Image
            source={{ 
              uri: matchProfile.avatar_url || 
              'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1287&q=80' 
            }}
            style={styles.profileImage}
          />
          
          <Text style={styles.profileName}>
            {matchProfile.first_name} {matchProfile.last_name || ''}
          </Text>
          
          {matchProfile.occupation && (
            <Text style={styles.profileOccupation}>{matchProfile.occupation}</Text>
          )}
          
          {matchProfile.location && (
            <View style={styles.locationContainer}>
              <MapPin size={16} color={colors.gray[500]} />
              <Text style={styles.locationText}>{matchProfile.location}</Text>
            </View>
          )}
          
          <View style={styles.compatibilityBadge}>
            <Text style={styles.compatibilityText}>{compatibility.score}% Match</Text>
          </View>
        </View>

        {/* Flow Progress Section */}
        <View style={styles.flowProgressSection}>
          <View style={styles.flowStatusHeader}>
            <StatusIcon size={24} color={flowStatusInfo.color} />
            <View style={styles.flowStatusInfo}>
              <Text style={styles.flowStatusTitle}>{flowStatusInfo.title}</Text>
              <Text style={styles.flowStatusDescription}>{flowStatusInfo.description}</Text>
            </View>
          </View>

          {/* Progress indicators */}
          {flowState.progress && (
            <View style={styles.progressContainer}>
              <Text style={styles.progressTitle}>Progress</Text>
              <View style={styles.progressSteps}>
                {['Chat', 'Agreement', 'Signatures', 'Active'].map((step, index) => {
                  const isCompleted = flowState.progress?.completed_stages.includes(
                    ['chat_initiated', 'agreement_proposed', 'signature_collection', 'agreement_active'][index] as FlowStage
                  );
                  const isCurrent = flowState.stage === ['chat_initiated', 'agreement_proposed', 'signature_collection', 'agreement_active'][index];
                  
                  return (
                    <View key={step} style={styles.progressStep}>
                      <View style={[
                        styles.progressDot,
                        isCompleted && styles.progressDotCompleted,
                        isCurrent && styles.progressDotCurrent
                      ]}>
                        {isCompleted && <CheckCircle size={12} color="white" />}
                      </View>
                      <Text style={[
                        styles.progressStepText,
                        isCompleted && styles.progressStepTextCompleted,
                        isCurrent && styles.progressStepTextCurrent
                      ]}>
                        {step}
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          )}

          {/* Flow errors */}
          {flowState.error && (
            <View style={styles.flowErrorContainer}>
              <AlertCircle size={16} color={colors.error[500]} />
              <Text style={styles.flowErrorText}>{flowState.error}</Text>
            </View>
          )}
        </View>
        
        {/* Compatibility details section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Why You Matched</Text>
          
          <View style={styles.scoreBreakdown}>
            <View style={styles.scoreItem}>
              <Text style={styles.scoreValue}>{compatibility.score}%</Text>
              <Text style={styles.scoreLabel}>Overall</Text>
            </View>
            
            {compatibility.locationCompatibility && (
              <View style={styles.scoreItem}>
                <Text style={styles.scoreValue}>{compatibility.locationCompatibility.score}%</Text>
                <Text style={styles.scoreLabel}>Location</Text>
              </View>
            )}
            
            {/* Additional scores could be shown here */}
          </View>
          
          {/* Positive factors */}
          <View style={styles.factorsContainer}>
            <Text style={styles.factorTitle}>Strengths</Text>
            {compatibility.positiveFactors.map((factor, index) => (
              <View key={index} style={styles.factorItem}>
                <CheckCircle size={16} color={colors.success[500]} style={styles.factorIcon} />
                <Text style={styles.factorText}>{factor}</Text>
              </View>
            ))}
          </View>
          
          {/* Potential challenges */}
          {compatibility.negativeFactors.length > 0 && (
            <View style={styles.factorsContainer}>
              <Text style={styles.factorTitle}>Potential Challenges</Text>
              {compatibility.negativeFactors.map((factor, index) => (
                <View key={index} style={styles.factorItem}>
                  <AlertCircle size={16} color={colors.warning[500]} style={styles.factorIcon} />
                  <Text style={styles.factorText}>{factor}</Text>
                </View>
              ))}
            </View>
          )}
          
          {/* Location compatibility */}
          {compatibility.locationCompatibility && (
            <View style={styles.factorsContainer}>
              <Text style={styles.factorTitle}>Location Compatibility</Text>
              <View style={styles.factorItem}>
                <Home size={16} color={colors.primary[500]} style={styles.factorIcon} />
                <Text style={styles.factorText}>
                  {compatibility.locationCompatibility.score}% Location Match
                </Text>
              </View>
              
              {/* Show matching locations if they exist */}
              {compatibility.locationCompatibility.matchingLocations && 
               compatibility.locationCompatibility.matchingLocations.length > 0 && (
                <View style={styles.factorItem}>
                  <MapPin size={16} color={colors.primary[500]} style={styles.factorIcon} />
                  <Text style={styles.factorText}>
                    Matching locations: {compatibility.locationCompatibility.matchingLocations.join(', ')}
                  </Text>
                </View>
              )}
              
              {/* Show match reasons if they exist */}
              {compatibility.locationCompatibility.matchReasons && 
               compatibility.locationCompatibility.matchReasons.length > 0 && (
                <View style={styles.factorItem}>
                  <CheckCircle size={16} color={colors.primary[500]} style={styles.factorIcon} />
                  <Text style={styles.factorText}>
                    Reason: {compatibility.locationCompatibility.matchReasons[0]}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
        
        {/* Recommended next steps */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Next Steps</Text>
          <Text style={styles.sectionDescription}>
            {flowStatusInfo.canProceed 
              ? 'Continue the roommate matching process by taking the next step in your journey.'
              : 'Your roommate agreement is complete. You can view the details anytime.'
            }
          </Text>
        </View>
        
        {/* Action buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={handleFlowAction}
            disabled={isStartingFlow || flowState.isLoading}
          >
            {(isStartingFlow || flowState.isLoading) ? (
              <View style={styles.loadingButton}>
                <ActivityIndicator size="small" color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>Processing...</Text>
              </View>
            ) : (
              <>
                <StatusIcon size={20} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={styles.primaryButtonText}>{flowStatusInfo.actionText}</Text>
                <ArrowRight size={16} color="#FFFFFF" style={styles.buttonArrow} />
              </>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handleViewProfile}
          >
            <User size={20} color={colors.primary[600]} style={styles.buttonIcon} />
            <Text style={styles.secondaryButtonText}>View Full Profile</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  backButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 16,
    color: colors.error[600],
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary[600],
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  profileHeader: {
    backgroundColor: 'white',
    padding: 24,
    alignItems: 'center',
    position: 'relative',
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  profileName: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 4,
  },
  profileOccupation: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    marginLeft: 4,
    fontSize: 14,
    color: colors.gray[600],
  },
  compatibilityBadge: {
    position: 'absolute',
    top: 24,
    right: 24,
    backgroundColor: colors.primary[600],
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  compatibilityText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  sectionContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 20,
    marginTop: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
    marginBottom: 8,
  },
  scoreBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  scoreItem: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary[600],
  },
  scoreLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
  },
  factorsContainer: {
    marginBottom: 20,
  },
  factorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  factorItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  factorIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  factorText: {
    fontSize: 14,
    color: colors.gray[700],
    flex: 1,
    lineHeight: 20,
  },
  actionContainer: {
    paddingHorizontal: 24,
    paddingTop: 20,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 10,
  },
  primaryButton: {
    backgroundColor: colors.primary[600],
  },
  secondaryButton: {
    backgroundColor: colors.primary[50],
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: colors.primary[600],
    fontSize: 16,
    fontWeight: '600',
  },
  loadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 10,
  },
  buttonArrow: {
    marginLeft: 8,
  },
  flowProgressSection: {
    backgroundColor: 'white',
    padding: 24,
    marginTop: 12,
  },
  flowStatusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  flowStatusInfo: {
    marginLeft: 12,
  },
  flowStatusTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 4,
  },
  flowStatusDescription: {
    fontSize: 14,
    color: colors.gray[600],
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.gray[900],
    marginBottom: 8,
  },
  progressSteps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressStep: {
    alignItems: 'center',
  },
  progressDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressDotCompleted: {
    backgroundColor: colors.success[500],
    borderColor: colors.success[500],
  },
  progressDotCurrent: {
    backgroundColor: colors.primary[600],
    borderColor: colors.primary[600],
  },
  progressStepText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  progressStepTextCompleted: {
    fontWeight: '700',
    color: colors.success[600],
  },
  progressStepTextCurrent: {
    fontWeight: '700',
    color: colors.primary[600],
  },
  flowErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: colors.error[200],
    borderRadius: 8,
  },
  flowErrorText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.error[600],
  },
});
