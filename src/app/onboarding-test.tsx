import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { useTheme } from '@design-system/ThemeProvider';

export default function OnboardingTest() {
  const theme = useTheme();
  const styles = createStyles(theme);

  const resetOnboarding = async () => {
    try {
      await AsyncStorage.removeItem('onboardingCompleted');
      console.log('🟡 [OnboardingTest] Onboarding status reset');
      Alert.alert('Success', 'Onboarding status reset! Going to splash...', [
        { text: 'OK', onPress: () => router.replace('/splash') }
      ]);
    } catch (error) {
      console.error('🔴 [OnboardingTest] Error resetting onboarding:', error);
      Alert.alert('Error', 'Failed to reset onboarding status');
    }
  };

  const checkOnboardingStatus = async () => {
    try {
      const status = await AsyncStorage.getItem('onboardingCompleted');
      console.log('🟡 [OnboardingTest] Current onboarding status:', status);
      Alert.alert('Onboarding Status', `Current status: ${status || 'not set'}`);
    } catch (error) {
      console.error('🔴 [OnboardingTest] Error checking onboarding:', error);
      Alert.alert('Error', 'Failed to check onboarding status');
    }
  };

  const goToOnboarding = () => {
    router.push('/onboarding');
  };

  const goToSplash = () => {
    router.replace('/splash');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Onboarding Flow Test</Text>
        <Text style={styles.subtitle}>
          Test the onboarding flow and reset functionality
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={checkOnboardingStatus}>
            <Text style={styles.buttonText}>Check Onboarding Status</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.button, styles.resetButton]} onPress={resetOnboarding}>
            <Text style={styles.buttonText}>Reset Onboarding & Go to Splash</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={goToOnboarding}>
            <Text style={styles.buttonText}>Go to Onboarding (Direct)</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={goToSplash}>
            <Text style={styles.buttonText}>Go to Splash</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Expected Flow:</Text>
          <Text style={styles.infoText}>1. Splash Screen</Text>
          <Text style={styles.infoText}>2. Onboarding (3 slides)</Text>
          <Text style={styles.infoText}>3. Auth Screen (Login/Register)</Text>
          <Text style={styles.infoText}>4. Main App</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors?.background || '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: theme.colors?.text || '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: theme.colors?.textSecondary || '#6B7280',
  },
  buttonContainer: {
    gap: 15,
    marginBottom: 40,
  },
  button: {
    backgroundColor: theme.colors?.primary || '#4F46E5',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  resetButton: {
    backgroundColor: theme.colors?.error || '#EF4444',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  infoContainer: {
    backgroundColor: theme.colors?.surface || '#F9FAFB',
    padding: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors?.border || '#E5E7EB',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: theme.colors?.text || '#1F2937',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: theme.colors?.textSecondary || '#6B7280',
  },
}); 