/**
 * Root Layout - Complete Setup with Auth Provider and <PERSON>rror Boundaries
 * This layout provides the AuthProvider, Error Boundaries, and configures all necessary screens
 */

import React, { useEffect } from 'react';
import { Stack, useRouter, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { QueryClientProvider } from '@tanstack/react-query';
import { AuthContextAdapterProvider, useAuthAdapter } from '@context/AuthContextAdapter';
import { AuthProvider } from '@context/AuthContext';
import { ThemeProvider } from '@design-system';
import { ToastProvider } from '@core/errors/ToastContext';
import { FavoritesProvider } from '../contexts/FavoritesContext';
import { TestingProvider } from '../components/testing/TestingProvider';
import { View, Text } from 'react-native';

import { queryClient } from '@utils/queryClient';
import { logEnvironmentValidation } from '@utils/validateEnvironment';
import { useSession } from '@hooks/useSession';
import { applyGlobalColorPatch } from '@utils/globalColorPatch';
import { applyStylePatches } from '@utils/stylePatches';

// Error Boundary for React errors including hooks violations
class AuthErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🔴 [AuthErrorBoundary] Caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🔴 [AuthErrorBoundary] Error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
            Authentication Error
          </Text>
          <Text style={{ textAlign: 'center', color: '#666' }}>
            There was an issue with the authentication system. Please restart the app.
          </Text>
          <Text style={{ marginTop: 10, fontSize: 12, color: '#999' }}>
            Error: {this.state.error?.message}
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

// Session management component
function SessionManager() {
  useSession(); // Initialize session management
  return null;
}

// Navigation handler to manage auth state changes
function NavigationHandler() {
  const router = useRouter();
  const segments = useSegments();
  const { authState, authLoaded } = useAuthAdapter();

  useEffect(() => {
    if (!authLoaded) return; // Wait for auth to be loaded

    const inAuthGroup = segments[0] === '(auth)';
    const inTabsGroup = segments[0] === '(tabs)';
    const inOnboarding = segments[0] === 'onboarding';
    const inSplash = segments[0] === 'splash';
    const inProviderFlow = segments[0] === 'provider';

    console.log('🔄 [NavigationHandler] Auth state changed:', {
      isAuthenticated: authState.isAuthenticated,
      authStatus: authState.authStatus,
      currentSegments: segments.join('/'),
      inAuthGroup,
      inTabsGroup,
      inOnboarding,
      inSplash,
      inProviderFlow,
    });

    // Don't interfere if user is in splash, onboarding, or provider registration flow
    if (inSplash || inOnboarding || inProviderFlow) {
      console.log('🔄 [NavigationHandler] User in splash/onboarding/provider flow, not interfering');
      return;
    }

    // Handle logout: if user is not authenticated and not in auth group, redirect to login
    if (!authState.isAuthenticated && authState.authStatus === 'unauthenticated' && !inAuthGroup) {
      console.log('🔄 [NavigationHandler] User logged out, redirecting to login');
      router.replace('/(auth)/login');
      return;
    }

    // Handle login: if user is authenticated and in auth group, redirect to main app
    if (authState.isAuthenticated && authState.authStatus === 'authenticated' && inAuthGroup) {
      console.log('🔄 [NavigationHandler] User logged in, redirecting to main app');
      router.replace('/(tabs)');
      return;
    }
  }, [authState.isAuthenticated, authState.authStatus, segments, authLoaded, router]);

  return null;
}

export default function RootLayout() {
  const router = useRouter();
  const segments = useSegments();
  
  // Add navigation debugging
  useEffect(() => {
    if (__DEV__) {
      console.log('🔄 Navigation changed:', segments.join('/'));
    }
  }, [segments]);

  // Validate environment variables on app startup
  React.useEffect(() => {
    logEnvironmentValidation();
    
    // Apply global color patches to prevent [object Object] issues
    try {
      applyGlobalColorPatch();
      applyStylePatches();
      console.log('✅ Global color patches applied successfully');
    } catch (error) {
      console.warn('⚠️ Failed to apply global color patches:', error);
    }
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <QueryClientProvider client={queryClient}>

            <AuthErrorBoundary>
              <AuthContextAdapterProvider>
                <AuthProvider>
                  <FavoritesProvider>
                    <TestingProvider>
                  <SessionManager />
                  <NavigationHandler />
                  <ToastProvider>
                <Stack
                  screenOptions={{
                    headerStyle: {
                      backgroundColor: '#4F46E5',
                    },
                    headerTintColor: '#fff',
                    headerTitleStyle: {
                      fontWeight: 'bold',
                    },
                  }}
                >
                  {/* Splash Screen */}
                  <Stack.Screen
                    name="splash"
                    options={{
                      headerShown: false,
                      gestureEnabled: false,
                    }}
                  />

                  {/* Onboarding */}
                  <Stack.Screen
                    name="onboarding"
                    options={{
                      headerShown: false,
                      gestureEnabled: false,
                      animation: 'fade'
                    }}
                  />

                  {/* Auth Screens */}
                  <Stack.Screen
                    name="(auth)"
                    options={{
                      headerShown: false,
                      gestureEnabled: false,
                    }}
                  />

                  {/* Main App */}
                  <Stack.Screen
                    name="(tabs)"
                    options={{
                      headerShown: false,
                    }}
                  />



                  {/* Index (fallback) */}
                  <Stack.Screen
                    name="index"
                    options={{
                      title: 'RoomieMatch AI',
                      headerShown: true,
                    }}
                  />
                </Stack>
                <StatusBar style="light" />
              </ToastProvider>
                    </TestingProvider>
                  </FavoritesProvider>
                </AuthProvider>
              </AuthContextAdapterProvider>
        </AuthErrorBoundary>
          </QueryClientProvider>
      </ThemeProvider>
    </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
