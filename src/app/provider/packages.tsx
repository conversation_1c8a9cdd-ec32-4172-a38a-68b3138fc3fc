import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, TextInput, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { ArrowLeft, Plus, Edit2, Trash2, Package, Tag, Check } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { pricingConfigService } from '@services/pricingConfigService';
import { supabase } from "@utils/supabaseUtils";
import { showToast } from '@utils/toast';
import { useAuth } from '@context/AuthContext';

export default function PackagesScreen() {
  const { colors } = useTheme();
  const { state, actions } = useAuth();
  const [loading, setLoading] = useState(true);
  const [packages, setPackages] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [providerId, setProviderId] = useState<string | null>(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [currentPackage, setCurrentPackage] = useState<any>(null);
  const [formData, setFormData] = useState<any>({
    name: '',
    description: '',
    price: '',
    original_price: '',
    services: [],
    is_featured: false,
    is_active: true,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // First get the provider ID for the current user
      const { data: providerData, error: providerError } = await supabase
        .from('service_providers')
        .select('id')
        .eq('user_id', authState.user?.id)
        .single();
      
      if (providerError) throw providerError;
      
      setProviderId(providerData.id);
      
      // Load provider services
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('id, name, price')
        .eq('provider_id', providerData.id)
        .eq('is_active', true);
      
      if (servicesError) throw servicesError;
      
      setServices(servicesData || []);
      
      // Load existing packages
      const packages = await pricingConfigService.getServicePackages(providerData.id);
      setPackages(packages);
      
    } catch (error) {
      console.error('Error loading provider data:', error);
      showToast('Failed to load provider data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPackage = () => {
    setCurrentPackage(null);
    setFormData({
      name: '',
      description: '',
      price: '',
      original_price: '',
      services: [],
      is_featured: false,
      is_active: true,
    });
    setIsEditModalVisible(true);
  };

  const handleEditPackage = (pkg: any) => {
    setCurrentPackage(pkg);
    setFormData({
      name: pkg.name,
      description: pkg.description || '',
      price: pkg.price.toString(),
      original_price: pkg.original_price ? pkg.original_price.toString() : '',
      services: pkg.services || [],
      is_featured: pkg.is_featured,
      is_active: pkg.is_active,
      valid_from: pkg.valid_from,
      valid_until: pkg.valid_until,
    });
    setIsEditModalVisible(true);
  };

  const handleDeletePackage = async (pkg: any) => {
    Alert.alert(
      'Delete Package',
      `Are you sure you want to delete the "${pkg.name}" package?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await pricingConfigService.deleteServicePackage(pkg.id);
              showToast('Package deleted', 'success');
              loadData();
            } catch (error) {
              console.error('Error deleting package:', error);
              showToast('Failed to delete package', 'error');
            }
          }
        }
      ]
    );
  };

  const handleSavePackage = async () => {
    try {
      // Validate form data
      if (!formData.name || !formData.price || formData.services.length === 0) {
        showToast('Name, price, and at least one service are required', 'error');
        return;
      }

      const packageData = {
        provider_id: providerId as string,
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        original_price: formData.original_price ? parseFloat(formData.original_price) : null,
        services: formData.services,
        is_featured: formData.is_featured,
        is_active: formData.is_active,
        valid_from: formData.valid_from,
        valid_until: formData.valid_until,
      };

      if (currentPackage) {
        await pricingConfigService.updateServicePackage(currentPackage.id, packageData);
        showToast('Package updated', 'success');
      } else {
        await pricingConfigService.createServicePackage(packageData);
        showToast('Package created', 'success');
      }

      setIsEditModalVisible(false);
      loadData();
    } catch (error) {
      console.error('Error saving package:', error);
      showToast('Failed to save package', 'error');
    }
  };

  const toggleServiceSelection = (serviceId: string) => {
    const updatedServices = [...formData.services];
    const index = updatedServices.findIndex(s => s.service_id === serviceId);
    
    if (index >= 0) {
      // Remove service
      updatedServices.splice(index, 1);
    } else {
      // Add service with default quantity
      updatedServices.push({
        service_id: serviceId,
        quantity: 1
      });
    }
    
    setFormData({
      ...formData,
      services: updatedServices
    });
  };

  const updateServiceQuantity = (serviceId: string, quantity: number) => {
    const updatedServices = formData.services.map((s: any) => {
      if (s.service_id === serviceId) {
        return { ...s, quantity };
      }
      return s;
    });
    
    setFormData({
      ...formData,
      services: updatedServices
    });
  };

  const isServiceSelected = (serviceId: string) => {
    return formData.services.some((s: any) => s.service_id === serviceId);
  };

  const getServiceQuantity = (serviceId: string) => {
    const service = formData.services.find((s: any) => s.service_id === serviceId);
    return service ? service.quantity : 1;
  };

  const getServiceName = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    return service ? service.name : 'Unknown Service';
  };

  const getServicePrice = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    return service ? service.price : 0;
  };

  const calculateSavings = (pkg: any) => {
    if (!pkg.original_price || pkg.original_price <= pkg.price) return null;
    
    const savings = pkg.original_price - pkg.price;
    const percentage = Math.round((savings / pkg.original_price) * 100);
    
    return {
      amount: savings,
      percentage
    };
  };

  const renderPackagesList = () => {
    if (packages.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No service packages created yet. Create packages to offer bundled services at discounted rates.
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={packages}
        keyExtractor={item => item.id}
        renderItem={({ item }) => {
          const savings = calculateSavings(item);
          
          return (
            <View style={[styles.packageCard, { backgroundColor: colors.surface }]}>
              <View style={styles.packageHeader}>
                <View style={styles.packageTitleContainer}>
                  <Text style={[styles.packageTitle, { color: colors.text }]}>{item.name}</Text>
                  {item.is_featured && (
                    <View style={[styles.featuredBadge, { backgroundColor: colors.primary + '20' }]}>
                      <Text style={[styles.featuredBadgeText, { color: colors.primary }]}>Featured</Text>
                    </View>
                  )}
                </View>
                <View style={styles.packageActions}>
                  <TouchableOpacity 
                    style={styles.actionButton} 
                    onPress={() => handleEditPackage(item)}
                  >
                    <Edit2 size={18} color={colors.primary} />
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.actionButton} 
                    onPress={() => handleDeletePackage(item)}
                  >
                    <Trash2 size={18} color="#ef4444" />
                  </TouchableOpacity>
                </View>
              </View>
              
              <View style={styles.packageContent}>
                {item.description && (
                  <Text style={[styles.packageDescription, { color: colors.textLight }]}>
                    {item.description}
                  </Text>
                )}
                
                <View style={styles.priceContainer}>
                  <Text style={[styles.packagePrice, { color: colors.primary }]}>
                    ${parseFloat(item.price).toFixed(2)}
                  </Text>
                  
                  {item.original_price && (
                    <Text style={[styles.originalPrice, { color: colors.textLight }]}>
                      ${parseFloat(item.original_price).toFixed(2)}
                    </Text>
                  )}
                  
                  {savings && (
                    <View style={[styles.savingsBadge, { backgroundColor: '#10b981' + '20' }]}>
                      <Text style={[styles.savingsText, { color: '#10b981' }]}>
                        Save {savings.percentage}%
                      </Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.servicesContainer}>
                  <Text style={[styles.servicesTitle, { color: colors.text }]}>
                    Included Services:
                  </Text>
                  
                  {item.services.map((service: any, index: number) => (
                    <View key={service.service_id} style={styles.serviceItem}>
                      <Text style={[styles.serviceQuantity, { color: colors.primary }]}>
                        {service.quantity}x
                      </Text>
                      <Text style={[styles.serviceName, { color: colors.text }]}>
                        {getServiceName(service.service_id)}
                      </Text>
                    </View>
                  ))}
                </View>
                
                {(item.valid_from || item.valid_until) && (
                  <View style={styles.validityContainer}>
                    <Text style={[styles.validityText, { color: colors.textLight }]}>
                      {item.valid_from && `Available from ${new Date(item.valid_from).toLocaleDateString()}`}
                      {item.valid_from && item.valid_until && ' to '}
                      {item.valid_until && `${new Date(item.valid_until).toLocaleDateString()}`}
                    </Text>
                  </View>
                )}
              </View>
              
              {!item.is_active && (
                <View style={[styles.inactiveBadge, { backgroundColor: colors.border }]}>
                  <Text style={[styles.inactiveBadgeText, { color: colors.textLight }]}>Inactive</Text>
                </View>
              )}
            </View>
          );
        }}
        contentContainerStyle={styles.listContent}
      />
    );
  };

  const renderEditModal = () => {
    if (!isEditModalVisible) return null;
    
    const title = currentPackage ? 'Edit Package' : 'Add Package';
    
    return (
      <View style={[
        styles.modalOverlay, 
        { backgroundColor: 'rgba(0, 0, 0, 0.5)' }
      ]}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>{title}</Text>
            <TouchableOpacity onPress={() => setIsEditModalVisible(false)}>
              <ArrowLeft size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={[1]} // Dummy data to make FlatList work
            renderItem={() => (
              <View style={styles.modalForm}>
                <View style={styles.formField}>
                  <Text style={[styles.fieldLabel, { color: colors.text }]}>Package Name*</Text>
                  <TextInput
                    style={[
                      styles.textInput, 
                      { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
                    ]}
                    placeholder="Enter package name"
                    placeholderTextColor={colors.textLight}
                    value={formData.name}
                    onChangeText={text => setFormData({ ...formData, name: text })}
                  />
                </View>
                
                <View style={styles.formField}>
                  <Text style={[styles.fieldLabel, { color: colors.text }]}>Description</Text>
                  <TextInput
                    style={[
                      styles.textInput, 
                      styles.textArea,
                      { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
                    ]}
                    placeholder="Enter package description"
                    placeholderTextColor={colors.textLight}
                    value={formData.description}
                    onChangeText={text => setFormData({ ...formData, description: text })}
                    multiline
                    numberOfLines={3}
                  />
                </View>
                
                <View style={[styles.formField, styles.priceFields]}>
                  <View style={styles.priceField}>
                    <Text style={[styles.fieldLabel, { color: colors.text }]}>Package Price*</Text>
                    <View style={styles.priceInputContainer}>
                      <View style={[styles.currencySymbol, { backgroundColor: colors.primary + '20' }]}>
                        <Text style={[styles.currencyText, { color: colors.primary }]}>$</Text>
                      </View>
                      <TextInput
                        style={[
                          styles.textInput, 
                          styles.priceInput,
                          { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0.00"
                        placeholderTextColor={colors.textLight}
                        value={formData.price}
                        onChangeText={text => {
                          const filtered = text.replace(/[^0-9.]/g, '');
                          setFormData({ ...formData, price: filtered });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                  
                  <View style={styles.priceField}>
                    <Text style={[styles.fieldLabel, { color: colors.text }]}>Original Price</Text>
                    <View style={styles.priceInputContainer}>
                      <View style={[styles.currencySymbol, { backgroundColor: colors.textLight + '20' }]}>
                        <Text style={[styles.currencyText, { color: colors.textLight }]}>$</Text>
                      </View>
                      <TextInput
                        style={[
                          styles.textInput, 
                          styles.priceInput,
                          { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0.00"
                        placeholderTextColor={colors.textLight}
                        value={formData.original_price}
                        onChangeText={text => {
                          const filtered = text.replace(/[^0-9.]/g, '');
                          setFormData({ ...formData, original_price: filtered });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                </View>
                
                <View style={styles.formField}>
                  <Text style={[styles.fieldLabel, { color: colors.text }]}>Select Services*</Text>
                  {services.length === 0 ? (
                    <Text style={[styles.noServicesText, { color: colors.textLight }]}>
                      No services available. Create services first.
                    </Text>
                  ) : (
                    <View style={styles.servicesList}>
                      {services.map(service => (
                        <View key={service.id} style={styles.serviceSelectionItem}>
                          <TouchableOpacity
                            style={[
                              styles.checkbox,
                              isServiceSelected(service.id) && {
                                backgroundColor: colors.primary,
                                borderColor: colors.primary
                              },
                              { borderColor: colors.border }
                            ]}
                            onPress={() => toggleServiceSelection(service.id)}
                          >
                            {isServiceSelected(service.id) && (
                              <Check size={16} color="#ffffff" />
                            )}
                          </TouchableOpacity>
                          
                          <View style={styles.serviceInfo}>
                            <Text style={[styles.serviceSelectName, { color: colors.text }]}>
                              {service.name}
                            </Text>
                            <Text style={[styles.serviceSelectPrice, { color: colors.textLight }]}>
                              ${parseFloat(service.price).toFixed(2)}
                            </Text>
                          </View>
                          
                          {isServiceSelected(service.id) && (
                            <View style={styles.quantityControl}>
                              <TouchableOpacity
                                style={[styles.quantityButton, { borderColor: colors.border }]}
                                onPress={() => {
                                  const currentQty = getServiceQuantity(service.id);
                                  if (currentQty > 1) {
                                    updateServiceQuantity(service.id, currentQty - 1);
                                  }
                                }}
                              >
                                <Text style={[styles.quantityButtonText, { color: colors.text }]}>-</Text>
                              </TouchableOpacity>
                              
                              <Text style={[styles.quantityText, { color: colors.text }]}>
                                {getServiceQuantity(service.id)}
                              </Text>
                              
                              <TouchableOpacity
                                style={[styles.quantityButton, { borderColor: colors.border }]}
                                onPress={() => {
                                  const currentQty = getServiceQuantity(service.id);
                                  updateServiceQuantity(service.id, currentQty + 1);
                                }}
                              >
                                <Text style={[styles.quantityButtonText, { color: colors.text }]}>+</Text>
                              </TouchableOpacity>
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                  )}
                </View>
                
                <View style={styles.formField}>
                  <Text style={[styles.fieldLabel, { color: colors.text }]}>Package Options</Text>
                  
                  <View style={styles.checkboxContainer}>
                    <TouchableOpacity
                      style={[
                        styles.checkbox,
                        formData.is_featured && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary
                        },
                        { borderColor: colors.border }
                      ]}
                      onPress={() => setFormData({ ...formData, is_featured: !formData.is_featured })}
                    >
                      {formData.is_featured && (
                        <Check size={16} color="#ffffff" />
                      )}
                    </TouchableOpacity>
                    <Text style={[styles.checkboxLabel, { color: colors.text }]}>
                      Feature this package (highlight in listings)
                    </Text>
                  </View>
                  
                  <View style={[styles.checkboxContainer, { marginTop: 12 }]}>
                    <TouchableOpacity
                      style={[
                        styles.checkbox,
                        formData.is_active && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary
                        },
                        { borderColor: colors.border }
                      ]}
                      onPress={() => setFormData({ ...formData, is_active: !formData.is_active })}
                    >
                      {formData.is_active && (
                        <Check size={16} color="#ffffff" />
                      )}
                    </TouchableOpacity>
                    <Text style={[styles.checkboxLabel, { color: colors.text }]}>
                      Active
                    </Text>
                  </View>
                </View>
              </View>
            )}
            keyExtractor={() => 'form'}
            ListFooterComponent={
              <View style={styles.modalActions}>
                <Button
                  title="Cancel"
                  onPress={() => setIsEditModalVisible(false)}
                  variant="secondary"
                  style={styles.cancelButton}
                />
                <Button
                  title="Save Package"
                  onPress={handleSavePackage}
                />
              </View>
            }
          />
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Service Packages',
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textLight }]}>
            Loading packages...
          </Text>
        </View>
      ) : (
        <View style={styles.content}>
          <Text style={[styles.pageDescription, { color: colors.textLight }]}>
            Create service packages to bundle multiple services at discounted rates.
          </Text>
          
          <View style={styles.packagesContainer}>
            {renderPackagesList()}
          </View>
          
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={handleAddPackage}
          >
            <Plus size={24} color="#ffffff" />
          </TouchableOpacity>
          
          {renderEditModal()}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  pageDescription: {
    fontSize: 16,
    marginBottom: 20,
  },
  packagesContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  listContent: {
    paddingBottom: 80, // Space for floating button
  },
  packageCard: {
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  packageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  packageTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  packageTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  featuredBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  featuredBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  packageActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  packageContent: {
    padding: 16,
  },
  packageDescription: {
    fontSize: 14,
    marginBottom: 12,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: '700',
  },
  originalPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  savingsBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '500',
  },
  servicesContainer: {
    marginTop: 8,
  },
  servicesTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  serviceQuantity: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  serviceName: {
    fontSize: 14,
  },
  validityContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  validityText: {
    fontSize: 12,
  },
  inactiveBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
  },
  inactiveBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalForm: {
    paddingBottom: 20,
  },
  formField: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  priceFields: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceField: {
    flex: 1,
    marginHorizontal: 4,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    width: 40,
    height: 46,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  currencyText: {
    fontSize: 18,
    fontWeight: '600',
  },
  priceInput: {
    flex: 1,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderLeftWidth: 0,
  },
  noServicesText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  servicesList: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#e2e8f0',
    padding: 8,
    maxHeight: 200,
  },
  serviceSelectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceSelectName: {
    fontSize: 14,
    fontWeight: '500',
  },
  serviceSelectPrice: {
    fontSize: 12,
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderWidth: 1,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: 8,
    minWidth: 20,
    textAlign: 'center',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  cancelButton: {
    marginRight: 8,
  },
});