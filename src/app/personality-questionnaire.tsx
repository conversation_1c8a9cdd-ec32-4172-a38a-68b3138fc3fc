import React, { useState, useEffect } from 'react';

import { Stack, useRouter } from 'expo-router';
import { ChevronLeft, CheckCircle } from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useToast } from '@context/ToastContext';
import { useTheme } from '@design-system';
import { SliderQuestion } from '@components/personality/SliderQuestion';
// Dynamic import to avoid circular dependencies - see usage below
// import { authService } from '@services/authService';
import { personalityService, PersonalityTraitCategory } from '@services/personalityService';
import { useColorFix } from '@hooks/useColorFix';

export default function PersonalityQuestionnaire() {
  const { fix } = useColorFix();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();
  const toast = useToast();

  const [userId, setUserId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [responses, setResponses] = useState<Record<string, number>>({});
  const [questions, setQuestions] = useState<
    Array<{
      id: string;
      question: string;
      category: PersonalityTraitCategory;
    }>
  >([]);

  // Define the questions grouped by personality category
  const questionGroups = [
    {
      title: 'Basic Personality',
      questions: [
        {
          id: 'extraversion',
          question: 'I enjoy spending time with large groups of people',
          category: PersonalityTraitCategory.Extraversion,
        },
        {
          id: 'introversion',
          question: 'I need plenty of alone time to recharge',
          category: PersonalityTraitCategory.Extraversion,
        },
        {
          id: 'openness',
          question: 'I enjoy trying new experiences and activities',
          category: PersonalityTraitCategory.Openness,
        },
        {
          id: 'conscientiousness',
          question: 'I keep my belongings neat and organized',
          category: PersonalityTraitCategory.Conscientiousness,
        },
        {
          id: 'agreeableness',
          question: 'I avoid conflicts and try to find compromises',
          category: PersonalityTraitCategory.Agreeableness,
        },
      ],
    },
    {
      title: 'Lifestyle Preferences',
      questions: [
        {
          id: 'cleanliness',
          question: 'Keeping common spaces clean is important to me',
          category: PersonalityTraitCategory.Lifestyle,
        },
        {
          id: 'noise_tolerance',
          question: "I'm comfortable with noise in my living environment",
          category: PersonalityTraitCategory.Lifestyle,
        },
        {
          id: 'social_hosting',
          question: 'I enjoy hosting friends and social gatherings',
          category: PersonalityTraitCategory.Lifestyle,
        },
        {
          id: 'sleep_schedule',
          question: 'I tend to stay up late at night',
          category: PersonalityTraitCategory.Habits,
        },
      ],
    },
    {
      title: 'Living Habits',
      questions: [
        {
          id: 'cooking',
          question: 'I enjoy cooking regularly',
          category: PersonalityTraitCategory.Habits,
        },
        {
          id: 'sharing',
          question: "I'm comfortable sharing household items",
          category: PersonalityTraitCategory.Habits,
        },
        {
          id: 'communication',
          question: 'I prefer direct communication about issues',
          category: PersonalityTraitCategory.Communication,
        },
        {
          id: 'conflict_resolution',
          question: 'I address problems as soon as they arise',
          category: PersonalityTraitCategory.ConflictResolution,
        },
      ],
    },
  ];

  useEffect(() => {
    async function initializeUser() {
      try {
        setLoading(true);
        // Dynamic import to avoid circular dependencies
        const { authService } = await import('@services/standardized');
        const { data } = await authService.getSession();
        if (data?.user?.id) {
          setUserId(data.user.id);

          // Flatten all questions into a single array
          const allQuestions = questionGroups.flatMap(group => group.questions);
          setQuestions(allQuestions);

          // Try to load existing responses if any
          try {
            const traits = await personalityService.getUserTraits(data.user.id);

            // Convert traits to response format if they exist
            const existingResponses: Record<string, number> = {};
            if (traits && Array.isArray(traits)) {
              traits.forEach(trait => {
                if (trait && trait.trait_name) {
                  existingResponses[trait.trait_name] = trait.trait_value;
                }
              });
            }

            setResponses(existingResponses);
          } catch (traitError) {
            console.warn('Could not load personality traits, using empty responses:', traitError);
            // Initialize with empty responses
            setResponses({});
          }
        } else {
          router.replace('/login');
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        toast.showToast('error', 'Could not load user data');
      } finally {
        setLoading(false);
      }
    }

    initializeUser();
  }, []);

  // Update a single response
  const handleResponseChange = (questionId: string, value: number) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value,
    }));
  };

  // Get current group of questions
  const getCurrentQuestions = () => {
    if (currentStep < questionGroups.length) {
      return questionGroups[currentStep].questions;
    }
    return [];
  };

  // Move to next step
  const handleNext = () => {
    if (currentStep < questionGroups.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  };

  // Move to previous step
  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  // Submit all responses
  const handleSubmit = async () => {
    if (!userId) {
      return;
    }

    try {
      setSubmitting(true);

      // Prepare traits array for batch update
      // Add null check for responses
      const traits = responses
        ? Object.entries(responses).map(([name, value]) => {
            // Find the category for this trait
            const question = questions.find(q => q.id === name);
            return {
              category: question?.category || PersonalityTraitCategory.Lifestyle,
              name,
              value,
            };
          })
        : [];

      // Update all traits at once
      await personalityService.setTraits(userId, traits);

      // Show success message
      toast.showToast('success', 'Personality profile updated successfully');

      // Navigate back to profile or to compatibility view
      router.back();
    } catch (error) {
      console.error('Error saving personality data:', error);
      toast.showToast('error', 'Failed to save your responses');
    } finally {
      setSubmitting(false);
    }
  };

  // Check if current step is complete
  const isCurrentStepComplete = () => {
    const currentQuestions = getCurrentQuestions();
    // Add null check for responses
    return responses ? currentQuestions.every(q => responses[q.id] !== undefined) : false;
  };

  // Calculate overall completion percentage
  const calculateCompletion = () => {
    const totalQuestions = questions.length;
    // Add null check for responses to prevent "Cannot convert undefined value to object" error
    const answeredQuestions = responses ? Object.keys(responses).length : 0;
    return totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;
  };

  if (loading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Stack.Screen
        options={{
          title: 'Personality Questionnaire',
          headerShown: false,
        }}
      />

      {/* Header */}
      <View style={[styles.header, { backgroundColor: '#FFFFFF' }]}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ChevronLeft size={24} color="#1F2937" />
        </TouchableOpacity>

        <Text style={styles.title}>Personality Profile</Text>

        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>{calculateCompletion()}% Complete</Text>
        </View>
      </View>

      {/* Question Group Title */}
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>{questionGroups[currentStep].title}</Text>
        <Text style={styles.stepDescription}>
          Step {currentStep + 1} of {questionGroups.length}
        </Text>
      </View>

      {/* Questions */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {getCurrentQuestions().map(question => (
          <SliderQuestion
            key={question.id}
            question={question.question}
            value={responses[question.id] || 50}
            onChange={value => handleResponseChange(question.id, value)}
          />
        ))}

        <View style={{ height: 100 }} />
      </ScrollView>

      {/* Bottom Action Buttons */}
      <View style={[styles.buttonContainer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[styles.button, styles.backButtonStyle, { borderColor: '#D1D5DB' }]}
          onPress={handleBack}
        >
          <Text style={[styles.buttonText, { color: '#1F2937' }]}>Back</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.nextButton,
            { backgroundColor: "#3B82F6" },
            (!isCurrentStepComplete() || submitting) && styles.disabledButton,
          ]}
          onPress={handleNext}
          disabled={!isCurrentStepComplete() || submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={[styles.buttonText, { color: '#FFFFFF' }]}>
              {currentStep < questionGroups.length - 1 ? 'Next' : 'Complete'}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  progressContainer: {
    backgroundColor: '#ecfdf5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#10b981',
  },
  stepHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: 'white',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  button: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonStyle: {
    backgroundColor: 'white',
    borderWidth: 1,
    marginRight: 8,
  },
  nextButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
});
