import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
  Image,
  FlatList,
  useColorScheme,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router';
import { useAuth } from '@context/AuthContext';
import { useToast } from '@components/ui/Toast';
import {
  Brain,
  BarChart3,
  Settings,
  Zap,
  Target,
  TrendingUp,
  Heart,
  Users,
  Shield,
  Globe,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Filter,
  Info,
  Award,
  Star,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ArrowLeft,
  MapPin,
  Clock,
  MessageCircle,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Sparkles,
  Search,
  Sliders,
} from 'lucide-react-native';

import {
  useSmartMatching,
  SmartMatch,
  MatchingPreferences,
  MatchingAnalytics,
  SmartMatchingSuggestion,
} from '@/hooks/useSmartMatching';
import { useProfile } from '@/hooks/useProfile';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';

const { width } = Dimensions.get('window');

// Enhanced smart matching dashboard data structures
interface DashboardTab {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  badge?: number;
}

export default function SmartMatchingDashboard() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const router = useRouter();
  const { toast } = useToast();
  const { profile } = useProfile();
  const {
    isLoading,
    error,
    matches,
    preferences,
    analytics,
    suggestions,
    findMatches,
    updatePreferences,
    refreshAnalytics,
    hasMatches,
    hasPreferences,
    hasAnalytics,
    isConfigured,
  } = useSmartMatching();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('matches');
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    analytics: true,
    suggestions: true,
    filters: false,
  });

  const tabs: DashboardTab[] = useMemo(
    () => [
      {
        id: 'matches',
        title: 'Matches',
        icon: Heart,
        badge: matches.length,
      },
      {
        id: 'analytics',
        title: 'Analytics',
        icon: BarChart3,
      },
      {
        id: 'suggestions',
        title: 'Insights',
        icon: Sparkles,
        badge: suggestions.length,
      },
      {
        id: 'preferences',
        title: 'Settings',
        icon: Sliders,
      },
    ],
    [matches.length, suggestions.length]
  );

  useEffect(() => {
    initializeDashboard();
  }, []);

  const initializeDashboard = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      await findMatches();
    } catch (error) {
      logger.error('Error initializing smart matching dashboard', 'SmartMatchingDashboard', {
        error: error instanceof Error ? error.message : String(error),
        userId: user.id,
      });
      toast?.show('Could not load matching data', 'error');
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast, findMatches]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([findMatches(), refreshAnalytics()]);
    setRefreshing(false);
  }, [findMatches, refreshAnalytics]);

  const handleToggleSection = useCallback((section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  }, []);

  const handleMatchAction = useCallback(
    async (matchId: string, action: 'like' | 'pass' | 'view') => {
      try {
        // Update analytics based on action
        if (analytics) {
          const updates = {
            user_engagement: {
              ...analytics.user_engagement,
              profiles_viewed:
                action === 'view'
                  ? analytics.user_engagement.profiles_viewed + 1
                  : analytics.user_engagement.profiles_viewed,
              matches_liked:
                action === 'like'
                  ? analytics.user_engagement.matches_liked + 1
                  : analytics.user_engagement.matches_liked,
              matches_passed:
                action === 'pass'
                  ? analytics.user_engagement.matches_passed + 1
                  : analytics.user_engagement.matches_passed,
            },
          };
          // Note: This would typically call updateAnalytics from the hook
        }

        if (action === 'like') {
          toast?.show('Match liked! They will be notified.', 'success');
        } else if (action === 'pass') {
          toast?.show("Match passed. We'll find you better matches.", 'success');
        }
      } catch (error) {
        logger.error('Error handling match action', 'SmartMatchingDashboard', {
          error: error instanceof Error ? error.message : String(error),
          matchId,
          action,
        });
        toast?.show('Could not process match action', 'error');
      }
    },
    [analytics, toast]
  );

  // Render functions
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
      <View style={styles.headerContent}>
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Smart Matching</Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            AI-powered roommate discovery • {matches.length} matches found
          </Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) }]}
            onPress={() => router.push('/(tabs)/profile/unified-preferences?tab=matching')}
          >
            <Filter size={20} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: theme.colors.primary }]}
            onPress={onRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color={theme.colors.white} />
            ) : (
              <RefreshCw size={20} color={theme.colors.white} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderTabBar = () => (
    <View style={[styles.tabBar, { backgroundColor: theme.colors.surface }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScroll}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              {
                backgroundColor: activeTab === tab.id ? theme.colors.primary : 'transparent',
              },
            ]}
            onPress={() => setActiveTab(tab.id)}
          >
            <View style={styles.tabContent}>
              <tab.icon
                size={20}
                color={activeTab === tab.id ? theme.colors.white : theme.colors.textSecondary}
              />
              <Text
                style={[
                  styles.tabText,
                  {
                    color: activeTab === tab.id ? theme.colors.white : theme.colors.textSecondary,
                  },
                ]}
              >
                {tab.title}
              </Text>
              {tab.badge !== undefined && tab.badge > 0 && (
                <View style={[styles.tabBadge, { backgroundColor: theme.colors.red }]}>
                  <Text style={[styles.tabBadgeText, { color: theme.colors.white }]}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderMatchCard = ({ item: match }: { item: SmartMatch }) => (
    <View style={[styles.matchCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.matchHeader}>
        <View style={styles.matchProfile}>
          <Image
            source={{ uri: match.profile.photos[0] || 'https://via.placeholder.com/60' }}
            style={styles.matchAvatar}
          />
          <View style={styles.matchInfo}>
            <Text style={[styles.matchName, { color: theme.colors.text }]}>
              {match.profile.full_name}
            </Text>
            <Text style={[styles.matchAge, { color: theme.colors.textSecondary }]}>
              {match.profile.age} years old
            </Text>
            <View style={styles.matchLocation}>
              <MapPin size={12} color={theme.colors.textSecondary} />
              <Text style={[styles.matchLocationText, { color: theme.colors.textSecondary }]}>
                {match.distance_km}km away
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.matchScore}>
          <View
            style={[
              styles.scoreCircle,
              {
                backgroundColor:
                  match.score.match_quality === 'excellent'
                    ? colorWithOpacity(theme.colors.green, 0.2)
                    : match.score.match_quality === 'very_good'
                      ? colorWithOpacity(theme.colors.blue, 0.2)
                      : match.score.match_quality === 'good'
                        ? colorWithOpacity(theme.colors.orange, 0.2)
                        : colorWithOpacity(theme.colors.red, 0.2),
              },
            ]}
          >
            <Text
              style={[
                styles.scoreText,
                {
                  color:
                    match.score.match_quality === 'excellent'
                      ? theme.colors.green
                      : match.score.match_quality === 'very_good'
                        ? theme.colors.blue
                        : match.score.match_quality === 'good'
                          ? theme.colors.orange
                          : theme.colors.red,
                },
              ]}
            >
              {Math.round(match.score.overall_score)}%
            </Text>
          </View>
          <Text style={[styles.scoreLabel, { color: theme.colors.textSecondary }]}>
            {match.score.match_quality ? match.score.match_quality.replace('_', ' ') : 'Unknown'}
          </Text>
        </View>
      </View>

      <View style={styles.matchDetails}>
        <Text style={[styles.matchBio, { color: theme.colors.text }]} numberOfLines={2}>
          {match.profile.bio || 'No bio available'}
        </Text>

        <View style={styles.matchInsights}>
          <View style={styles.insightItem}>
            <Brain size={14} color={theme.colors.purple} />
            <Text style={[styles.insightText, { color: theme.colors.textSecondary }]}>
              {match.compatibility_insights?.personality_match || 'Calculating...'}
            </Text>
          </View>
          <View style={styles.insightItem}>
            <Heart size={14} color={theme.colors.red} />
            <Text style={[styles.insightText, { color: theme.colors.textSecondary }]}>
              {match.mutual_interests?.length || 0} shared interests
            </Text>
          </View>
        </View>

        <View style={styles.matchReasons}>
          {(match.match_reasons || []).slice(0, 2).map((reason, index) => (
            <View
              key={index}
              style={[styles.reasonTag, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]}
            >
              <Text style={[styles.reasonText, { color: theme.colors.primary }]}>{reason}</Text>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.matchActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.red, 0.2) }]}
          onPress={() => handleMatchAction(match.profile.id, 'pass')}
        >
          <ThumbsDown size={20} color={theme.colors.red} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.blue, 0.2) }]}
          onPress={() => handleMatchAction(match.profile.id, 'view')}
        >
          <Eye size={20} color={theme.colors.blue} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.green, 0.2) }]}
          onPress={() => handleMatchAction(match.profile.id, 'like')}
        >
          <ThumbsUp size={20} color={theme.colors.green} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) }]}
          onPress={() => {
            // Use query parameters to prevent [object Object] issues
            const userId = String(match.profile.user_id).trim();
            if (userId && userId !== 'undefined' && userId !== 'null' && userId !== '[object Object]') {
              router.push(`/chat?recipientId=${encodeURIComponent(userId)}&recipientName=${encodeURIComponent(match.profile.name || 'User')}&context=match`);
            } else {
              console.warn('Invalid user ID for chat:', match.profile.user_id);
              Alert.alert('Error', 'Unable to start conversation. Please try again.');
            }
          }}
        >
          <MessageCircle size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAnalyticsOverview = () => {
    if (!analytics) return null;

    return (
      <View style={styles.analyticsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.analyticsScroll}
        >
          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.analyticsHeader}>
              <Target size={20} color={theme.colors.primary} />
              <Text style={[styles.analyticsValue, { color: theme.colors.text }]}>
                {analytics.total_matches_found}
              </Text>
            </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary }]}>
              Total Matches
            </Text>
          </View>

          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.analyticsHeader}>
              <Heart size={20} color={theme.colors.red} />
              <Text style={[styles.analyticsValue, { color: theme.colors.text }]}>
                {Math.round(analytics.average_compatibility)}%
              </Text>
            </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary }]}>
              Avg Compatibility
            </Text>
          </View>

          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.analyticsHeader}>
              <Star size={20} color={theme.colors.yellow} />
              <Text style={[styles.analyticsValue, { color: theme.colors.text }]}>
                {Math.round(analytics.best_match_score)}%
              </Text>
            </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary }]}>Best Match</Text>
          </View>

          <View style={[styles.analyticsCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.analyticsHeader}>
              <Eye size={20} color={theme.colors.blue} />
              <Text style={[styles.analyticsValue, { color: theme.colors.text }]}>
                {analytics.user_engagement.profiles_viewed}
              </Text>
            </View>
            <Text style={[styles.analyticsLabel, { color: theme.colors.textSecondary }]}>
              Profiles Viewed
            </Text>
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderSuggestions = () => (
    <View style={styles.suggestionsContainer}>
      {suggestions.map((suggestion, index) => (
        <View key={index} style={[styles.suggestionCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.suggestionHeader}>
            <View style={styles.suggestionInfo}>
              <Sparkles size={20} color={theme.colors.primary} />
              <Text style={[styles.suggestionTitle, { color: theme.colors.text }]}>
                {suggestion.title}
              </Text>
            </View>
            <View
              style={[
                styles.impactBadge,
                {
                  backgroundColor:
                    suggestion.impact === 'high'
                      ? colorWithOpacity(theme.colors.red, 0.2)
                      : suggestion.impact === 'medium'
                        ? colorWithOpacity(theme.colors.orange, 0.2)
                        : colorWithOpacity(theme.colors.green, 0.2),
                },
              ]}
            >
              <Text
                style={[
                  styles.impactText,
                  {
                    color:
                      suggestion.impact === 'high'
                        ? theme.colors.red
                        : suggestion.impact === 'medium'
                          ? theme.colors.orange
                          : theme.colors.green,
                  },
                ]}
              >
                {suggestion.impact} impact
              </Text>
            </View>
          </View>
          <Text style={[styles.suggestionDescription, { color: theme.colors.textSecondary }]}>
            {suggestion.description}
          </Text>
          <View style={styles.suggestionFooter}>
            <Text style={[styles.suggestionImprovement, { color: theme.colors.primary }]}>
              {suggestion.estimated_improvement}
            </Text>
            <Text style={[styles.suggestionConfidence, { color: theme.colors.textSecondary }]}>
              {suggestion.confidence}% confidence
            </Text>
          </View>
        </View>
      ))}
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'matches':
        return (
          <FlatList
            data={matches}
            renderItem={renderMatchCard}
            keyExtractor={item => item.profile.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.matchesList}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Search size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No matches found</Text>
                <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
                  Try adjusting your preferences or completing your profile for better matches.
                </Text>
                <TouchableOpacity
                  style={[styles.emptyButton, { backgroundColor: theme.colors.primary }]}
                  onPress={() => router.push('/(tabs)/profile/unified-preferences?tab=matching')}
                >
                  <Text style={[styles.emptyButtonText, { color: theme.colors.white }]}>
                    Adjust Preferences
                  </Text>
                </TouchableOpacity>
              </View>
            }
          />
        );

      case 'analytics':
        return (
          <ScrollView 
            style={styles.tabContent} 
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
          >
            {renderAnalyticsOverview()}
          </ScrollView>
        );

      case 'suggestions':
        return (
          <ScrollView 
            style={styles.tabContent} 
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
          >
            {renderSuggestions()}
          </ScrollView>
        );

      case 'preferences':
        return (
          <View style={styles.tabContent}>
            <TouchableOpacity
              style={[styles.preferenceItem, { backgroundColor: theme.colors.surface }]}
              onPress={() => router.push('/(tabs)/profile/unified-preferences?tab=matching')}
            >
              <View style={styles.preferenceInfo}>
                <Filter size={20} color={theme.colors.primary} />
                <Text style={[styles.preferenceTitle, { color: theme.colors.text }]}>
                  Matching Preferences
                </Text>
              </View>
              <ArrowRight size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.preferenceItem, { backgroundColor: theme.colors.surface }]}
              onPress={() => router.push('/profile/ai-compatibility-dashboard')}
            >
              <View style={styles.preferenceInfo}>
                <Brain size={20} color={theme.colors.purple} />
                <Text style={[styles.preferenceTitle, { color: theme.colors.text }]}>
                  AI Compatibility Engine
                </Text>
              </View>
              <ArrowRight size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.preferenceItem, { backgroundColor: theme.colors.surface }]}
              onPress={() => router.push('/profile/compatibility-insights')}
            >
              <View style={styles.preferenceInfo}>
                <Sparkles size={20} color={theme.colors.yellow} />
                <Text style={[styles.preferenceTitle, { color: theme.colors.text }]}>
                  Compatibility Insights
                </Text>
              </View>
              <ArrowRight size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  if (loading || isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Finding your perfect matches...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen options={{ headerShown: false }} />

      {/* Enhanced Header */}
      <View style={[styles.headerContainer, { 
        backgroundColor: theme.colors.surface,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <TouchableOpacity 
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
            borderRadius: 8,
            padding: 6,
          }}>
            <ArrowLeft size={20} color={theme.colors.primary} />
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.error, 0.15),
            borderRadius: 10,
            padding: 8,
            marginRight: 12,
          }}>
            <Heart size={24} color={theme.colors.error} />
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Smart Matching
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              {matches.length} matches • AI-powered compatibility
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={[styles.refreshButton, { 
            backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
            shadowColor: theme.colors.success,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 3,
          }]}
          onPress={onRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color={theme.colors.success} />
          ) : (
            <RefreshCw size={18} color={theme.colors.success} />
          )}
        </TouchableOpacity>
      </View>

      {renderTabBar()}

      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500',
  },
  refreshButton: {
    padding: 10,
    borderRadius: 10,
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBar: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabScroll: {
    paddingHorizontal: 20,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabBadge: {
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
  },
  tabBadgeText: {
    fontSize: 10,
    fontWeight: '700',
  },
  tabContent: {
    flex: 1,
  },
  analyticsContainer: {
    paddingVertical: 16,
  },
  analyticsScroll: {
    paddingHorizontal: 20,
  },
  analyticsCard: {
    width: 140,
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  analyticsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analyticsValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  analyticsLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  matchesList: {
    padding: 20,
  },
  matchCard: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  matchProfile: {
    flexDirection: 'row',
    flex: 1,
  },
  matchAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  matchInfo: {
    flex: 1,
  },
  matchName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  matchAge: {
    fontSize: 14,
    marginBottom: 4,
  },
  matchLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  matchLocationText: {
    fontSize: 12,
  },
  matchScore: {
    alignItems: 'center',
  },
  scoreCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  scoreText: {
    fontSize: 14,
    fontWeight: '700',
  },
  scoreLabel: {
    fontSize: 10,
    textAlign: 'center',
  },
  matchDetails: {
    marginBottom: 16,
  },
  matchBio: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  matchInsights: {
    marginBottom: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  insightText: {
    fontSize: 12,
    flex: 1,
  },
  matchReasons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  reasonTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  reasonText: {
    fontSize: 10,
    fontWeight: '600',
  },
  matchActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionsContainer: {
    padding: 20,
  },
  suggestionCard: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  suggestionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  suggestionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  impactBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  impactText: {
    fontSize: 10,
    fontWeight: '600',
  },
  suggestionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  suggestionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  suggestionImprovement: {
    fontSize: 12,
    fontWeight: '600',
  },
  suggestionConfidence: {
    fontSize: 12,
  },
  preferenceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  preferenceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
