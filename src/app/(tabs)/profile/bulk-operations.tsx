import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { PremiumFeatureGate } from '@components/premium/PremiumFeatureGate';
import { usePremiumFeatures } from '@hooks/usePremiumFeatures';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';

interface BulkOperation {
  id: string;
  type: 'property_import' | 'tenant_screening' | 'service_management' | 'user_verification' | 'data_export';
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  total_items: number;
  processed_items: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

interface BulkOperationTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'properties' | 'users' | 'services' | 'data';
  requires_file: boolean;
  supports_scheduling: boolean;
}

const BULK_OPERATION_TEMPLATES: BulkOperationTemplate[] = [
  {
    id: 'property_bulk_import',
    name: 'Bulk Property Import',
    description: 'Import multiple properties from CSV file',
    icon: 'upload',
    category: 'properties',
    requires_file: true,
    supports_scheduling: true,
  },
  {
    id: 'tenant_screening_batch',
    name: 'Batch Tenant Screening',
    description: 'Screen multiple tenant applications at once',
    icon: 'user-check',
    category: 'users',
    requires_file: false,
    supports_scheduling: true,
  },
  {
    id: 'service_provider_verification',
    name: 'Service Provider Verification',
    description: 'Bulk verify service provider credentials',
    icon: 'shield-check',
    category: 'services',
    requires_file: false,
    supports_scheduling: false,
  },
  {
    id: 'user_data_export',
    name: 'User Data Export',
    description: 'Export user data for analytics and compliance',
    icon: 'download',
    category: 'data',
    requires_file: false,
    supports_scheduling: true,
  },
  {
    id: 'property_status_update',
    name: 'Property Status Update',
    description: 'Update multiple property statuses at once',
    icon: 'edit',
    category: 'properties',
    requires_file: true,
    supports_scheduling: false,
  },
  {
    id: 'automated_matching',
    name: 'Automated Matching',
    description: 'Run AI matching for all unmatched users',
    icon: 'zap',
    category: 'users',
    requires_file: false,
    supports_scheduling: true,
  },
];

export default function BulkOperationsScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { state } = useAuth();
  const { trackFeatureUsage } = usePremiumFeatures();
  
  const [isLoading, setIsLoading] = useState(false);
  const [activeOperations, setActiveOperations] = useState<BulkOperation[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'properties' | 'users' | 'services' | 'data'>('all');
  const [showScheduler, setShowScheduler] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<BulkOperationTemplate | null>(null);
  const [scheduledTime, setScheduledTime] = useState('');
  const [operationNotes, setOperationNotes] = useState('');

  useEffect(() => {
    trackFeatureUsage('bulk_operations');
    loadActiveOperations();
  }, []);

  const loadActiveOperations = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for active operations
      const mockOperations: BulkOperation[] = [
        {
          id: '1',
          type: 'property_import',
          name: 'Property Import - Downtown Area',
          description: 'Importing 150 properties from CSV file',
          status: 'running',
          progress: 65,
          total_items: 150,
          processed_items: 98,
          created_at: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'tenant_screening',
          name: 'Weekly Tenant Screening',
          description: 'Screening 45 new applications',
          status: 'completed',
          progress: 100,
          total_items: 45,
          processed_items: 45,
          created_at: new Date(Date.now() - 86400000).toISOString(),
          completed_at: new Date().toISOString(),
        },
        {
          id: '3',
          type: 'data_export',
          name: 'Monthly Analytics Export',
          description: 'Exporting user activity data',
          status: 'pending',
          progress: 0,
          total_items: 1,
          processed_items: 0,
          created_at: new Date().toISOString(),
        },
      ];

      setActiveOperations(mockOperations);
    } catch (error) {
      logger.error('Error loading bulk operations', error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOperation = async (template: BulkOperationTemplate) => {
    if (template.requires_file) {
      Alert.alert(
        'File Required',
        'This operation requires a file upload. File picker will open.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Choose File', onPress: () => mockFileSelection(template) }
        ]
      );
      return;
    }

    if (template.supports_scheduling) {
      setSelectedTemplate(template);
      setShowScheduler(true);
      return;
    }

    await executeOperation(template);
  };

  const mockFileSelection = (template: BulkOperationTemplate) => {
    Alert.alert(
      'File Selected',
      'sample_data.csv has been selected. Start the operation?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Start Operation', onPress: () => executeOperation(template) }
      ]
    );
  };

  const executeOperation = async (template: BulkOperationTemplate) => {
    try {
      const newOperation: BulkOperation = {
        id: Date.now().toString(),
        type: template.id as any,
        name: template.name,
        description: template.description,
        status: 'pending',
        progress: 0,
        total_items: Math.floor(Math.random() * 200) + 50,
        processed_items: 0,
        created_at: new Date().toISOString(),
      };

      setActiveOperations(prev => [newOperation, ...prev]);
      
      // Simulate operation progress
      simulateProgress(newOperation.id);
      
      Alert.alert('Success', `${template.name} has been started successfully.`);
      setShowScheduler(false);
      setSelectedTemplate(null);
      setOperationNotes('');
      setScheduledTime('');
      
    } catch (error) {
      logger.error('Error starting bulk operation', error as Error);
      Alert.alert('Error', 'Failed to start the operation. Please try again.');
    }
  };

  const simulateProgress = (operationId: string) => {
    const interval = setInterval(() => {
      setActiveOperations(prev => prev.map(op => {
        if (op.id === operationId && op.status === 'pending') {
          const newProgress = Math.min(op.progress + Math.random() * 20, 100);
          const isCompleted = newProgress >= 100;
          
          return {
            ...op,
            status: isCompleted ? 'completed' : 'running',
            progress: newProgress,
            processed_items: Math.floor((newProgress / 100) * op.total_items),
            completed_at: isCompleted ? new Date().toISOString() : undefined,
          };
        }
        return op;
      }));
    }, 2000);

    // Clear interval after operation completes
    setTimeout(() => clearInterval(interval), 15000);
  };

  const filteredTemplates = BULK_OPERATION_TEMPLATES.filter(template => 
    selectedCategory === 'all' || template.category === selectedCategory
  );

  const getStatusColor = (status: BulkOperation['status']) => {
    switch (status) {
      case 'pending': return theme.colors.warning;
      case 'running': return theme.colors.primary;
      case 'completed': return theme.colors.success;
      case 'failed': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = (status: BulkOperation['status']) => {
    switch (status) {
      case 'pending': return 'clock';
      case 'running': return 'play-circle';
      case 'completed': return 'check-circle';
      case 'failed': return 'x-circle';
      default: return 'circle';
    }
  };

  const renderCategoryFilter = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryFilter}>
      {['all', 'properties', 'users', 'services', 'data'].map((category) => (
        <TouchableOpacity
          key={category}
          style={[
            styles.categoryButton,
            {
              backgroundColor: selectedCategory === category ? theme.colors.primary : theme.colors.surface,
            }
          ]}
          onPress={() => setSelectedCategory(category as any)}
        >
          <Text style={[
            styles.categoryButtonText,
            {
              color: selectedCategory === category ? '#fff' : theme.colors.text,
            }
          ]}>
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderOperationTemplate = (template: BulkOperationTemplate) => (
    <TouchableOpacity
      key={template.id}
      style={[styles.templateCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleStartOperation(template)}
    >
      <View style={styles.templateHeader}>
        <View style={[styles.templateIcon, { backgroundColor: theme.colors.primary + '20' }]}>
          <Feather name={template.icon as any} size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.templateInfo}>
          <Text style={[styles.templateName, { color: theme.colors.text }]}>
            {template.name}
          </Text>
          <Text style={[styles.templateDescription, { color: theme.colors.textSecondary }]}>
            {template.description}
          </Text>
        </View>
        <Feather name="chevron-right" size={20} color={theme.colors.textSecondary} />
      </View>
      
      <View style={styles.templateFeatures}>
        {template.requires_file && (
          <View style={styles.featureBadge}>
            <Feather name="file" size={12} color={theme.colors.primary} />
            <Text style={[styles.featureText, { color: theme.colors.primary }]}>File Required</Text>
          </View>
        )}
        {template.supports_scheduling && (
          <View style={styles.featureBadge}>
            <Feather name="calendar" size={12} color={theme.colors.success} />
            <Text style={[styles.featureText, { color: theme.colors.success }]}>Schedulable</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderActiveOperation = (operation: BulkOperation) => (
    <View key={operation.id} style={[styles.operationCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.operationHeader}>
        <View style={styles.operationInfo}>
          <Text style={[styles.operationName, { color: theme.colors.text }]}>
            {operation.name}
          </Text>
          <Text style={[styles.operationDescription, { color: theme.colors.textSecondary }]}>
            {operation.description}
          </Text>
        </View>
        <View style={styles.operationStatus}>
          <Feather 
            name={getStatusIcon(operation.status) as any} 
            size={16} 
            color={getStatusColor(operation.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(operation.status) }]}>
            {operation.status.charAt(0).toUpperCase() + operation.status.slice(1)}
          </Text>
        </View>
      </View>

      {operation.status === 'running' && (
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: theme.colors.primary,
                  width: `${operation.progress}%`
                }
              ]} 
            />
          </View>
          <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
            {operation.processed_items} / {operation.total_items} items ({Math.round(operation.progress)}%)
          </Text>
        </View>
      )}

      <View style={styles.operationMeta}>
        <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
          Started: {new Date(operation.created_at).toLocaleString()}
        </Text>
        {operation.completed_at && (
          <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
            Completed: {new Date(operation.completed_at).toLocaleString()}
          </Text>
        )}
      </View>
    </View>
  );

  return (
    <PremiumFeatureGate featureId="bulk_operations">
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Feather name="arrow-left" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Bulk Operations
          </Text>
          <TouchableOpacity onPress={loadActiveOperations}>
            <Feather name="refresh-cw" size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Active Operations */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Active Operations ({activeOperations.length})
            </Text>
            
            {activeOperations.length === 0 ? (
              <View style={[styles.emptyState, { backgroundColor: theme.colors.surface }]}>
                <Feather name="activity" size={48} color={theme.colors.border} />
                <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
                  No Active Operations
                </Text>
                <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
                  Start a bulk operation to see progress here.
                </Text>
              </View>
            ) : (
              activeOperations.map(renderActiveOperation)
            )}
          </View>

          {/* Available Operations */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Available Operations
            </Text>
            
            {renderCategoryFilter()}
            
            <View style={styles.templatesContainer}>
              {filteredTemplates.map(renderOperationTemplate)}
            </View>
          </View>

          {/* Enterprise Features */}
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Enterprise Features
            </Text>
            <Text style={[styles.enterpriseFeatures, { color: theme.colors.textSecondary }]}>
              • Advanced scheduling and automation{'\n'}
              • Custom operation templates{'\n'}
              • API integration for external systems{'\n'}
              • Detailed audit logs and reporting{'\n'}
              • Error handling and retry mechanisms{'\n'}
              • Multi-tenant operation management
            </Text>
          </View>
        </ScrollView>

        {/* Scheduler Modal */}
        {showScheduler && selectedTemplate && (
          <View style={styles.schedulerOverlay}>
            <View style={[styles.schedulerModal, { backgroundColor: theme.colors.surface }]}>
              <View style={styles.schedulerHeader}>
                <Text style={[styles.schedulerTitle, { color: theme.colors.text }]}>
                  Schedule Operation
                </Text>
                <TouchableOpacity onPress={() => setShowScheduler(false)}>
                  <Feather name="x" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>
              
              <Text style={[styles.schedulerDescription, { color: theme.colors.textSecondary }]}>
                {selectedTemplate.name}: {selectedTemplate.description}
              </Text>

              <View style={styles.schedulerOptions}>
                <TouchableOpacity
                  style={[styles.scheduleOption, { backgroundColor: theme.colors.primary }]}
                  onPress={() => executeOperation(selectedTemplate)}
                >
                  <Text style={styles.scheduleOptionText}>Run Now</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.scheduleOption, { backgroundColor: theme.colors.border }]}
                  onPress={() => {
                    Alert.alert('Coming Soon', 'Scheduled operations will be available in the next update.');
                  }}
                >
                  <Text style={[styles.scheduleOptionText, { color: theme.colors.text }]}>
                    Schedule for Later
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </SafeAreaView>
    </PremiumFeatureGate>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  categoryFilter: {
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  templatesContainer: {
    gap: 12,
  },
  templateCard: {
    padding: 16,
    borderRadius: 12,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  templateDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  templateFeatures: {
    flexDirection: 'row',
    gap: 8,
  },
  featureBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  featureText: {
    fontSize: 12,
    marginLeft: 4,
  },
  operationCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  operationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  operationInfo: {
    flex: 1,
  },
  operationName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  operationDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  operationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
  },
  operationMeta: {
    gap: 2,
  },
  metaText: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    borderRadius: 12,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
  },
  enterpriseFeatures: {
    fontSize: 14,
    lineHeight: 20,
    padding: 16,
  },
  schedulerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  schedulerModal: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    width: '90%',
  },
  schedulerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  schedulerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  schedulerDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  schedulerOptions: {
    gap: 12,
  },
  scheduleOption: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  scheduleOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
}); 