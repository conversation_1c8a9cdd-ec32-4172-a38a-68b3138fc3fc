import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
  useColorScheme,
  RefreshControl,
  Alert,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { zeroVerificationService } from '@services/zeroVerificationService';
import { profileCompletionService } from '@services/profileCompletionService';
import { useToast } from '@components/ui/Toast';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';
import {
  Shield,
  BadgeCheck,
  UserCheck,
  Award,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  ChevronRight,
  Info,
  Zap,
  Lock,
  Eye,
  Users,
  FileText,
  CreditCard,
  <PERSON><PERSON>les,
  ArrowLeft,
  Refresh<PERSON>w,
  DollarSign,
  Coins,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

// Enhanced verification data structure
interface VerificationDashboardData {
  trust_score: {
    current_score: number;
    max_score: number;
    level: 'Basic' | 'Fair' | 'Good' | 'Excellent';
    level_color: string;
  };
  identity_verification: {
    is_verified: boolean;
    status: 'not_started' | 'pending' | 'verified' | 'failed';
    score_contribution: number;
    max_contribution: number;
    last_updated?: string;
    is_free: boolean;
  };
  background_check: {
    is_verified: boolean;
    status: 'not_started' | 'pending' | 'in_progress' | 'completed' | 'failed';
    score_contribution: number;
    max_contribution: number;
    check_type?: string;
    last_updated?: string;
    is_free: boolean;
  };
  profile_completion: {
    percentage: number;
    score_contribution: number;
    max_contribution: number;
  };
  reference_checks: {
    completed_count: number;
    total_requested: number;
    score_contribution: number;
    max_contribution: number;
    is_free: boolean;
  };
  verification_benefits: {
    priority_matching: boolean;
    enhanced_visibility: boolean;
    trust_badge: boolean;
    premium_features: boolean;
  };
  cost_savings: {
    monthly_savings: number;
    total_saved: number;
    free_services_count: number;
  };
}

// Updated verification actions with zero-cost system
const VERIFICATION_ACTIONS = [
  {
    id: 'identity',
    title: 'Identity Verification',
    description: 'FREE manual review with document upload',
    icon: BadgeCheck,
    route: '/verification/id-verification-free',
    score_weight: 40,
    estimated_time: '5-10 minutes',
    difficulty: 'Easy',
    is_free: true,
    original_cost: '$7 per check',
  },
  {
    id: 'background',
    title: 'Background Check',
    description: 'FREE public records & reference verification',
    icon: Shield,
    route: '/(tabs)/profile/verification/background',
    score_weight: 40,
    estimated_time: '24-48 hours',
    difficulty: 'Easy',
    is_free: true,
    original_cost: '$35 per check',
  },
  {
    id: 'references',
    title: 'Reference Verification',
    description: 'FREE reference collection and verification',
    icon: Users,
    route: '/verification/reference-check-free',
    score_weight: 15,
    estimated_time: '1-3 business days',
    difficulty: 'Medium',
    is_free: true,
    original_cost: '$15 per check',
  },
  {
    id: 'profile',
    title: 'Complete Profile',
    description: 'Fill out all profile sections for maximum trust',
    icon: UserCheck,
    route: '/profile',
    score_weight: 5,
    estimated_time: '10-15 minutes',
    difficulty: 'Easy',
    is_free: true,
    original_cost: 'Always free',
  },
];

export default function VerificationDashboardScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const router = useRouter();
  const { toast } = useToast();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [verificationData, setVerificationData] = useState<VerificationDashboardData>({
    trust_score: {
      current_score: 0,
      max_score: 100,
      level: 'Basic',
      level_color: '#64748b',
    },
    identity_verification: {
      is_verified: false,
      status: 'not_started',
      score_contribution: 0,
      max_contribution: 40,
      is_free: true,
    },
    background_check: {
      is_verified: false,
      status: 'not_started',
      score_contribution: 0,
      max_contribution: 40,
      is_free: true,
    },
    profile_completion: {
      percentage: 0,
      score_contribution: 0,
      max_contribution: 15,
    },
    reference_checks: {
      completed_count: 0,
      total_requested: 0,
      score_contribution: 0,
      max_contribution: 5,
      is_free: true,
    },
    verification_benefits: {
      priority_matching: false,
      enhanced_visibility: false,
      trust_badge: false,
      premium_features: false,
    },
    cost_savings: {
      monthly_savings: 57, // $7 + $35 + $15 = $57 per user per verification cycle
      total_saved: 0,
      free_services_count: 4,
    },
  });

  useEffect(() => {
    fetchVerificationData();
  }, [fetchVerificationData]);

  const fetchVerificationData = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Use zero-cost verification service instead of expensive ones
      const [verificationStatus, profileCompletion] = await Promise.all([
        zeroVerificationService.getVerificationStatus(user.id),
        profileCompletionService.calculateProfileCompletion(user.id),
      ]);

      // Process verification status from zero-cost service
      const verificationData = verificationStatus?.data;
      const identityScore = verificationData?.identity ? 40 : 0;
      const backgroundScore = verificationData?.background ? 40 : 0;
      const referenceScore = verificationData?.reference ? 5 : 0;

      // Process profile completion
      const profilePercentage = profileCompletion?.data || 0;
      const profileScore = Math.round((profilePercentage / 100) * 15);

      // Calculate total trust score
      const totalScore = identityScore + backgroundScore + profileScore + referenceScore;
      const trustLevel = getTrustLevel(totalScore);

      // Calculate cost savings (compared to expensive services)
      const totalSaved = totalScore > 0 ? 57 : 0; // One-time savings per verification cycle

      setVerificationData({
        trust_score: {
          current_score: totalScore,
          max_score: 100,
          level: trustLevel.level,
          level_color: trustLevel.color,
        },
        identity_verification: {
          is_verified: verificationData?.identity || false,
          status: verificationData?.identity ? 'verified' : 'not_started',
          score_contribution: identityScore,
          max_contribution: 40,
          last_updated: verificationData?.last_verified_at,
          is_free: true,
        },
        background_check: {
          is_verified: verificationData?.background || false,
          status: verificationData?.background ? 'completed' : 'not_started',
          score_contribution: backgroundScore,
          max_contribution: 40,
          last_updated: verificationData?.last_verified_at,
          is_free: true,
        },
        profile_completion: {
          percentage: profilePercentage,
          score_contribution: profileScore,
          max_contribution: 15,
        },
        reference_checks: {
          completed_count: verificationData?.reference ? 1 : 0,
          total_requested: 1,
          score_contribution: referenceScore,
          max_contribution: 5,
          is_free: true,
        },
        verification_benefits: {
          priority_matching: totalScore >= 60,
          enhanced_visibility: totalScore >= 40,
          trust_badge: totalScore >= 80,
          premium_features: totalScore >= 90,
        },
        cost_savings: {
          monthly_savings: 57,
          total_saved: totalSaved,
          free_services_count: 4,
        },
      });

      logger.info('Zero-cost verification data loaded', 'VerificationDashboard', {
        userId: user.id,
        trustScore: totalScore,
        costSavings: totalSaved,
      });
    } catch (error) {
      logger.error('Error fetching verification data', 'VerificationDashboard', {}, error as Error);
      toast({
        type: 'error',
        title: 'Error',
        message: 'Failed to load verification data',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchVerificationData();
    setRefreshing(false);
  }, [fetchVerificationData]);

  const getTrustLevel = (
    score: number
  ): { level: 'Basic' | 'Fair' | 'Good' | 'Excellent'; color: string } => {
    if (score >= 85) return { level: 'Excellent', color: '#10b981' };
    if (score >= 70) return { level: 'Good', color: '#3b82f6' };
    if (score >= 50) return { level: 'Fair', color: '#f59e0b' };
    return { level: 'Basic', color: '#64748b' };
  };

  const getStatusIcon = (status: string, isVerified: boolean) => {
    if (isVerified) return <CheckCircle size={20} color="#10b981" />;
    if (status === 'pending') return <Clock size={20} color="#f59e0b" />;
    return <AlertCircle size={20} color="#64748b" />;
  };

  const getStatusText = (status: string, isVerified: boolean) => {
    if (isVerified) return 'Verified';
    if (status === 'pending') return 'Pending';
    if (status === 'in_progress') return 'In Progress';
    if (status === 'failed') return 'Failed';
    return 'Not Started';
  };

  const handleActionPress = (action: typeof VERIFICATION_ACTIONS[0]) => {
    // Show coming soon alert for now, but navigate to free alternatives
    if (action.id === 'identity' || action.id === 'background' || action.id === 'references') {
      Alert.alert(
        '🎉 Zero-Cost Verification',
        `This verification is completely FREE! You're saving ${action.original_cost} compared to expensive services.\n\nWould you like to continue with our free verification system?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Continue FREE', 
            onPress: () => {
              // For now, show implementation status
              Alert.alert(
                '🚧 Coming Soon',
                `Free ${action.title} is being implemented.\n\nThis will replace expensive services and save you money!`,
                [{ text: 'OK' }]
              );
            }
          },
        ]
      );
    } else {
      router.push(action.route as any);
    }
  };

  if (loading) {
    return (
      <SafeAreaView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        edges={['top']}
      >
        <Stack.Screen
          options={{
            title: 'Verification Dashboard',
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background },
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading verification data...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderTrustScoreCard = () => (
    <View style={[styles.trustScoreCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.trustScoreHeader}>
        <Award size={24} color={verificationData.trust_score.level_color} />
        <View style={styles.trustScoreInfo}>
          <Text style={[styles.trustScoreTitle, { color: theme.colors.text }]}>Trust Score</Text>
          <View style={styles.levelBadgeContainer}>
            <Text
              style={[styles.trustScoreLevel, { color: verificationData.trust_score.level_color }]}
            >
              {verificationData.trust_score.level}
            </Text>
            <View style={[styles.freeBadge, { backgroundColor: colorWithOpacity('#10b981', 0.1) }]}>
              <Coins size={12} color="#10b981" />
              <Text style={[styles.freeBadgeText, { color: '#10b981' }]}>FREE</Text>
            </View>
          </View>
        </View>
        <View style={styles.trustScoreValue}>
          <Text style={[styles.trustScoreNumber, { color: theme.colors.text }]}>
            {verificationData.trust_score.current_score}
          </Text>
          <Text style={[styles.trustScoreMax, { color: theme.colors.textSecondary }]}>
            /{verificationData.trust_score.max_score}
          </Text>
        </View>
      </View>

      <View style={styles.trustScoreProgress}>
        <View style={[styles.trustScoreProgressBar, { backgroundColor: theme.colors.border }]}>
          <View
            style={[
              styles.trustScoreProgressFill,
              {
                width: `${verificationData.trust_score.current_score}%`,
                backgroundColor: verificationData.trust_score.level_color,
              },
            ]}
          />
        </View>
      </View>

      <Text style={[styles.trustScoreDescription, { color: theme.colors.textSecondary }]}>
        {getTrustLevelDescription(verificationData.trust_score.level)}
      </Text>

      {/* Cost savings info */}
      <View style={[styles.savingsContainer, { backgroundColor: colorWithOpacity('#10b981', 0.05) }]}>
        <DollarSign size={16} color="#10b981" />
        <Text style={[styles.savingsText, { color: '#10b981' }]}>
          You're saving ${verificationData.cost_savings.monthly_savings} per verification cycle!
        </Text>
      </View>
    </View>
  );

  const getTrustLevelDescription = (level: string): string => {
    switch (level) {
      case 'Excellent':
        return 'Outstanding trust level! You have maximum credibility with potential roommates.';
      case 'Good':
        return 'Strong trust level. Most roommates will feel confident connecting with you.';
      case 'Fair':
        return 'Moderate trust level. Complete more verifications to build stronger credibility.';
      default:
        return 'Building trust. Complete verifications to increase your credibility with roommates.';
    }
  };

  const renderVerificationStatus = () => (
    <View style={[styles.verificationStatusCard, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Verification Status</Text>

      <View style={styles.verificationItems}>
        <View style={styles.verificationItem}>
          <View style={styles.verificationItemHeader}>
            <BadgeCheck
              size={20}
              color={
                verificationData.identity_verification.is_verified
                  ? theme.colors.success
                  : theme.colors.textSecondary
              }
            />
            <Text style={[styles.verificationItemTitle, { color: theme.colors.text }]}>
              Identity Verification
            </Text>
            {getStatusIcon(
              verificationData.identity_verification.status,
              verificationData.identity_verification.is_verified
            )}
          </View>
          <View style={styles.verificationItemDetails}>
            <Text style={[styles.verificationItemStatus, { color: theme.colors.textSecondary }]}>
              {getStatusText(
                verificationData.identity_verification.status,
                verificationData.identity_verification.is_verified
              )}
            </Text>
            <Text style={[styles.verificationItemScore, { color: theme.colors.primary }]}>
              +{verificationData.identity_verification.score_contribution} points
            </Text>
          </View>
        </View>

        <View style={styles.verificationItem}>
          <View style={styles.verificationItemHeader}>
            <Shield
              size={20}
              color={
                verificationData.background_check.is_verified ? theme.colors.success : theme.colors.textSecondary
              }
            />
            <Text style={[styles.verificationItemTitle, { color: theme.colors.text }]}>
              Background Check
            </Text>
            {getStatusIcon(
              verificationData.background_check.status,
              verificationData.background_check.is_verified
            )}
          </View>
          <View style={styles.verificationItemDetails}>
            <Text style={[styles.verificationItemStatus, { color: theme.colors.textSecondary }]}>
              {getStatusText(
                verificationData.background_check.status,
                verificationData.background_check.is_verified
              )}
            </Text>
            <Text style={[styles.verificationItemScore, { color: theme.colors.primary }]}>
              +{verificationData.background_check.score_contribution} points
            </Text>
          </View>
        </View>

        <View style={styles.verificationItem}>
          <View style={styles.verificationItemHeader}>
            <UserCheck
              size={20}
              color={
                verificationData.profile_completion.percentage >= 80
                  ? theme.colors.success
                  : theme.colors.textSecondary
              }
            />
            <Text style={[styles.verificationItemTitle, { color: theme.colors.text }]}>
              Profile Completion
            </Text>
            <Text style={[styles.verificationItemPercentage, { color: theme.colors.primary }]}>
              {verificationData.profile_completion.percentage}%
            </Text>
          </View>
          <View style={styles.verificationItemDetails}>
            <Text style={[styles.verificationItemStatus, { color: theme.colors.textSecondary }]}>
              {verificationData.profile_completion.percentage >= 80 ? 'Complete' : 'In Progress'}
            </Text>
            <Text style={[styles.verificationItemScore, { color: theme.colors.primary }]}>
              +{verificationData.profile_completion.score_contribution} points
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderVerificationActions = () => (
    <View style={[styles.actionsCard, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Improve Your Trust Score</Text>

      {VERIFICATION_ACTIONS.map(action => {
        const IconComponent = action.icon;
        const isCompleted = getActionCompletionStatus(action.id);

        return (
          <TouchableOpacity
            key={action.id}
            style={[
              styles.actionItem,
              { backgroundColor: isCompleted ? theme.colors.background : 'transparent' },
            ]}
            onPress={() => handleActionPress(action)}
            accessibilityRole="button"
            accessibilityLabel={`${action.title}: ${action.description}`}
          >
            <View style={styles.actionItemContent}>
              <View style={styles.actionItemHeader}>
                <IconComponent size={24} color={isCompleted ? theme.colors.success : theme.colors.primary} />
                <View style={styles.actionItemInfo}>
                  <View style={styles.actionTitleContainer}>
                    <Text style={[styles.actionItemTitle, { color: theme.colors.text }]}>
                      {action.title}
                    </Text>
                    {action.is_free && (
                      <View style={[styles.freeBadge, { backgroundColor: colorWithOpacity('#10b981', 0.1) }]}>
                        <Coins size={10} color="#10b981" />
                        <Text style={[styles.freeBadgeText, { color: '#10b981', fontSize: 10 }]}>FREE</Text>
                      </View>
                    )}
                  </View>
                  <Text style={[styles.actionItemDescription, { color: theme.colors.textSecondary }]}>
                    {action.description}
                  </Text>
                </View>
                <View style={styles.actionItemMeta}>
                  <Text style={[styles.actionItemScore, { color: theme.colors.primary }]}>
                    +{action.score_weight}
                  </Text>
                  {isCompleted && <CheckCircle size={16} color={theme.colors.success} />}
                </View>
              </View>
              <View style={styles.actionItemFooter}>
                <Text style={[styles.actionItemTime, { color: theme.colors.textSecondary }]}>
                  {action.estimated_time} • {action.difficulty}
                  {action.is_free && (
                    <Text style={{ color: '#10b981' }}> • Saves {action.original_cost}</Text>
                  )}
                </Text>
                <ChevronRight size={16} color={theme.colors.textSecondary} />
              </View>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  const getActionCompletionStatus = (actionId: string): boolean => {
    switch (actionId) {
      case 'identity':
        return verificationData.identity_verification.is_verified;
      case 'background':
        return verificationData.background_check.is_verified;
      case 'references':
        return verificationData.reference_checks.completed_count > 0;
      case 'profile':
        return verificationData.profile_completion.percentage >= 80;
      default:
        return false;
    }
  };

  const renderCostSavingsCard = () => (
    <View style={[styles.costSavingsCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.costSavingsHeader}>
        <View style={styles.costSavingsIconContainer}>
          <Sparkles size={24} color="#10b981" />
        </View>
        <View style={styles.costSavingsInfo}>
          <Text style={[styles.costSavingsTitle, { color: theme.colors.text }]}>
            Zero-Cost Verification
          </Text>
          <Text style={[styles.costSavingsSubtitle, { color: theme.colors.textSecondary }]}>
            100% FREE vs expensive alternatives
          </Text>
        </View>
      </View>

      <View style={styles.savingsBreakdown}>
        <View style={styles.savingsItem}>
          <Text style={[styles.savingsLabel, { color: theme.colors.textSecondary }]}>
            Identity Verification
          </Text>
          <View style={styles.savingsValue}>
            <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>$7</Text>
            <Text style={[styles.freePrice, { color: '#10b981' }]}>FREE</Text>
          </View>
        </View>
        
        <View style={styles.savingsItem}>
          <Text style={[styles.savingsLabel, { color: theme.colors.textSecondary }]}>
            Background Check
          </Text>
          <View style={styles.savingsValue}>
            <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>$35</Text>
            <Text style={[styles.freePrice, { color: '#10b981' }]}>FREE</Text>
          </View>
        </View>

        <View style={styles.savingsItem}>
          <Text style={[styles.savingsLabel, { color: theme.colors.textSecondary }]}>
            Reference Check
          </Text>
          <View style={styles.savingsValue}>
            <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>$15</Text>
            <Text style={[styles.freePrice, { color: '#10b981' }]}>FREE</Text>
          </View>
        </View>

        <View style={[styles.savingsTotal, { borderTopColor: theme.colors.border }]}>
          <Text style={[styles.savingsTotalLabel, { color: theme.colors.text }]}>
            Total Savings Per Cycle
          </Text>
          <Text style={[styles.savingsTotalValue, { color: '#10b981' }]}>
            ${verificationData.cost_savings.monthly_savings}
          </Text>
        </View>
      </View>

      <View style={[styles.savingsHighlight, { backgroundColor: colorWithOpacity('#10b981', 0.1) }]}>
        <Info size={16} color="#10b981" />
        <Text style={[styles.savingsHighlightText, { color: '#10b981' }]}>
          Our zero-cost system provides the same security as expensive services!
        </Text>
      </View>
    </View>
  );

  const renderBenefitsCard = () => (
    <View style={[styles.benefitsCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.benefitsHeader}>
        <Sparkles size={20} color={theme.colors.primary} />
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Verification Benefits</Text>
      </View>

      <View style={styles.benefitsList}>
        <View style={styles.benefitItem}>
          <Zap
            size={16}
            color={
              verificationData.verification_benefits.priority_matching
                ? theme.colors.success
                : theme.colors.textSecondary
            }
          />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>Priority Matching</Text>
          {verificationData.verification_benefits.priority_matching && (
            <CheckCircle size={16} color={theme.colors.success} />
          )}
        </View>

        <View style={styles.benefitItem}>
          <Eye
            size={16}
            color={
              verificationData.verification_benefits.enhanced_visibility
                ? theme.colors.success
                : theme.colors.textSecondary
            }
          />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>Enhanced Visibility</Text>
          {verificationData.verification_benefits.enhanced_visibility && (
            <CheckCircle size={16} color={theme.colors.success} />
          )}
        </View>

        <View style={styles.benefitItem}>
          <BadgeCheck
            size={16}
            color={
              verificationData.verification_benefits.trust_badge ? theme.colors.success : theme.colors.textSecondary
            }
          />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>Trust Badge</Text>
          {verificationData.verification_benefits.trust_badge && (
            <CheckCircle size={16} color={theme.colors.success} />
          )}
        </View>

        <View style={styles.benefitItem}>
          <Star
            size={16}
            color={
              verificationData.verification_benefits.premium_features
                ? theme.colors.success
                : theme.colors.textSecondary
            }
          />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>Premium Features</Text>
          {verificationData.verification_benefits.premium_features && (
            <CheckCircle size={16} color={theme.colors.success} />
          )}
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      edges={['top']}
    >
      <Stack.Screen options={{ headerShown: false }} />

      {/* Enhanced Header */}
      <View style={[styles.headerContainer, { 
        backgroundColor: theme.colors.surface,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <TouchableOpacity 
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
            borderRadius: 8,
            padding: 6,
          }}>
            <ArrowLeft size={20} color={theme.colors.primary} />
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
            borderRadius: 10,
            padding: 8,
            marginRight: 12,
          }}>
            <Shield size={24} color={theme.colors.success} />
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Verification Dashboard
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              Trust Score: {verificationData.trust_score.current_score}/100 • {verificationData.trust_score.level}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={[styles.refreshButton, { 
            backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
            shadowColor: theme.colors.primary,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 3,
          }]}
          onPress={onRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color={theme.colors.primary} />
          ) : (
            <RefreshCw size={18} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >

        {renderTrustScoreCard()}
        {renderCostSavingsCard()}
        {renderVerificationStatus()}
        {renderVerificationActions()}
        {renderBenefitsCard()}
      </ScrollView>

      <ToastComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500',
  },
  refreshButton: {
    padding: 10,
    borderRadius: 10,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
  },
  refreshButton: {
    alignSelf: 'center',
    padding: 8,
  },
  refreshText: {
    fontSize: 14,
  },
  trustScoreCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  trustScoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  trustScoreInfo: {
    flex: 1,
    marginLeft: 12,
  },
  trustScoreTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  trustScoreLevel: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 2,
  },
  trustScoreValue: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  trustScoreNumber: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  trustScoreMax: {
    fontSize: 16,
    marginLeft: 2,
  },
  trustScoreProgress: {
    marginBottom: 12,
  },
  trustScoreProgressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  trustScoreProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  trustScoreDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  verificationStatusCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  verificationItems: {
    gap: 16,
  },
  verificationItem: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  verificationItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  verificationItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },
  verificationItemPercentage: {
    fontSize: 14,
    fontWeight: '600',
  },
  verificationItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 28,
  },
  verificationItemStatus: {
    fontSize: 14,
  },
  verificationItemScore: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  actionItem: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  actionItemContent: {
    gap: 8,
  },
  actionItemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  actionItemInfo: {
    flex: 1,
    marginLeft: 12,
  },
  actionItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  actionItemDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  actionItemMeta: {
    alignItems: 'center',
    gap: 4,
  },
  actionItemScore: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 36,
  },
  actionItemTime: {
    fontSize: 12,
  },
  benefitsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  benefitsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  benefitsList: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 14,
    flex: 1,
  },
  // Zero-cost verification styles
  levelBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  freeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    gap: 2,
  },
  freeBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    gap: 8,
  },
  savingsText: {
    fontSize: 12,
    fontWeight: '600',
    flex: 1,
  },
  costSavingsCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  costSavingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  costSavingsIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  costSavingsInfo: {
    flex: 1,
    marginLeft: 12,
  },
  costSavingsTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  costSavingsSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  savingsBreakdown: {
    marginBottom: 16,
  },
  savingsItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  savingsLabel: {
    fontSize: 14,
    flex: 1,
  },
  savingsValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  freePrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  savingsTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 1,
  },
  savingsTotalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  savingsTotalValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  savingsHighlight: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  savingsHighlightText: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  actionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});
