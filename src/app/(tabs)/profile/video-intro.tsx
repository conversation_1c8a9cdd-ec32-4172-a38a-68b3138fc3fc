import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
  Image,
  useColorScheme,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Video, ResizeMode, AVPlaybackStatus } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'expo-camera';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { useTheme, colorWithOpacity } from '@design-system';
// Updated to use design system colorWithOpacity function
import { useToast } from '@components/ui/Toast';
import { Button } from '@design-system';
import { ThumbnailPreview } from '@components/ui/ThumbnailPreview';
import { VideoCompressionService } from '@services/videoCompressionService';
import {
  Video as VideoIcon,
  Upload,
  Camera as CameraIcon,
  Trash2,
  Play,
  ArrowLeft,
  Save,
  RefreshCw,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');
const videoCompressionService = new VideoCompressionService();

// Use centralized color utility for React Native compatibility

export default function VideoIntroScreen() {
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const { user, authLoaded } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const videoRef = useRef<Video>(null);

  const [video, setVideo] = useState<string | null>(null);
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [status, setStatus] = useState<AVPlaybackStatus | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [loadingVideo, setLoadingVideo] = useState(true);
  const [videoStats, setVideoStats] = useState<{
    originalSize?: number;
    compressedSize?: number;
    compressionRatio?: number;
  }>({});
  const cameraRef = useRef<any>(null);

  React.useEffect(() => {
    const initScreen = async () => {
      if (!authLoaded) return; // Wait for auth to load
      
      // Fetch video introduction
      fetchVideoIntro();

      // Request camera permissions
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    };

    initScreen();
  }, [authLoaded, user?.id]);

  const fetchVideoIntro = useCallback(async () => {
    try {
      setLoadingVideo(true);

      if (!user?.id) return;

      const { data, error } = await supabase
        .from('user_profiles')
        .select('video_intro_url, video_thumbnail_url, video_compression_stats')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (data?.video_intro_url) {
        setVideo(data.video_intro_url);

        // Set thumbnail if available
        if (data?.video_thumbnail_url) {
          setThumbnail(data.video_thumbnail_url);
        }

        // Set video stats if available
        if (data?.video_compression_stats) {
          setVideoStats({
            originalSize: data.video_compression_stats.originalSize,
            compressedSize: data.video_compression_stats.compressedSize,
            compressionRatio: data.video_compression_stats.compressionRatio,
          });
        }
      }
    } catch (error) {
      logger.error('Failed to get video intro', 'VideoIntroScreen.fetchVideoIntro', {
        error: error instanceof Error ? error.message : String(error),
      });
      toast?.show('Failed to load your video introduction', 'error');
    } finally {
      setLoadingVideo(false);
    }
  }, [user?.id, toast]);

  const pickVideo = async () => {
    try {
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant permission to access your media library');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: [ImagePicker.MediaType.Videos],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 1,
        videoMaxDuration: 60, // 60 seconds max
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const videoUri = result.assets[0].uri;
        setVideo(videoUri);
        uploadVideo(videoUri);
      }
    } catch (error) {
      logger.error('Error picking video', 'VideoIntroScreen.pickVideo', {
        error: error instanceof Error ? error.message : String(error),
      });
      Alert.alert('Error', 'Failed to select video');
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || isRecording) return;

    // Check camera permissions first
    if (hasPermission !== true) {
      const { status } = await Camera.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant permission to access your camera');
        return;
      }
      setHasPermission(true);
    }

    setIsRecording(true);
    try {
      const { uri } = await cameraRef.current.recordAsync({
        maxDuration: 60, // 60 seconds max
        quality: '720p',
      });
      setVideo(uri);
      uploadVideo(uri);
    } catch (error) {
      logger.error('Error recording video', 'VideoIntroScreen.startRecording', {
        error: error instanceof Error ? error.message : String(error),
      });
      Alert.alert('Error', 'Failed to record video');
    } finally {
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (cameraRef.current && isRecording) {
      cameraRef.current.stopRecording();
      setIsRecording(false);
    }
  };

  const uploadVideo = useCallback(async (videoUri: string) => {
    if (!user?.id) return;

    try {
      setIsUploading(true);

      // Compress video before uploading
      const compressionResult = await videoCompressionService.compressVideo(videoUri);
      setVideoStats({
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio,
      });

      // Upload video to storage
      const fileName = `video-intro-${user.id}-${Date.now()}.mp4`;
      const filePath = `public/video-intros/${fileName}`;

      // Generate thumbnail
      const thumbnailUri = await videoCompressionService.generateThumbnail(compressionResult.uri, {
        time: 0, // First frame
        quality: 0.8,
      });

      const thumbnailFileName = `thumbnail-${user.id}-${Date.now()}.jpg`;
      const thumbnailPath = `public/video-thumbnails/${thumbnailFileName}`;

      // Upload video file
      const fileResponse = await fetch(compressionResult.uri);
      const fileBlob = await fileResponse.blob();

      const { error: uploadError } = await supabase.storage
        .from('user-videos')
        .upload(filePath, fileBlob, {
          contentType: 'video/mp4',
          upsert: true,
        });

      if (uploadError) throw uploadError;

      // Upload thumbnail file
      const thumbnailResponse = await fetch(thumbnailUri);
      const thumbnailBlob = await thumbnailResponse.blob();

      const { error: thumbnailUploadError } = await supabase.storage
        .from('user-videos')
        .upload(thumbnailPath, thumbnailBlob, {
          contentType: 'image/jpeg',
          upsert: true,
        });

      if (thumbnailUploadError) throw thumbnailUploadError;

      // Get public URLs
      const { data: videoData } = await supabase.storage.from('user-videos').getPublicUrl(filePath);
      const { data: thumbnailData } = await supabase.storage
        .from('user-videos')
        .getPublicUrl(thumbnailPath);

      if (!videoData) throw new Error('Failed to get video URL');

      const videoPublicUrl = videoData.publicUrl;
      const thumbnailPublicUrl = thumbnailData?.publicUrl;

      // Update profile with video URL and thumbnail URL
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          video_intro_url: videoPublicUrl,
          video_thumbnail_url: thumbnailPublicUrl,
          video_compression_stats: {
            originalSize: compressionResult.originalSize,
            compressedSize: compressionResult.compressedSize,
            compressionRatio: compressionResult.compressionRatio,
          },
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      // Update local state
      setVideo(videoPublicUrl);
      setThumbnail(thumbnailPublicUrl);

      // Show compression stats to the user
      toast?.show(
        `Video uploaded successfully! Compressed from ${(compressionResult.originalSize / 1024 / 1024).toFixed(2)}MB to ${(compressionResult.compressedSize / 1024 / 1024).toFixed(2)}MB (${compressionResult.compressionRatio.toFixed(1)}% reduction)`,
        'success'
      );
    } catch (error) {
      logger.error('Failed to upload video', 'VideoIntroScreen.uploadVideo', {
        error: String(error),
      });
      toast?.show('Failed to upload video', 'error');
    } finally {
      setIsUploading(false);
    }
  }, [user?.id, toast]);

  const deleteVideo = useCallback(async () => {
    if (!user?.id || !video) return;

    Alert.alert('Delete Video', 'Are you sure you want to delete your video introduction?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          try {
            setIsUploading(true);

            // Extract file path from URL if it's a storage URL
            if (video.includes('user-videos')) {
              const filePath = video.split('user-videos/')[1];
              // Delete from storage
              await supabase.storage.from('user-videos').remove([filePath]);

              // Delete thumbnail if exists
              if (thumbnail && thumbnail.includes('user-videos')) {
                const thumbnailPath = thumbnail.split('user-videos/')[1];
                await supabase.storage.from('user-videos').remove([thumbnailPath]);
              }
            }

            // Update profile to remove video URL
            const { error: deleteError } = await supabase
              .from('user_profiles')
              .update({
                video_intro_url: null,
                video_thumbnail_url: null,
                video_compression_stats: null,
              })
              .eq('id', user.id);

            if (deleteError) throw deleteError;

            setVideo(null);
            setThumbnail(null);
            setVideoStats({});
            toast?.show('Video introduction deleted successfully', 'success');
          } catch (error) {
            logger.error('Failed to delete video', 'VideoIntroScreen.deleteVideo', {
              error: error instanceof Error ? error.message : String(error),
            });
            toast?.show('Failed to delete video', 'error');
          } finally {
            setIsUploading(false);
          }
        },
      },
    ]);
  }, [user?.id, video, toast]);

  const togglePlayback = useCallback(() => {
    if (!videoRef.current) return;

    // Check if status is a success object with isPlaying property
    if (status && 'isPlaying' in status && status.isPlaying) {
      videoRef.current.pauseAsync();
      setIsPlaying(false);
    } else {
      videoRef.current.playAsync();
      setIsPlaying(true);
    }
  }, [status]);

  // Format milliseconds to mm:ss format
  const formatTime = (millis: number) => {
    if (!millis) return '0:00';
    const totalSeconds = Math.floor(millis / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const getDuration = () => {
    if (!status || !('durationMillis' in status)) return '0:00';
    return formatTime(status.durationMillis as number);
  };

  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { color: theme.colors.text }]}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>No access to camera</Text>
        <Button onPress={() => router.back()}>Go Back</Button>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen options={{ headerShown: false }} />

      {/* Enhanced Header */}
      <View style={[styles.headerContainer, { 
        backgroundColor: theme.colors.surface,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <TouchableOpacity 
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
            borderRadius: 8,
            padding: 6,
          }}>
            <ArrowLeft size={20} color={theme.colors.primary} />
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
            borderRadius: 10,
            padding: 8,
            marginRight: 12,
          }}>
            <VideoIcon size={24} color={theme.colors.warning} />
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Video Introduction
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              {video ? 'Edit your video intro' : 'Add a video introduction'}
            </Text>
          </View>
        </View>
        
        {video && (
          <TouchableOpacity
            style={[styles.deleteButton, { 
              backgroundColor: colorWithOpacity(theme.colors.error, 0.15),
              shadowColor: theme.colors.error,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 3,
            }]}
            onPress={deleteVideo}
            disabled={isUploading}
          >
            {isUploading ? (
              <ActivityIndicator size="small" color={theme.colors.error} />
            ) : (
              <Trash2 size={18} color={theme.colors.error} />
            )}
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.content}>
        {loadingVideo ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.text, { color: theme.colors.text }]}>Loading video...</Text>
          </View>
        ) : video ? (
          <View style={styles.videoContainer}>
            {status?.isLoaded ? (
              <Video
                ref={videoRef}
                style={styles.video}
                source={{ uri: video }}
                useNativeControls={false}
                resizeMode={ResizeMode.COVER}
                onPlaybackStatusUpdate={status => setStatus(status)}
                onError={error => {
                  logger.error('Video playback error', 'VideoIntroScreen', {
                    error: String(error),
                  });
                  Alert.alert('Error', 'Failed to play video');
                }}
                posterSource={thumbnail ? { uri: thumbnail } : undefined}
                usePoster={!!thumbnail}
              />
            ) : (
              <ThumbnailPreview
                thumbnailUri={thumbnail}
                onPlay={() => {
                  if (videoRef.current) {
                    videoRef.current.playAsync();
                    setIsPlaying(true);
                  }
                }}
                isPlaying={isPlaying}
                placeholderText="Video Introduction"
                style={styles.video}
                duration={
                  status && 'durationMillis' in status
                    ? (status.durationMillis as number) / 1000
                    : undefined
                }
              />
            )}

            <TouchableOpacity
              style={[styles.playButton, isPlaying ? { opacity: 0 } : {}]}
              onPress={togglePlayback}
            >
              <Play size={48} color="#fff" />
            </TouchableOpacity>

            {videoStats.compressionRatio && (
              <View style={[styles.statsContainer, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) }]}>
                <Text style={[styles.statsText, { color: theme.colors.text }]}>
                  Compression: {videoStats.compressionRatio.toFixed(1)}% saved
                </Text>
              </View>
            )}
          </View>
        ) : isRecording ? (
          <View style={styles.cameraContainer}>
            <View style={styles.camera}>
              <View style={styles.recordingIndicator}>
                <View style={styles.recordingDot} />
                <Text style={styles.recordingText}>Recording...</Text>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.recordButton, { backgroundColor: theme.colors.error }]}
              onPress={stopRecording}
            >
              <View style={styles.stopIcon} />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.optionsContainer}>
            <Text style={[styles.title, { color: theme.colors.text }]}>Add a Video Introduction</Text>
            <Text style={[styles.subtitle, { color: theme.colors.text }]}>
              A short video can help potential roommates get to know you better
            </Text>

            <View style={styles.optionsButtons}>
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2), borderColor: theme.colors.primary },
                ]}
                onPress={pickVideo}
              >
                <Upload size={48} color={theme.colors.primary} />
                <Text style={[styles.optionText, { color: theme.colors.text }]}>Upload Video</Text>
                <Text style={[styles.optionSubtext, { color: colorWithOpacity(theme.colors.text, 0.5) }]}>
                  Choose from your library
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2), borderColor: theme.colors.primary },
                ]}
                onPress={startRecording}
              >
                <CameraIcon size={48} color={theme.colors.primary} />
                <Text style={[styles.optionText, { color: theme.colors.text }]}>Record Video</Text>
                <Text style={[styles.optionSubtext, { color: colorWithOpacity(theme.colors.text, 0.5) }]}>
                  Create a new video now
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={[styles.tips, { color: colorWithOpacity(theme.colors.text, 0.5) }]}>
              Tips: Keep it under 60 seconds, find good lighting, and be yourself!
            </Text>
          </View>
        )}
      </View>

      {isUploading && (
        <View style={styles.uploadingOverlay}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.uploadingText}>Processing video...</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 10,
    borderRadius: 10,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    marginTop: 12,
  },
  videoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  video: {
    width: width - 32,
    height: ((width - 32) * 16) / 9,
    borderRadius: 8,
  },
  playButton: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoActions: {
    flexDirection: 'row',
    marginTop: 16,
    width: '100%',
  },
  optionsContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
  },
  optionsButtons: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    marginBottom: 32,
  },
  optionButton: {
    width: '45%',
    height: 180,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
  },
  optionText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 4,
  },
  optionSubtext: {
    fontSize: 12,
    textAlign: 'center',
  },
  tips: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 24,
    lineHeight: 20,
  },
  cameraContainer: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  recordButton: {
    position: 'absolute',
    bottom: 24,
    alignSelf: 'center',
    width: 72,
    height: 72,
    borderRadius: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'white',
    borderRadius: 4,
  },
  recordingIndicator: {
    position: 'absolute',
    top: 16,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'red',
    marginRight: 8,
  },
  recordingText: {
    color: 'white',
    fontSize: 14,
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    color: 'white',
    fontSize: 18,
    marginTop: 16,
  },
  thumbnailContainer: {
    width: width - 32,
    height: ((width - 32) * 16) / 9,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  thumbnailPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    position: 'absolute',
    bottom: 70,
    right: 10,
    padding: 6,
    borderRadius: 12,
  },
  statsText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  warningBanner: {
    padding: 16,
    borderWidth: 2,
    borderColor: 'red',
    marginBottom: 16,
  },
  warningText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
