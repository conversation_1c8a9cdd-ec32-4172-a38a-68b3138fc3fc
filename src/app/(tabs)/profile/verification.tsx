import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';

interface VerificationStatusType {
  identity: boolean;
  phone: boolean;
  email: boolean;
  income: boolean;
  background: boolean;
  references: boolean;
}

interface VerificationItemType {
  id: string;
  title: string;
  description: string;
  icon: string;
  status: boolean;
  route: string | null;
  premium: boolean;
  disabled?: boolean;
  disabledReason?: string;
}

export default function VerificationScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const { state } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatusType>({
    identity: false,
    phone: true, // Assume phone is verified from signup
    email: true, // Assume email is verified from signup
    income: false,
    background: false,
    references: false,
  });

  const verificationItems: VerificationItemType[] = [
    {
      id: 'identity',
      title: 'Identity Verification',
      description: 'Verify your identity with a government-issued ID',
      icon: 'user-check',
      status: verificationStatus.identity,
      route: 'verification-dashboard',
      premium: false,
    },
    {
      id: 'phone',
      title: 'Phone Number',
      description: 'Verify your phone number with SMS',
      icon: 'phone',
      status: verificationStatus.phone,
      route: null, // Already verified
      premium: false,
    },
    {
      id: 'email',
      title: 'Email Address',
      description: 'Verify your email address',
      icon: 'mail',
      status: verificationStatus.email,
      route: null, // Already verified
      premium: false,
    },
    {
      id: 'income',
      title: 'Income Verification',
      description: 'Premium income verification coming soon',
      icon: 'dollar-sign',
      status: verificationStatus.income,
      route: 'verification-dashboard',
      premium: true,
      disabled: true,
      disabledReason: 'Premium income verification features coming soon',
    },
    {
      id: 'background',
      title: 'Background Check',
      description: 'Zero-cost background verification',
      icon: 'shield',
      status: verificationStatus.background,
      route: 'verification/background',
      premium: false,
    },
    {
      id: 'references',
      title: 'References',
      description: 'Add references from previous roommates',
      icon: 'users',
      status: verificationStatus.references,
      route: 'verification-dashboard',
      premium: false,
    },
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Simulate fetching verification status
      await new Promise(resolve => setTimeout(resolve, 1000));
      logger.info('Verification status refreshed', 'VerificationScreen');
    } catch (error) {
      logger.error('Error refreshing verification status', 'VerificationScreen', { error });
    } finally {
      setRefreshing(false);
    }
  };

  const handleVerificationPress = (item: any) => {
    if (item.route) {
      router.push(`/(tabs)/profile/${item.route}`);
    } else if (item.status) {
      Alert.alert('Already Verified', `Your ${item.title.toLowerCase()} is already verified.`);
    } else {
      Alert.alert('Coming Soon', 'This verification feature is coming soon!');
    }
  };

  const getVerificationScore = () => {
    const total = Object.keys(verificationStatus).length;
    const verified = Object.values(verificationStatus).filter(Boolean).length;
    return Math.round((verified / total) * 100);
  };

  const getVerificationStyle = (id: string) => {
    return {
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
    };
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Verification Score Card */}
      <View style={[styles.scoreCard, { backgroundColor: colors.surface }]}>
        <View style={styles.scoreHeader}>
          <Feather name="shield" size={24} color={colors.primary} />
          <Text style={[styles.scoreTitle, { color: colors.text }]}>
            Verification Score
          </Text>
        </View>
        <View style={styles.scoreContent}>
          <Text style={[styles.scoreNumber, { color: colors.primary }]}>
            {getVerificationScore()}%
          </Text>
          <Text style={[styles.scoreDescription, { color: colors.textSecondary }]}>
            Complete all verifications to increase your trustworthiness
          </Text>
        </View>
      </View>

      {/* Verification Items */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Verification Checklist
        </Text>
        
        {verificationItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.verificationCard, 
              getVerificationStyle(item.id),
              { opacity: item.disabled ? 0.5 : 1 }
            ]}
            onPress={() => {
              if (item.disabled) {
                Alert.alert(
                  'Coming Soon',
                  item.disabledReason || 'This premium feature is coming soon',
                  [{ text: 'OK' }]
                );
              } else {
                router.push(`/(tabs)/profile/verification/${item.id}`);
              }
            }}
            disabled={item.disabled}
          >
            <View style={styles.itemLeft}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: item.status ? colors.success : colors.backgroundSecondary }
              ]}>
                <Feather 
                  name={item.icon as any} 
                  size={20} 
                  color={item.status ? '#fff' : colors.textSecondary} 
                />
              </View>
              <View style={styles.itemContent}>
                <View style={styles.itemHeader}>
                  <Text style={[styles.itemTitle, { color: colors.text }]}>
                    {item.title}
                  </Text>
                  {item.disabled ? (
                    <View style={[styles.comingSoonBadge, { backgroundColor: colors.backgroundSecondary, borderColor: colors.border }]}>
                      <Text style={[styles.comingSoonText, { color: colors.textSecondary }]}>COMING SOON</Text>
                    </View>
                  ) : item.premium ? (
                    <View style={[styles.premiumBadge, { backgroundColor: colors.warning }]}>
                      <Text style={styles.premiumText}>PRO</Text>
                    </View>
                  ) : null}
                </View>
                <Text style={[styles.itemDescription, { color: colors.textSecondary }]}>
                  {item.description}
                </Text>
              </View>
            </View>
            <View style={styles.itemRight}>
              {item.status ? (
                <Feather name="check-circle" size={20} color={colors.success} />
              ) : (
                <Feather name="chevron-right" size={20} color={colors.textSecondary} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Benefits Section */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Verification Benefits
        </Text>
        
        <View style={styles.benefitItem}>
          <Feather name="trending-up" size={20} color={colors.primary} />
          <Text style={[styles.benefitText, { color: colors.text }]}>
            Increase profile visibility by up to 75%
          </Text>
        </View>
        
        <View style={styles.benefitItem}>
          <Feather name="shield" size={20} color={colors.primary} />
          <Text style={[styles.benefitText, { color: colors.text }]}>
            Build trust with potential roommates
          </Text>
        </View>
        
        <View style={styles.benefitItem}>
          <Feather name="star" size={20} color={colors.primary} />
          <Text style={[styles.benefitText, { color: colors.text }]}>
            Get priority in search results
          </Text>
        </View>
        
        <View style={styles.benefitItem}>
          <Feather name="lock" size={20} color={colors.primary} />
          <Text style={[styles.benefitText, { color: colors.text }]}>
            Enhanced security and safety
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  scoreCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  scoreContent: {
    alignItems: 'center',
  },
  scoreNumber: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  scoreDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  verificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  itemLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  premiumBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  comingSoonBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
    borderWidth: 1,
  },
  comingSoonText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  itemDescription: {
    fontSize: 14,
  },
  itemRight: {
    marginLeft: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
}); 