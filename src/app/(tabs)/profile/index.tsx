import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  RefreshControl,
  ScrollView,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  Alert,
  Image,
  Dimensions,
  Platform,
  Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { unifiedProfileService } from '@services/unified-profile';
import { UnifiedProfileCard } from '@components/profile/UnifiedProfileCard';
import { ProfileErrorBoundary } from '@components/profile/ProfileErrorBoundary';
import { AccessibilityProfileWrapper, ProfileAccessibilityMessages } from '@components/profile/AccessibilityProfileWrapper';
import { UnifiedLoadingStates } from '@components/profile/UnifiedLoadingStates';
import { logger } from '@utils/logger';
import { useAuth } from '@context/AuthContext';
import { useTheme } from '@design-system';
// Temporarily disabled to fix component loading issue
// import { verificationService, backgroundCheckService, profileCompletionService } from '@services';
import { Feather } from '@expo/vector-icons';
import { supabase } from '@lib/supabase';
// Removed: using theme colors instead of constants
import { StatusBar } from 'expo-status-bar';

// Simplified menu structure - 3 main categories
interface MenuItem {
  id: string;
  icon: keyof typeof Feather.glyphMap;
  title: string;
  subtitle: string;
  route: string;
  badge?: {
    type: 'verification' | 'completion' | 'notification';
    value: boolean | number | string;
  };
}

interface CategorySection {
  id: string;
  title: string;
  icon: keyof typeof Feather.glyphMap;
  description: string;
  items: MenuItem[];
}

// Theme-dependent styles - moved to file scope so all components can access
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.text,
  },
  errorText: {
    textAlign: 'center',
    fontSize: 16,
    margin: 20,
    color: theme.colors.error,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  categoryCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  categoryDescription: {
    fontSize: 14,
    marginTop: 2,
    color: theme.colors.textSecondary,
  },
  categoryItems: {
    gap: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44,
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    marginLeft: 12,
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  menuItemSubtitle: {
    fontSize: 14,
    marginTop: 2,
    color: theme.colors.textSecondary,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  advancedToggle: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    // ACCESSIBILITY: Ensure minimum touch target height (44px for WCAG 2.1 AA compliance)
    minHeight: 44,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  advancedToggleContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  advancedToggleLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  advancedToggleTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  advancedToggleSubtitle: {
    fontSize: 12,
    marginTop: 2,
    color: theme.colors.textSecondary,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 12,
    paddingHorizontal: 8,
    color: theme.colors.textSecondary,
  },
  advancedSection: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  businessHeader: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 8,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  businessHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  businessIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  businessTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  businessSubtitle: {
    fontSize: 14,
    marginTop: 2,
    color: theme.colors.textSecondary,
  },
  businessSection: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.colors.surface,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    // ACCESSIBILITY: Ensure minimum touch target height (44px for WCAG 2.1 AA compliance)
    minHeight: 44,
    padding: 16,
    borderRadius: 12,
    gap: 8,
    backgroundColor: theme.colors.error,
  },
  signOutText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 32,
  },
});

export default function ProfileScreen() {
  const { state, actions } = useAuth();
  const [profileData, setProfileData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [verificationStatus, setVerificationStatus] = useState<{
    is_verified: boolean;
    has_background_check: boolean;
  }>({
    is_verified: false,
    has_background_check: false,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  const router = useRouter();
  const theme = useTheme();
  const styles = createStyles(theme);
  const { colors } = theme;
  
  // Simplified accessibility to avoid dependency loops
  // NOTE: Accessibility features disabled for Expo Go compatibility
  const [screenReaderEnabled, setScreenReaderEnabled] = useState(false);
  
  useEffect(() => {
    // For Expo Go compatibility, disable accessibility features that require native modules
    setScreenReaderEnabled(false);
  }, []);

  const announceChange = useCallback((message: string) => {
    // Disabled for Expo Go compatibility - will work in development builds
    console.log('Accessibility announcement:', message);
  }, []);

  /**
   * Helper function to safely extract data from API responses
   */
  function extractApiResponseData<T>(response: any): T | null {
    if (!response) return null;
    if (response.error) {
      logger.error('API response contained an error', 'ProfileScreen', { error: response.error });
      return null;
    }
    return response.data || null;
  }

  // Fetch profile data with loading states and accessibility
  const fetchProfileData = useCallback(async () => {
    try {
      if (!state?.user) return;

      // Don't use loadingState in the dependency - it causes infinite loops
      setLoading(true);
      if (screenReaderEnabled) {
        announceChange(ProfileAccessibilityMessages.LOADING_STARTED('Profile loading'));
      }

      const { data: profile, error } = await unifiedProfileService.getCurrentProfile();
      if (error) throw new Error(error);
      
      setProfileData(profile);

      // Temporarily use mock data to fix component loading
      setCompletionPercentage(75); // Mock completion percentage
      setVerificationStatus({
        is_verified: false,
        has_background_check: false,
      });

      if (screenReaderEnabled) {
        announceChange(ProfileAccessibilityMessages.PROFILE_LOADED);
      }

      // TODO: Re-enable service calls after fixing imports
      // const completionResponse = await profileCompletionService.calculateProfileCompletion(state.user.id);
      // const verifyStatus = await verificationService.getVerificationStatus();
      // const bgCheckStatus = await backgroundCheckService.getBackgroundCheckStatus();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load profile';
      console.error('Error fetching profile:', error);
      if (screenReaderEnabled) {
        announceChange(ProfileAccessibilityMessages.ERROR_OCCURRED(errorMessage));
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [state?.user, announceChange, screenReaderEnabled]);

  useEffect(() => {
    if (state?.user) {
      fetchProfileData();
    } else {
      setLoading(false);
    }
  }, [state?.user, fetchProfileData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchProfileData();
  }, [fetchProfileData]);

  // Navigation handler with accessibility
  const navigateTo = useCallback((path: string, sectionName?: string) => {
    try {
      router.push(path as any);
      if (sectionName && screenReaderEnabled) {
        announceChange(ProfileAccessibilityMessages.NAVIGATION_CHANGED(sectionName));
      }
    } catch (error) {
      logger.error('Navigation failed', 'ProfileScreen.navigateTo', { path, error });
      Alert.alert('Navigation Error', 'Unable to navigate to the requested page.');
      if (screenReaderEnabled) {
        announceChange('Navigation failed. Please try again.');
      }
    }
  }, [router, announceChange, screenReaderEnabled]);

  // Check service provider status before navigation
  const checkServiceProviderAndNavigate = useCallback(async (path: string) => {
    if (path.includes('service-provider') || path.includes('property-manager')) {
      try {
        // Check if user has service provider data
        const response = await fetch(`/api/user/${state?.user?.id}/service_provider`);
        if (!response.ok) {
          Alert.alert(
            'Feature Not Available',
            'You need to set up a service provider profile first. Would you like to create one?',
            [
              { text: 'Maybe Later', style: 'cancel' },
              { text: 'Set Up Profile', onPress: () => navigateTo('/provider/setup') }
            ]
          );
          return;
        }
      } catch (error) {
        logger.error('Service provider check failed', 'ProfileScreen', { error });
        Alert.alert(
          'Feature Not Available',
          'Service provider features are currently unavailable. Please try again later.'
        );
        return;
      }
    }
    navigateTo(path);
  }, [state?.user?.id, navigateTo]);

  // Sign out handler
  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await actions?.signOut();
              // Navigation will be handled automatically by NavigationHandler
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Simplified menu structure - Implementing 3-category design
  const categoryData: CategorySection[] = [
    {
      id: 'my-profile',
      title: 'My Profile',
      icon: 'user',
      description: 'Basic info, photos, and preferences',
      items: [
        {
          id: 'edit-basic',
          icon: 'edit-3',
          title: 'Edit Basic Info',
          subtitle: 'Name, bio, contact information',
          route: '/(tabs)/profile/edit',
        },
        {
          id: 'media',
          icon: 'camera',
          title: 'Photos & Videos',
          subtitle: 'Manage your profile media',
          route: '/(tabs)/profile/media',
        },
        {
          id: 'preferences',
          icon: 'heart',
          title: 'Living Preferences',
          subtitle: 'Lifestyle, roommate preferences, and interests',
          route: '/(tabs)/profile/living-preferences',
        },
        {
          id: 'verification',
          icon: 'shield',
          title: 'Verification Status',
          subtitle: verificationStatus.is_verified ? 'Verified account' : 'Complete verification',
          route: '/(tabs)/profile/verification',
          badge: {
            type: 'verification' as const,
            value: verificationStatus.is_verified,
          },
        },
        {
          id: 'payment-methods',
          icon: 'credit-card',
          title: 'Payment Methods',
          subtitle: 'Manage cards, bank accounts, and payment options',
          route: '/(tabs)/profile/payment-methods',
        },
      ],
    },
    {
      id: 'living-roommates',
      title: 'Living & Roommates',
      icon: 'home',
      description: 'Household and roommate management',
      items: [
        {
          id: 'household',
          icon: 'users',
          title: 'My Household',
          subtitle: 'Current living situation and household details',
          route: '/(tabs)/profile/household',
        },
        {
          id: 'roommate-relations',
          icon: 'user-plus',
          title: 'Roommate Relations',
          subtitle: 'Manage current roommates and relationships',
          route: '/(tabs)/profile/roommate-relations',
        },
        {
          id: 'find-roommates',
          icon: 'search',
          title: 'Find Roommates',
          subtitle: 'Discover compatible matches',
          route: '/matching', // Link to main matching features
        },
      ],
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings',
      description: 'Account and app preferences',
      items: [
        {
          id: 'account',
          icon: 'user-check',
          title: 'Account & Security',
          subtitle: 'Password, security settings, and personal info',
          route: '/(tabs)/profile/account-settings',
        },
        {
          id: 'notifications',
          icon: 'bell',
          title: 'Notifications',
          subtitle: 'Manage notification preferences',
          route: '/(tabs)/profile/notifications',
        },
        {
          id: 'privacy',
          icon: 'lock',
          title: 'Privacy',
          subtitle: 'Control who sees your information',
          route: '/(tabs)/profile/privacy',
        },
      ],
    },
  ];

  // Progressive disclosure - Advanced features (previously overwhelming users)
  const advancedItems: MenuItem[] = [
    {
      id: 'ai-compatibility',
      icon: 'cpu',
      title: 'AI Compatibility Settings',
      subtitle: 'Advanced matching preferences and AI insights',
      route: '/(tabs)/profile/ai-compatibility',
    },
    {
      id: 'analytics',
      icon: 'bar-chart-2',
      title: 'Analytics & Insights',
      subtitle: 'Profile performance and detailed insights',
      route: '/(tabs)/profile/analytics',
    },
    {
      id: 'advanced-features',
      icon: 'zap',
      title: 'Advanced Features',
      subtitle: 'Experimental and power user features',
      route: '/(tabs)/profile/advanced',
    },
  ];

  // Business features - moved out of main profile (as per restructure recommendation)
  const businessItems: MenuItem[] = [
    {
      id: 'property-manager',
      icon: 'home',
      title: 'Property Manager Dashboard',
      subtitle: 'Manage properties and tenant relations',
      route: '/(tabs)/profile/property-manager-dashboard',
    },
    {
      id: 'service-provider',
      icon: 'briefcase',
      title: 'Service Provider Hub',
      subtitle: 'Manage services and bookings',
      route: '/(tabs)/profile/unified-service-provider',
    },
  ];

  if (loading) {
    return (
      <AccessibilityProfileWrapper 
        screenTitle="Profile Loading"
        announceOnFocus={false}
      >
        <UnifiedLoadingStates 
          state="loading"
          message="Loading your profile information..."
        />
      </AccessibilityProfileWrapper>
    );
  }

  if (!state?.user) {
    return (
      <AccessibilityProfileWrapper 
        screenTitle="Profile Authentication Required"
        announceOnFocus={true}
      >
              <View style={styles.container}>
        <Text
          style={styles.errorText}
            accessible={true}
            accessibilityRole="text"
            accessibilityLabel="Authentication required message"
          >
            Please sign in to view your profile.
          </Text>
        </View>
      </AccessibilityProfileWrapper>
    );
  }

  return (
    <ProfileErrorBoundary>
      <AccessibilityProfileWrapper 
        screenTitle="Profile"
        skipLinks={[
          { label: 'Skip to main content', target: 'main-content', accessibilityHint: 'Jump to profile information' },
          { label: 'Skip to settings', target: 'settings-section', accessibilityHint: 'Jump to profile settings' },
        ]}
      >
        <SafeAreaView style={styles.container}>
          <ScrollView
            style={styles.scrollView}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            showsVerticalScrollIndicator={false}
            accessible={false}
            accessibilityLabel="Profile content"
          >
          {/* Profile Header */}
          <View style={styles.header}>
            <UnifiedProfileCard 
              profile={profileData} 
              stats={{
                verifiedStatus: verificationStatus.is_verified,
                profileCompletion: completionPercentage,
                matchCount: 23,
                joinedDate: '2024',
                responseRate: 95,
                rating: 4.8,
              }}
              showStats={true}
              showEnhancedDesign={true}
              onEdit={() => navigateTo('/(tabs)/profile/edit', 'Edit Profile')}
              onPhotoPress={() => {
                // Handle photo selection
                console.log('Photo selection pressed');
              }}
              onStatsPress={() => {
                // Navigate to detailed stats
                navigateTo('/(tabs)/profile/analytics', 'Profile Analytics');
              }}
            />
          </View>

          {/* Profile Completion removed - using simplified structure */}

          {/* Main Categories */}
                        {categoryData.map((category) => (
                <CategoryCard
                  key={category.id}
                  category={category}
                  colors={colors}
                  onItemPress={(route) => navigateTo(route, category.title)}
                />
              ))}

          {/* Progressive Disclosure - Advanced Features */}
          <TouchableOpacity
            style={styles.advancedToggle}
            onPress={() => setShowAdvanced(!showAdvanced)}
          >
            <View style={styles.advancedToggleContent}>
              <View style={styles.advancedToggleLeft}>
                <Feather name="sliders" size={20} color={colors.primary} />
                <View>
                  <Text style={styles.advancedToggleTitle}>
                    Advanced Features
                  </Text>
                  <Text style={styles.advancedToggleSubtitle}>
                    Power user tools and analytics
                  </Text>
                </View>
              </View>
              <Feather 
                name={showAdvanced ? 'chevron-up' : 'chevron-down'} 
                size={20} 
                color={colors.text} 
              />
            </View>
          </TouchableOpacity>

          {/* Advanced Features Section */}
          {showAdvanced && (
            <View style={styles.advancedSection}>
              <Text style={styles.sectionSubtitle}>
                These features provide deeper insights and customization options
              </Text>
                              {advancedItems.map((item) => (
                  <MenuItemRow
                    key={item.id}
                    item={item}
                    colors={colors}
                    onPress={() => navigateTo(item.route, item.title)}
                  />
                ))}
            </View>
          )}

          {/* Business Features - Conditional Display */}
          {profileData?.role && ['property_manager', 'service_provider'].includes(profileData.role) && (
            <>
              <View style={styles.businessHeader}>
                <View style={styles.businessHeaderContent}>
                  <View style={[styles.businessIcon, { backgroundColor: colors.warning + '20' }]}>
                    <Feather name="briefcase" size={24} color={colors.warning} />
                  </View>
                  <View>
                    <Text style={styles.businessTitle}>
                      Business Features
                    </Text>
                    <Text style={styles.businessSubtitle}>
                      Professional tools and dashboards
                    </Text>
                  </View>
                </View>
              </View>
              
              <View style={styles.businessSection}>
                                 {businessItems.map((item) => (
                   <MenuItemRow
                     key={item.id}
                     item={item}
                     colors={colors}
                     onPress={() => checkServiceProviderAndNavigate(item.route)}
                   />
                 ))}
              </View>
            </>
          )}

          {/* Sign Out Button */}
          <TouchableOpacity
            style={styles.signOutButton}
            onPress={handleSignOut}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Sign out of your account"
            accessibilityHint="Tap to sign out and return to login screen"
            // ACCESSIBILITY: Ensure minimum touch target size (44x44pt)
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <Feather 
              name="log-out" 
              size={20} 
              // ACCESSIBILITY FIX: Use theme surface color instead of hardcoded white for proper contrast
              color={colors.surface || '#FFFFFF'}
              // ACCESSIBILITY: Make icon decorative since button has proper label
              accessible={false}
            />
            <Text style={[styles.signOutText, { 
              // ACCESSIBILITY FIX: Ensure sufficient color contrast for text on danger background
                                color: theme.colors.surface, // Use theme surface color for WCAG 2.1 compliance
              fontWeight: '600', // Make text slightly bolder for readability
              // Ensure contrast ratio meets WCAG 2.1 AA minimum requirement (4.5:1)
            }]}>
              Sign Out
            </Text>
          </TouchableOpacity>

          <View style={styles.bottomSpacing} />
        </ScrollView>
      </SafeAreaView>
    </AccessibilityProfileWrapper>
  </ProfileErrorBoundary>
  );
}

// Category Card Component
interface CategoryCardProps {
  category: CategorySection;
  colors: any;
  onItemPress: (route: string) => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, colors, onItemPress }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.categoryCard}>
      <View style={styles.categoryHeader}>
        <View style={styles.categoryHeaderLeft}>
          <View style={[styles.categoryIcon, { backgroundColor: colors.primary + '20' }]}>
            <Feather name={category.icon} size={24} color={colors.primary} />
          </View>
          <View>
            <Text style={styles.categoryTitle}>{category.title}</Text>
            <Text style={styles.categoryDescription}>
              {category.description}
            </Text>
          </View>
        </View>
      </View>
    
    <View style={styles.categoryItems}>
      {category.items.map((item) => (
        <MenuItemRow
          key={item.id}
          item={item}
          colors={colors}
          onPress={() => onItemPress(item.route)}
        />
      ))}
    </View>
  </View>
  );
};

// Menu Item Row Component
interface MenuItemRowProps {
  item: MenuItem;
  colors: any;
  onPress: () => void;
}

const MenuItemRow: React.FC<MenuItemRowProps> = ({ item, colors, onPress }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  return (
    <TouchableOpacity 
      style={styles.menuItem} 
    onPress={onPress}
    accessible={true}
    accessibilityRole="button"
    accessibilityLabel={`${item.title}. ${item.subtitle}${item.badge ? `. Status: ${item.badge.type === 'verification' && item.badge.value ? 'Verified' : item.badge.type === 'verification' ? 'Not verified' : item.badge.value}` : ''}`}
    accessibilityHint={`Navigate to ${item.title} screen`}
    accessibilityState={item.badge?.type === 'verification' ? { 
      selected: !!item.badge.value 
    } : undefined}
    // ACCESSIBILITY: Ensure minimum touch target size (44x44pt) for WCAG 2.1 compliance
    hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
  >
    <View style={styles.menuItemLeft}>
      <Feather 
        name={item.icon} 
        size={20} 
        color={colors.text}
        // ACCESSIBILITY: Make icon decorative since parent button has comprehensive label
        accessible={false}
        importantForAccessibility="no-hide-descendants"
      />
      <View style={styles.menuItemText}>
        <Text 
          style={styles.menuItemTitle}
          // ACCESSIBILITY: Make text part of parent button accessibility tree
          accessible={false}
        >
          {item.title}
        </Text>
        <Text 
          style={styles.menuItemSubtitle}
          // ACCESSIBILITY: Make text part of parent button accessibility tree
          accessible={false}
        >
          {item.subtitle}
        </Text>
      </View>
    </View>
    
    <View style={styles.menuItemRight}>
      {item.badge && (
        <View 
          style={[
            styles.badge,
            { backgroundColor: item.badge.type === 'verification' && item.badge.value 
              ? colors.success 
              : colors.warning 
            }
          ]}
          // ACCESSIBILITY: Make badge semantically meaningful for verification status
          accessible={false}
          importantForAccessibility="no-hide-descendants"
        >
          {item.badge.type === 'verification' && (
            <Feather 
              name={item.badge.value ? 'check' : 'alert-circle'} 
              size={12} 
              // ACCESSIBILITY FIX: Use theme surface color instead of hardcoded white for proper contrast
              color={colors.surface || '#FFFFFF'}
              // ACCESSIBILITY: Make icon decorative since badge status is in parent label
              accessible={false}
            />
          )}
        </View>
      )}
      <Feather 
        name="chevron-right" 
        size={16} 
        color={colors.text}
        // ACCESSIBILITY: Make chevron decorative navigation indicator
        accessible={false}
      />
    </View>
  </TouchableOpacity>
  );
};


