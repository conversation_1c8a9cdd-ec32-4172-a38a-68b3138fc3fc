import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Switch,
  FlatList,
  useColorScheme,
  RefreshControl,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';
import { useTheme, colorWithOpacity } from '@design-system';
import { useToast } from '@components/ui/Toast';
import { Button } from '@design-system';
import {
  Bell,
  BellOff,
  Clock,
  CheckCircle,
  X,
  Settings,
  Mail,
  MessageSquare,
  Heart,
  AlertCircle,
  UserPlus,
  ArrowLeft,
  RefreshCw,
  Trash2,
} from 'lucide-react-native';

// Use centralized color utility for React Native compatibility

// Mock notification types
const NOTIFICATION_TYPES = {
  MESSAGE: 'message',
  MATCH: 'match',
  SYSTEM: 'system',
  REMINDER: 'reminder',
  ALERT: 'alert',
};

// Mock notifications
const MOCK_NOTIFICATIONS = [
  {
    id: '1',
    type: NOTIFICATION_TYPES.MESSAGE,
    title: 'New Message',
    message: 'Sarah sent you a new message',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    read: false,
  },
  {
    id: '2',
    type: NOTIFICATION_TYPES.MATCH,
    title: 'New Match',
    message: 'You have a new roommate match!',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    read: true,
  },
  {
    id: '3',
    type: NOTIFICATION_TYPES.SYSTEM,
    title: 'Profile Updated',
    message: 'Your profile has been updated successfully',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    read: true,
  },
  {
    id: '4',
    type: NOTIFICATION_TYPES.REMINDER,
    title: 'Complete Your Profile',
    message: 'Add more details to get better matches',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    read: false,
  },
  {
    id: '5',
    type: NOTIFICATION_TYPES.ALERT,
    title: 'Verification Required',
    message: 'Please verify your identity to continue',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    read: false,
  },
];

// Interface for notification settings
interface NotificationSettings {
  messages: boolean;
  matches: boolean;
  reminders: boolean;
  system: boolean;
  email: boolean;
}

// Function to get notification icon based on type
const getNotificationIcon = (type: string, color: string) => {
  switch (type) {
    case NOTIFICATION_TYPES.MESSAGE:
      return <MessageSquare size={24} color={color} />;
    case NOTIFICATION_TYPES.MATCH:
      return <Heart size={24} color={color} />;
    case NOTIFICATION_TYPES.SYSTEM:
      return <Settings size={24} color={color} />;
    case NOTIFICATION_TYPES.REMINDER:
      return <Clock size={24} color={color} />;
    case NOTIFICATION_TYPES.ALERT:
      return <AlertCircle size={24} color={color} />;
    default:
      return <Bell size={24} color={color} />;
  }
};

// Function to format timestamp
const formatTimestamp = (timestamp: string) => {
  const now = new Date();
  const notificationDate = new Date(timestamp);
  const diffMs = now.getTime() - notificationDate.getTime();
  const diffMins = Math.round(diffMs / 60000);
  const diffHrs = Math.round(diffMs / (60000 * 60));
  const diffDays = Math.round(diffMs / (60000 * 60 * 24));

  if (diffMins < 60) {
    return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffHrs < 24) {
    return `${diffHrs} ${diffHrs === 1 ? 'hour' : 'hours'} ago`;
  } else {
    return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
  }
};

export default function NotificationsScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState(MOCK_NOTIFICATIONS);
  const [showSettings, setShowSettings] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    messages: true,
    matches: true,
    reminders: true,
    system: true,
    email: true,
  });

  // Mark notification as read
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
    toast?.show('Notification marked as read', 'success');
  }, [toast]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
    toast?.show('All notifications marked as read', 'success');
  }, [toast]);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    toast?.show('All notifications cleared', 'success');
  }, [toast]);

  // Toggle notification setting
  const toggleNotificationSetting = useCallback((setting: keyof NotificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
      toast?.show('Notifications refreshed', 'success');
    }, 1000);
  }, [toast]);

  // Render notification item
  const renderNotification = ({ item }: { item: (typeof MOCK_NOTIFICATIONS)[0] }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        { backgroundColor: item.read ? theme.colors.background : colorWithOpacity(theme.colors.surface, 0.2) },
      ]}
      onPress={() => markAsRead(item.id)}
    >
      <View style={styles.notificationIcon}>
        {getNotificationIcon(item.type, theme.colors.primary)}
        {!item.read && <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]} />}
      </View>
      <View style={styles.notificationContent}>
        <Text style={[styles.notificationTitle, { color: theme.colors.text }]}>{item.title}</Text>
        <Text style={[styles.notificationMessage, { color: colorWithOpacity(theme.colors.text, 0.8) }]}>
          {item.message}
        </Text>
        <Text style={[styles.notificationTime, { color: colorWithOpacity(theme.colors.text, 0.6) }]}>
          {formatTimestamp(item.timestamp)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  // Render notification settings
  const renderNotificationSettings = () => (
    <View style={styles.settingsContainer}>
      <Text style={[styles.settingsTitle, { color: theme.colors.text }]}>Notification Settings</Text>

      <View style={styles.settingsList}>
        <View style={[styles.settingItem, { borderColor: theme.colors.border }]}>
          <View style={styles.settingInfo}>
            <MessageSquare size={20} color={theme.colors.text} />
            <Text style={[styles.settingTitle, { color: theme.colors.text }]}>Messages</Text>
          </View>
          <Switch
            value={notificationSettings.messages}
            onValueChange={() => toggleNotificationSetting('messages')}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.background}
          />
        </View>

        <View style={[styles.settingItem, { borderColor: theme.colors.border }]}>
          <View style={styles.settingInfo}>
            <Heart size={20} color={theme.colors.text} />
            <Text style={[styles.settingTitle, { color: theme.colors.text }]}>Matches</Text>
          </View>
          <Switch
            value={notificationSettings.matches}
            onValueChange={() => toggleNotificationSetting('matches')}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.background}
          />
        </View>

        <View style={[styles.settingItem, { borderColor: theme.colors.border }]}>
          <View style={styles.settingInfo}>
            <Clock size={20} color={theme.colors.text} />
            <Text style={[styles.settingTitle, { color: theme.colors.text }]}>Reminders</Text>
          </View>
          <Switch
            value={notificationSettings.reminders}
            onValueChange={() => toggleNotificationSetting('reminders')}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.background}
          />
        </View>

        <View style={[styles.settingItem, { borderColor: theme.colors.border }]}>
          <View style={styles.settingInfo}>
            <Settings size={20} color={theme.colors.text} />
            <Text style={[styles.settingTitle, { color: theme.colors.text }]}>System Updates</Text>
          </View>
          <Switch
            value={notificationSettings.system}
            onValueChange={() => toggleNotificationSetting('system')}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.background}
          />
        </View>

        <View style={[styles.settingItem, { borderColor: theme.colors.border }]}>
          <View style={styles.settingInfo}>
            <Mail size={20} color={theme.colors.text} />
            <Text style={[styles.settingTitle, { color: theme.colors.text }]}>Email Notifications</Text>
          </View>
          <Switch
            value={notificationSettings.email}
            onValueChange={() => toggleNotificationSetting('email')}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.background}
          />
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          onPress={() => setShowSettings(false)}
          variant="filled"
          color="primary"
          style={styles.saveButton}
        >
          Save Settings
        </Button>
      </View>
    </View>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      edges={['top']}
    >
      <Stack.Screen options={{ headerShown: false }} />

      {/* Enhanced Header */}
      <View style={[styles.headerContainer, { 
        backgroundColor: theme.colors.surface,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <TouchableOpacity 
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
            borderRadius: 8,
            padding: 6,
          }}>
            <ArrowLeft size={20} color={theme.colors.primary} />
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <View style={{
            backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
            borderRadius: 10,
            padding: 8,
            marginRight: 12,
          }}>
            <Bell size={24} color={theme.colors.warning} />
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Notifications
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              {notifications.filter(n => !n.read).length} unread • {notifications.length} total
            </Text>
          </View>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerActionButton, { 
              backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
              marginRight: 8,
            }]}
            onPress={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color={theme.colors.primary} />
            ) : (
              <RefreshCw size={18} color={theme.colors.primary} />
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.headerActionButton, { 
              backgroundColor: colorWithOpacity(theme.colors.textSecondary, 0.15),
            }]}
            onPress={() => setShowSettings(!showSettings)}
          >
            <Settings size={18} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : showSettings ? (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {renderNotificationSettings()}
        </ScrollView>
      ) : (
        <View style={styles.mainContainer}>
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }]}
              onPress={markAllAsRead}
            >
              <CheckCircle size={16} color={theme.colors.primary} />
              <Text style={[styles.actionText, { color: theme.colors.primary }]}>Mark all as read</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colorWithOpacity(theme.colors.error, 0.1) }]}
              onPress={clearNotifications}
            >
              <X size={16} color={theme.colors.error} />
              <Text style={[styles.actionText, { color: theme.colors.error }]}>Clear all</Text>
            </TouchableOpacity>
          </View>

          {notifications.length > 0 ? (
            <FlatList
              data={notifications}
              renderItem={renderNotification}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.notificationsContainer}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  colors={[theme.colors.primary]}
                  tintColor={theme.colors.primary}
                />
              }
            />
          ) : (
            <View style={styles.emptyContainer}>
              <BellOff size={64} color={colorWithOpacity(theme.colors.text, 0.25)} />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Notifications</Text>
              <Text style={[styles.emptyMessage, { color: colorWithOpacity(theme.colors.text, 0.6) }]}>
                You don't have any notifications at the moment.
              </Text>
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionButton: {
    padding: 10,
    borderRadius: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  notificationsContainer: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
  },
  notificationIcon: {
    marginRight: 12,
    position: 'relative',
  },
  unreadDot: {
    position: 'absolute',
    width: 10,
    height: 10,
    borderRadius: 5,
    right: -2,
    top: -2,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 16,
    textAlign: 'center',
  },
  settingsContainer: {
    flex: 1,
  },
  settingsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingsList: {
    marginBottom: 24,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingTitle: {
    fontSize: 16,
    marginLeft: 12,
  },
  buttonContainer: {
    marginTop: 24,
  },
  saveButton: {
    marginBottom: 12,
  },
});
