import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  ActivityIndicator,
  SafeAreaView,
  Modal,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useColorScheme } from 'react-native';
import { logger } from '@utils/logger';

interface VerificationMethod {
  id: string;
  title: string;
  description: string;
  icon: string;
  time: string;
  difficulty: 'Easy' | 'Medium' | 'Advanced';
  premium: boolean;
  disabled?: boolean;
  disabledReason?: string;
}

interface IncomeSource {
  id: string;
  type: string;
  amount: string;
  frequency: 'monthly' | 'yearly';
  verified: boolean;
}

export default function IncomeVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>([]);
  const [totalIncome, setTotalIncome] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<string[]>([]);
  const [showCalculator, setShowCalculator] = useState(false);
  const [tempIncomeAmount, setTempIncomeAmount] = useState('');
  const [tempIncomeType, setTempIncomeType] = useState('');

  const verificationMethods: VerificationMethod[] = [
    {
      id: 'pay_stub',
      title: 'Pay Stub Upload',
      description: 'Upload recent pay stubs (last 2-3 months)',
      icon: 'file-text',
      time: '5 minutes',
      difficulty: 'Easy',
      premium: false,
    },
    {
      id: 'tax_return',
      title: 'Tax Return',
      description: 'Upload last year\'s tax return (W-2, 1099)',
      icon: 'folder',
      time: '10 minutes',
      difficulty: 'Medium',
      premium: false,
    },
    {
      id: 'bank_statements',
      title: 'Bank Statements',
      description: 'Upload 3 months of bank statements',
      icon: 'credit-card',
      time: '15 minutes',
      difficulty: 'Medium',
      premium: false,
    },
    {
      id: 'employment_letter',
      title: 'Employment Letter',
      description: 'Official letter from employer with salary details',
      icon: 'briefcase',
      time: '7 minutes',
      difficulty: 'Easy',
      premium: false,
    },
    {
      id: 'instant_verification',
      title: 'Instant Bank Verification',
      description: 'Premium instant bank connection service coming soon',
      icon: 'zap',
      time: '2 minutes',
      difficulty: 'Easy',
      premium: true,
      disabled: true,
      disabledReason: 'Premium instant bank verification service coming soon',
    },
  ];

  const incomeTypes = [
    'Salary/Wages',
    'Freelance/Contract',
    'Business Income',
    'Investment Income',
    'Rental Income',
    'Government Benefits',
    'Pension/Retirement',
    'Other Income',
  ];

  const calculateTotalIncome = () => {
    const total = incomeSources.reduce((sum, source) => {
      const amount = parseFloat(source.amount) || 0;
      // Convert to monthly if yearly
      const monthlyAmount = source.frequency === 'yearly' ? amount / 12 : amount;
      return sum + monthlyAmount;
    }, 0);
    setTotalIncome(total);
  };

  useEffect(() => {
    calculateTotalIncome();
  }, [incomeSources]);

  const handleMethodSelect = (methodId: string) => {
    const method = verificationMethods.find(m => m.id === methodId);
    
    // Check if method is disabled (premium coming soon)
    if (method?.disabled) {
      Alert.alert(
        'Coming Soon',
        method.disabledReason || 'This premium feature is coming soon',
        [{ text: 'OK' }]
      );
      return;
    }
    
    setSelectedMethod(methodId);
    setCurrentStep(1);
  };

  const handleDocumentUpload = async () => {
    setIsUploading(true);
    try {
      // Simulate document upload
      await new Promise(resolve => setTimeout(resolve, 3000));
      const newDoc = `document_${Date.now()}`;
      setUploadedDocuments([...uploadedDocuments, newDoc]);
      setCurrentStep(2);
      logger.info('Income document uploaded', { method: selectedMethod, document: newDoc });
    } catch (error) {
      logger.error('Failed to upload income document', error as Error);
      Alert.alert('Upload Failed', 'Please try again');
    } finally {
      setIsUploading(false);
    }
  };

  const handleAddIncomeSource = () => {
    if (!tempIncomeAmount || !tempIncomeType) {
      Alert.alert('Missing Information', 'Please fill in all fields');
      return;
    }

    const newSource: IncomeSource = {
      id: Date.now().toString(),
      type: tempIncomeType,
      amount: tempIncomeAmount,
      frequency: 'monthly',
      verified: false,
    };

    setIncomeSources([...incomeSources, newSource]);
    setTempIncomeAmount('');
    setTempIncomeType('');
    setShowCalculator(false);
  };

  const handleRemoveIncomeSource = (id: string) => {
    setIncomeSources(incomeSources.filter(source => source.id !== id));
  };

  const handleCompleteVerification = () => {
    if (totalIncome === 0) {
      Alert.alert('No Income Added', 'Please add at least one income source');
      return;
    }
    setCurrentStep(3);
  };

  const renderMethodSelection = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Choose Verification Method
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Select how you'd like to verify your income. Choose the method that works best for you.
      </Text>

      {verificationMethods.map((method) => (
        <TouchableOpacity
          key={method.id}
          style={[
            styles.methodCard,
            {
              backgroundColor: theme.colors.surface,
              borderColor: selectedMethod === method.id ? theme.colors.primary : theme.colors.border,
              borderWidth: selectedMethod === method.id ? 2 : 1,
              opacity: method.disabled ? 0.5 : 1,
            },
          ]}
          onPress={() => handleMethodSelect(method.id)}
          disabled={method.disabled}
        >
          <View style={styles.methodLeft}>
            <View
              style={[
                styles.methodIcon,
                { backgroundColor: selectedMethod === method.id ? theme.colors.primary : theme.colors.backgroundSecondary },
              ]}
            >
              <Feather
                name={method.icon as any}
                size={24}
                color={selectedMethod === method.id ? '#fff' : theme.colors.textSecondary}
              />
            </View>
            <View style={styles.methodInfo}>
              <View style={styles.methodHeader}>
                <Text style={[styles.methodTitle, { color: theme.colors.text }]}>
                  {method.title}
                </Text>
                {method.disabled ? (
                  <View style={[styles.comingSoonBadge, { backgroundColor: theme.colors.backgroundSecondary }]}>
                    <Text style={[styles.comingSoonText, { color: theme.colors.textSecondary }]}>COMING SOON</Text>
                  </View>
                ) : method.premium ? (
                  <View style={[styles.premiumBadge, { backgroundColor: theme.colors.warning }]}>
                    <Text style={styles.premiumText}>PRO</Text>
                  </View>
                ) : null}
              </View>
              <Text style={[styles.methodDescription, { color: theme.colors.textSecondary }]}>
                {method.description}
              </Text>
              <View style={styles.methodMeta}>
                <View style={styles.metaItem}>
                  <Feather name="clock" size={12} color={theme.colors.textSecondary} />
                  <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                    {method.time}
                  </Text>
                </View>
                <View style={styles.metaItem}>
                  <Feather name="bar-chart" size={12} color={theme.colors.textSecondary} />
                  <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                    {method.difficulty}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          {selectedMethod === method.id && !method.disabled && (
            <Feather name="check-circle" size={20} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderDocumentUpload = () => {
    const method = verificationMethods.find(m => m.id === selectedMethod);
    
    return (
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
          Upload Documents
        </Text>
        <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
          {method?.description}
        </Text>

        <View style={[styles.uploadTips, { backgroundColor: theme.colors.backgroundSecondary }]}>
          <Feather name="info" size={16} color={theme.colors.primary} />
          <View style={styles.tipsContent}>
            <Text style={[styles.tipsTitle, { color: theme.colors.text }]}>
              Document Guidelines:
            </Text>
            <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
              • Clear, high-quality images or PDFs
            </Text>
            <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
              • All text must be readable
            </Text>
            <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
              • Recent documents (within 3 months)
            </Text>
            <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
              • Personal information may be redacted
            </Text>
          </View>
        </View>

        {uploadedDocuments.map((doc, index) => (
          <View key={doc} style={[styles.uploadedDoc, { backgroundColor: theme.colors.backgroundSecondary }]}>
            <Feather name="file" size={16} color={theme.colors.success} />
            <Text style={[styles.uploadedDocText, { color: theme.colors.text }]}>
              Document {index + 1}
            </Text>
            <Feather name="check-circle" size={16} color={theme.colors.success} />
          </View>
        ))}

        <TouchableOpacity
          style={[
            styles.uploadButton,
            { backgroundColor: theme.colors.primary },
            isUploading && styles.uploadButtonDisabled,
          ]}
          onPress={handleDocumentUpload}
          disabled={isUploading}
        >
          {isUploading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <>
              <Feather name="upload" size={20} color="#fff" />
              <Text style={styles.uploadButtonText}>
                {uploadedDocuments.length > 0 ? 'Upload Another Document' : 'Upload Document'}
              </Text>
            </>
          )}
        </TouchableOpacity>

        {uploadedDocuments.length > 0 && (
          <TouchableOpacity
            style={[styles.continueButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => setCurrentStep(2)}
          >
            <Text style={styles.continueButtonText}>Continue to Income Calculation</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderIncomeCalculation = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Calculate Total Income
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Add all your income sources to calculate your total monthly income
      </Text>

      <View style={[styles.incomeCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.incomeLabel, { color: theme.colors.textSecondary }]}>
          Total Monthly Income
        </Text>
        <Text style={[styles.incomeAmount, { color: theme.colors.primary }]}>
          ${totalIncome.toLocaleString()}
        </Text>
      </View>

      {incomeSources.map((source) => (
        <View key={source.id} style={[styles.sourceCard, { backgroundColor: theme.colors.backgroundSecondary }]}>
          <View style={styles.sourceInfo}>
            <Text style={[styles.sourceType, { color: theme.colors.text }]}>
              {source.type}
            </Text>
            <Text style={[styles.sourceAmount, { color: theme.colors.textSecondary }]}>
              ${parseFloat(source.amount).toLocaleString()}/{source.frequency}
            </Text>
          </View>
          <TouchableOpacity onPress={() => handleRemoveIncomeSource(source.id)}>
            <Feather name="x" size={20} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      ))}

      <TouchableOpacity
        style={[styles.addIncomeButton, { borderColor: theme.colors.primary }]}
        onPress={() => setShowCalculator(true)}
      >
        <Feather name="plus" size={20} color={theme.colors.primary} />
        <Text style={[styles.addIncomeText, { color: theme.colors.primary }]}>
          Add Income Source
        </Text>
      </TouchableOpacity>

      {totalIncome > 0 && (
        <TouchableOpacity
          style={[styles.completeButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleCompleteVerification}
        >
          <Text style={styles.completeButtonText}>Complete Income Verification</Text>
        </TouchableOpacity>
      )}

      {/* Income Calculator Modal */}
      <Modal visible={showCalculator} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                Add Income Source
              </Text>
              <TouchableOpacity onPress={() => setShowCalculator(false)}>
                <Feather name="x" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
                Income Type
              </Text>
              <ScrollView style={styles.incomeTypeList} showsVerticalScrollIndicator={false}>
                {incomeTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.incomeTypeItem,
                      {
                        backgroundColor: tempIncomeType === type ? theme.colors.primary : theme.colors.surface,
                      },
                    ]}
                    onPress={() => setTempIncomeType(type)}
                  >
                    <Text
                      style={[
                        styles.incomeTypeText,
                        { color: tempIncomeType === type ? '#fff' : theme.colors.text },
                      ]}
                    >
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
                Monthly Amount
              </Text>
              <TextInput
                style={[styles.amountInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
                placeholder="Enter amount"
                placeholderTextColor={theme.colors.textSecondary}
                value={tempIncomeAmount}
                onChangeText={setTempIncomeAmount}
                keyboardType="numeric"
              />

              <TouchableOpacity
                style={[
                  styles.addButton,
                  { 
                    backgroundColor: tempIncomeAmount && tempIncomeType ? theme.colors.primary : theme.colors.backgroundSecondary,
                  },
                ]}
                onPress={handleAddIncomeSource}
                disabled={!tempIncomeAmount || !tempIncomeType}
              >
                <Text style={styles.addButtonText}>Add Income Source</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );

  const renderSuccess = () => (
    <View style={styles.stepContent}>
      <View style={styles.successContainer}>
        <View style={[styles.successIcon, { backgroundColor: theme.colors.success }]}>
          <Feather name="check" size={48} color="#fff" />
        </View>
        <Text style={[styles.successTitle, { color: theme.colors.text }]}>
          Income Verified!
        </Text>
        <Text style={[styles.successDescription, { color: theme.colors.textSecondary }]}>
          Your income of ${totalIncome.toLocaleString()}/month has been successfully verified.
        </Text>
      </View>

      <View style={[styles.benefitsCard, { backgroundColor: theme.colors.backgroundSecondary }]}>
        <Text style={[styles.benefitsTitle, { color: theme.colors.text }]}>
          Benefits of Income Verification:
        </Text>
        <View style={styles.benefitItem}>
          <Feather name="star" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Priority in roommate matching
          </Text>
        </View>
        <View style={styles.benefitItem}>
          <Feather name="shield" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Increased trust with landlords
          </Text>
        </View>
        <View style={styles.benefitItem}>
          <Feather name="trending-up" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Better rental opportunities
          </Text>
        </View>
        <View style={styles.benefitItem}>
          <Feather name="lock" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Secure income information storage
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.finishButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => router.back()}
      >
        <Text style={styles.finishButtonText}>Complete Verification</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderMethodSelection();
      case 1:
        return renderDocumentUpload();
      case 2:
        return renderIncomeCalculation();
      case 3:
        return renderSuccess();
      default:
        return renderMethodSelection();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Income Verification</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
  },
  methodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  methodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  methodInfo: {
    flex: 1,
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  premiumBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#fff',
  },
  comingSoonBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  comingSoonText: {
    fontSize: 10,
    fontWeight: '700',
  },
  methodDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  methodMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  metaText: {
    fontSize: 12,
    marginLeft: 4,
  },
  uploadTips: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  tipsContent: {
    marginLeft: 12,
    flex: 1,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  uploadedDoc: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  uploadedDocText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  continueButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  incomeCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  incomeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  incomeAmount: {
    fontSize: 32,
    fontWeight: '700',
  },
  sourceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  sourceInfo: {
    flex: 1,
  },
  sourceType: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  sourceAmount: {
    fontSize: 14,
  },
  addIncomeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    marginBottom: 24,
  },
  addIncomeText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  completeButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  incomeTypeList: {
    maxHeight: 200,
    marginBottom: 24,
  },
  incomeTypeItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  incomeTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  amountInput: {
    padding: 16,
    borderRadius: 12,
    fontSize: 16,
    marginBottom: 24,
  },
  addButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  successIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  successDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  benefitsCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 32,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 14,
    marginLeft: 12,
  },
  finishButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  finishButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
}); 