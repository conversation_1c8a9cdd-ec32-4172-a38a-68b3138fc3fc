import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { logger } from '@utils/logger';

interface CountryCode {
  code: string;
  country: string;
  flag: string;
}

export default function PhoneVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedCountry, setSelectedCountry] = useState<CountryCode>({
    code: '+1',
    country: 'United States',
    flag: '🇺🇸',
  });
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  const codeInputRefs = useRef<(TextInput | null)[]>([]);

  const countryCodes: CountryCode[] = [
    { code: '+1', country: 'United States', flag: '🇺🇸' },
    { code: '+1', country: 'Canada', flag: '🇨🇦' },
    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+39', country: 'Italy', flag: '🇮🇹' },
    { code: '+34', country: 'Spain', flag: '🇪🇸' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' },
    { code: '+91', country: 'India', flag: '🇮🇳' },
    { code: '+61', country: 'Australia', flag: '🇦🇺' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
  ];

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && currentStep === 1) {
      setCanResend(true);
    }
  }, [countdown, currentStep]);

  const formatPhoneNumber = (number: string) => {
    // Simple US phone number formatting
    const cleaned = number.replace(/\D/g, '');
    if (selectedCountry.code === '+1') {
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`;
      }
    }
    return cleaned;
  };

  const handlePhoneNumberChange = (text: string) => {
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length <= 10) {
      setPhoneNumber(cleaned);
    }
  };

  const handleSendCode = async () => {
    if (!phoneNumber || phoneNumber.length < 10) {
      Alert.alert('Invalid Number', 'Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate sending SMS
      await new Promise(resolve => setTimeout(resolve, 2000));
      setCurrentStep(1);
      setCountdown(30);
      setCanResend(false);
      logger.info('Verification code sent', { phone: `${selectedCountry.code}${phoneNumber}` });
    } catch (error) {
      logger.error('Failed to send verification code', error as Error);
      Alert.alert('Error', 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCodeChange = (text: string, index: number) => {
    if (text.length > 1) return;
    
    const newCode = [...verificationCode];
    newCode[index] = text;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (text && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all fields are filled
    if (newCode.every(digit => digit !== '') && newCode.join('').length === 6) {
      handleVerifyCode(newCode.join(''));
    }
  };

  const handleCodeKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !verificationCode[index] && index > 0) {
      codeInputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyCode = async (code?: string) => {
    const codeToVerify = code || verificationCode.join('');
    if (codeToVerify.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter the complete 6-digit code');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate code verification
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Check if code is correct (simulate verification)
      if (codeToVerify === '123456' || codeToVerify === '000000') {
        setIsVerified(true);
        setCurrentStep(2);
        logger.info('Phone verification successful', { phone: `${selectedCountry.code}${phoneNumber}` });
      } else {
        Alert.alert('Invalid Code', 'The verification code you entered is incorrect. Please try again.');
        setVerificationCode(['', '', '', '', '', '']);
        codeInputRefs.current[0]?.focus();
      }
    } catch (error) {
      logger.error('Phone verification failed', error as Error);
      Alert.alert('Verification Failed', 'Please try again');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setCanResend(false);
    setCountdown(30);
    setVerificationCode(['', '', '', '', '', '']);
    
    try {
      // Simulate resending SMS
      await new Promise(resolve => setTimeout(resolve, 1000));
      Alert.alert('Code Resent', 'A new verification code has been sent to your phone');
      logger.info('Verification code resent', { phone: `${selectedCountry.code}${phoneNumber}` });
    } catch (error) {
      logger.error('Failed to resend verification code', error as Error);
      Alert.alert('Error', 'Failed to resend code. Please try again.');
    }
  };

  const renderPhoneInput = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Enter Your Phone Number
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        We'll send you a verification code to confirm your phone number
      </Text>

      <View style={[styles.phoneInputContainer, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity 
          style={[styles.countrySelector, { borderColor: theme.colors.border }]}
          onPress={() => {
            // In a real app, this would open a country picker modal
            Alert.alert('Country Selection', 'Country picker would open here');
          }}
        >
          <Text style={styles.flagText}>{selectedCountry.flag}</Text>
          <Text style={[styles.countryCode, { color: theme.colors.text }]}>
            {selectedCountry.code}
          </Text>
          <Feather name="chevron-down" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
        
        <TextInput
          style={[styles.phoneInput, { color: theme.colors.text }]}
          placeholder="(*************"
          placeholderTextColor={theme.colors.textSecondary}
          value={formatPhoneNumber(phoneNumber)}
          onChangeText={handlePhoneNumberChange}
          keyboardType="phone-pad"
          maxLength={14}
        />
      </View>

      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name="info" size={16} color={theme.colors.primary} />
        <View style={styles.infoContent}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Why do we need your phone number?
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            • Enhanced security for your account
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            • Two-factor authentication
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            • Important notifications about your matches
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            • Emergency contact for roommate safety
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.sendButton,
          { 
            backgroundColor: phoneNumber.length >= 10 ? theme.colors.primary : theme.colors.surface,
          },
          isLoading && styles.sendButtonDisabled,
        ]}
        onPress={handleSendCode}
        disabled={phoneNumber.length < 10 || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <>
            <Feather name="message-square" size={20} color="#fff" />
            <Text style={styles.sendButtonText}>Send Verification Code</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderCodeVerification = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Enter Verification Code
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        We sent a 6-digit code to {selectedCountry.flag} {selectedCountry.code} {formatPhoneNumber(phoneNumber)}
      </Text>

      <View style={styles.codeContainer}>
        {verificationCode.map((digit, index) => (
          <TextInput
            key={index}
            ref={(ref) => (codeInputRefs.current[index] = ref)}
            style={[
              styles.codeInput,
              {
                backgroundColor: theme.colors.surface,
                borderColor: digit ? theme.colors.primary : theme.colors.border,
                color: theme.colors.text,
              },
            ]}
            value={digit}
            onChangeText={(text) => handleCodeChange(text, index)}
            onKeyPress={({ nativeEvent }) => handleCodeKeyPress(nativeEvent.key, index)}
            keyboardType="number-pad"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      <View style={styles.resendContainer}>
        {countdown > 0 ? (
          <Text style={[styles.countdownText, { color: theme.colors.textSecondary }]}>
            Resend code in {countdown}s
          </Text>
        ) : (
          <TouchableOpacity onPress={handleResendCode} disabled={!canResend}>
            <Text style={[styles.resendText, { color: canResend ? theme.colors.primary : theme.colors.textSecondary }]}>
              Didn't receive a code? Resend
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={[styles.changeNumberButton, { borderColor: theme.colors.border }]}
        onPress={() => setCurrentStep(0)}
      >
        <Feather name="edit" size={16} color={theme.colors.textSecondary} />
        <Text style={[styles.changeNumberText, { color: theme.colors.textSecondary }]}>
          Change Phone Number
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.verifyButton,
          { 
            backgroundColor: verificationCode.every(d => d !== '') ? theme.colors.primary : theme.colors.surface,
          },
          isLoading && styles.verifyButtonDisabled,
        ]}
        onPress={() => handleVerifyCode()}
        disabled={!verificationCode.every(d => d !== '') || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.verifyButtonText}>Verify Phone Number</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderSuccess = () => (
    <View style={styles.stepContent}>
      <View style={styles.successContainer}>
        <View style={[styles.successIcon, { backgroundColor: theme.colors.success }]}>
          <Feather name="check" size={48} color="#fff" />
        </View>
        <Text style={[styles.successTitle, { color: theme.colors.text }]}>
          Phone Number Verified!
        </Text>
        <Text style={[styles.successDescription, { color: theme.colors.textSecondary }]}>
          Your phone number {selectedCountry.flag} {selectedCountry.code} {formatPhoneNumber(phoneNumber)} has been successfully verified.
        </Text>
      </View>

      <View style={[styles.benefitsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.benefitsTitle, { color: theme.colors.text }]}>
          What's next?
        </Text>
        <View style={styles.benefitItem}>
          <Feather name="shield" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Enhanced account security
          </Text>
        </View>
        <View style={styles.benefitItem}>
          <Feather name="bell" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Important notifications enabled
          </Text>
        </View>
        <View style={styles.benefitItem}>
          <Feather name="users" size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
            Increased trust with potential roommates
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.completeButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => router.back()}
      >
        <Text style={styles.completeButtonText}>Complete Verification</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderPhoneInput();
      case 1:
        return renderCodeVerification();
      case 2:
        return renderSuccess();
      default:
        return renderPhoneInput();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Feather name="arrow-left" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Phone Verification</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCurrentStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    borderRadius: 12,
    padding: 4,
  },
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRightWidth: 1,
    marginRight: 12,
  },
  flagText: {
    fontSize: 18,
    marginRight: 8,
  },
  countryCode: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 4,
  },
  phoneInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 16,
    paddingHorizontal: 12,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  codeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  codeInput: {
    width: 48,
    height: 56,
    borderRadius: 12,
    borderWidth: 2,
    fontSize: 20,
    fontWeight: '600',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  countdownText: {
    fontSize: 14,
  },
  resendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  changeNumberButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
  },
  changeNumberText: {
    fontSize: 14,
    marginLeft: 8,
  },
  verifyButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  verifyButtonDisabled: {
    opacity: 0.6,
  },
  verifyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  successIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  successDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  benefitsCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 32,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 14,
    marginLeft: 12,
  },
  completeButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
}); 