import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useColorScheme } from 'react-native';
import { logger } from '@utils/logger';
import * as ImagePicker from 'expo-image-picker';
import { SupabaseStorageService } from '@utils/supabaseStorageService';
import { supabase } from '@services/supabaseService';
import { useAuth } from '@hooks/useAuth';
import * as FileSystem from 'expo-file-system';

interface DocumentType {
  id: string;
  name: string;
  description: string;
  icon: string;
  accepted: boolean;
  required: boolean;
}

interface VerificationStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
}

export default function IdentityVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedDocument, setSelectedDocument] = useState<string>('');
  const [uploadedImage, setUploadedImage] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationResults, setVerificationResults] = useState<any>(null);

  const documentTypes: DocumentType[] = [
    {
      id: 'drivers_license',
      name: "Driver's License",
      description: 'Government-issued driver\'s license (front and back)',
      icon: 'credit-card',
      accepted: true,
      required: false,
    },
    {
      id: 'passport',
      name: 'Passport',
      description: 'Valid passport (photo page)',
      icon: 'book',
      accepted: true,
      required: false,
    },
    {
      id: 'state_id',
      name: 'State ID',
      description: 'Government-issued state identification',
      icon: 'user',
      accepted: true,
      required: false,
    },
    {
      id: 'military_id',
      name: 'Military ID',
      description: 'Active military identification',
      icon: 'shield',
      accepted: true,
      required: false,
    },
  ];

  const verificationSteps: VerificationStep[] = [
    {
      id: 'document_selection',
      title: 'Select Document Type',
      description: 'Choose the type of ID you want to verify',
      completed: selectedDocument !== '',
      current: currentStep === 0,
    },
    {
      id: 'document_upload',
      title: 'Upload Document',
      description: 'Take clear photos of your document',
      completed: uploadedImage !== '',
      current: currentStep === 1,
    },
    {
      id: 'verification_process',
      title: 'AI Verification',
      description: 'Our AI system verifies your document',
      completed: verificationResults?.status === 'verified',
      current: currentStep === 2,
    },
    {
      id: 'completion',
      title: 'Verification Complete',
      description: 'Your identity has been successfully verified',
      completed: verificationResults?.status === 'verified',
      current: currentStep === 3,
    },
  ];

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocument(documentId);
    if (currentStep === 0) {
      setCurrentStep(1);
    }
  };

  const handleImageUpload = async () => {
    setIsUploading(true);
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to upload documents');
        return;
      }

      // Show image picker options
      Alert.alert(
        'Select Photo',
        'Choose how you want to add your document photo',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Camera', onPress: () => pickImage('camera') },
          { text: 'Photo Library', onPress: () => pickImage('library') },
        ]
      );

    } catch (error) {
      logger.error('Failed to upload document', error as Error);
      Alert.alert('Upload Failed', 'Please try again');
      setIsUploading(false);
    }
  };

  const pickImage = async (source: 'camera' | 'library') => {
    try {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const options: ImagePicker.ImagePickerOptions = {
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
      };

      let result;
      if (source === 'camera') {
        result = await ImagePicker.launchCameraAsync(options);
      } else {
        result = await ImagePicker.launchImageLibraryAsync(options);
      }

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await uploadToStorage(asset.uri, user.id);
      } else {
        setIsUploading(false);
      }
    } catch (error) {
      logger.error('Failed to pick image', error as Error);
      Alert.alert('Error', 'Failed to select image');
      setIsUploading(false);
    }
  };

  const uploadToStorage = async (imageUri: string, userId: string) => {
    try {
      // Debug environment variables and auth status
      console.log('🔍 Environment check:', {
        hasSupabaseUrl: !!process.env.EXPO_PUBLIC_SUPABASE_URL,
        hasSupabaseKey: !!process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
        supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
      });

      // Check authentication context
      console.log('👤 Identity verification auth context:', {
        userId: user?.id,
        userEmail: user?.email,
        isAuthenticated: !!user,
      });

      // **DIAGNOSTIC TEST**: Try uploading a tiny text file first to isolate the issue
      console.log('🧪 Running diagnostic test with small text file...');
      try {
        const testText = "test file content";
        const testArrayBuffer = new TextEncoder().encode(testText);
        const testPath = `test/${userId}/diagnostic_test_${Date.now()}.txt`;
        
        console.log('🧪 Testing storage with tiny file:', {
          size: testArrayBuffer.byteLength,
          path: testPath
        });

        const { data: testData, error: testError } = await supabase.storage
          .from('varification')
          .upload(testPath, testArrayBuffer, {
            contentType: 'text/plain',
            cacheControl: '3600',
            upsert: true
          });

        if (testError) {
          console.error('❌ DIAGNOSTIC: Text file upload failed:', testError);
        } else {
          console.log('✅ DIAGNOSTIC: Text file upload SUCCESS:', testData);
          
          // Clean up test file
          await supabase.storage.from('varification').remove([testPath]);
          console.log('🧹 Test file cleaned up');
        }
      } catch (diagError) {
        console.error('💥 DIAGNOSTIC: Exception during test:', diagError);
      }

      // Validate file
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        throw new Error('Selected file does not exist');
      }

      console.log('📁 File validation passed:', {
        exists: fileInfo.exists,
        size: fileInfo.size,
        uri: imageUri
      });

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024;
      if (fileInfo.size && fileInfo.size > maxSize) {
        throw new Error('File size too large. Please select a smaller image.');
      }

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `${selectedDocument}_${timestamp}.jpg`;
      const storagePath = `${selectedDocument}/${userId}/${filename}`;

      console.log('📤 Upload parameters:', {
        documentType: selectedDocument,
        path: storagePath,
        size: fileInfo.size,
        bucket: 'varification'
      });

      logger.info('Starting document upload', { 
        documentType: selectedDocument, 
        path: storagePath,
        size: fileInfo.size 
      });

      // Upload to Supabase storage using intelligent uploader (same as create listing)
      const { uploadVerificationDocumentFromUri } = await import('@utils/verificationImageUpload');
      
      // Map document type to match our verification types
      const documentTypeMap: { [key: string]: string } = {
        'drivers_license': 'drivers_license',
        'driver_license': 'drivers_license', // Handle both variations
        'state_id': 'state_id',
        'passport': 'passport',
        'military_id': 'military_id',
        'identity_varification': 'identity_varification'
      };
      
      const mappedDocumentType = documentTypeMap[selectedDocument] || selectedDocument;
      
      const uploadResult = await uploadVerificationDocumentFromUri(imageUri, {
        documentType: mappedDocumentType as any,
        userId: userId,
        quality: 0.8 // Higher quality for legal documents
      });

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Upload failed');
      }

      logger.info('Document uploaded successfully', { 
        documentType: selectedDocument,
        publicUrl: uploadResult.publicUrl 
      });

      // Save to database
      await saveVerificationRecord(userId, uploadResult.publicUrl!, uploadResult.path!);

      // Update UI state
      setUploadedImage(uploadResult.publicUrl!);
      if (currentStep === 1) {
        setCurrentStep(2);
        // Auto-start verification process after upload
        setTimeout(() => {
          handleVerification();
        }, 1000); // Give UI time to update before starting verification
      }

    } catch (error) {
      logger.error('Failed to upload to storage', error as Error);
      Alert.alert('Upload Failed', error instanceof Error ? error.message : 'Please try again');
    } finally {
      setIsUploading(false);
    }
  };

  const saveVerificationRecord = async (userId: string, publicUrl: string, filePath: string) => {
    try {
      const verificationData = {
        user_id: userId,
        verification_type: 'id_document',
        status: 'pending',
        verification_data: {
          document_type: selectedDocument,
          file_url: publicUrl,
          file_path: filePath,
          uploaded_at: new Date().toISOString(),
          verification_method: 'manual', // Will be 'automated' when AI verification is implemented
        },
      };

      const { data, error } = await supabase
        .from('identity_verifications')
        .insert(verificationData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Verification record created', { verificationId: data.id });
      return data;

    } catch (error) {
      logger.error('Failed to save verification record', error as Error);
      // Don't throw here - file is already uploaded, just log the database issue
      Alert.alert('Warning', 'Document uploaded but database record failed. Please contact support.');
    }
  };

  const handleVerification = async () => {
    setIsVerifying(true);
    try {
      // TODO: Replace with real AI verification service
      // For now, simulate the process but with shorter delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const results = {
        status: 'pending', // Changed from 'verified' to be more realistic
        confidence: 85,
        details: {
          nameMatch: null, // Will be populated by real verification
          documentValid: true,
          photoMatch: null,
          expirationValid: null,
        },
        message: 'Document uploaded successfully. Verification in progress...'
      };
      
      setVerificationResults(results);
      logger.info('Identity verification initiated', { results });
      
      // Show results for 3 seconds, then advance to completion
      setTimeout(() => {
        setCurrentStep(3);
      }, 3000);
    } catch (error) {
      logger.error('Identity verification failed', error as Error);
      setVerificationResults({ status: 'failed', error: 'Verification failed' });
    } finally {
      setIsVerifying(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {verificationSteps.map((step, index) => (
        <View key={step.id} style={styles.stepContainer}>
          <View
            style={[
              styles.stepCircle,
              {
                              backgroundColor: step.completed
                ? theme.colors.success
                : step.current
                ? theme.colors.primary
                : theme.colors.surface,
              },
            ]}
          >
            {step.completed ? (
              <Feather name="check" size={16} color="#fff" />
            ) : (
              <Text
                style={[
                  styles.stepNumber,
                  { color: step.current ? '#fff' : theme.colors.textSecondary },
                ]}
              >
                {index + 1}
              </Text>
            )}
          </View>
          {index < verificationSteps.length - 1 && (
            <View
              style={[
                styles.stepConnector,
                { backgroundColor: step.completed ? theme.colors.success : theme.colors.surface },
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderDocumentSelection = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Select Document Type
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Choose the type of government-issued ID you'd like to verify
      </Text>

      {documentTypes.map((doc) => (
        <TouchableOpacity
          key={doc.id}
          style={[
            styles.documentCard,
            {
              backgroundColor: theme.colors.surface,
              borderColor: selectedDocument === doc.id ? theme.colors.primary : theme.colors.border,
              borderWidth: selectedDocument === doc.id ? 2 : 1,
            },
          ]}
          onPress={() => handleDocumentSelect(doc.id)}
        >
          <View style={styles.documentLeft}>
            <View
              style={[
                styles.documentIcon,
                { backgroundColor: selectedDocument === doc.id ? theme.colors.primary : theme.colors.surface },
              ]}
            >
              <Feather
                name={doc.icon as any}
                size={24}
                color={selectedDocument === doc.id ? '#fff' : theme.colors.textSecondary}
              />
            </View>
            <View style={styles.documentInfo}>
              <Text style={[styles.documentName, { color: theme.colors.text }]}>
                {doc.name}
              </Text>
              <Text style={[styles.documentDescription, { color: theme.colors.textSecondary }]}>
                {doc.description}
              </Text>
            </View>
          </View>
          {selectedDocument === doc.id && (
            <Feather name="check-circle" size={20} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderDocumentUpload = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Upload Your Document
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Take clear photos of your {documentTypes.find(d => d.id === selectedDocument)?.name}
      </Text>

      <View style={[styles.uploadTips, { backgroundColor: theme.colors.surface }]}>
        <Feather name="info" size={16} color={theme.colors.primary} />
        <View style={styles.tipsContent}>
          <Text style={[styles.tipsTitle, { color: theme.colors.text }]}>
            Photo Tips:
          </Text>
          <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
            • Ensure good lighting
          </Text>
          <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
            • Keep document flat and straight
          </Text>
          <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
            • Include all four corners
          </Text>
          <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
            • Avoid glare and shadows
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.uploadButton,
          { backgroundColor: theme.colors.primary },
          isUploading && styles.uploadButtonDisabled,
        ]}
        onPress={handleImageUpload}
        disabled={isUploading}
      >
        {isUploading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <>
            <Feather name="camera" size={20} color="#fff" />
            <Text style={styles.uploadButtonText}>
              {uploadedImage ? 'Re-upload Document' : 'Upload Document'}
            </Text>
          </>
        )}
      </TouchableOpacity>

      {uploadedImage && (
        <View style={styles.uploadPreviewContainer}>
          <View style={[styles.uploadSuccess, { backgroundColor: theme.colors.surface }]}>
            <Feather name="check-circle" size={16} color={theme.colors.success} />
            <Text style={[styles.uploadSuccessText, { color: theme.colors.success }]}>
              Document uploaded successfully
            </Text>
          </View>
          
          <View style={[styles.imagePreview, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.previewLabel, { color: theme.colors.text }]}>Document Preview:</Text>
            <Image 
              source={{ uri: uploadedImage }} 
              style={styles.previewImage}
              resizeMode="cover"
            />
            <Text style={[styles.previewNote, { color: theme.colors.textSecondary }]}>
              Your document has been securely uploaded and optimized
            </Text>
          </View>
        </View>
      )}

      {/* Show Start Verification button when image is uploaded */}
      {uploadedImage && (
        <TouchableOpacity
          style={[styles.continueButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => {
            setCurrentStep(2);
            handleVerification();
          }}
        >
          <Text style={styles.continueButtonText}>Start Verification</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderVerificationProcess = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        AI Verification in Progress
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Our AI system is analyzing your document...
      </Text>

      {/* Show uploaded document preview during verification */}
      {uploadedImage && (
        <View style={[styles.verificationImagePreview, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.previewLabel, { color: theme.colors.text }]}>
            Uploaded Document:
          </Text>
          <Image 
            source={{ uri: uploadedImage }} 
            style={styles.verificationPreviewImage}
            resizeMode="cover"
          />
          <Text style={[styles.previewNote, { color: theme.colors.textSecondary }]}>
            Analyzing {documentTypes.find(d => d.id === selectedDocument)?.name}...
          </Text>
        </View>
      )}

      <View style={styles.verificationContainer}>
        {isVerifying ? (
          <View style={styles.verificationLoading}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.verificationText, { color: theme.colors.text }]}>
              Analyzing document...
            </Text>
          </View>
        ) : verificationResults ? (
          <View style={styles.verificationResults}>
            <View
              style={[
                styles.verificationStatus,
                {
                  backgroundColor:
                    verificationResults.status === 'verified' 
                      ? theme.colors.success 
                      : verificationResults.status === 'pending'
                      ? theme.colors.primary
                      : theme.colors.error,
                },
              ]}
            >
              <Feather
                name={
                  verificationResults.status === 'verified' 
                    ? 'check-circle' 
                    : verificationResults.status === 'pending'
                    ? 'clock'
                    : 'x-circle'
                }
                size={48}
                color="#fff"
              />
            </View>
            <Text style={[styles.verificationTitle, { color: theme.colors.text }]}>
              {verificationResults.status === 'verified'
                ? 'Verification Successful!'
                : verificationResults.status === 'pending'
                ? 'Document Uploaded Successfully!'
                : 'Verification Failed'}
            </Text>
            <Text style={[styles.verificationMessage, { color: theme.colors.textSecondary }]}>
              {verificationResults.status === 'verified'
                ? `Confidence: ${verificationResults.confidence}%`
                : verificationResults.status === 'pending'
                ? 'Your document has been submitted for review. You will be notified once verification is complete.'
                : 'Please try again with a clearer document image.'}
            </Text>
            
            {/* Continue button for pending status */}
            {verificationResults.status === 'pending' && (
              <TouchableOpacity
                style={[styles.continueVerificationButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => setCurrentStep(3)}
              >
                <Text style={styles.continueVerificationButtonText}>Continue to Complete</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : null}
      </View>
    </View>
  );

  const renderCompletion = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Identity Verified!
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Your identity has been successfully verified. You can now enjoy enhanced trust and security features.
      </Text>

      <View style={[styles.completionCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name="shield" size={48} color={theme.colors.success} />
        <Text style={[styles.completionTitle, { color: theme.colors.text }]}>
          Enhanced Profile Trust
        </Text>
        <Text style={[styles.completionDescription, { color: theme.colors.textSecondary }]}>
          Your verified identity increases trust with potential roommates and unlocks premium features.
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.completeButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => router.back()}
      >
        <Text style={styles.completeButtonText}>Complete Verification</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderDocumentSelection();
      case 1:
        return renderDocumentUpload();
      case 2:
        return renderVerificationProcess();
      case 3:
        return renderCompletion();
      default:
        return renderDocumentSelection();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Identity Verification</Text>
        <View style={{ width: 24 }} />
      </View>

      {renderStepIndicator()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: '600',
  },
  stepConnector: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
  },
  documentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  documentLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  uploadTips: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  tipsContent: {
    marginLeft: 12,
    flex: 1,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  uploadSuccess: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  uploadSuccessText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  verificationContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  verificationLoading: {
    alignItems: 'center',
  },
  verificationText: {
    fontSize: 16,
    marginTop: 16,
  },
  verificationResults: {
    alignItems: 'center',
  },
  verificationStatus: {
    width: 96,
    height: 96,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  verificationTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  verificationConfidence: {
    fontSize: 16,
  },
  completionCard: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
    marginBottom: 32,
  },
  completionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  completionDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  completeButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  uploadPreviewContainer: {
    marginTop: 8,
  },
  imagePreview: {
    marginTop: 12,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  previewImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
    marginBottom: 8,
  },
  previewNote: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  verificationImagePreview: {
    marginVertical: 16,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  verificationPreviewImage: {
    width: 160,
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  continueButton: {
    marginTop: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  verificationMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
    lineHeight: 20,
  },
  continueVerificationButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  continueVerificationButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
}); 