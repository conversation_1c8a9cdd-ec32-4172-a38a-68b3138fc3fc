/**
 * PHASE 3: ADVANCED AI ANALYTICS DASHBOARD
 * 
 * Comprehensive AI analytics dashboard that integrates:
 * - Predictive Analytics with ML models
 * - Smart Matching insights
 * - Compatibility Engine analytics
 * - Real-time AI usage tracking
 * - Premium feature integration
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { router } from 'expo-router';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';
import { Brain, TrendingUp, Users, Target, Sparkles, BarChart3, Zap, Settings, ArrowRight, AlertCircle, CheckCircle, Clock } from 'lucide-react-native';

// Import the existing AI hooks
import { usePredictiveAnalytics } from '@hooks/usePredictiveAnalytics';
import { useSmartMatching } from '@hooks/useSmartMatching';
import { useCompatibilityEngine } from '@hooks/useCompatibilityEngine';
import { useProfile } from '@hooks/useProfile';
import { getSupabaseClient } from '@services/supabaseService';
import { logger } from '@utils/logger';

interface AIAnalyticsData {
  aiUsage: {
    totalTokens: number;
    totalCost: number;
    successRate: number;
    avgProcessingTime: number;
  };
  sentimentAnalysis: {
    positiveCount: number;
    neutralCount: number;
    negativeCount: number;
    avgScore: number;
  };
  moderationActions: {
    totalActions: number;
    automatedActions: number;
    humanReviews: number;
    successRate: number;
  };
  compatibility: {
    totalComparisons: number;
    avgCompatibility: number;
    bestMatchScore: number;
    distribution: Record<string, number>;
  };
}

export default function AIAnalyticsDashboard() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { profile } = useProfile();
  
  // AI Hooks
  const predictiveAnalytics = usePredictiveAnalytics();
  const smartMatching = useSmartMatching();
  const compatibilityEngine = useCompatibilityEngine();
  
  // State
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AIAnalyticsData | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'predictive' | 'matching' | 'compatibility'>('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setIsRefreshing(true);
      
      const supabase = getSupabaseClient();
      const userId = profile?.id;
      
      if (!userId) return;

      // Get AI usage analytics
      const { data: aiUsageData } = await supabase
        .from('ai_usage_tracking')
        .select('total_tokens, cost_estimate, success, processing_time')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      // Get sentiment analysis data
      const { data: sentimentData } = await supabase
        .from('message_sentiments')
        .select('sentiment, score')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      // Get moderation actions data
      const { data: moderationData } = await supabase
        .from('moderation_actions')
        .select('action_type, automated, confidence_score')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      // Process the data
      const aiUsage = {
        totalTokens: aiUsageData?.reduce((sum, item) => sum + (item.total_tokens || 0), 0) || 0,
        totalCost: aiUsageData?.reduce((sum, item) => sum + (item.cost_estimate || 0), 0) || 0,
        successRate: aiUsageData?.length ? (aiUsageData.filter(item => item.success).length / aiUsageData.length) * 100 : 0,
        avgProcessingTime: aiUsageData?.length ? aiUsageData.reduce((sum, item) => sum + (item.processing_time || 0), 0) / aiUsageData.length : 0,
      };

      const sentimentAnalysis = {
        positiveCount: sentimentData?.filter(item => item.sentiment === 'positive').length || 0,
        neutralCount: sentimentData?.filter(item => item.sentiment === 'neutral').length || 0,
        negativeCount: sentimentData?.filter(item => item.sentiment === 'negative').length || 0,
        avgScore: sentimentData?.length ? sentimentData.reduce((sum, item) => sum + item.score, 0) / sentimentData.length : 0,
      };

      const moderationActions = {
        totalActions: moderationData?.length || 0,
        automatedActions: moderationData?.filter(item => item.automated).length || 0,
        humanReviews: moderationData?.filter(item => !item.automated).length || 0,
        successRate: moderationData?.length ? (moderationData.filter(item => (item.confidence_score || 0) > 0.7).length / moderationData.length) * 100 : 0,
      };

      const compatibility = {
        totalComparisons: compatibilityEngine.analytics?.total_comparisons || 0,
        avgCompatibility: compatibilityEngine.analytics?.average_compatibility || 0,
        bestMatchScore: compatibilityEngine.analytics?.best_match_score || 0,
        distribution: compatibilityEngine.analytics?.compatibility_distribution || {},
      };

      setAnalyticsData({
        aiUsage,
        sentimentAnalysis,
        moderationActions,
        compatibility,
      });

      logger.info('AI analytics data loaded successfully', 'AIAnalyticsDashboard', { userId });
    } catch (error) {
      logger.error('Failed to load AI analytics data', 'AIAnalyticsDashboard', { error });
    } finally {
      setIsRefreshing(false);
    }
  };

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* AI Performance Overview */}
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.cardHeader}>
          <Brain size={24} color={theme.colors.primary} />
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>AI Performance</Text>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.primary }]}>
              {analyticsData?.aiUsage.totalTokens.toLocaleString() || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Total Tokens</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.success }]}>
              {analyticsData?.aiUsage.successRate.toFixed(1) || '0'}%
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Success Rate</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.warning }]}>
              {analyticsData?.aiUsage.avgProcessingTime.toFixed(0) || '0'}ms
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Avg Processing</Text>
          </View>
        </View>
      </View>

      {/* Sentiment Analysis */}
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.cardHeader}>
          <TrendingUp size={24} color={theme.colors.purple} />
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Sentiment Analysis</Text>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.success }]}>
              {analyticsData?.sentimentAnalysis.positiveCount || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Positive</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.textSecondary }]}>
              {analyticsData?.sentimentAnalysis.neutralCount || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Neutral</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.error }]}>
              {analyticsData?.sentimentAnalysis.negativeCount || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Negative</Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Quick Actions</Text>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.push('/(tabs)/profile/predictive-analytics-dashboard')}
        >
          <Target size={20} color={theme.colors.white} />
          <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
            View Predictive Analytics
          </Text>
          <ArrowRight size={20} color={theme.colors.white} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.purple }]}
          onPress={() => router.push('/(tabs)/profile/smart-matching-dashboard')}
        >
          <Users size={20} color={theme.colors.white} />
          <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
            Smart Matching Insights
          </Text>
          <ArrowRight size={20} color={theme.colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPredictiveTab = () => (
    <View style={styles.tabContent}>
      {predictiveAnalytics.isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading predictive models...
          </Text>
        </View>
      ) : (
        <>
          {/* Model Performance */}
          <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.cardHeader}>
              <BarChart3 size={24} color={theme.colors.primary} />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Prediction Models</Text>
            </View>
            {predictiveAnalytics.models.map((model, index) => (
              <View key={model.id} style={styles.modelItem}>
                <View style={styles.modelInfo}>
                  <Text style={[styles.modelName, { color: theme.colors.text }]}>
                    {model.name}
                  </Text>
                  <Text style={[styles.modelVersion, { color: theme.colors.textSecondary }]}>
                    v{model.version} • {model.accuracy}% accuracy
                  </Text>
                </View>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: model.status === 'active' ? theme.colors.success : theme.colors.warning }
                ]}>
                  <Text style={[styles.statusText, { color: theme.colors.white }]}>
                    {model.status.toUpperCase()}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          {/* Recent Predictions */}
          <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.cardHeader}>
              <Sparkles size={24} color={theme.colors.yellow} />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Recent Predictions</Text>
            </View>
            {predictiveAnalytics.predictions.slice(0, 3).map((prediction, index) => (
              <View key={index} style={styles.predictionItem}>
                <View style={styles.predictionInfo}>
                  <Text style={[styles.predictionScore, { color: theme.colors.text }]}>
                    {prediction.predicted_success_rate}% Success Rate
                  </Text>
                  <Text style={[styles.predictionDetails, { color: theme.colors.textSecondary }]}>
                    {prediction.recommendation.action} • {prediction.risk_factors.length} risk factors
                  </Text>
                </View>
                <View style={[
                  styles.confidenceBadge,
                  { backgroundColor: prediction.confidence_interval.confidence_level > 90 ? theme.colors.success : theme.colors.warning }
                ]}>
                  <Text style={[styles.confidenceText, { color: theme.colors.white }]}>
                    {prediction.confidence_interval.confidence_level}%
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </>
      )}
    </View>
  );

  const renderMatchingTab = () => (
    <View style={styles.tabContent}>
      {smartMatching.isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading matching analytics...
          </Text>
        </View>
      ) : (
        <>
          {/* Matching Performance */}
          <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.cardHeader}>
              <Users size={24} color={theme.colors.purple} />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Matching Performance</Text>
            </View>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.primary }]}>
                  {smartMatching.analytics?.total_matches_found || '0'}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Total Matches</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.success }]}>
                  {smartMatching.analytics?.average_compatibility.toFixed(1) || '0'}%
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Avg Compatibility</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.warning }]}>
                  {smartMatching.analytics?.best_match_score || '0'}%
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Best Match</Text>
              </View>
            </View>
          </View>

          {/* Match Quality Distribution */}
          <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.cardHeader}>
              <BarChart3 size={24} color={theme.colors.primary} />
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Match Quality</Text>
            </View>
            {Object.entries(smartMatching.analytics?.matches_by_quality || {}).map(([quality, count]) => (
              <View key={quality} style={styles.qualityItem}>
                <Text style={[styles.qualityLabel, { color: theme.colors.text }]}>
                  {quality.replace('_', ' ').toUpperCase()}
                </Text>
                <Text style={[styles.qualityCount, { color: theme.colors.textSecondary }]}>
                  {count}
                </Text>
              </View>
            ))}
          </View>
        </>
      )}
    </View>
  );

  const renderCompatibilityTab = () => (
    <View style={styles.tabContent}>
      {/* Compatibility Analytics */}
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.cardHeader}>
          <Target size={24} color={theme.colors.success} />
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Compatibility Analytics</Text>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.primary }]}>
              {analyticsData?.compatibility.totalComparisons || '0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Comparisons</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.success }]}>
              {analyticsData?.compatibility.avgCompatibility.toFixed(1) || '0'}%
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Avg Score</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: theme.colors.warning }]}>
              {analyticsData?.compatibility.bestMatchScore || '0'}%
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Best Score</Text>
          </View>
        </View>
      </View>

      {/* Effectiveness Metrics */}
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.cardHeader}>
          <TrendingUp size={24} color={theme.colors.purple} />
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Algorithm Effectiveness</Text>
        </View>
        <View style={styles.effectivenessItem}>
          <Text style={[styles.effectivenessLabel, { color: theme.colors.text }]}>Personality</Text>
          <View style={styles.effectivenessBar}>
            <View style={[
              styles.effectivenessFill,
              { 
                backgroundColor: theme.colors.primary,
                width: `${compatibilityEngine.analytics?.personality_effectiveness || 0}%`
              }
            ]} />
          </View>
          <Text style={[styles.effectivenessValue, { color: theme.colors.textSecondary }]}>
            {compatibilityEngine.analytics?.personality_effectiveness.toFixed(1) || '0'}%
          </Text>
        </View>
        <View style={styles.effectivenessItem}>
          <Text style={[styles.effectivenessLabel, { color: theme.colors.text }]}>Lifestyle</Text>
          <View style={styles.effectivenessBar}>
            <View style={[
              styles.effectivenessFill,
              { 
                backgroundColor: theme.colors.success,
                width: `${compatibilityEngine.analytics?.lifestyle_effectiveness || 0}%`
              }
            ]} />
          </View>
          <Text style={[styles.effectivenessValue, { color: theme.colors.textSecondary }]}>
            {compatibilityEngine.analytics?.lifestyle_effectiveness.toFixed(1) || '0'}%
          </Text>
        </View>
      </View>
    </View>
  );

  const renderTabButton = (
    tab: 'overview' | 'predictive' | 'matching' | 'compatibility',
    icon: React.ReactNode,
    label: string
  ) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tab && { backgroundColor: theme.colors.primary }
      ]}
      onPress={() => setActiveTab(tab)}
    >
      {icon}
      <Text style={[
        styles.tabButtonText,
        { color: activeTab === tab ? theme.colors.white : theme.colors.textSecondary }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  if (!profile) {
    return (
      <View style={[styles.container, styles.center, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>
          Profile not available
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <Brain size={28} color={theme.colors.primary} />
          <View style={styles.headerText}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              AI Analytics
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              Advanced insights & predictions
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={[styles.refreshButton, { backgroundColor: theme.colors.primary }]}
          onPress={loadAnalyticsData}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <ActivityIndicator size="small" color={theme.colors.white} />
          ) : (
            <Zap size={20} color={theme.colors.white} />
          )}
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabNavigation, { backgroundColor: theme.colors.surface }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScrollView}>
          {renderTabButton('overview', <BarChart3 size={20} color={activeTab === 'overview' ? theme.colors.white : theme.colors.textSecondary} />, 'Overview')}
          {renderTabButton('predictive', <Target size={20} color={activeTab === 'predictive' ? theme.colors.white : theme.colors.textSecondary} />, 'Predictive')}
          {renderTabButton('matching', <Users size={20} color={activeTab === 'matching' ? theme.colors.white : theme.colors.textSecondary} />, 'Matching')}
          {renderTabButton('compatibility', <Sparkles size={20} color={activeTab === 'compatibility' ? theme.colors.white : theme.colors.textSecondary} />, 'Compatibility')}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={loadAnalyticsData}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'predictive' && renderPredictiveTab()}
        {activeTab === 'matching' && renderMatchingTab()}
        {activeTab === 'compatibility' && renderCompatibilityTab()}
      </ScrollView>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  refreshButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabNavigation: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tabScrollView: {
    paddingHorizontal: 20,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 20,
  },
  card: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  modelItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modelInfo: {
    flex: 1,
  },
  modelName: {
    fontSize: 16,
    fontWeight: '500',
  },
  modelVersion: {
    fontSize: 12,
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  predictionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  predictionInfo: {
    flex: 1,
  },
  predictionScore: {
    fontSize: 16,
    fontWeight: '500',
  },
  predictionDetails: {
    fontSize: 12,
    marginTop: 2,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  qualityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  qualityLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  qualityCount: {
    fontSize: 14,
  },
  effectivenessItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  effectivenessLabel: {
    fontSize: 14,
    fontWeight: '500',
    width: 80,
  },
  effectivenessBar: {
    flex: 1,
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    marginHorizontal: 12,
  },
  effectivenessFill: {
    height: '100%',
    borderRadius: 4,
  },
  effectivenessValue: {
    fontSize: 12,
    fontWeight: '500',
    width: 40,
    textAlign: 'right',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
}); 