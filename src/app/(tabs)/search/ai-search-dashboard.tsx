import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  Dimensions,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  Search,
  Filter,
  MapPin,
  Heart,
  MessageCircle,
  Star,
  Clock,
  Bookmark,
  Settings,
  ChevronDown,
  ChevronUp,
  X,
  Sliders,
  TrendingUp,
  Users,
  Home,
} from 'lucide-react-native';
import { useTheme, colorWithOpacity, type Theme } from '@design-system';
import {
  useAISearch,
  SearchQuery,
  SearchFilters,
  SearchResult,
  SearchSuggestion,
  SavedSearch,
} from '@hooks/useAISearch';
import { useAuth } from '@context/AuthContext';
import { hapticFeedback } from '@utils/hapticFeedback';
import SmartFilterPanel from '@components/search/SmartFilterPanel';

const { width } = Dimensions.get('window');

export default function AISearchDashboard() {
  const { authState } = useAuth();
  const theme = useTheme();
  const colors = theme.colors;
  const user = authState.user;
  const router = useRouter();
  const styles = createStyles(colors);

  // Helper function to safely get color values
  const getColor = (colorValue: any, fallback: string = '#000000'): string => {
    if (typeof colorValue === 'string') return colorValue;
    if (colorValue && typeof colorValue === 'object') {
      return colorValue[500] || colorValue[400] || colorValue[600] || fallback;
    }
    return fallback;
  };

  const {
    searchHistory,
    savedSearches,
    analytics,
    preferences,
    currentResults,
    isSearching,
    isInitialized,
    initialize,
    performSearch,
    getSearchSuggestions,
    saveSearch,
    deleteSavedSearch,
  } = useAISearch();

  // State
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState<'room' | 'housemate' | 'both'>('both');
  const [showFilters, setShowFilters] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [activeTab, setActiveTab] = useState<'search' | 'saved' | 'history' | 'analytics'>(
    'search'
  );
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');

  const searchInputRef = useRef<TextInput>(null);
  const suggestionsTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize
  useEffect(() => {
    if (user?.id && !isInitialized) {
      initialize(user.id);
    }
  }, [user?.id, isInitialized, initialize]);

  // Handle search text changes with debounced suggestions
  const handleSearchTextChange = useCallback(
    (text: string) => {
      setSearchText(text);

      if (suggestionsTimeoutRef.current) {
        clearTimeout(suggestionsTimeoutRef.current);
      }

      if (text.length > 1) {
        setShowSuggestions(true);
        suggestionsTimeoutRef.current = setTimeout(async () => {
          if (user?.id) {
            try {
              const newSuggestions = await getSearchSuggestions(text, user.id);
              setSuggestions(newSuggestions);
            } catch (error) {
              console.error('Error getting suggestions:', error);
            }
          }
        }, 300);
      } else {
        setShowSuggestions(false);
        setSuggestions([]);
      }
    },
    [user?.id, getSearchSuggestions]
  );

  // Perform search
  const handleSearch = useCallback(async () => {
    if (!user?.id || !searchText.trim()) return;

    hapticFeedback.selection();
    setShowSuggestions(false);

    const query: SearchQuery = {
      text: searchText.trim(),
      type: searchType,
      filters,
      timestamp: new Date(),
    };

    try {
      await performSearch(query, user.id);
    } catch (error) {
      console.error('Error performing search:', error);
      Alert.alert('Search Error', 'Failed to perform search. Please try again.');
    }
  }, [user?.id, searchText, searchType, filters, performSearch]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback(
    (suggestion: SearchSuggestion) => {
      hapticFeedback.selection();
      setSearchText(suggestion.text);
      setShowSuggestions(false);

      // Auto-search if it's a query suggestion
      if (suggestion.type === 'query') {
        setTimeout(() => handleSearch(), 100);
      }
    },
    [handleSearch]
  );

  // Save current search
  const handleSaveSearch = useCallback(async () => {
    if (!user?.id || !searchText.trim() || !saveSearchName.trim()) return;

    const query: SearchQuery = {
      text: searchText.trim(),
      type: searchType,
      filters,
      timestamp: new Date(),
    };

    try {
      await saveSearch(query, saveSearchName.trim(), user.id);
      setSaveSearchName('');
      setShowSaveModal(false);
      Alert.alert('Success', 'Search saved successfully!');
    } catch (error) {
      console.error('Error saving search:', error);
      Alert.alert('Error', 'Failed to save search. Please try again.');
    }
  }, [user?.id, searchText, searchType, filters, saveSearchName, saveSearch]);

  // Load saved search
  const handleLoadSavedSearch = useCallback(
    (savedSearch: SavedSearch) => {
      hapticFeedback.selection();
      setSearchText(savedSearch.query.text);
      setSearchType(savedSearch.query.type);
      setFilters(savedSearch.query.filters);
      setActiveTab('search');

      // Auto-search
      setTimeout(() => handleSearch(), 100);
    },
    [handleSearch]
  );

  // Render search input
  const renderSearchInput = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Search size={20} color={colors.textSecondary} style={styles.searchIcon} />
        <TextInput
          ref={searchInputRef}
          style={styles.searchInput}
          placeholder="Search for rooms or housemates..."
          placeholderTextColor={colors.textSecondary}
          value={searchText}
          onChangeText={handleSearchTextChange}
          onSubmitEditing={handleSearch}
          returnKeyType="search"
        />
        {searchText.length > 0 && (
          <TouchableOpacity
            onPress={() => {
              setSearchText('');
              setShowSuggestions(false);
            }}
            style={styles.clearButton}
          >
            <X size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => {
          hapticFeedback.selection();
          setShowFilters(!showFilters);
        }}
      >
        <Filter
          size={20}
                      color={showFilters ? getColor(theme.colors.primary, '#0ea5e9') : colors.textSecondary}
        />
      </TouchableOpacity>
    </View>
  );

  // Render search type selector
  const renderSearchTypeSelector = () => (
    <View style={styles.typeSelector}>
      {(['both', 'room', 'housemate'] as const).map(type => (
        <TouchableOpacity
          key={type}
          style={[styles.typeButton, searchType === type && styles.typeButtonActive]}
          onPress={() => {
            hapticFeedback.selection();
            setSearchType(type);
          }}
        >
          <Text style={[styles.typeButtonText, searchType === type && styles.typeButtonTextActive]}>
            {type === 'both' ? 'All' : type === 'room' ? 'Rooms' : 'Housemates'}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // Render suggestions
  const renderSuggestions = () => {
    if (!showSuggestions || suggestions.length === 0) return null;

    return (
      <View style={styles.suggestionsContainer}>
        <FlatList
          data={suggestions}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.suggestionItem}
              onPress={() => handleSuggestionSelect(item)}
            >
              <View style={styles.suggestionContent}>
                <Search size={16} color={colors.textSecondary} />
                <Text style={styles.suggestionText}>{item.text}</Text>
              </View>
              <Text style={styles.suggestionCategory}>{item.category}</Text>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        />
      </View>
    );
  };

  // Render search result card
  const renderSearchResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      style={styles.resultCard}
      onPress={() => {
        hapticFeedback.selection();
        // Navigate to result details - using a placeholder for now
        console.log('Navigate to result details:', item.id);
      }}
    >
      <View style={styles.resultHeader}>
        <View style={styles.resultInfo}>
          <Text style={styles.resultName}>{item.profile.name}</Text>
          <View style={styles.resultMeta}>
            <MapPin size={14} color={colors.textSecondary} />
            <Text style={styles.resultLocation}>
              {item.profile.location} • {item.distance.toFixed(1)}km
            </Text>
          </View>
        </View>
        <View style={styles.compatibilityBadge}>
          <Text style={styles.compatibilityScore}>{item.compatibilityScore}%</Text>
        </View>
      </View>

      <Text style={styles.resultBio} numberOfLines={2}>
        {item.profile.bio}
      </Text>

      <View style={styles.resultInsights}>
        <Text style={styles.insightsTitle}>Top Match Reasons:</Text>
        {item.aiInsights.matchReasons.slice(0, 2).map((reason, index) => (
          <Text key={index} style={styles.insightItem}>
            • {reason}
          </Text>
        ))}
      </View>

      <View style={styles.resultActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Heart size={16} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <MessageCircle size={16} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Bookmark size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  // Render saved search item
  const renderSavedSearch = ({ item }: { item: SavedSearch }) => (
    <TouchableOpacity style={styles.savedSearchCard} onPress={() => handleLoadSavedSearch(item)}>
      <View style={styles.savedSearchHeader}>
        <Text style={styles.savedSearchName}>{item.name}</Text>
        <TouchableOpacity
          onPress={() => {
            hapticFeedback.selection();
            Alert.alert('Delete Search', 'Are you sure you want to delete this saved search?', [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: () => user?.id && deleteSavedSearch(item.id, user.id),
              },
            ]);
          }}
        >
          <X size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      <Text style={styles.savedSearchQuery}>{item.query.text}</Text>
      <View style={styles.savedSearchMeta}>
        <Text style={styles.savedSearchDate}>{item.lastUsed.toLocaleDateString()}</Text>
        <Text style={styles.savedSearchResults}>{item.resultCount} results</Text>
      </View>
    </TouchableOpacity>
  );

  // Render analytics
  const renderAnalytics = () => {
    if (!analytics) {
      return (
        <View style={styles.emptyState}>
          <TrendingUp size={48} color={colors.textSecondary} />
          <Text style={styles.emptyStateText}>No analytics data yet</Text>
          <Text style={styles.emptyStateSubtext}>Start searching to see your analytics</Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.analyticsContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>{analytics.totalSearches}</Text>
            <Text style={styles.analyticsLabel}>Total Searches</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>
              {analytics.averageResultsPerSearch.toFixed(1)}
            </Text>
            <Text style={styles.analyticsLabel}>Avg Results</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>{analytics.searchSuccessRate.toFixed(1)}%</Text>
            <Text style={styles.analyticsLabel}>Success Rate</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>
              {analytics.userEngagement.conversationStartRate.toFixed(1)}%
            </Text>
            <Text style={styles.analyticsLabel}>Conversation Rate</Text>
          </View>
        </View>

        <View style={styles.analyticsSection}>
          <Text style={styles.analyticsSectionTitle}>Popular Search Terms</Text>
          <View style={styles.tagContainer}>
            {analytics.popularSearchTerms.slice(0, 10).map((term, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{term}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    );
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'search':
        return (
          <View style={styles.searchContent}>
            {currentResults.length > 0 ? (
              <FlatList
                data={currentResults}
                keyExtractor={item => item.id}
                renderItem={renderSearchResult}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.resultsList}
              />
            ) : (
              <View style={styles.emptyState}>
                <Search size={48} color={colors.textSecondary} />
                <Text style={styles.emptyStateText}>Start your search</Text>
                <Text style={styles.emptyStateSubtext}>
                  Find your perfect roommate or room with AI-powered matching
                </Text>
              </View>
            )}
          </View>
        );

      case 'saved':
        return (
          <View style={styles.savedContent}>
            {savedSearches.length > 0 ? (
              <FlatList
                data={savedSearches}
                keyExtractor={item => item.id}
                renderItem={renderSavedSearch}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.savedList}
              />
            ) : (
              <View style={styles.emptyState}>
                <Bookmark size={48} color={colors.textSecondary} />
                <Text style={styles.emptyStateText}>No saved searches</Text>
                <Text style={styles.emptyStateSubtext}>
                  Save your searches for quick access later
                </Text>
              </View>
            )}
          </View>
        );

      case 'history':
        return (
          <View style={styles.historyContent}>
            {searchHistory.length > 0 ? (
              <FlatList
                data={searchHistory}
                keyExtractor={(item, index) => `${item.timestamp.getTime()}-${index}`}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.historyItem}
                    onPress={() => {
                      setSearchText(item.text);
                      setSearchType(item.type);
                      setFilters(item.filters);
                      setActiveTab('search');
                    }}
                  >
                    <Text style={styles.historyText}>{item.text}</Text>
                    <Text style={styles.historyDate}>{item.timestamp.toLocaleDateString()}</Text>
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.historyList}
              />
            ) : (
              <View style={styles.emptyState}>
                <Clock size={48} color={colors.textSecondary} />
                <Text style={styles.emptyStateText}>No search history</Text>
                <Text style={styles.emptyStateSubtext}>Your recent searches will appear here</Text>
              </View>
            )}
          </View>
        );

      case 'analytics':
        return renderAnalytics();

      default:
        return null;
    }
  };

  // Render tab bar
  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'search', label: 'Search', icon: Search },
        { key: 'saved', label: 'Saved', icon: Bookmark },
        { key: 'history', label: 'History', icon: Clock },
        { key: 'analytics', label: 'Analytics', icon: TrendingUp },
      ].map(({ key, label, icon: Icon }) => (
        <TouchableOpacity
          key={key}
          style={[styles.tabButton, activeTab === key && styles.tabButtonActive]}
          onPress={() => {
            hapticFeedback.selection();
            setActiveTab(key as any);
          }}
        >
          <Icon
            size={20}
            color={activeTab === key ? getColor(theme.colors.primary, '#0ea5e9') : colors.textSecondary}
          />
          <Text style={[styles.tabLabel, activeTab === key && styles.tabLabelActive]}>{label}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>AI Search</Text>
        <View style={styles.headerActions}>
          {searchText.length > 0 && (
            <TouchableOpacity style={styles.saveButton} onPress={() => setShowSaveModal(true)}>
              <Bookmark size={20} color={getColor(colors.primary, '#0ea5e9')} />
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.settingsButton}>
            <Settings size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {renderSearchInput()}
      {renderSearchTypeSelector()}
      {renderSuggestions()}

      {isSearching && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={getColor(colors.primary, '#0ea5e9')} />
          <Text style={styles.loadingText}>Searching with AI...</Text>
        </View>
      )}

      {renderTabBar()}
      {renderTabContent()}

      {/* Save Search Modal */}
      <Modal
        visible={showSaveModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSaveModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save Search</Text>
            <TextInput
              style={styles.modalInput}
              placeholder="Enter search name..."
              value={saveSearchName}
              onChangeText={setSaveSearchName}
              autoFocus
            />
            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.modalButton} onPress={() => setShowSaveModal(false)}>
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={handleSaveSearch}
                disabled={!saveSearchName.trim()}
              >
                <Text style={styles.modalButtonTextPrimary}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.text,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    saveButton: {
      padding: 8,
    },
    settingsButton: {
      padding: 8,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.surface,
      gap: 12,
    },
    searchInputContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.inputBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      height: 48,
    },
    searchIcon: {
      marginRight: 12,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,
      color: colors.text,
    },
    clearButton: {
      padding: 4,
    },
    filterButton: {
      width: 48,
      height: 48,
      borderRadius: 12,
      backgroundColor: colors.inputBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    typeSelector: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      paddingBottom: 16,
      backgroundColor: colors.surface,
      gap: 8,
    },
    typeButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      backgroundColor: colors.inputBackground,
      alignItems: 'center',
    },
    typeButtonActive: {
      backgroundColor: colors.primary,
    },
    typeButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    typeButtonTextActive: {
      color: colors.white,
    },
    suggestionsContainer: {
      backgroundColor: colors.surface,
      marginHorizontal: 20,
      borderRadius: 12,
      maxHeight: 200,
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    suggestionItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    suggestionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      gap: 12,
    },
    suggestionText: {
      fontSize: 16,
      color: colors.text,
      flex: 1,
    },
    suggestionCategory: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    loadingContainer: {
      alignItems: 'center',
      paddingVertical: 32,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: colors.textSecondary,
    },
    tabBar: {
      flexDirection: 'row',
      backgroundColor: colors.surface,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    tabButton: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: 12,
      gap: 4,
    },
    tabButtonActive: {
      borderTopWidth: 2,
      borderTopColor: colors.primary,
    },
    tabLabel: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.textSecondary,
    },
    tabLabelActive: {
      color: colors.primary,
    },
    searchContent: {
      flex: 1,
    },
    resultsList: {
      padding: 20,
      gap: 16,
    },
    resultCard: {
      backgroundColor: colors.white,
      borderRadius: 16,
      padding: 16,
      elevation: 2,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    resultHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    resultInfo: {
      flex: 1,
    },
    resultName: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    resultMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    resultLocation: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    compatibilityBadge: {
      backgroundColor: colors.success?.[100] || '#dcfce7',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 20,
    },
    compatibilityScore: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.success?.[700] || '#15803d',
    },
    resultBio: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    resultInsights: {
      marginBottom: 16,
    },
    insightsTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 6,
    },
    insightItem: {
      fontSize: 13,
      color: colors.textSecondary,
      lineHeight: 18,
    },
    resultActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      gap: 12,
    },
    actionButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: colors.inputBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    savedContent: {
      flex: 1,
    },
    savedList: {
      padding: 20,
      gap: 12,
    },
    savedSearchCard: {
      backgroundColor: colors.white,
      borderRadius: 12,
      padding: 16,
      elevation: 1,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    savedSearchHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    savedSearchName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    savedSearchQuery: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    savedSearchMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    savedSearchDate: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    savedSearchResults: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    historyContent: {
      flex: 1,
    },
    historyList: {
      padding: 20,
      gap: 8,
    },
    historyItem: {
      backgroundColor: colors.white,
      borderRadius: 8,
      padding: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    historyText: {
      fontSize: 14,
      color: colors.text,
      flex: 1,
    },
    historyDate: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    analyticsContainer: {
      flex: 1,
      padding: 20,
    },
    analyticsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 24,
    },
    analyticsCard: {
      backgroundColor: colors.white,
      borderRadius: 12,
      padding: 16,
      width: (width - 52) / 2,
      alignItems: 'center',
      elevation: 1,
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    analyticsValue: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.primary?.[500] || colors.primary || '#0ea5e9',
      marginBottom: 4,
    },
    analyticsLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    analyticsSection: {
      marginBottom: 24,
    },
    analyticsSectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 12,
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    tag: {
      backgroundColor: colors.primary?.[100] || '#dbeafe',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    tagText: {
      fontSize: 12,
      color: colors.primary?.[700] || '#1d4ed8',
      fontWeight: '500',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyStateText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.textSecondary,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateSubtext: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 20,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    modalContent: {
      backgroundColor: colors.white,
      borderRadius: 16,
      padding: 24,
      width: '100%',
      maxWidth: 400,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    modalInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      marginBottom: 24,
    },
    modalActions: {
      flexDirection: 'row',
      gap: 12,
    },
    modalButton: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
    },
    modalButtonPrimary: {
      backgroundColor: colors.primary?.[500] || colors.primary || '#0ea5e9',
      borderColor: colors.primary?.[500] || colors.primary || '#0ea5e9',
    },
    modalButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    modalButtonTextPrimary: {
      color: colors.white,
    },
  });
