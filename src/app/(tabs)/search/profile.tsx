import { useLocalSearchParams, useRouter } from 'expo-router';
import {
  MapPin,
  Star,
  DollarSign,
  Calendar,
  Heart,
  MessageCircle,
  ChevronLeft,
  CircleAlert,
  Loader,
  Clock,
  Moon,
  Volume2,
  Users,
  Coffee,
} from 'lucide-react-native';
import VerifiedBadge from '@components/badges/VerifiedBadge';
import BackgroundCheckBadge from '@components/badges/BackgroundCheckBadge';
import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { supabase } from '@utils/supabaseUtils';
import { Profile as UserProfile } from '@types/models';
import { unifiedProfileService } from '@services/unified-profile';
import { matchingService } from '@services/matchingService';
import { useColorFix } from '@hooks/useColorFix';

export default function ProfileScreen() {
  const { fix } = useColorFix();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const params = useLocalSearchParams();
  // Handle both direct id and query parameter formats
  const profileId =
    typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : null;

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [compatibility, setCompatibility] = useState<{
    score: number;
    factors: { factor: string; score: number; description: string }[];
  } | null>(null);
  const [currentImage, setCurrentImage] = useState(0);
  const [currentUserProfile, setCurrentUserProfile] = useState<UserProfile | null>(null);
  const [mutualInterests, setMutualInterests] = useState<string[]>([]);
  const [lifestyleCompatibility, setLifestyleCompatibility] = useState<{
    score: number;
    matches: {
      category: string;
      icon: any;
      userValue: string;
      otherValue: string;
      isMatch: boolean;
    }[];
  } | null>(null);

  useEffect(() => {
    let isMounted = true;

    async function loadProfileData() {
      try {
        setLoading(true);

        if (!profileId) {
          if (isMounted) {
            setError('Profile ID is missing');
            setLoading(false);
          }
          return;
        }

        // Set a timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
          if (isMounted && loading) {
            setError('Loading timed out. Please try again.');
            setLoading(false);
          }
        }, 10000); // 10 second timeout

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          if (isMounted) {
            setError('Authentication error');
            setLoading(false);
          }
          clearTimeout(timeoutId);
          return;
        }

        // Fetch current user's profile
        try {
          const currentUser = await unifiedProfileService.getUserProfile(user.id);
          if (currentUser && isMounted) {
            setCurrentUserProfile(currentUser);
          }
        } catch (userError) {
          console.error('Error fetching current user profile:', userError);
          // Continue anyway - we can show the profile without compatibility
        }

        // Fetch profile data
        try {
          const profileData = await unifiedProfileService.getUserProfile(profileId);
          if (!profileData) {
            if (isMounted) {
              setError('Profile not found');
              setLoading(false);
            }
            clearTimeout(timeoutId);
            return;
          }

          if (isMounted) {
            setProfile(profileData);
          }
        } catch (profileError) {
          console.error('Error fetching profile data:', profileError);
          if (isMounted) {
            setError('Failed to load profile data');
            setLoading(false);
          }
          clearTimeout(timeoutId);
          return;
        }

        // Fetch compatibility data
        try {
          const compatibilityData = await matchingService.getCompatibilityScore(profileId);
          if (isMounted) {
            setCompatibility(compatibilityData);
          }
        } catch (compatError) {
          console.error('Error fetching compatibility data:', compatError);
          // Continue anyway - we can show profile without compatibility
        }

        // Find mutual interests
        if (isMounted && currentUserProfile && profile) {
          if (currentUserProfile.preferences?.interests && profile.preferences?.interests) {
            const currentUserInterests = currentUserProfile.preferences.interests || [];
            const profileInterests = profile.preferences.interests || [];

            // Find intersection of interests
            const mutual = currentUserInterests.filter(interest =>
              profileInterests.includes(interest)
            );

            setMutualInterests(mutual);
          }

          // Calculate lifestyle compatibility
          if (currentUserProfile?.preferences && profile?.preferences) {
            const lifestyleCategories = [
              {
                key: 'schedule',
                label: 'Daily Schedule',
                icon: Clock,
                userValue: currentUserProfile.preferences.schedule || 'Not specified',
                otherValue: profile.preferences.schedule || 'Not specified',
              },
              {
                key: 'noise_level',
                label: 'Noise Preference',
                icon: Volume2,
                userValue: currentUserProfile.preferences.noise_level || 'Not specified',
                otherValue: profile.preferences.noise_level || 'Not specified',
              },
              {
                key: 'sleep_habits',
                label: 'Sleep Habits',
                icon: Moon,
                userValue: currentUserProfile.preferences.sleep_habits || 'Not specified',
                otherValue: profile.preferences.sleep_habits || 'Not specified',
              },
              {
                key: 'socializing',
                label: 'Socializing',
                icon: Users,
                userValue: currentUserProfile.preferences.socializing || 'Not specified',
                otherValue: profile.preferences.socializing || 'Not specified',
              },
              {
                key: 'cleanliness',
                label: 'Cleanliness',
                icon: Coffee,
                userValue: currentUserProfile.preferences.cleanliness || 'Not specified',
                otherValue: profile.preferences.cleanliness || 'Not specified',
              },
            ];

            // Filter out categories where both values are not specified
            const validCategories = lifestyleCategories.filter(
              category =>
                category.userValue !== 'Not specified' && category.otherValue !== 'Not specified'
            );

            // Calculate matches and score
            const matches = validCategories.map(category => ({
              category: category.label,
              icon: category.icon,
              userValue: category.userValue,
              otherValue: category.otherValue,
              isMatch: category.userValue.toLowerCase() === category.otherValue.toLowerCase(),
            }));

            const matchCount = matches.filter(m => m.isMatch).length;
            const score =
              validCategories.length > 0
                ? Math.round((matchCount / validCategories.length) * 100)
                : 0;

            setLifestyleCompatibility({ score, matches });
          }
        }

        clearTimeout(timeoutId);
        if (isMounted) {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error loading profile:', err);
        if (isMounted) {
          setError('Failed to load profile data');
          setLoading(false);
        }
      }
    }

    if (profileId) {
      loadProfileData();
    }

    return () => {
      isMounted = false;
    };
  }, [profileId]);

  const handleLike = async () => {
    if (!profile) return;
    try {
      await matchingService.likeProfile(profile.id);
      // Show success feedback
    } catch (err) {
      console.error('Error liking profile:', err);
      // Show error feedback
    }
  };

  const handleMessage = () => {
    if (!profile) return;
    
    // Use the same navigation pattern as Room Detail for Agreement integration
    const queryParams = new URLSearchParams();
    queryParams.set('roomId', 'null'); // No room for housemate chats
    queryParams.set('recipientId', profile.id);
    queryParams.set('recipientName', profile.first_name || profile.display_name || profile.username || 'User');
    queryParams.set('source', 'profile_detail');
    router.push(`/chat?${queryParams.toString()}`);
  };

  if (loading) {
    return (
      <View
        style={[
          styles.container,
          { paddingTop: insets.top, justifyContent: 'center', alignItems: 'center' },
        ]}
      >
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={{ marginTop: 16, color: '#64748B' }}>Loading profile...</Text>
      </View>
    );
  }

  if (error || !profile) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color="#1E293B" />
          </TouchableOpacity>
        </View>

        <View style={styles.notFoundContainer}>
                      <CircleAlert size={48} color="#EF4444" />
          <Text style={styles.notFoundTitle}>Profile Not Found</Text>
          <Text style={styles.notFoundText}>
            {error || "The profile you're looking for doesn't exist or has been removed."}
          </Text>
          <TouchableOpacity style={styles.backToSearchButton} onPress={() => router.back()}>
            <Text style={styles.backToSearchText}>Back to Search</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Process profile images
  const profileImages =
    profile.gallery_images && profile.gallery_images.length > 0
      ? profile.gallery_images
      : profile.avatar_url
        ? [profile.avatar_url]
        : ['https://via.placeholder.com/400'];

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return '?';

    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color="#1E293B" />
          </TouchableOpacity>

          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={styles.imageScroller}
            onMomentumScrollEnd={event => {
              const slideWidth = event.nativeEvent.layoutMeasurement.width;
              const currentIndex = Math.floor(event.nativeEvent.contentOffset.x / slideWidth);
              setCurrentImage(currentIndex);
            }}
          >
            {profileImages.map((image, index) => (
              <Image key={index} source={{ uri: image }} style={styles.image} resizeMode="cover" />
            ))}
          </ScrollView>

          {/* Image pagination indicator */}
          {profileImages.length > 1 && (
            <View style={styles.pagination}>
              {profileImages.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.paginationDot,
                    currentImage === index && styles.paginationDotActive,
                  ]}
                />
              ))}
            </View>
          )}
        </View>

        <View style={styles.content}>
          <View style={styles.profileHeader}>
            <View>
              <View style={styles.nameContainer}>
                <Text style={styles.name}>
                  {profile.first_name || profile.username || 'User'},{' '}
                  {calculateAge(profile.date_of_birth)}
                </Text>
                <View style={styles.badgesContainer}>
                  {profile.is_verified && (
                    <VerifiedBadge size="medium" showTooltip style={styles.badge} />
                  )}
                  {profile.background_check_verified && (
                    <BackgroundCheckBadge size="medium" showTooltip style={styles.badge} />
                  )}
                </View>
              </View>
              <Text style={styles.occupation}>{profile.occupation || 'No occupation listed'}</Text>
            </View>

            {compatibility && (
              <View style={styles.matchScore}>
                <Star size={20} color="#6366F1" />
                <Text style={styles.matchText}>{compatibility.score}% Match</Text>
              </View>
            )}
          </View>

          <View style={styles.locationContainer}>
            <MapPin size={16} color="#64748B" />
            <Text style={styles.location}>{profile.location || 'Location not specified'}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About Me</Text>
            <Text style={styles.bio}>{profile.bio || 'No bio provided'}</Text>
          </View>

          {/* Compatibility breakdown section */}
          {compatibility && compatibility.factors && compatibility.factors.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Compatibility Breakdown</Text>
              <View style={styles.compatibilityContainer}>
                {compatibility.factors.map((factor, index) => (
                  <View key={index} style={styles.compatibilityItem}>
                    <View style={styles.compatibilityHeader}>
                      <Text style={styles.compatibilityFactor}>{factor.factor}</Text>
                      <Text style={styles.compatibilityScore}>{factor.score}%</Text>
                    </View>
                    <View style={styles.compatibilityBar}>
                      <View
                        style={[
                          styles.compatibilityFill,
                          {
                            width: `${factor.score}%`,
                            backgroundColor: getScoreColor(factor.score),
                          },
                        ]}
                      />
                    </View>
                    <Text style={styles.compatibilityDescription}>{factor.description}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {profile.preferences && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Preferences</Text>
              <View style={styles.preferencesGrid}>
                {Object.entries(profile.preferences).map(([key, value]) => {
                  // Skip interests as they're displayed separately
                  if (key === 'interests') return null;

                  return (
                    <View key={key} style={styles.preferenceItem}>
                      <Text style={styles.preferenceLabel}>
                        {key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ')}
                      </Text>
                      <Text style={styles.preferenceValue}>
                        {typeof value === 'string' ? value : JSON.stringify(value)}
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          )}

          {profile.preferences?.interests && profile.preferences.interests.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeaderRow}>
                <Text style={styles.sectionTitle}>Interests</Text>
                {mutualInterests.length > 0 && (
                  <View style={styles.mutualInterestsBadge}>
                    <Text style={styles.mutualInterestsText}>{mutualInterests.length} Mutual</Text>
                  </View>
                )}
              </View>

              <View style={styles.interestsContainer}>
                {profile.preferences.interests.map((interest: string) => {
                  const isMutual = mutualInterests.includes(interest);
                  return (
                    <View
                      key={interest}
                      style={[styles.interestChip, isMutual && styles.mutualInterestChip]}
                    >
                      <Text style={[styles.interestText, isMutual && styles.mutualInterestText]}>
                        {interest}
                      </Text>
                    </View>
                  );
                })}
              </View>

              {mutualInterests.length > 0 && (
                <Text style={styles.mutualInterestsDescription}>
                  You and {profile.first_name || 'this person'} both enjoy{' '}
                  {mutualInterests.slice(0, 3).join(', ')}
                  {mutualInterests.length > 3
                    ? ` and ${mutualInterests.length - 3} more activities`
                    : ''}
                </Text>
              )}
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Move-in Details</Text>
            <View style={styles.detailsContainer}>
              <View style={styles.detailItem}>
                <DollarSign size={20} color="#6366F1" />
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Budget Range</Text>
                  <Text style={styles.detailValue}>
                    {profile.preferences?.budget_min && profile.preferences?.budget_max
                      ? `$${profile.preferences.budget_min}-${profile.preferences.budget_max}`
                      : 'Not specified'}
                  </Text>
                </View>
              </View>
              <View style={styles.detailItem}>
                <Calendar size={20} color="#6366F1" />
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Move-in Date</Text>
                  <Text style={styles.detailValue}>
                    {profile.preferences?.move_in_date
                      ? new Date(profile.preferences.move_in_date).toLocaleDateString()
                      : 'Flexible'}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Add the schedule/lifestyle compatibility section */}
          {lifestyleCompatibility && lifestyleCompatibility.matches.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeaderRow}>
                <Text style={styles.sectionTitle}>Lifestyle Compatibility</Text>
                <View
                  style={[
                    styles.lifestyleScoreBadge,
                    { backgroundColor: getLifestyleScoreColor(lifestyleCompatibility.score) },
                  ]}
                >
                  <Text style={styles.lifestyleScoreText}>{lifestyleCompatibility.score}%</Text>
                </View>
              </View>

              <View style={styles.lifestyleContainer}>
                {lifestyleCompatibility.matches.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <View key={index} style={styles.lifestyleItem}>
                      <View style={styles.lifestyleIconContainer}>
                        <Icon size={20} color="#6366F1" />
                      </View>
                      <View style={styles.lifestyleContent}>
                        <Text style={styles.lifestyleCategory}>{item.category}</Text>
                        <View style={styles.lifestyleValues}>
                          <Text style={styles.lifestyleValue}>You: {item.userValue}</Text>
                          <Text style={styles.lifestyleValue}>
                            Them: {item.otherValue}
                            {item.isMatch && <Text style={styles.lifestyleMatch}> • Match!</Text>}
                          </Text>
                        </View>
                      </View>
                    </View>
                  );
                })}
              </View>

              <Text style={styles.lifestyleDescription}>
                {getLifestyleDescription(lifestyleCompatibility)}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity style={styles.messageButton} onPress={handleMessage}>
          <MessageCircle size={20} color="#FFFFFF" />
          <Text style={styles.messageButtonText}>Message</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.likeButton} onPress={handleLike}>
          <Heart size={20} color="#6366F1" />
          <Text style={styles.likeButtonText}>Like Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Helper function to get color based on compatibility score
function getScoreColor(score: number): string {
  if (score >= 80) return '#10b981'; // Green for high compatibility
  if (score >= 60) return '#3b82f6'; // Blue for good compatibility
  if (score >= 40) return '#f59e0b'; // Yellow for medium compatibility
  return '#ef4444'; // Red for low compatibility
}

// Helper function to get lifestyle score color
function getLifestyleScoreColor(score: number): string {
  if (score >= 80) return '#dcfce7'; // Light green for high compatibility
  if (score >= 60) return '#dbeafe'; // Light blue for good compatibility
  if (score >= 40) return '#fef3c7'; // Light yellow for medium compatibility
  return '#fee2e2'; // Light red for low compatibility
}

// Helper function to generate lifestyle compatibility description
function getLifestyleDescription(compatibility: {
  score: number;
  matches: { category: string; isMatch: boolean }[];
}): string {
  const { score, matches } = compatibility;
  const matchCount = matches.filter(m => m.isMatch).length;

  if (matchCount === 0) {
    return 'You have different lifestyle preferences, which might require some adjustments and compromise.';
  } else if (matchCount === matches.length) {
    return 'Perfect lifestyle match! Your daily routines and preferences align very well.';
  } else if (score >= 60) {
    const matchCategories = matches
      .filter(m => m.isMatch)
      .map(m => m.category.toLowerCase())
      .slice(0, 2);
    return `Good lifestyle compatibility, especially regarding ${matchCategories.join(' and ')}.`;
  } else {
    const matchCategories = matches
      .filter(m => m.isMatch)
      .map(m => m.category.toLowerCase())
      .slice(0, 1);
    return `You have some lifestyle differences, but you share similar ${matchCategories[0] || 'preferences'}.`;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 1,
    width: 40,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notFoundContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  notFoundTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1E293B',
    marginTop: 16,
    marginBottom: 8,
  },
  notFoundText: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    marginBottom: 24,
  },
  backToSearchButton: {
    backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  backToSearchText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  imageScroller: {
    height: 400,
  },
  image: {
    width: 400,
    height: 400,
    backgroundColor: '#F1F5F9',
  },
  pagination: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    margin: 4,
  },
  paginationDotActive: {
    backgroundColor: '#FFFFFF',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  content: {
    padding: 24,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1E293B',
    marginRight: 4,
  },
  badgesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  badge: {
    marginLeft: 4,
  },
  occupation: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 4,
  },
  matchScore: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  matchText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  location: {
    marginLeft: 4,
    fontSize: 14,
    color: '#64748B',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12,
  },
  bio: {
    fontSize: 16,
    color: '#475569',
    lineHeight: 24,
  },
  compatibilityContainer: {
    gap: 16,
  },
  compatibilityItem: {
    marginBottom: 12,
  },
  compatibilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  compatibilityFactor: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E293B',
  },
  compatibilityScore: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
  },
  compatibilityBar: {
    height: 8,
    backgroundColor: '#E2E8F0',
    borderRadius: 4,
    marginBottom: 4,
  },
  compatibilityFill: {
    height: 8,
    borderRadius: 4,
  },
  compatibilityDescription: {
    fontSize: 12,
    color: '#64748B',
  },
  preferencesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  preferenceItem: {
    width: '45%',
  },
  preferenceLabel: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 4,
  },
  preferenceValue: {
    fontSize: 16,
    color: '#1E293B',
    fontWeight: '500',
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  interestChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F1F5F9',
    borderRadius: 16,
  },
  interestText: {
    fontSize: 14,
    color: '#64748B',
  },
  detailsContainer: {
    gap: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailContent: {
    marginLeft: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#64748B',
  },
  detailValue: {
    fontSize: 16,
    color: '#1E293B',
    fontWeight: '500',
    marginTop: 2,
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  messageButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366F1',
    borderRadius: 24,
    paddingVertical: 12,
  },
  messageButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  likeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#6366F1',
    borderRadius: 24,
    paddingVertical: 12,
  },
  likeButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  mutualInterestsBadge: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  mutualInterestsText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
  },
  mutualInterestChip: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  mutualInterestText: {
    color: '#6366F1',
    fontWeight: '600',
  },
  mutualInterestsDescription: {
    marginTop: 12,
    fontSize: 14,
    color: '#64748B',
    fontStyle: 'italic',
  },
  lifestyleScoreBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  lifestyleScoreText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
  },
  lifestyleContainer: {
    gap: 16,
  },
  lifestyleItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  lifestyleIconContainer: {
    width: 40,
    height: 40,
    backgroundColor: '#EEF2FF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  lifestyleContent: {
    flex: 1,
  },
  lifestyleCategory: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 4,
  },
  lifestyleValues: {
    gap: 2,
  },
  lifestyleValue: {
    fontSize: 14,
    color: '#64748B',
  },
  lifestyleMatch: {
    color: '#10b981',
    fontWeight: '500',
  },
  lifestyleDescription: {
    marginTop: 16,
    fontSize: 14,
    color: '#1E293B',
    fontStyle: 'italic',
    lineHeight: 20,
  },
});
