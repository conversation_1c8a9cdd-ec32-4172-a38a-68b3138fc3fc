import { useState, useEffect } from 'react';

import { useLocalSearchParams, useRouter } from 'expo-router';
import {
  ChevronLeft,
  MessageCircle,
  MapPin,
  Calendar,
  DollarSign,
  Wifi,
  Tv,
  Car,
  Bath,
  Coffee,
  AlertCircle,
  Edit2,
} from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Button } from '@design-system';
import { SaveRoomButton } from '@components/common/SaveRoomButton';
import { SavedRoomNotes } from '@components/common/SavedRoomNotes';
import { SaveToMemoryBank, SavedRoomMemoryIntegration } from '@components/common/MemoryBank';
import Section from '@components/layout/Section';
import { LocationDetails, MapViewComponent } from '@components/location';
import { PropertyDetails } from '@components/room/PropertyDetails';
import { useSavedRooms } from '@hooks/useSavedRooms';
import { useAuth } from '@context/AuthContext';
import type { LocationData } from '@services/LocationService';
import { locationService } from '@services/LocationService';
// MIGRATION: Replace legacy roomService with unifiedRoomService
import { unifiedRoomService, type RoomWithDetails } from '@services/enhanced/UnifiedRoomService';
import { colorWithOpacity, type Theme } from '@design-system';
import { useMinimalTheme as useTheme } from '@design-system/MinimalTheme';

const AMENITIES = [
  { icon: Wifi, label: 'Free WiFi' },
  { icon: Tv, label: 'Smart TV' },
  { icon: Car, label: 'Parking' },
  { icon: Bath, label: 'Private Bath' },
  { icon: Coffee, label: 'Kitchen' },
];

const { width } = Dimensions.get('window');

// Safe color utility to prevent [object Object] issues
const safeColor = (color: any): string => {
  if (typeof color === 'string') return color;
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected, converting to fallback:', color);
    return '#000000'; // Fallback to black
  }
  return String(color);
};

// Safe colorWithOpacity wrapper
const safeColorWithOpacity = (color: any, opacity: number): string => {
  const safeColorValue = safeColor(color);
  try {
    return colorWithOpacity(safeColorValue, opacity);
  } catch (error) {
    console.warn('colorWithOpacity failed, using fallback:', error);
    return `rgba(0, 0, 0, ${opacity})`; // Fallback
  }
};

export default function RoomDetailsScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { id } = useLocalSearchParams();
  const roomId = Array.isArray(id) ? id[0] : id;
  const { savedRooms, isRoomSaved } = useSavedRooms();
  const { authState, authLoaded } = useAuth();
  const currentUserId = authState.user?.id;

  const [room, setRoom] = useState<RoomWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [locationData, setLocationData] = useState<LocationData | null>(null);
  const [userCoordinates, setUserCoordinates] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Get saved room data if this room is saved
  const savedRoomData = roomId ? savedRooms.find(r => r.id === roomId) : null;
  const isSaved = roomId ? isRoomSaved(roomId) : false;

  // Check if current user is the owner of the room
  const isOwner = room?.owner_id === currentUserId;

  useEffect(() => {
    async function loadRoom() {
      if (!roomId) {
        return;
      }

      try {
        setLoading(true);
        const result = await unifiedRoomService.getRoomById(roomId, currentUserId);
        const { data, error: apiError } = { data: result.data, error: result.error };

        if (apiError) {
          throw new Error(apiError);
        }

        setRoom(data);

        // Load location data if location_id exists
        if (data.location_id) {
          try {
            const locationData = await locationService.getLocationById(data.location_id);
            setLocationData(locationData);
          } catch (locationError) {
            console.warn('Failed to load location data:', locationError);
            // Continue without location data
          }
        }

        // Get user's current location for distance calculation
        try {
          const userLocation = await locationService.getCurrentLocation();
          if (userLocation) {
            setUserCoordinates(userLocation);
          }
        } catch (locationError) {
          console.warn('Failed to get user location:', locationError);
          // Continue without user location
        }
      } catch (err) {
        setError('Failed to load room details');
        console.error('Error loading room details:', err);
      } finally {
        setLoading(false);
      }
    }

    loadRoom();
  }, [roomId]);

  if (loading) {
    return (
      <View style={[styles.container, styles.centeredContainer, { paddingTop: insets.top, backgroundColor: safeColor(theme.colors.background) }]}>
        <ActivityIndicator size="large" color={safeColor(theme.colors.primary)} />
        <Text style={[styles.loadingText, { color: safeColor(theme.colors.textSecondary) }]}>Loading room details...</Text>
      </View>
    );
  }

  if (error || !room) {
    return (
      <View style={[styles.container, styles.centeredContainer, { paddingTop: insets.top, backgroundColor: safeColor(theme.colors.background) }]}>
        <AlertCircle size={48} color={safeColor(theme.colors.error)} />
        <Text style={[styles.errorText, { color: safeColor(theme.colors.error) }]}>{error || 'Room not found'}</Text>
        <Button onPress={() => router.back()} style={{ marginTop: 20 }}>
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: safeColor(theme.colors.background) }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          style={styles.gallery}
        >
          {(room.images || []).map((image, index) => (
            <Image key={index} source={{ uri: image }} style={[styles.image, { width }]} />
          ))}
          {(!room.images || room.images.length === 0) && (
            <View style={[styles.noImageContainer, { width }]}>
              <Text style={styles.noImageText}>No images available</Text>
            </View>
          )}
        </ScrollView>

        {/* Header Buttons */}
        <View style={[styles.header, { paddingTop: insets.top + 16 }]}>
          <TouchableOpacity style={[styles.headerButton, { backgroundColor: safeColor(theme.colors.surface) }]} onPress={() => router.back()}>
            <ChevronLeft size={24} color={safeColor(theme.colors.text)} />
          </TouchableOpacity>
          {roomId && (
            <SaveRoomButton
              roomId={roomId}
              style={styles.headerButton}
              showBackground={true}
              size={24}
            />
          )}
        </View>

        {/* Content */}
        <View style={styles.content}>
          <Section>
            <View style={styles.titleContainer}>
              <Text style={[styles.title, { color: safeColor(theme.colors.text) }]}>{room.title}</Text>
              {isOwner && (
                <TouchableOpacity
                  style={[styles.editButton, { backgroundColor: safeColor(theme.colors.primary) }]}
                  onPress={() => router.push(`/rooms/${roomId}/edit`)}
                >
                  <Edit2 size={16} color={safeColor(theme.colors.background)} />
                  <Text style={[styles.editButtonText, { color: safeColor(theme.colors.background) }]}>Edit</Text>
                </TouchableOpacity>
              )}
            </View>
            {!locationData && (
                          <View style={styles.locationContainer}>
              <MapPin size={16} color={safeColor(theme.colors.textSecondary)} />
              <Text style={styles.location}>{room.location}</Text>
            </View>
            )}

            <View style={styles.detailsGrid}>
              <View style={[styles.detailItem, { backgroundColor: safeColor(theme.colors.surface) }]}>
                <DollarSign size={20} color={safeColor(theme.colors.primary)} />
                <View>
                  <Text style={[styles.detailLabel, { color: safeColor(theme.colors.textSecondary) }]}>Monthly Rent</Text>
                  <Text style={[styles.detailValue, { color: safeColor(theme.colors.text) }]}>${room.price}</Text>
                </View>
              </View>
              {room.move_in_date && (
                <View style={[styles.detailItem, { backgroundColor: safeColor(theme.colors.surface) }]}>
                  <Calendar size={20} color={safeColor(theme.colors.primary)} />
                  <View>
                    <Text style={[styles.detailLabel, { color: safeColor(theme.colors.textSecondary) }]}>Move-in Date</Text>
                    <Text style={[styles.detailValue, { color: safeColor(theme.colors.text) }]}>
                      {new Date(room.move_in_date).toLocaleDateString()}
                    </Text>
                  </View>
                </View>
              )}
            </View>

            {/* Show notes component if room is saved */}
            {isSaved && savedRoomData && savedRoomData.saved_id && (
              <>
                <SavedRoomNotes
                  savedId={savedRoomData.saved_id}
                  notes={savedRoomData.notes}
                  roomId={roomId}
                />
                {/* Memory Bank integration for saved rooms */}
                <SavedRoomMemoryIntegration
                  roomId={roomId}
                  savedId={savedRoomData.saved_id}
                  roomName={room.title}
                  notes={savedRoomData.notes}
                />
              </>
            )}

            {/* Add Memory Bank button for all rooms */}
            {roomId && !isOwner && (
              <SaveToMemoryBank
                roomId={roomId}
                roomName={room.title}
                roomDescription={room.description || ''}
                roomPrice={`$${room.price}`}
                roomLocation={locationData?.name || room.location || ''}
              />
            )}
          </Section>

          {/* Location Details Section */}
          {locationData && (
            <Section title="Location">
              <LocationDetails
                location={locationData}
                distance={
                  userCoordinates && locationData.latitude && locationData.longitude
                    ? locationService.calculateDistance(userCoordinates, {
                        latitude: locationData.latitude,
                        longitude: locationData.longitude,
                      })
                    : undefined
                }
              />

              {/* Only render map if we have valid coordinates */}
              {locationData.latitude &&
                locationData.longitude &&
                !isNaN(locationData.latitude) &&
                !isNaN(locationData.longitude) && (
                  <View style={styles.mapContainer}>
                    <MapViewComponent
                      style={styles.map}
                      initialRegion={{
                        latitude: locationData.latitude,
                        longitude: locationData.longitude,
                        latitudeDelta: 0.0922,
                        longitudeDelta: 0.0421,
                      }}
                      markers={[
                        {
                          id: locationData.id,
                          coordinate: {
                            latitude: locationData.latitude,
                            longitude: locationData.longitude,
                          },
                          title: locationData.name,
                          description: locationData.description,
                        },
                      ]}
                      showUserLocation={true}
                    />
                  </View>
                )}
            </Section>
          )}

          {room.description && (
            <Section title="Description">
              <Text style={[styles.description, { color: safeColor(theme.colors.textSecondary) }]}>{room.description}</Text>
            </Section>
          )}

          {/* Property Details Component */}
          {roomId && <PropertyDetails roomId={roomId} />}

          <Section title="Amenities">
            <View style={styles.amenitiesGrid}>
              {AMENITIES.map((amenity, index) => {
                const Icon = amenity.icon;
                return (
                  <View key={index} style={styles.amenityItem}>
                    <View style={[styles.amenityIcon, { backgroundColor: safeColorWithOpacity(safeColor(theme.colors.primary), 0.1) }]}>
                      <Icon size={20} color={safeColor(theme.colors.primary)} />
                    </View>
                    <Text style={[styles.amenityLabel, { color: safeColor(theme.colors.text) }]}>{amenity.label}</Text>
                  </View>
                );
              })}
            </View>
          </Section>

          {room.landlord && (
            <Section title="About the Owner">
              <View style={[styles.ownerContainer, { backgroundColor: safeColor(theme.colors.surface) }]}>
                <Image
                  source={{
                    uri: room.landlord.avatar_url || 'https://via.placeholder.com/64?text=User',
                  }}
                  style={[styles.ownerImage, { backgroundColor: safeColor(theme.colors.surfaceVariant) }]}
                />
                <View style={styles.ownerInfo}>
                  <Text style={[styles.ownerName, { color: safeColor(theme.colors.text) }]}>
                    {room.landlord.display_name || room.landlord.username || 'Landlord'}
                  </Text>
                  <Text style={[styles.ownerStats, { color: safeColor(theme.colors.textSecondary) }]}>Response rate: 95%</Text>
                  <Text style={[styles.ownerStats, { color: safeColor(theme.colors.textSecondary) }]}>Response time: {'< 1 hour'}</Text>
                </View>
              </View>
            </Section>
          )}
        </View>
      </ScrollView>

      {/* Enhanced Footer Actions */}
      <View style={[styles.footer, { backgroundColor: safeColor(theme.colors.surface), borderTopColor: safeColor(theme.colors.border) }]}>
        <View style={[styles.enhancedPriceContainer, { backgroundColor: safeColorWithOpacity(safeColor(theme.colors.primary), 0.05) }]}>
          <View style={styles.priceRow}>
            <Text style={[styles.enhancedPrice, { color: safeColor(theme.colors.text) }]}>${room.price}</Text>
            <Text style={[styles.enhancedPriceUnit, { color: safeColor(theme.colors.textSecondary) }]}>/month</Text>
          </View>
          <Text style={[styles.priceLabel, { color: safeColor(theme.colors.textSecondary) }]}>Monthly Rent</Text>
        </View>
        <View style={styles.enhancedActions}>
          {/* Enhanced Message Button */}
          <TouchableOpacity
            style={[styles.enhancedMessageButton, { 
              backgroundColor: safeColorWithOpacity(safeColor(theme.colors.primary), 0.08),
              borderColor: safeColor(theme.colors.primary) 
            }]}
            onPress={async () => {
              if (!currentUserId) {
                router.push('/login?redirectTo=%2Frooms');
                return;
              }

              if (room.landlord?.id) {
                try {
                  // Import RoomChatService dynamically to avoid circular dependencies
                  const { default: RoomChatService } = await import('@services/rooms/RoomChatService');
                  
                  // Create room chat context
                  const roomContext = {
                    roomId: roomId,
                    roomTitle: room.title,
                    roomPrice: room.price,
                    roomLocation: room.location,
                    ownerId: room.landlord.id,
                    ownerName: room.landlord.display_name || room.landlord.username || 'Room Owner',
                    roomImages: room.images,
                    roomType: room.room_type
                  };

                  // Create room chat with enhanced context
                  const result = await RoomChatService.createRoomChat(
                    currentUserId,
                    roomContext,
                    {
                      includeRoomDetails: true,
                      autoNavigateToChat: true,
                      initialMessage: `Hi! I'm interested in your room "${room.title}" in ${room.location}. Could we discuss the details?`
                    }
                  );

                  if (!result.success) {
                    // Fallback to basic navigation if service fails
                    const queryParams = new URLSearchParams();
                    queryParams.set('roomId', roomId);
                    queryParams.set('recipientId', room.landlord.id);
                    queryParams.set('recipientName', room.landlord.display_name || room.landlord.username || 'Owner');
                    queryParams.set('source', 'room_detail');
                    
                    router.push(`/chat?${queryParams.toString()}`);
                  }
                } catch (error) {
                  console.error('Error creating room chat:', error);
                  
                  // Fallback to basic navigation
                  const queryParams = new URLSearchParams();
                  queryParams.set('roomId', roomId);
                  queryParams.set('recipientId', room.landlord.id);
                  queryParams.set('recipientName', room.landlord.display_name || room.landlord.username || 'Owner');
                  queryParams.set('source', 'room_detail');
                  
                  router.push(`/chat?${queryParams.toString()}`);
                }
              } else {
                // Navigate to general contact or messages page if no specific landlord
                router.push('/(tabs)/messages');
              }
            }}
            activeOpacity={0.7}
            accessibilityLabel="Send message to property owner"
            accessibilityHint="Opens chat with the room owner"
          >
            <View style={styles.buttonContent}>
              <MessageCircle size={20} color={safeColor(theme.colors.primary)} />
              <Text style={[styles.enhancedMessageButtonText, { color: safeColor(theme.colors.primary) }]}>
                Message
              </Text>
            </View>
          </TouchableOpacity>

          {/* Enhanced Schedule Button */}
          <TouchableOpacity
            style={[styles.enhancedScheduleButton, { backgroundColor: safeColor(theme.colors.primary) }]}
            onPress={() => {
              // Navigate to schedule tour screen with room details using query string format
              const queryParams = new URLSearchParams();
              queryParams.set('roomId', roomId);
              queryParams.set('roomTitle', room.title);
              queryParams.set('roomPrice', room.price.toString());
              queryParams.set('ownerId', room.landlord?.id || '');
              queryParams.set('ownerName', room.landlord?.display_name || room.landlord?.username || 'Owner');
              
              router.push(`/schedule-tour?${queryParams.toString()}`);
            }}
            activeOpacity={0.8}
            accessibilityLabel="Schedule a visit to this room"
            accessibilityHint="Opens scheduling interface for room viewing"
          >
            <View style={styles.buttonContent}>
              <Calendar size={20} color={safeColor(theme.colors.background)} />
              <Text style={[styles.enhancedScheduleButtonText, { color: safeColor(theme.colors.background) }]}>
                Schedule Visit
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const createStyles = (theme: any) => {
  // Ensure theme colors are safe strings
  const safeTheme = {
    colors: {
      background: safeColor(theme.colors?.background),
      surface: safeColor(theme.colors?.surface),
      surfaceVariant: safeColor(theme.colors?.surfaceVariant),
      text: safeColor(theme.colors?.text),
      textSecondary: safeColor(theme.colors?.textSecondary),
      primary: safeColor(theme.colors?.primary),
      border: safeColor(theme.colors?.border),
      error: safeColor(theme.colors?.error),
    }
  };

  return StyleSheet.create({
    container: {
      flex: 1,
    },
    centeredContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      textAlign: 'center',
    },
    errorText: {
      marginTop: 16,
      fontSize: 16,
      textAlign: 'center',
    },
    gallery: {
      height: 300,
    },
    image: {
      height: 300,
      backgroundColor: safeTheme.colors.surfaceVariant,
    },
    noImageContainer: {
      height: 300,
      backgroundColor: safeTheme.colors.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noImageText: {
      fontSize: 16,
      color: safeTheme.colors.textSecondary,
    },
    header: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: 24,
      zIndex: 10,
    },
    headerButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: safeTheme.colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: safeTheme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    content: {
      flex: 1,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: safeTheme.colors.text,
      marginBottom: 8,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    location: {
      marginLeft: 4,
      fontSize: 16,
      color: safeTheme.colors.textSecondary,
    },
    detailsGrid: {
      flexDirection: 'row',
      gap: 16,
      marginTop: 8,
    },
    detailItem: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: safeTheme.colors.surface,
      padding: 16,
      borderRadius: 12,
      gap: 12,
      shadowColor: safeTheme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    },
    detailLabel: {
      fontSize: 14,
      color: safeTheme.colors.textSecondary,
    },
    detailValue: {
      fontSize: 16,
      fontWeight: '600',
      color: safeTheme.colors.text,
    },
    description: {
      fontSize: 16,
      color: safeTheme.colors.textSecondary,
      lineHeight: 24,
    },
    amenitiesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
    },
    amenityItem: {
      width: '45%',
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    amenityIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: safeColorWithOpacity(safeTheme.colors.primary, 0.1),
      justifyContent: 'center',
      alignItems: 'center',
    },
    amenityLabel: {
      fontSize: 16,
      color: safeTheme.colors.text,
    },
    ownerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: safeTheme.colors.surface,
      padding: 16,
      borderRadius: 12,
      shadowColor: safeTheme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    },
    ownerImage: {
      width: 64,
      height: 64,
      borderRadius: 32,
      marginRight: 16,
      backgroundColor: safeTheme.colors.surfaceVariant,
    },
    ownerInfo: {
      flex: 1,
    },
    ownerName: {
      fontSize: 18,
      fontWeight: '600',
      color: safeTheme.colors.text,
      marginBottom: 4,
    },
    ownerStats: {
      fontSize: 14,
      color: safeTheme.colors.textSecondary,
    },
    footer: {
      backgroundColor: safeTheme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: safeTheme.colors.border,
      padding: 20,
      paddingBottom: 24, // Extra padding for better visual spacing
      shadowColor: safeTheme.colors.text,
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.05,
      shadowRadius: 8,
      elevation: 8,
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'baseline',
      marginBottom: 16,
    },
    price: {
      fontSize: 24,
      fontWeight: '700',
      color: safeTheme.colors.text,
    },
    priceUnit: {
      marginLeft: 4,
      fontSize: 16,
      color: safeTheme.colors.textSecondary,
    },
    // ✅ Enhanced Price Container
    enhancedPriceContainer: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: safeColorWithOpacity(safeTheme.colors.primary, 0.1),
    },
    priceRow: {
      flexDirection: 'row',
      alignItems: 'baseline',
      marginBottom: 4,
    },
    enhancedPrice: {
      fontSize: 28,
      fontWeight: '800',
      letterSpacing: -0.5,
    },
    enhancedPriceUnit: {
      marginLeft: 6,
      fontSize: 18,
      fontWeight: '500',
    },
    priceLabel: {
      fontSize: 14,
      fontWeight: '500',
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    actions: {
      flexDirection: 'row',
      gap: 12,
    },
    messageButton: {
      flex: 1,
      backgroundColor: safeColorWithOpacity(safeTheme.colors.primary, 0.1),
    },
    scheduleButton: {
      flex: 2,
    },
    // ✅ Enhanced Button Styles
    enhancedActions: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 4,
    },
    enhancedMessageButton: {
      flex: 1,
      borderWidth: Platform.OS === 'android' ? 0.5 : 1.5, // Much thinner border on Android
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 16,
      shadowColor: safeTheme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.08,
      shadowRadius: 6,
      elevation: 3,
      // Ensure proper background for touch response
      backgroundColor: safeColorWithOpacity(safeTheme.colors.primary, 0.05),
    },
    enhancedScheduleButton: {
      flex: 2,
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 20,
      shadowColor: safeTheme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 12,
      elevation: 8,
    },
    buttonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
    },
    enhancedMessageButtonText: {
      fontSize: 16,
      fontWeight: '600',
      textAlign: 'center',
    },
    enhancedScheduleButtonText: {
      fontSize: 16,
      fontWeight: '700',
      textAlign: 'center',
    },
    mapContainer: {
      marginTop: 16,
    },
    map: {
      height: 220,
      borderRadius: 12,
      overflow: 'hidden',
    },
    editButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      padding: 8,
      backgroundColor: safeTheme.colors.primary,
      borderRadius: 8,
    },
    editButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: safeTheme.colors.background,
    },
  });
};
