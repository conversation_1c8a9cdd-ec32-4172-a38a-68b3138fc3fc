import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
  Switch,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system/ThemeProvider';
import { adminService } from '@services/adminService';
import { 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  Download, 
  Calendar,
  Filter,
  FileText,
  Users,
  DollarSign,
  Home,
  Shield,
  Clock,
  Target,
  Settings,
  RefreshCw,
  Plus,
  Eye,
  Share,
  Printer,
  Mail,
  ChevronDown,
  ChevronRight,
  X,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react-native';

// Types for reporting and analytics
interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'users' | 'financial' | 'listings' | 'matching' | 'safety' | 'compliance';
  type: 'standard' | 'custom';
  parameters: ReportParameter[];
  schedule?: ReportSchedule;
  lastGenerated?: string;
  isActive: boolean;
}

interface ReportParameter {
  id: string;
  name: string;
  type: 'date_range' | 'select' | 'multi_select' | 'number' | 'text';
  required: boolean;
  options?: string[];
  defaultValue?: any;
}

interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string;
  recipients: string[];
  isActive: boolean;
}

interface GeneratedReport {
  id: string;
  templateId: string;
  templateName: string;
  generatedAt: string;
  generatedBy: string;
  parameters: Record<string, any>;
  status: 'generating' | 'completed' | 'failed';
  fileUrl?: string;
  fileSize?: number;
  format: 'pdf' | 'csv' | 'excel';
}

interface AnalyticsMetric {
  id: string;
  name: string;
  value: number;
  previousValue?: number;
  change?: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  format: 'number' | 'currency' | 'percentage';
  category: string;
}

interface ChartData {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'area';
  data: any[];
  xAxis?: string;
  yAxis?: string;
  colors?: string[];
}

export default function AdvancedReportsScreen() {
  const { colors, spacing } = useTheme();
  const styles = createStyles(colors, spacing);
  const router = useRouter();

  // State management
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'templates' | 'generated' | 'builder'>('overview');
  
  // Data states
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([]);
  const [analyticsMetrics, setAnalyticsMetrics] = useState<AnalyticsMetric[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);

  // Modal states
  const [reportBuilderVisible, setReportBuilderVisible] = useState(false);
  const [reportPreviewVisible, setReportPreviewVisible] = useState(false);
  const [scheduleModalVisible, setScheduleModalVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState<ReportTemplate | null>(null);

  // Form states
  const [reportBuilder, setReportBuilder] = useState({
    name: '',
    description: '',
    category: 'users' as const,
    dateRange: { start: '', end: '' },
    filters: {},
    metrics: [],
    groupBy: '',
    format: 'pdf' as const,
  });

  // Load reports data
  const loadReportsData = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Load report templates
      const templatesResponse = await adminService.getReportTemplates();
      if (templatesResponse.data) {
        setReportTemplates(templatesResponse.data);
      }

      // Load generated reports
      const reportsResponse = await adminService.getGeneratedReports();
      if (reportsResponse.data) {
        setGeneratedReports(reportsResponse.data);
      }

      // Load analytics metrics
      const metricsResponse = await adminService.getAnalyticsMetrics();
      if (metricsResponse.data) {
        setAnalyticsMetrics(metricsResponse.data);
      }

      // Load chart data
      const chartsResponse = await adminService.getReportChartData();
      if (chartsResponse.data) {
        setChartData(chartsResponse.data);
      }
    } catch (error) {
      console.error('Error loading reports data:', error);
      Alert.alert('Error', 'Failed to load reports data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadReportsData();
  }, [loadReportsData]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadReportsData(true);
  }, [loadReportsData]);

  // Handle report generation
  const handleGenerateReport = useCallback(async (templateId: string, parameters: Record<string, any>) => {
    try {
      Alert.alert(
        'Generate Report',
        'This will generate a new report with the selected parameters. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Generate',
            onPress: async () => {
              const response = await adminService.generateReport(templateId, parameters);
              if (response.data?.success) {
                Alert.alert('Success', 'Report generation started. You will be notified when it\'s ready.');
                loadReportsData(true);
              } else {
                Alert.alert('Error', 'Failed to start report generation.');
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to generate report.');
    }
  }, [loadReportsData]);

  // Handle report download
  const handleDownloadReport = useCallback(async (reportId: string) => {
    try {
      const response = await adminService.downloadReport(reportId);
      if (response.data?.downloadUrl) {
        // In a real app, this would trigger a download
        Alert.alert('Download Ready', 'Report download has started.');
      } else {
        Alert.alert('Error', 'Failed to prepare report download.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to download report.');
    }
  }, []);

  // Handle report scheduling
  const handleScheduleReport = useCallback(async (templateId: string, schedule: ReportSchedule) => {
    try {
      const response = await adminService.scheduleReport(templateId, schedule);
      if (response.data?.success) {
        Alert.alert('Success', 'Report has been scheduled successfully.');
        setScheduleModalVisible(false);
        loadReportsData(true);
      } else {
        Alert.alert('Error', 'Failed to schedule report.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to schedule report.');
    }
  }, [loadReportsData]);

  // Handle custom report creation
  const handleCreateCustomReport = useCallback(async () => {
    try {
      if (!reportBuilder.name.trim()) {
        Alert.alert('Error', 'Please enter a report name.');
        return;
      }

      const response = await adminService.createCustomReport(reportBuilder);
      if (response.data?.success) {
        Alert.alert('Success', 'Custom report template has been created.');
        setReportBuilderVisible(false);
        setReportBuilder({
          name: '',
          description: '',
          category: 'users',
          dateRange: { start: '', end: '' },
          filters: {},
          metrics: [],
          groupBy: '',
          format: 'pdf',
        });
        loadReportsData(true);
      } else {
        Alert.alert('Error', 'Failed to create custom report.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create custom report.');
    }
  }, [reportBuilder, loadReportsData]);

  // Render metric card
  const renderMetricCard = (metric: AnalyticsMetric) => {
    const changeColor = metric.changeType === 'increase' ? colors.success : 
                       metric.changeType === 'decrease' ? colors.error : colors.textSecondary;
    
    return (
      <View key={metric.id} style={styles.metricCard}>
        <View style={styles.metricHeader}>
          <Text style={styles.metricName}>{metric.name}</Text>
          <View style={styles.metricCategory}>
            <Text style={styles.metricCategoryText}>{metric.category}</Text>
          </View>
        </View>
        <View style={styles.metricContent}>
          <Text style={styles.metricValue}>
            {metric.format === 'currency' && '$'}
            {metric.value.toLocaleString()}
            {metric.format === 'percentage' && '%'}
          </Text>
          {metric.change !== undefined && (
            <View style={styles.metricChange}>
              <Text style={[styles.metricChangeText, { color: changeColor }]}>
                {metric.change > 0 ? '+' : ''}{metric.change}
                {metric.format === 'percentage' && '%'}
              </Text>
              <Text style={styles.metricChangeLabel}>vs previous</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Render report template item
  const renderReportTemplate = (template: ReportTemplate) => (
    <TouchableOpacity
      key={template.id}
      style={styles.templateItem}
      onPress={() => {
        setSelectedReport(template);
        setReportPreviewVisible(true);
      }}
    >
      <View style={styles.templateHeader}>
        <View style={styles.templateInfo}>
          <Text style={styles.templateName}>{template.name}</Text>
          <Text style={styles.templateDescription} numberOfLines={2}>
            {template.description}
          </Text>
        </View>
        <View style={styles.templateMeta}>
          <View style={[
            styles.templateCategoryBadge,
            { backgroundColor: getCategoryColor(template.category) + '20' }
          ]}>
            <Text style={[
              styles.templateCategoryText,
              { color: getCategoryColor(template.category) }
            ]}>
              {template.category.toUpperCase()}
            </Text>
          </View>
          <Switch
            value={template.isActive}
            onValueChange={(value) => handleToggleTemplate(template.id, value)}
            trackColor={{ false: colors.border, true: colors.primary + '40' }}
            thumbColor={template.isActive ? colors.primary : colors.textSecondary}
          />
        </View>
      </View>
      <View style={styles.templateFooter}>
        <View style={styles.templateActions}>
          <TouchableOpacity 
            style={styles.templateActionButton}
            onPress={() => handleGenerateReport(template.id, {})}
          >
            <FileText size={16} color={colors.primary} />
            <Text style={styles.templateActionText}>Generate</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.templateActionButton}
            onPress={() => {
              setSelectedReport(template);
              setScheduleModalVisible(true);
            }}
          >
            <Calendar size={16} color={colors.warning} />
            <Text style={styles.templateActionText}>Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.templateActionButton}
            onPress={() => {
              setSelectedReport(template);
              setReportPreviewVisible(true);
            }}
          >
            <Eye size={16} color={colors.textSecondary} />
            <Text style={styles.templateActionText}>Preview</Text>
          </TouchableOpacity>
        </View>
        {template.lastGenerated && (
          <Text style={styles.templateLastGenerated}>
            Last: {new Date(template.lastGenerated).toLocaleDateString()}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  // Render generated report item
  const renderGeneratedReport = (report: GeneratedReport) => (
    <View key={report.id} style={styles.generatedReportItem}>
      <View style={styles.generatedReportHeader}>
        <View style={styles.generatedReportInfo}>
          <Text style={styles.generatedReportName}>{report.templateName}</Text>
          <Text style={styles.generatedReportDate}>
            Generated: {new Date(report.generatedAt).toLocaleString()}
          </Text>
        </View>
        <View style={[
          styles.reportStatusBadge,
          { backgroundColor: getStatusColor(report.status) + '20' }
        ]}>
          <Text style={[
            styles.reportStatusText,
            { color: getStatusColor(report.status) }
          ]}>
            {report.status.toUpperCase()}
          </Text>
        </View>
      </View>
      <View style={styles.generatedReportFooter}>
        <View style={styles.reportMeta}>
          <Text style={styles.reportFormat}>{report.format.toUpperCase()}</Text>
          {report.fileSize && (
            <Text style={styles.reportSize}>
              {(report.fileSize / 1024 / 1024).toFixed(1)} MB
            </Text>
          )}
        </View>
        <View style={styles.reportActions}>
          {report.status === 'completed' && (
            <>
              <TouchableOpacity 
                style={styles.reportActionButton}
                onPress={() => handleDownloadReport(report.id)}
              >
                <Download size={16} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.reportActionButton}
                onPress={() => handleShareReport(report.id)}
              >
                <Share size={16} color={colors.success} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </View>
  );

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'users': return colors.primary;
      case 'financial': return colors.success;
      case 'listings': return colors.warning;
      case 'matching': return colors.info;
      case 'safety': return colors.error;
      case 'compliance': return colors.purple || colors.primary;
      default: return colors.textSecondary;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return colors.success;
      case 'generating': return colors.warning;
      case 'failed': return colors.error;
      default: return colors.textSecondary;
    }
  };

  // Handle toggle template
  const handleToggleTemplate = async (templateId: string, isActive: boolean) => {
    try {
      const response = await adminService.toggleReportTemplate(templateId, isActive);
      if (response.data?.success) {
        loadReportsData(true);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle report template.');
    }
  };

  // Handle share report
  const handleShareReport = async (reportId: string) => {
    Alert.alert(
      'Share Report',
      'How would you like to share this report?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Email', onPress: () => console.log('Email share') },
        { text: 'Download Link', onPress: () => console.log('Link share') },
      ]
    );
  };

  // Render tab buttons
  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      {[
        { key: 'overview', label: 'Overview', icon: BarChart3 },
        { key: 'templates', label: 'Templates', icon: FileText },
        { key: 'generated', label: 'Generated', icon: Download },
        { key: 'builder', label: 'Builder', icon: Plus },
      ].map((tab) => {
        const Icon = tab.icon;
        const isActive = selectedTab === tab.key;
        return (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              isActive && { backgroundColor: colors.primary + '20' }
            ]}
            onPress={() => setSelectedTab(tab.key as any)}
          >
            <Icon 
              size={16} 
              color={isActive ? colors.primary : colors.textSecondary} 
            />
            <Text style={[
              styles.tabLabel,
              { color: isActive ? colors.primary : colors.textSecondary }
            ]}>
              {tab.label}
            </Text>
            {tab.key === 'generated' && generatedReports.filter(r => r.status === 'completed').length > 0 && (
              <View style={styles.tabBadge}>
                <Text style={styles.tabBadgeText}>
                  {generatedReports.filter(r => r.status === 'completed').length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading reports data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Advanced Reports</Text>
          <Text style={styles.headerSubtitle}>
            Analytics & reporting dashboard
          </Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => setReportBuilderVisible(true)}
          >
            <Plus size={20} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleRefresh}>
            <RefreshCw size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      {renderTabButtons()}

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'overview' && (
          <>
            {/* Key Metrics */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Key Metrics</Text>
              <View style={styles.metricsGrid}>
                {analyticsMetrics.map(renderMetricCard)}
              </View>
            </View>

            {/* Quick Actions */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Quick Reports</Text>
              <View style={styles.quickActionsGrid}>
                <TouchableOpacity style={styles.quickActionCard}>
                  <Users size={24} color={colors.primary} />
                  <Text style={styles.quickActionTitle}>User Report</Text>
                  <Text style={styles.quickActionDescription}>
                    Generate user activity report
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.quickActionCard}>
                  <DollarSign size={24} color={colors.success} />
                  <Text style={styles.quickActionTitle}>Financial Report</Text>
                  <Text style={styles.quickActionDescription}>
                    Revenue and transaction analysis
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.quickActionCard}>
                  <Home size={24} color={colors.warning} />
                  <Text style={styles.quickActionTitle}>Listings Report</Text>
                  <Text style={styles.quickActionDescription}>
                    Property and listing analytics
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.quickActionCard}>
                  <Shield size={24} color={colors.error} />
                  <Text style={styles.quickActionTitle}>Safety Report</Text>
                  <Text style={styles.quickActionDescription}>
                    Security and compliance overview
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}

        {selectedTab === 'templates' && (
          <View style={styles.templatesList}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Report Templates</Text>
              <TouchableOpacity 
                style={styles.addButton}
                onPress={() => setReportBuilderVisible(true)}
              >
                <Plus size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
            {reportTemplates.map(renderReportTemplate)}
          </View>
        )}

        {selectedTab === 'generated' && (
          <View style={styles.generatedReportsList}>
            <Text style={styles.sectionTitle}>Generated Reports</Text>
            {generatedReports.map(renderGeneratedReport)}
          </View>
        )}

        {selectedTab === 'builder' && (
          <View style={styles.builderContainer}>
            <Text style={styles.sectionTitle}>Custom Report Builder</Text>
            <View style={styles.builderCard}>
              <Text style={styles.builderDescription}>
                Create custom reports with advanced filtering and data visualization options.
              </Text>
              <TouchableOpacity 
                style={styles.builderButton}
                onPress={() => setReportBuilderVisible(true)}
              >
                <Plus size={20} color={colors.white} />
                <Text style={styles.builderButtonText}>Start Building</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Report Builder Modal */}
      <Modal
        visible={reportBuilderVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setReportBuilderVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Custom Report Builder</Text>
            <TouchableOpacity onPress={() => setReportBuilderVisible(false)}>
              <X size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Report Name *</Text>
              <TextInput
                style={styles.formInput}
                value={reportBuilder.name}
                onChangeText={(text) => setReportBuilder(prev => ({ ...prev, name: text }))}
                placeholder="Enter report name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Description</Text>
              <TextInput
                style={[styles.formInput, styles.textArea]}
                value={reportBuilder.description}
                onChangeText={(text) => setReportBuilder(prev => ({ ...prev, description: text }))}
                placeholder="Enter report description"
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.createButton}
                onPress={handleCreateCustomReport}
              >
                <FileText size={20} color={colors.white} />
                <Text style={styles.createButtonText}>Create Report</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

// Styles
const createStyles = (colors: any, spacing: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
    alignItems: 'center',
  },
  actionButton: {
    padding: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    borderRadius: 8,
    gap: spacing.xs,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  tabBadge: {
    backgroundColor: colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.xs,
  },
  tabBadgeText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  addButton: {
    padding: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.primary + '20',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  metricCard: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    width: '48%',
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  metricName: {
    fontSize: 14,
    color: colors.textSecondary,
    flex: 1,
  },
  metricCategory: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  metricCategoryText: {
    fontSize: 10,
    color: colors.primary,
    fontWeight: 'bold',
  },
  metricContent: {
    gap: spacing.xs,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  metricChangeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  metricChangeLabel: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  quickActionCard: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    width: '48%',
    alignItems: 'center',
    gap: spacing.sm,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
  },
  quickActionDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  templatesList: {
    gap: spacing.md,
  },
  templateItem: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  templateInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  templateDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  templateMeta: {
    alignItems: 'flex-end',
    gap: spacing.sm,
  },
  templateCategoryBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
  },
  templateCategoryText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  templateFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  templateActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  templateActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  templateActionText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  templateLastGenerated: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  generatedReportsList: {
    gap: spacing.md,
  },
  generatedReportItem: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  generatedReportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  generatedReportInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  generatedReportName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  generatedReportDate: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  reportStatusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
  },
  reportStatusText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  generatedReportFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reportMeta: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  reportFormat: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  reportSize: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  reportActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  reportActionButton: {
    padding: spacing.xs,
    borderRadius: 6,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  builderContainer: {
    gap: spacing.lg,
  },
  builderCard: {
    backgroundColor: colors.surface,
    padding: spacing.xl,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
    gap: spacing.lg,
  },
  builderDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  builderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  builderButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },
  formGroup: {
    marginBottom: spacing.lg,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  formInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: 16,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalActions: {
    gap: spacing.md,
    marginTop: spacing.xl,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    padding: spacing.md,
    borderRadius: 12,
    gap: spacing.sm,
  },
  createButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
}); 