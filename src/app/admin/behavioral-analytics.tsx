import React, { useEffect, useState } from 'react';

import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  FlatList,
} from 'react-native';

import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { supabase } from "@utils/supabaseUtils";
import { colors } from '@constants/colors';

interface BehavioralData {
  userId: string;
  username: string;
  riskScore: number;
  severity: 'low' | 'medium' | 'high';
  detectedAt: string;
  patterns: any[];
  status: string;
}

export default function BehavioralAnalytics() {
  const router = useRouter();
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [behavioralData, setBehavioralData] = useState<BehavioralData[]>([]);
  const [expandedUsers, setExpandedUsers] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadBehavioralData();
  }, []);

  const loadBehavioralData = async () => {
    setLoading(true);
    try {
      // Check if user is admin
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: profileData } = await supabase
        .from('user_profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profileData || profileData.role !== 'admin') {
        throw new Error('Unauthorized: Admin access required');
      }

      // Get behavioral analytics data
      const { data: analyticsData, error } = await supabase
        .from('behavioral_analytics')
        .select(
          `
          id,
          user_id,
          detected_patterns,
          risk_score,
          severity,
          detected_at,
          status,
          user_profiles (username, display_name)
          `
        )
        .order('risk_score', { ascending: false })
        .limit(50);

      if (error) {
        throw error;
      }

      // Format data for display
      const formattedData: BehavioralData[] = analyticsData.map((item) => ({
        userId: item.user_id,
        username: item.user_profiles?.username || item.user_profiles?.display_name || 'Unknown',
        riskScore: item.risk_score,
        severity: item.severity,
        detectedAt: new Date(item.detected_at).toLocaleString(),
        patterns: item.detected_patterns || [],
        status: item.status,
      }));

      setBehavioralData(formattedData);
    } catch (error) {
      console.error('Error loading behavioral data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBehavioralData();
    setRefreshing(false);
  };

  const toggleUserExpand = (userId: string) => {
    setExpandedUsers((prev) => ({
      ...prev,
      [userId]: !prev[userId],
    }));
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getStatusBadge = (status: string) => {
    let color = '';
    let icon = '';

    switch (status) {
      case 'pending_review':
        color = '#f59e0b';
        icon = 'clock-outline';
        break;
      case 'confirmed_fraud':
        color = '#ef4444';
        icon = 'alert-circle';
        break;
      case 'false_positive':
        color = '#10b981';
        icon = 'check-circle';
        break;
      case 'under_monitoring':
        color = '#3b82f6';
        icon = 'eye-outline';
        break;
      case 'resolved':
        color = '#6b7280';
        icon = 'check';
        break;
      default:
        color = '#6b7280';
        icon = 'help-circle-outline';
    }

    return (
      <View style={[styles.statusBadge, { backgroundColor: color + '20' }]}>
        <MaterialCommunityIcons name={icon} size={14} color={color} />
        <Text style={[styles.statusText, { color }]}>
          {status
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')}
        </Text>
      </View>
    );
  };

  const renderUserItem = ({ item }: { item: BehavioralData }) => {
    const isExpanded = expandedUsers[item.userId] || false;
    
    const handleMonitorAction = async () => {
      if (!user) return;
      
      try {
        const { error } = await supabase
          .from('behavioral_analytics')
          .update({
            status: 'under_monitoring',
            reviewer_id: user.id,
            reviewed_at: new Date().toISOString()
          })
          .eq('user_id', item.userId);
          
        if (error) {
          console.error('Error updating status:', error);
          return;
        }
        
        // Update local state
        setBehavioralData(prevData => 
          prevData.map(data => 
            data.userId === item.userId 
              ? { ...data, status: 'under_monitoring' } 
              : data
          )
        );
      } catch (error) {
        console.error('Error in monitor action:', error);
      }
    };
    
    const handleFlagAction = async () => {
      if (!user) return;
      
      try {
        const { error } = await supabase
          .from('behavioral_analytics')
          .update({
            status: 'confirmed_fraud',
            reviewer_id: user.id,
            reviewed_at: new Date().toISOString()
          })
          .eq('user_id', item.userId);
          
        if (error) {
          console.error('Error updating status:', error);
          return;
        }
        
        // Notify security team about confirmed fraud cases
        try {
          await supabase.functions.invoke('notify-security-team', {
            body: {
              userId: item.userId,
              username: item.username,
              riskScore: item.riskScore,
              reviewerId: user.id,
              patterns: item.patterns
            }
          });
        } catch (notifyError) {
          console.error('Error notifying security team:', notifyError);
        }
        
        // Update local state
        setBehavioralData(prevData => 
          prevData.map(data => 
            data.userId === item.userId 
              ? { ...data, status: 'confirmed_fraud' } 
              : data
          )
        );
      } catch (error) {
        console.error('Error in flag action:', error);
      }
    };

    return (
      <View style={styles.userCard}>
        <TouchableOpacity
          style={styles.userHeader}
          onPress={() => toggleUserExpand(item.userId)}
        >
          <View style={styles.userInfo}>
            <Text style={styles.username}>{item.username}</Text>
            <Text style={styles.userId}>{item.userId}</Text>
          </View>

          <View style={styles.statsContainer}>
            <View
              style={[
                styles.riskScoreBadge,
                { backgroundColor: getSeverityColor(item.severity) + '20' },
              ]}
            >
              <Text
                style={[styles.riskScoreText, { color: getSeverityColor(item.severity) }]}
              >
                {item.riskScore}
              </Text>
            </View>
            {getStatusBadge(item.status)}
            <MaterialCommunityIcons
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={24}
              color="#6b7280"
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.detailsContainer}>
            <Text style={styles.detailsTitle}>Detected Patterns</Text>
            {item.patterns.map((pattern, index) => (
              <View key={index} style={styles.patternItem}>
                <View
                  style={[
                    styles.severityIndicator,
                    { backgroundColor: getSeverityColor(pattern.severity) },
                  ]}
                />
                <View style={styles.patternDetails}>
                  <Text style={styles.patternType}>{pattern.pattern_type}</Text>
                  <Text style={styles.patternDescription}>{pattern.description}</Text>
                  <Text style={styles.confidenceText}>
                    Confidence: {Math.round(pattern.confidence * 100)}%
                  </Text>
                </View>
              </View>
            ))}

            <Text style={styles.detailsFooter}>Detected at: {item.detectedAt}</Text>

            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={[styles.actionButton, styles.viewButton]}
                onPress={() => router.push(`/admin/user/${item.userId}`)}
              >
                <MaterialCommunityIcons name="account-details" size={16} color="#3b82f6" />
                <Text style={styles.viewButtonText}>View User</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.monitorButton]}
                onPress={handleMonitorAction}
              >
                <MaterialCommunityIcons name="eye-outline" size={16} color="#8b5cf6" />
                <Text style={styles.monitorButtonText}>Monitor</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.flagButton]}
                onPress={handleFlagAction}
              >
                <MaterialCommunityIcons name="flag-outline" size={16} color="#ef4444" />
                <Text style={styles.flagButtonText}>Flag</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Behavioral Analytics',
          headerBackTitle: 'Admin',
        }}
      />

      <View style={styles.container}>
        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
            <Text style={styles.loadingText}>Loading behavioral analytics data...</Text>
          </View>
        ) : (
          <FlatList
            data={behavioralData}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.userId}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <MaterialCommunityIcons name="chart-line" size={48} color="#d1d5db" />
                <Text style={styles.emptyText}>No behavioral analytics data found</Text>
              </View>
            }
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  listContainer: {
    padding: 16,
  },
  userCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userHeader: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  userId: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  riskScoreBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  riskScoreText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  detailsContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  patternItem: {
    flexDirection: 'row',
    marginBottom: 12,
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
  },
  severityIndicator: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  patternDetails: {
    flex: 1,
  },
  patternType: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  patternDescription: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 4,
  },
  confidenceText: {
    fontSize: 12,
    color: '#6b7280',
  },
  detailsFooter: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 8,
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 8,
  },
  viewButton: {
    backgroundColor: '#eff6ff',
  },
  viewButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
  monitorButton: {
    backgroundColor: '#f5f3ff',
  },
  monitorButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#8b5cf6',
    fontWeight: '500',
  },
  flagButton: {
    backgroundColor: '#fef2f2',
  },
  flagButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#ef4444',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
});