import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,

  Platform,
} from 'react-native';
import { Stack } from 'expo-router';
import { useRouter, usePathname } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Users,
  Home,
  Shield,
  FileText,
  BarChart2,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  AlertTriangle,
  CreditCard,
  MessageSquare,
  Zap,
  Eye,
  UserCheck,
  Flag,
  TrendingUp,
  Server,
  HelpCircle,
  ChevronRight,
} from 'lucide-react-native';

import { useTheme } from '../../design-system/ThemeProvider';
import { adminService } from '../../services/adminService';
import AdminAccessGuard from '../../components/admin/AdminAccessGuard';

interface AdminMenuItem {
  key: string;
  title: string;
  icon: React.ComponentType<any>;
  route: string;
  badge?: number;
  color?: string;
  description?: string;
  category: 'main' | 'moderation' | 'analytics' | 'settings';
}

interface AdminNotificationCounts {
  pendingUsers: number;
  flaggedContent: number;
  suspiciousProfiles: number;
  pendingRooms: number;
  criticalAlerts: number;
}

const AdminDrawerContent = () => {
  const { colors, spacing } = useTheme();
  const styles = createStyles(colors, spacing);
  const router = useRouter();
  const pathname = usePathname();
  const { width } = useWindowDimensions();

  // State for notification counts
  const [notificationCounts, setNotificationCounts] = useState<AdminNotificationCounts>({
    pendingUsers: 0,
    flaggedContent: 0,
    suspiciousProfiles: 0,
    pendingRooms: 0,
    criticalAlerts: 0,
  });

  const [loading, setLoading] = useState(true);
  const [adminUser, setAdminUser] = useState<any>(null);

  // Load notification counts
  useEffect(() => {
    const loadNotificationCounts = async () => {
      try {
        const response = await adminService.getNotificationCounts();
        if (response.data) {
          setNotificationCounts(response.data);
        }
      } catch (error) {
        console.error('Error loading notification counts:', error);
      } finally {
        setLoading(false);
      }
    };

    const loadAdminUser = async () => {
      try {
        const response = await adminService.getCurrentAdminUser();
        if (response.data) {
          setAdminUser(response.data);
        }
      } catch (error) {
        console.error('Error loading admin user:', error);
      }
    };

    loadNotificationCounts();
    loadAdminUser();

    // Refresh counts every 30 seconds
    const interval = setInterval(loadNotificationCounts, 30000);
    return () => clearInterval(interval);
  }, []);

  // Define admin menu items
  const menuItems: AdminMenuItem[] = [
    // Main Navigation
    {
      key: 'dashboard',
      title: 'Dashboard',
      icon: Home,
      route: '/admin',
      color: colors.primary,
      description: 'Overview and key metrics',
      category: 'main',
    },
    {
      key: 'users',
      title: 'User Management',
      icon: Users,
      route: '/admin/users',
      badge: notificationCounts.pendingUsers,
      color: colors.primary,
      description: 'Manage users and accounts',
      category: 'main',
    },
    {
      key: 'rooms',
      title: 'Room Listings',
      icon: Home,
      route: '/admin/rooms',
      badge: notificationCounts.pendingRooms,
      color: colors.info,
      description: 'Manage room listings',
      category: 'main',
    },

    // Moderation
    {
      key: 'content-moderation',
      title: 'Content Moderation',
      icon: FileText,
      route: '/admin/content-moderation',
      badge: notificationCounts.flaggedContent,
      color: colors.warning,
      description: 'Review flagged content',
      category: 'moderation',
    },
    {
      key: 'suspicious-profiles',
      title: 'Security & Fraud',
      icon: Shield,
      route: '/admin/suspicious-profiles',
      badge: notificationCounts.suspiciousProfiles,
      color: colors.error,
      description: 'Monitor suspicious activity',
      category: 'moderation',
    },
    {
      key: 'verification',
      title: 'Verification Queue',
      icon: UserCheck,
      route: '/admin/verification',
      color: colors.success,
      description: 'User verification requests',
      category: 'moderation',
    },

    // Analytics
    {
      key: 'analytics',
      title: 'Platform Analytics',
      icon: BarChart2,
      route: '/admin/analytics',
      color: colors.info,
      description: 'Platform performance metrics',
      category: 'analytics',
    },
    {
      key: 'behavioral-analytics',
      title: 'Behavioral Analytics',
      icon: TrendingUp,
      route: '/admin/behavioral-analytics',
      color: colors.info,
      description: 'User behavior insights',
      category: 'analytics',
    },
    {
      key: 'payment-fraud',
      title: 'Payment Monitoring',
      icon: CreditCard,
      route: '/admin/payment-fraud-dashboard',
      color: colors.warning,
      description: 'Financial fraud detection',
      category: 'analytics',
    },

    // Settings & Tools
    {
      key: 'production-audit',
      title: 'Production Audit',
      icon: Server,
      route: '/admin/production-audit',
      color: colors.info,
      description: 'System health monitoring',
      category: 'settings',
    },
    {
      key: 'emergency',
      title: 'Emergency Tools',
      icon: Zap,
      route: '/admin/emergency',
      badge: notificationCounts.criticalAlerts,
      color: colors.error,
      description: 'Emergency response tools',
      category: 'settings',
    },
    {
      key: 'settings',
      title: 'Admin Settings',
      icon: Settings,
      route: '/admin/settings',
      color: colors.textSecondary,
      description: 'System configuration',
      category: 'settings',
    },
  ];

  // Handle navigation
  const handleNavigation = (route: string) => {
    router.push(route);
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout from admin panel?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await adminService.logout();
              // Navigation will be handled automatically by NavigationHandler
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Render menu item
  const renderMenuItem = (item: AdminMenuItem) => {
    const isActive = pathname === item.route || 
      (item.route !== '/admin' && pathname.startsWith(item.route));
    const IconComponent = item.icon;

    return (
      <TouchableOpacity
        key={item.key}
        style={[
          styles.menuItem,
          isActive && styles.activeMenuItem
        ]}
        onPress={() => handleNavigation(item.route)}
      >
        <View style={styles.menuItemLeft}>
          <View style={[
            styles.menuItemIcon,
            { backgroundColor: (item.color || colors.textSecondary) + '20' }
          ]}>
            <IconComponent 
              size={20} 
              color={isActive ? colors.primary : (item.color || colors.textSecondary)} 
            />
          </View>
          
          <View style={styles.menuItemText}>
            <Text style={[
              styles.menuItemTitle,
              isActive && styles.activeMenuItemTitle
            ]}>
              {item.title}
            </Text>
            {item.description && (
              <Text style={styles.menuItemDescription}>
                {item.description}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.menuItemRight}>
          {item.badge && item.badge > 0 && (
            <View style={[
              styles.badge,
              { backgroundColor: item.color || colors.error }
            ]}>
              <Text style={styles.badgeText}>
                {item.badge > 99 ? '99+' : item.badge}
              </Text>
            </View>
          )}
          <ChevronRight size={16} color={colors.textSecondary} />
        </View>
      </TouchableOpacity>
    );
  };

  // Render menu section
  const renderMenuSection = (title: string, category: 'main' | 'moderation' | 'analytics' | 'settings') => {
    const items = menuItems.filter(item => item.category === category);
    if (items.length === 0) return null;

    return (
      <View style={styles.menuSection}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {items.map(renderMenuItem)}
      </View>
    );
  };

  // Calculate total notifications
  const totalNotifications = Object.values(notificationCounts).reduce((sum, count) => sum + count, 0);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Admin Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileInfo}>
            <View style={styles.profileAvatar}>
              <Text style={styles.profileAvatarText}>
                {adminUser?.name?.charAt(0)?.toUpperCase() || 'A'}
              </Text>
            </View>
            <View style={styles.profileDetails}>
              <Text style={styles.profileName}>
                {adminUser?.name || 'Admin User'}
              </Text>
              <Text style={styles.profileRole}>
                {adminUser?.role || 'Administrator'}
              </Text>
              <Text style={styles.profileEmail}>
                {adminUser?.email || '<EMAIL>'}
              </Text>
            </View>
          </View>

          {/* Notification Summary */}
          {totalNotifications > 0 && (
            <View style={styles.notificationSummary}>
              <Bell size={16} color={colors.warning} />
              <Text style={styles.notificationText}>
                {totalNotifications} pending action{totalNotifications !== 1 ? 's' : ''}
              </Text>
            </View>
          )}
        </View>

        {/* Menu Sections */}
        {renderMenuSection('Main', 'main')}
        {renderMenuSection('Moderation', 'moderation')}
        {renderMenuSection('Analytics', 'analytics')}
        {renderMenuSection('Settings & Tools', 'settings')}

        {/* System Status */}
        <View style={styles.systemStatus}>
          <View style={styles.statusItem}>
            <Server size={16} color={colors.success} />
            <Text style={styles.statusText}>System Healthy</Text>
          </View>
          <View style={styles.statusItem}>
            <Eye size={16} color={colors.info} />
            <Text style={styles.statusText}>
              {adminUser?.lastLogin ? 
                `Last login: ${new Date(adminUser.lastLogin).toLocaleDateString()}` :
                'First login'
              }
            </Text>
          </View>
        </View>

        {/* Help & Support */}
        <TouchableOpacity style={styles.helpButton}>
          <HelpCircle size={20} color={colors.textSecondary} />
          <Text style={styles.helpText}>Help & Support</Text>
        </TouchableOpacity>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <LogOut size={20} color={colors.error} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const AdminLayout = () => {
  const { colors } = useTheme();

  return (
    <AdminAccessGuard requiredRole="admin" fallbackRoute="/">
    <Stack
      screenOptions={{
        headerStyle: {
            backgroundColor: colors.surface,
            shadowColor: colors.border,
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 3,
        },
          headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: 'bold',
            fontSize: 18,
        },
        }}
      >
        <Stack.Screen 
          name="index" 
          options={{ 
            title: 'Admin Dashboard',
            headerShown: true 
      }}
    />
        <Stack.Screen 
          name="users" 
          options={{ 
            title: 'User Management',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="content-moderation" 
          options={{ 
            title: 'Content Moderation',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="analytics" 
          options={{ 
            title: 'Analytics',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="suspicious-profiles" 
          options={{ 
            title: 'Security & Fraud',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="payment-fraud-dashboard" 
          options={{ 
            title: 'Payment Monitoring',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="behavioral-analytics" 
          options={{ 
            title: 'Behavioral Analytics',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="device-fingerprints" 
          options={{ 
            title: 'Device Fingerprints',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="provider-verification" 
          options={{ 
            title: 'Provider Verification',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="verification" 
          options={{ 
            title: 'Verification Queue',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="logs" 
          options={{ 
            title: 'System Logs',
            headerShown: true 
          }} 
        />
        <Stack.Screen 
          name="login" 
          options={{ 
            title: 'Admin Login',
            headerShown: true 
          }} 
        />
      </Stack>
    </AdminAccessGuard>
  );
};

const createStyles = (colors: any, spacing: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  scrollView: {
    flex: 1,
  },
  profileSection: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.primary + '10',
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  profileAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  profileAvatarText: {
    color: colors.surface,
    fontSize: 20,
    fontWeight: 'bold',
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  profileRole: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  profileEmail: {
    fontSize: 11,
    color: colors.textSecondary,
  },
  notificationSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning + '20',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  notificationText: {
    marginLeft: spacing.sm,
    fontSize: 12,
    color: colors.warning,
    fontWeight: '500',
  },
  menuSection: {
    paddingVertical: spacing.md,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 1,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.sm,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginHorizontal: spacing.md,
    borderRadius: 8,
  },
  activeMenuItem: {
    backgroundColor: colors.primary + '15',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 2,
  },
  activeMenuItemTitle: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  menuItemDescription: {
    fontSize: 11,
    color: colors.textSecondary,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
    marginRight: spacing.sm,
  },
  badgeText: {
    color: colors.surface,
    fontSize: 10,
    fontWeight: 'bold',
  },
  systemStatus: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: spacing.sm,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: spacing.sm,
    fontSize: 12,
    color: colors.textSecondary,
  },
  helpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginHorizontal: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.background,
  },
  helpText: {
    marginLeft: spacing.sm,
    fontSize: 14,
    color: colors.textSecondary,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginHorizontal: spacing.md,
    marginBottom: spacing.lg,
    borderRadius: 8,
    backgroundColor: colors.error + '10',
  },
  logoutText: {
    marginLeft: spacing.sm,
    fontSize: 14,
    color: colors.error,
    fontWeight: '500',
  },
});

export default AdminLayout;
