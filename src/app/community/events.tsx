import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { CommunityEvent, EventCategory, CommunityService } from '@services/communityService';
import { EventCard } from '@components/community/EventCard';
import { CalendarView } from '@components/community/CalendarView';
import { Button } from '@design-system';
import { useAuth } from '@hooks/useAuth';

type ViewMode = 'list' | 'calendar';
type FilterStatus = 'all' | 'upcoming' | 'past' | 'my_events';

export default function CommunityEventsScreen() {
  const router = useRouter();
  const { authState } = useAuth();
  const user = authState?.user;
  const communityService = new CommunityService();

  // State management
  const [events, setEvents] = useState<CommunityEvent[]>([]);
  const [categories, setCategories] = useState<EventCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('upcoming');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showFilters, setShowFilters] = useState(false);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Filter events when filters change
  useEffect(() => {
    if (!loading) {
      loadEvents();
    }
  }, [selectedCategory, filterStatus, searchQuery]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [eventsData, categoriesData] = await Promise.all([
        loadEvents(),
        communityService.getEventCategories(),
      ]);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load events. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadEvents = async () => {
    try {
      const filters: any = {};

      // Apply category filter
      if (selectedCategory) {
        filters.category_id = selectedCategory;
      }

      // Apply status filter
      const now = new Date().toISOString();
      switch (filterStatus) {
        case 'upcoming':
          filters.start_date = now;
          filters.status = 'active';
          break;
        case 'past':
          filters.end_date = now;
          filters.status = 'completed';
          break;
        case 'my_events':
          if (user) {
            filters.organizer_id = user.id;
          }
          break;
        case 'all':
        default:
          filters.status = 'active';
          break;
      }

      let eventsData: CommunityEvent[];

      if (searchQuery.trim()) {
        eventsData = await communityService.searchEvents(searchQuery, filters);
      } else {
        eventsData = await communityService.getCommunityEvents(filters);
      }

      setEvents(eventsData);
      return eventsData;
    } catch (error) {
      console.error('Error loading events:', error);
      throw error;
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadEvents();
    } catch (error) {
      console.error('Error refreshing events:', error);
    } finally {
      setRefreshing(false);
    }
  }, [selectedCategory, filterStatus, searchQuery]);

  const handleEventPress = (event: CommunityEvent) => {
    router.push(`/community/event/${event.id}`);
  };

  const handleCreateEvent = () => {
    if (!user) {
      Alert.alert('Authentication Required', 'Please log in to create events');
      return;
    }
    router.push('/community/create-event');
  };

  const handleRSVPUpdate = (updatedEvent: CommunityEvent) => {
    setEvents(prevEvents =>
      prevEvents.map(event => (event.id === updatedEvent.id ? updatedEvent : event))
    );
  };

  const handleDatePress = (date: Date) => {
    setSelectedDate(date);
    // Filter events for selected date if needed
  };

  const getFilteredEvents = () => {
    if (viewMode === 'calendar') {
      return events;
    }

    // For list view, we can apply additional client-side filtering if needed
    return events;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.headerTitle}>Community Events</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Ionicons name="filter" size={24} color="#1976d2" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, styles.viewToggleButton]}
            onPress={() => setViewMode(viewMode === 'list' ? 'calendar' : 'list')}
          >
            <Ionicons name={viewMode === 'list' ? 'calendar' : 'list'} size={24} color="#1976d2" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search events..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Filters */}
      {showFilters && (
        <View style={styles.filtersContainer}>
          {/* Status Filter */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
            {[
              { key: 'all', label: 'All Events' },
              { key: 'upcoming', label: 'Upcoming' },
              { key: 'past', label: 'Past' },
              { key: 'my_events', label: 'My Events' },
            ].map(filter => (
              <TouchableOpacity
                key={filter.key}
                style={[styles.filterChip, filterStatus === filter.key && styles.activeFilterChip]}
                onPress={() => setFilterStatus(filter.key as FilterStatus)}
              >
                <Text
                  style={[
                    styles.filterChipText,
                    filterStatus === filter.key && styles.activeFilterChipText,
                  ]}
                >
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Category Filter */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
            <TouchableOpacity
              style={[styles.filterChip, !selectedCategory && styles.activeFilterChip]}
              onPress={() => setSelectedCategory('')}
            >
              <Text
                style={[styles.filterChipText, !selectedCategory && styles.activeFilterChipText]}
              >
                All Categories
              </Text>
            </TouchableOpacity>
            {categories.map(category => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.filterChip,
                  selectedCategory === category.id && styles.activeFilterChip,
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text
                  style={[
                    styles.filterChipText,
                    selectedCategory === category.id && styles.activeFilterChipText,
                  ]}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );

  const renderListView = () => (
    <ScrollView
      style={styles.listContainer}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
      showsVerticalScrollIndicator={false}
    >
      {getFilteredEvents().length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="calendar-outline" size={64} color="#ccc" />
          <Text style={styles.emptyStateTitle}>No Events Found</Text>
          <Text style={styles.emptyStateText}>
            {searchQuery
              ? 'Try adjusting your search or filters'
              : 'Be the first to create an event for your community!'}
          </Text>
          {!searchQuery && (
            <Button
              title="Create Event"
              onPress={handleCreateEvent}
              style={styles.createEventButton}
            />
          )}
        </View>
      ) : (
        <View style={styles.eventsGrid}>
          {getFilteredEvents().map(event => (
            <EventCard
              key={event.id}
              event={event}
              onPress={handleEventPress}
              onRSVPUpdate={handleRSVPUpdate}
              showRSVPButton={true}
            />
          ))}
        </View>
      )}
    </ScrollView>
  );

  const renderCalendarView = () => (
    <CalendarView
      events={getFilteredEvents()}
      onEventPress={handleEventPress}
      onDatePress={handleDatePress}
      selectedDate={selectedDate}
      viewMode="month"
    />
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading events...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      <View style={styles.content}>
        {viewMode === 'list' ? renderListView() : renderCalendarView()}
      </View>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab} onPress={handleCreateEvent}>
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  viewToggleButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  filtersContainer: {
    marginTop: 8,
  },
  filterRow: {
    marginBottom: 8,
  },
  filterChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: '#1976d2',
  },
  filterChipText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterChipText: {
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  listContainer: {
    flex: 1,
  },
  eventsGrid: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  createEventButton: {
    backgroundColor: '#1976d2',
    paddingHorizontal: 24,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#1976d2',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});
