#!/usr/bin/env node

const { spawn } = require('child_process');
const QRCode = require('qrcode');
const fs = require('fs');

console.log('🚀 WeRoomies Proper Testing Distribution Setup');
console.log('===============================================');

// Method 1: Try Expo Go with tunneling (PREFERRED)
async function setupExpoGoTesting() {
  console.log('\n📱 Method 1: Expo Go Testing (PREFERRED)');
  console.log('=========================================');
  
  try {
    console.log('🔄 Starting Expo with tunneling...');
    
    // Start expo with tunnel in background
    const expoProcess = spawn('npx', ['expo', 'start', '--tunnel'], {
      stdio: 'pipe',
      detached: false
    });
    
    let tunnelUrl = null;
    let qrCode = null;
    
    // Listen for tunnel URL in output
    expoProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Expo output:', output);
      
      // Look for tunnel URL pattern
      const urlMatch = output.match(/exp:\/\/[^\s]+/);
      if (urlMatch) {
        tunnelUrl = urlMatch[0];
        console.log('✅ Found tunnel URL:', tunnelUrl);
      }
      
      // Look for QR code
      if (output.includes('█')) {
        qrCode = output;
      }
    });
    
    expoProcess.stderr.on('data', (data) => {
      console.log('Expo error:', data.toString());
    });
    
    // Wait for tunnel to be ready
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    if (tunnelUrl) {
      console.log('✅ Expo Go tunnel ready!');
      console.log('📱 Testers should:');
      console.log('1. Install Expo Go app from App Store/Play Store');
      console.log('2. Scan the QR code shown in terminal');
      console.log('3. App will load directly in Expo Go');
      console.log('\n🔗 Tunnel URL:', tunnelUrl);
      
      return { success: true, url: tunnelUrl, method: 'expo-go' };
    } else {
      throw new Error('Tunnel URL not found');
    }
    
  } catch (error) {
    console.log('❌ Expo Go tunneling failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Method 2: Web version testing
async function setupWebTesting() {
  console.log('\n🌐 Method 2: Web Version Testing');
  console.log('=================================');
  
  try {
    console.log('🔄 Starting web development server...');
    
    // Start expo web
    const webProcess = spawn('npx', ['expo', 'start', '--web'], {
      stdio: 'pipe',
      detached: false
    });
    
    // Wait for web server to start
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const webUrl = 'http://localhost:8081';
    const cloudflareUrl = 'https://dedicated-western-workstation-fall.trycloudflare.com';
    
    // Generate QR codes for web version
    const localQR = await QRCode.toString(webUrl, { type: 'terminal', small: true });
    const cloudflareQR = await QRCode.toString(cloudflareUrl, { type: 'terminal', small: true });
    
    console.log('✅ Web version ready!');
    console.log('\n🏠 LOCAL NETWORK QR CODE:');
    console.log(localQR);
    console.log('URL:', webUrl);
    
    console.log('\n🌍 EXTERNAL ACCESS QR CODE:');
    console.log(cloudflareQR);
    console.log('URL:', cloudflareUrl);
    
    console.log('\n📱 Testers should:');
    console.log('1. Scan QR code with phone camera');
    console.log('2. Open the link in mobile browser');
    console.log('3. App runs as Progressive Web App');
    
    // Save QR images
    await QRCode.toFile('web-testing-local.png', webUrl);
    await QRCode.toFile('web-testing-external.png', cloudflareUrl);
    
    return { 
      success: true, 
      localUrl: webUrl, 
      externalUrl: cloudflareUrl,
      method: 'web' 
    };
    
  } catch (error) {
    console.log('❌ Web testing setup failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Method 3: Development build (requires EAS)
async function setupDevelopmentBuild() {
  console.log('\n📦 Method 3: Development Build');
  console.log('==============================');
  
  console.log('⚠️  Development builds require EAS CLI and account');
  console.log('This method creates actual mobile apps for testing');
  console.log('\nTo set up development builds:');
  console.log('1. npm install -g @expo/cli');
  console.log('2. expo login');
  console.log('3. eas build --platform ios --profile development');
  console.log('4. eas build --platform android --profile development');
  console.log('\n📱 Testers install the built app directly');
  
  return { success: true, method: 'development-build', note: 'Manual setup required' };
}

// Main execution
async function main() {
  const results = [];
  
  // Try each method
  console.log('🔍 Testing all available methods...\n');
  
  // Method 1: Expo Go (preferred)
  const expoResult = await setupExpoGoTesting();
  results.push(expoResult);
  
  // Method 2: Web version
  const webResult = await setupWebTesting();
  results.push(webResult);
  
  // Method 3: Development build info
  const devBuildResult = await setupDevelopmentBuild();
  results.push(devBuildResult);
  
  // Summary
  console.log('\n📊 TESTING SETUP SUMMARY');
  console.log('========================');
  
  const workingMethods = results.filter(r => r.success);
  
  if (workingMethods.length === 0) {
    console.log('❌ No testing methods are currently working');
    console.log('💡 Recommended actions:');
    console.log('1. Check internet connection');
    console.log('2. Restart Expo development server');
    console.log('3. Try: npx expo start --tunnel --clear');
  } else {
    console.log('✅ Working testing methods:');
    workingMethods.forEach((method, index) => {
      console.log(`${index + 1}. ${method.method.toUpperCase()}: ${method.url || 'Ready'}`);
    });
  }
  
  // Generate comprehensive tester instructions
  const instructions = `
# WeRoomies Testing Instructions - CORRECTED

## 🎯 IMPORTANT: Choose the Right Method

### Method 1: Expo Go App (RECOMMENDED)
**If you see a QR code in the terminal:**
1. Download "Expo Go" app from App Store/Play Store
2. Open Expo Go app
3. Scan the QR code from your terminal
4. App loads directly in Expo Go

### Method 2: Web Browser Testing
**If Expo Go doesn't work:**
1. Scan the web QR code with your phone camera
2. Open the link in your mobile browser (Chrome/Safari)
3. App runs as a web app in your browser

### Method 3: Development Build
**For production-like testing:**
- Requires building actual mobile apps
- Contact developer for build files

## 🔧 Troubleshooting
- **QR code not working?** Make sure you're using the right method
- **Expo Go shows error?** Try web version instead  
- **Web version not loading?** Check if you're on the same WiFi network

Generated: ${new Date().toISOString()}
Status: ${workingMethods.length} of 3 methods working
`;

  fs.writeFileSync('CORRECTED_TESTING_INSTRUCTIONS.md', instructions);
  console.log('\n📄 Instructions saved to: CORRECTED_TESTING_INSTRUCTIONS.md');
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { setupExpoGoTesting, setupWebTesting, setupDevelopmentBuild }; 